<?php
/**
 * AJAX endpoint to get cash box movement details
 */

// Required includes
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/cash_manager.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize classes
$auth = new Auth();
$db = new Database();
$cashManager = new CashManager($db);

try {
    // Authentication check
    $auth->requireLogin();
    $auth->requirePermission('cash.view');
    
    // Get movement ID
    $movementId = (int)($_GET['id'] ?? 0);
    
    if ($movementId <= 0) {
        throw new Exception('معرف الحركة غير صالح');
    }
    
    // Get movement details
    $movement = $cashManager->getCashMovementById($movementId);
    
    if (!$movement) {
        throw new Exception('الحركة غير موجودة');
    }
    
    // Get cash box details
    $cashBox = $cashManager->getCashBoxById($movement['cash_box_id']);
    
    if (!$cashBox) {
        throw new Exception('الصندوق غير موجود');
    }
    
    // Build HTML content
    $html = '
    <div class="row">
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-info-circle me-2"></i>
                معلومات الحركة
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">رقم الحركة:</td>
                    <td><code>' . htmlspecialchars($movement['id']) . '</code></td>
                </tr>
                <tr>
                    <td class="fw-medium">نوع الحركة:</td>
                    <td>';
    
    if ($movement['type'] === 'deposit') {
        $html .= '<span class="badge bg-success"><i class="fas fa-plus me-1"></i> إيداع</span>';
    } else {
        $html .= '<span class="badge bg-warning"><i class="fas fa-minus me-1"></i> سحب</span>';
    }
    
    $html .= '</td>
                </tr>
                <tr>
                    <td class="fw-medium">المبلغ:</td>
                    <td class="fw-bold ' . ($movement['type'] === 'deposit' ? 'text-success' : 'text-warning') . '">
                        ' . ($movement['type'] === 'deposit' ? '+' : '-') . number_format($movement['amount'], 2) . ' 
                        ' . htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']) . '
                    </td>
                </tr>
                <tr>
                    <td class="fw-medium">الرصيد قبل الحركة:</td>
                    <td>' . number_format($movement['balance_before'], 2) . ' ' . htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">الرصيد بعد الحركة:</td>
                    <td class="fw-bold">' . number_format($movement['balance_after'], 2) . ' ' . htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">التاريخ والوقت:</td>
                    <td>' . date('Y-m-d H:i:s', strtotime($movement['created_at'])) . '</td>
                </tr>
            </table>
        </div>
        
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-cash-register me-2"></i>
                معلومات الصندوق
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">اسم الصندوق:</td>
                    <td>' . htmlspecialchars($cashBox['name']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">العملة:</td>
                    <td>' . htmlspecialchars($cashBox['currency_name'] . ' (' . $cashBox['currency_code'] . ')') . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">الفرع:</td>
                    <td>' . htmlspecialchars($cashBox['branch_name']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">المسؤول:</td>
                    <td>' . htmlspecialchars($cashBox['responsible_name']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">المستخدم المنفذ:</td>
                    <td>' . htmlspecialchars($movement['user_name'] ?: 'غير محدد') . '</td>
                </tr>
            </table>
        </div>
    </div>';
    
    if (!empty($movement['description']) || !empty($movement['reference_number'])) {
        $html .= '
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-file-alt me-2"></i>
                    تفاصيل إضافية
                </h6>';
        
        if (!empty($movement['description'])) {
            $html .= '
                <div class="mb-3">
                    <label class="form-label fw-medium">الوصف:</label>
                    <div class="p-3 bg-light rounded">
                        ' . nl2br(htmlspecialchars($movement['description'])) . '
                    </div>
                </div>';
        }
        
        if (!empty($movement['reference_number'])) {
            $html .= '
                <div class="mb-3">
                    <label class="form-label fw-medium">رقم المرجع:</label>
                    <div class="p-2 bg-light rounded">
                        <code class="fs-6">' . htmlspecialchars($movement['reference_number']) . '</code>
                    </div>
                </div>';
        }
        
        $html .= '
            </div>
        </div>';
    }
    
    // Add audit trail if available
    if (isset($movement['created_at']) && isset($movement['updated_at']) && $movement['created_at'] !== $movement['updated_at']) {
        $html .= '
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-history me-2"></i>
                    سجل التعديلات
                </h6>
                <div class="alert alert-info">
                    <small>
                        <strong>تاريخ الإنشاء:</strong> ' . date('Y-m-d H:i:s', strtotime($movement['created_at'])) . '<br>
                        <strong>آخر تعديل:</strong> ' . date('Y-m-d H:i:s', strtotime($movement['updated_at'])) . '
                    </small>
                </div>
            </div>
        </div>';
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

<?php
/**
 * إنشاء المشغل (Trigger) لحفظ المعاملات المحذوفة
 * Create Trigger for Saving Deleted Transactions
 */

require_once __DIR__ . '/../config/database.php';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<h2>إنشاء المشغل (Trigger) لحفظ المعاملات المحذوفة</h2>";
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // فحص وجود المشغل أولاً
    echo "<h3>فحص وجود المشغل:</h3>";
    
    try {
        $stmt = $pdo->query("SHOW TRIGGERS FROM " . DB_NAME . " WHERE Trigger = 'before_daily_transaction_delete'");
        $trigger_exists = $stmt->rowCount() > 0;
        
        if ($trigger_exists) {
            echo "<p style='color: orange;'>⚠ المشغل موجود بالفعل، سيتم حذفه وإعادة إنشاؤه</p>";
            
            // حذف المشغل الموجود
            $pdo->exec("DROP TRIGGER IF EXISTS before_daily_transaction_delete");
            echo "<p style='color: blue;'>✓ تم حذف المشغل القديم</p>";
        } else {
            echo "<p style='color: blue;'>ℹ المشغل غير موجود، سيتم إنشاؤه</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ خطأ في فحص المشغل: " . $e->getMessage() . "</p>";
    }
    
    // إنشاء المشغل
    echo "<h3>إنشاء المشغل:</h3>";
    
    $trigger_sql = "
    CREATE TRIGGER before_daily_transaction_delete
    BEFORE DELETE ON daily_transactions
    FOR EACH ROW
    BEGIN
        INSERT INTO deleted_daily_transactions (
            original_id, 
            transaction_number, 
            country_id, 
            base_amount, 
            customer_rate,
            operation_type, 
            calculated_amount, 
            exchange_rate, 
            recipient_amount,
            delivery_type, 
            transfer_amount, 
            recipient_name, 
            notes, 
            branch_id,
            created_by, 
            created_at, 
            updated_by, 
            updated_at, 
            deleted_by
        ) VALUES (
            OLD.id, 
            OLD.transaction_number, 
            OLD.country_id, 
            OLD.base_amount, 
            OLD.customer_rate,
            OLD.operation_type, 
            OLD.calculated_amount, 
            OLD.exchange_rate, 
            OLD.recipient_amount,
            OLD.delivery_type, 
            OLD.transfer_amount, 
            OLD.recipient_name, 
            OLD.notes, 
            OLD.branch_id,
            OLD.created_by, 
            OLD.created_at, 
            OLD.updated_by, 
            OLD.updated_at, 
            @current_user_id
        );
        
        INSERT INTO daily_transaction_history (
            transaction_id, 
            action_type, 
            old_values, 
            changed_by, 
            ip_address
        ) VALUES (
            OLD.id, 
            'deleted',
            JSON_OBJECT(
                'transaction_number', OLD.transaction_number,
                'country_id', OLD.country_id,
                'base_amount', OLD.base_amount,
                'delivery_type', OLD.delivery_type
            ),
            @current_user_id, 
            NULL
        );
    END
    ";
    
    try {
        $pdo->exec($trigger_sql);
        echo "<p style='color: green;'>✓ تم إنشاء المشغل before_daily_transaction_delete بنجاح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في إنشاء المشغل: " . $e->getMessage() . "</p>";
    }
    
    // التحقق من إنشاء المشغل
    echo "<h3>التحقق من إنشاء المشغل:</h3>";
    
    try {
        $stmt = $pdo->query("SHOW TRIGGERS FROM " . DB_NAME . " WHERE Trigger = 'before_daily_transaction_delete'");
        $trigger_exists = $stmt->rowCount() > 0;
        
        if ($trigger_exists) {
            echo "<p style='color: green;'>✓ المشغل موجود ويعمل بشكل صحيح</p>";
            
            // عرض تفاصيل المشغل
            $trigger_info = $stmt->fetch();
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>تفاصيل المشغل:</strong><br>";
            echo "الاسم: " . $trigger_info['Trigger'] . "<br>";
            echo "الحدث: " . $trigger_info['Event'] . "<br>";
            echo "الجدول: " . $trigger_info['Table'] . "<br>";
            echo "التوقيت: " . $trigger_info['Timing'] . "<br>";
            echo "</div>";
        } else {
            echo "<p style='color: red;'>✗ فشل في إنشاء المشغل</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في التحقق من المشغل: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>تم الانتهاء من إعداد المشغل!</h3>";
    echo "<p><a href='test_daily_transactions_system.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار النظام</a></p>";
    echo "<p><a href='../dashboard/daily_transactions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى المعاملات اليومية</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>خطأ في الإعداد</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء المشغل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>

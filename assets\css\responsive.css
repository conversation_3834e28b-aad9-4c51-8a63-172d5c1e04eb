/*
 * نظام الاستجابة الشامل للموقع
 * Comprehensive Responsive System
 */

/* ===== BREAKPOINTS ===== */
/* 
 * xs: 0-575px (الهواتف الصغيرة)
 * sm: 576-767px (الهواتف الكبيرة)
 * md: 768-991px (الأجهزة اللوحية)
 * lg: 992-1199px (أجهزة الكمبيوتر الصغيرة)
 * xl: 1200-1399px (أجهزة الكمبيوتر الكبيرة)
 * xxl: 1400px+ (الشاشات الكبيرة جداً)
 */

/* ===== BASE RESPONSIVE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  overflow-x: hidden;
}

body {
  overflow-x: hidden;
  position: relative;
}

/* ===== CONTAINER RESPONSIVE ===== */
.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
  max-width: 100%;
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
    padding-right: 20px;
    padding-left: 20px;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container {
    max-width: 720px;
    padding-right: 25px;
    padding-left: 25px;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .container {
    max-width: 960px;
    padding-right: 30px;
    padding-left: 30px;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/* Extra extra large devices (larger desktops, 1400px and up) */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

/* ===== SIDEBAR RESPONSIVE ===== */
#sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 250px;
  height: 100vh;
  z-index: 1000;
  transition: all 0.3s ease;
  transform: translateX(0);
}

/* إخفاء الشريط الجانبي على الأجهزة المحمولة بشكل افتراضي */
@media (max-width: 991.98px) {
  #sidebar {
    right: -250px;
    transform: translateX(0);
    box-shadow: none;
  }

  #sidebar.show {
    right: 0;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  }

  /* تحسين عرض الشريط الجانبي على الشاشات الصغيرة */
  #sidebar {
    width: 280px;
  }

  #sidebar.show {
    right: 0;
  }
}

/* تحسين إضافي للشاشات الصغيرة جداً */
@media (max-width: 575.98px) {
  #sidebar {
    width: 100%;
    right: -100%;
  }

  #sidebar.show {
    right: 0;
  }
}

/* طبقة التغطية للأجهزة المحمولة */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* ===== MAIN CONTENT RESPONSIVE ===== */
.main-content {
  transition: all 0.3s ease;
  min-height: 100vh;
  position: relative;
}

/* Desktop */
@media (min-width: 992px) {
  .main-content {
    margin-right: 250px;
    width: calc(100% - 250px);
    padding: 20px;
  }
}

/* Tablet and Mobile */
@media (max-width: 991.98px) {
  .main-content {
    margin-right: 0;
    width: 100%;
    padding: 15px;
  }
}

/* Mobile */
@media (max-width: 575.98px) {
  .main-content {
    padding: 10px;
  }
}

/* ===== SIDEBAR TOGGLE BUTTON ===== */
.sidebar-toggle-btn {
  position: fixed;
  top: 15px;
  right: 15px;
  z-index: 1001;
  display: none;
  border-radius: 8px;
  padding: 12px;
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: none;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.sidebar-toggle-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #0056b3, #004085);
}

.sidebar-toggle-btn:active {
  transform: scale(0.95);
}

.sidebar-toggle-btn i {
  font-size: 18px;
  transition: transform 0.3s ease;
}

/* إظهار الزر على الأجهزة المحمولة فقط */
@media (max-width: 991.98px) {
  .sidebar-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* تحسين موضع الزر على الشاشات الصغيرة جداً */
@media (max-width: 575.98px) {
  .sidebar-toggle-btn {
    top: 10px;
    right: 10px;
    width: 45px;
    height: 45px;
    padding: 10px;
  }

  .sidebar-toggle-btn i {
    font-size: 16px;
  }
}

/* ===== TABLE RESPONSIVE ===== */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive > .table {
  margin-bottom: 0;
}

/* Mobile table improvements */
@media (max-width: 767.98px) {
  .table-responsive {
    border: none;
  }
  
  .table-responsive .table {
    font-size: 0.875rem;
  }
  
  .table-responsive .table th,
  .table-responsive .table td {
    padding: 0.5rem 0.25rem;
    white-space: nowrap;
  }
  
  .table-responsive .table th {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

/* Very small screens - stack table data */
@media (max-width: 575.98px) {
  .table-mobile-stack {
    display: none;
  }
  
  .table-mobile-cards {
    display: block;
  }
  
  .mobile-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 1rem;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .mobile-card-header {
    font-weight: bold;
    color: #495057;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
  }
  
  .mobile-card-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f8f9fa;
  }
  
  .mobile-card-row:last-child {
    border-bottom: none;
  }
  
  .mobile-card-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.875rem;
  }
  
  .mobile-card-value {
    font-weight: 500;
    text-align: left;
  }
}

/* ===== CARD RESPONSIVE ===== */
.card {
  margin-bottom: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767.98px) {
  .card {
    margin-bottom: 1rem;
    border-radius: 8px;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .card-header {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 575.98px) {
  .card-body {
    padding: 0.75rem;
  }
  
  .card-header {
    padding: 0.5rem 0.75rem;
  }
}

/* ===== BUTTON RESPONSIVE ===== */
.btn {
  border-radius: 6px;
  transition: all 0.3s ease;
}

@media (max-width: 767.98px) {
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 575.98px) {
  .btn {
    padding: 0.5rem 0.875rem;
    font-size: 0.8rem;
  }
  
  .btn-group {
    flex-direction: column;
    width: 100%;
  }
  
  .btn-group .btn {
    margin-bottom: 0.5rem;
    border-radius: 6px !important;
  }
  
  .btn-group .btn:last-child {
    margin-bottom: 0;
  }
}

/* ===== FORM RESPONSIVE ===== */
.form-control {
  border-radius: 6px;
}

@media (max-width: 767.98px) {
  .form-control {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .form-label {
    font-size: 0.875rem;
    font-weight: 500;
  }
}

@media (max-width: 575.98px) {
  .row > .col,
  .row > [class*="col-"] {
    margin-bottom: 1rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
}

/* ===== MODAL RESPONSIVE ===== */
@media (max-width: 767.98px) {
  .modal-dialog {
    margin: 0.5rem;
    max-width: calc(100% - 1rem);
  }
  
  .modal-content {
    border-radius: 10px;
  }
  
  .modal-header {
    padding: 1rem;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  .modal-footer {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 575.98px) {
  .modal-dialog {
    margin: 0.25rem;
    max-width: calc(100% - 0.5rem);
  }
  
  .modal-header {
    padding: 0.75rem;
  }
  
  .modal-body {
    padding: 0.75rem;
  }
  
  .modal-footer {
    padding: 0.5rem 0.75rem;
    flex-direction: column;
  }
  
  .modal-footer .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .modal-footer .btn:last-child {
    margin-bottom: 0;
  }
}

/* ===== NAVIGATION RESPONSIVE ===== */
.navbar {
  padding: 0.5rem 1rem;
}

@media (max-width: 991.98px) {
  .navbar {
    padding: 0.5rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }
}

@media (max-width: 575.98px) {
  .navbar {
    padding: 0.25rem 0.5rem;
  }

  .navbar-brand {
    font-size: 1rem;
  }
}

/* ===== BADGE RESPONSIVE ===== */
@media (max-width: 575.98px) {
  .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}

/* ===== ALERT RESPONSIVE ===== */
@media (max-width: 575.98px) {
  .alert {
    padding: 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
  }
}

/* ===== BREADCRUMB RESPONSIVE ===== */
@media (max-width: 575.98px) {
  .breadcrumb {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }

  .breadcrumb-item {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

/* ===== PAGINATION RESPONSIVE ===== */
@media (max-width: 575.98px) {
  .pagination {
    justify-content: center;
  }

  .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  .pagination .page-item:not(.active):not(.disabled) {
    display: none;
  }

  .pagination .page-item.active,
  .pagination .page-item:first-child,
  .pagination .page-item:last-child,
  .pagination .page-item:nth-child(2),
  .pagination .page-item:nth-last-child(2) {
    display: block;
  }
}

/* ===== DROPDOWN RESPONSIVE ===== */
@media (max-width: 575.98px) {
  .dropdown-menu {
    min-width: 200px;
    max-width: calc(100vw - 2rem);
    font-size: 0.875rem;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
  }
}

/* ===== STATS/METRICS RESPONSIVE ===== */
.stats-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 576px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 575.98px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* ===== UTILITY CLASSES FOR RESPONSIVE ===== */
/* Hide/Show on different screen sizes */
.d-mobile-none {
  display: block;
}

.d-mobile-block {
  display: none;
}

@media (max-width: 767.98px) {
  .d-mobile-none {
    display: none !important;
  }

  .d-mobile-block {
    display: block !important;
  }
}

.d-tablet-none {
  display: block;
}

.d-tablet-block {
  display: none;
}

@media (min-width: 768px) and (max-width: 991.98px) {
  .d-tablet-none {
    display: none !important;
  }

  .d-tablet-block {
    display: block !important;
  }
}

.d-desktop-none {
  display: block;
}

.d-desktop-block {
  display: none;
}

@media (min-width: 992px) {
  .d-desktop-none {
    display: none !important;
  }

  .d-desktop-block {
    display: block !important;
  }
}

/* Text alignment responsive */
@media (max-width: 767.98px) {
  .text-mobile-center {
    text-align: center !important;
  }

  .text-mobile-right {
    text-align: right !important;
  }

  .text-mobile-left {
    text-align: left !important;
  }
}

/* Margin and padding responsive */
@media (max-width: 767.98px) {
  .p-mobile-1 { padding: 0.25rem !important; }
  .p-mobile-2 { padding: 0.5rem !important; }
  .p-mobile-3 { padding: 1rem !important; }

  .m-mobile-1 { margin: 0.25rem !important; }
  .m-mobile-2 { margin: 0.5rem !important; }
  .m-mobile-3 { margin: 1rem !important; }

  .mb-mobile-1 { margin-bottom: 0.25rem !important; }
  .mb-mobile-2 { margin-bottom: 0.5rem !important; }
  .mb-mobile-3 { margin-bottom: 1rem !important; }
}

/* Font size responsive */
@media (max-width: 767.98px) {
  .fs-mobile-small { font-size: 0.875rem !important; }
  .fs-mobile-normal { font-size: 1rem !important; }
  .fs-mobile-large { font-size: 1.125rem !important; }
}

/* Width responsive */
@media (max-width: 767.98px) {
  .w-mobile-100 { width: 100% !important; }
  .w-mobile-75 { width: 75% !important; }
  .w-mobile-50 { width: 50% !important; }
  .w-mobile-25 { width: 25% !important; }
}

/* ===== PRINT STYLES ===== */
@media print {
  #sidebar,
  .sidebar-toggle-btn,
  .btn,
  .dropdown,
  .modal,
  .alert {
    display: none !important;
  }

  .main-content {
    margin-right: 0 !important;
    width: 100% !important;
    padding: 0 !important;
  }

  .container {
    max-width: 100% !important;
    padding: 0 !important;
  }

  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
    break-inside: avoid;
  }

  .table {
    border-collapse: collapse !important;
  }

  .table th,
  .table td {
    border: 1px solid #000 !important;
    padding: 0.25rem !important;
  }
}

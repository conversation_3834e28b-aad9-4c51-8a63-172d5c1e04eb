-- إنشاء جدول تسجيل العمليات الشامل للنظام
-- System Activity Logs Table - يسجل جميع العمليات في النظام

CREATE TABLE IF NOT EXISTS system_activity_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- معلومات المستخدم
    user_id INT UNSIGNED NULL,
    username VARCHAR(100) NULL,
    user_full_name VARCHAR(255) NULL,
    user_role VARCHAR(100) NULL,
    
    -- معلومات العملية
    action_type ENUM(
        'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'LOGIN', 'LOGOUT', 
        'EXPORT', 'IMPORT', 'APPROVE', 'REJECT', 'CANCEL', 'RESTORE',
        'TRANSFER', 'EXCHANGE', 'PAYMENT', 'WITHDRAWAL', 'DEPOSIT',
        'SYSTEM', 'ERROR', 'WARNING', 'INFO'
    ) NOT NULL,
    
    module VARCHAR(50) NOT NULL COMMENT 'الوحدة: users, customers, transfers, exchange, etc.',
    operation VARCHAR(100) NOT NULL COMMENT 'العملية المحددة: add_user, edit_customer, etc.',
    description TEXT NOT NULL COMMENT 'وصف مفصل للعملية',
    
    -- معلومات السجل المتأثر
    target_table VARCHAR(50) NULL COMMENT 'الجدول المتأثر',
    target_id INT UNSIGNED NULL COMMENT 'معرف السجل المتأثر',
    target_identifier VARCHAR(255) NULL COMMENT 'معرف إضافي للسجل (اسم، رقم، إلخ)',
    
    -- البيانات القديمة والجديدة
    old_values JSON NULL COMMENT 'القيم القديمة قبل التغيير',
    new_values JSON NULL COMMENT 'القيم الجديدة بعد التغيير',
    changed_fields JSON NULL COMMENT 'الحقول التي تم تغييرها فقط',
    
    -- معلومات إضافية
    additional_data JSON NULL COMMENT 'بيانات إضافية حسب نوع العملية',
    
    -- معلومات الجلسة والشبكة
    session_id VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    request_method VARCHAR(10) NULL COMMENT 'GET, POST, PUT, DELETE',
    request_url TEXT NULL,
    
    -- معلومات النتيجة
    status ENUM('SUCCESS', 'FAILED', 'PARTIAL', 'PENDING') DEFAULT 'SUCCESS',
    error_message TEXT NULL,
    execution_time DECIMAL(8,3) NULL COMMENT 'وقت التنفيذ بالثواني',
    
    -- معلومات التوقيت
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- فهارس للبحث السريع
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_module (module),
    INDEX idx_operation (operation),
    INDEX idx_target_table_id (target_table, target_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    INDEX idx_ip_address (ip_address),
    INDEX idx_user_action_date (user_id, action_type, created_at),
    INDEX idx_module_operation_date (module, operation, created_at),
    
    -- قيد خارجي للمستخدم
    CONSTRAINT fk_system_activity_user 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE SET NULL 
        ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='سجل شامل لجميع العمليات في النظام';

-- إنشاء فهرس مركب للبحث المتقدم
CREATE INDEX idx_comprehensive_search 
ON system_activity_logs (module, action_type, created_at, user_id);

-- إنشاء فهرس للبحث النصي
CREATE FULLTEXT INDEX idx_description_search 
ON system_activity_logs (description);

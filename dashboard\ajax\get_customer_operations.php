<?php
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/customer_manager.php';
require_once __DIR__ . '/../../includes/database.php';

header('Content-Type: application/json; charset=utf-8');

$auth = new Auth();
try {
    $auth->requireLogin();
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

if (!$auth->hasPermission('customers.view')) {
    echo json_encode(['success' => false, 'error' => 'Permission denied']);
    exit;
}

$customerId = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;
if ($customerId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid customer ID']);
    exit;
}

$limit = isset($_GET['limit']) && (int)$_GET['limit'] > 0 ? (int)$_GET['limit'] : 1000;

$cm = new CustomerManager(new Database());
$operations = $cm->getCustomerOperations($customerId, $limit);

echo json_encode([
    'success' => true,
    'operations' => $operations,
]); 
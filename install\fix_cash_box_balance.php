<?php
/**
 * Fix cash box current balance calculation
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إصلاح الرصيد الحالي للصناديق</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الأرصدة الحالية</h3>\n";
    
    // Get all cash boxes with their current balances
    $result = $conn->query("
        SELECT cb.id, cb.name, cb.initial_balance, cb.current_balance,
               (SELECT SUM(CASE WHEN cm.movement_type = 'deposit' THEN cm.amount ELSE -cm.amount END)
                FROM cash_movements cm 
                WHERE cm.cash_box_id = cb.id) as calculated_balance
        FROM cash_boxes cb
        ORDER BY cb.id
    ");
    
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID</th><th>اسم الصندوق</th><th>الرصيد الابتدائي</th><th>الرصيد المسجل</th><th>الرصيد المحسوب</th><th>الفرق</th><th>الحالة</th>\n";
        echo "</tr>\n";
        
        $needsUpdate = [];
        
        while ($row = $result->fetch_assoc()) {
            $initialBalance = (float)$row['initial_balance'];
            $currentBalance = (float)$row['current_balance'];
            $calculatedBalance = $initialBalance + (float)($row['calculated_balance'] ?? 0);
            $difference = $currentBalance - $calculatedBalance;
            
            $status = (abs($difference) < 0.01) ? 'صحيح' : 'يحتاج تصحيح';
            $statusColor = (abs($difference) < 0.01) ? 'green' : 'red';
            
            if (abs($difference) >= 0.01) {
                $needsUpdate[] = [
                    'id' => $row['id'],
                    'name' => $row['name'],
                    'correct_balance' => $calculatedBalance
                ];
            }
            
            echo "<tr>\n";
            echo "<td>{$row['id']}</td>\n";
            echo "<td>{$row['name']}</td>\n";
            echo "<td>" . number_format($initialBalance, 2) . "</td>\n";
            echo "<td>" . number_format($currentBalance, 2) . "</td>\n";
            echo "<td>" . number_format($calculatedBalance, 2) . "</td>\n";
            echo "<td>" . number_format($difference, 2) . "</td>\n";
            echo "<td style='color: $statusColor;'>$status</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        echo "<h3>2. تصحيح الأرصدة</h3>\n";
        
        if (!empty($needsUpdate)) {
            foreach ($needsUpdate as $cashBox) {
                $updateSql = "UPDATE cash_boxes SET current_balance = ?, updated_at = NOW() WHERE id = ?";
                $stmt = $conn->prepare($updateSql);
                
                if ($stmt) {
                    $stmt->bind_param('di', $cashBox['correct_balance'], $cashBox['id']);
                    
                    if ($stmt->execute()) {
                        echo "<p style='color: green;'>✓ تم تصحيح رصيد صندوق '{$cashBox['name']}' إلى " . number_format($cashBox['correct_balance'], 2) . "</p>\n";
                    } else {
                        echo "<p style='color: red;'>✗ فشل في تصحيح رصيد صندوق '{$cashBox['name']}'</p>\n";
                    }
                    $stmt->close();
                } else {
                    echo "<p style='color: red;'>✗ فشل في تحضير استعلام التحديث للصندوق '{$cashBox['name']}'</p>\n";
                }
            }
        } else {
            echo "<p style='color: green;'>✓ جميع الأرصدة صحيحة</p>\n";
        }
        
        echo "<h3>3. التحقق من النتائج</h3>\n";
        
        // Verify the updates
        $verifyResult = $conn->query("
            SELECT cb.id, cb.name, cb.initial_balance, cb.current_balance,
                   (SELECT SUM(CASE WHEN cm.movement_type = 'deposit' THEN cm.amount ELSE -cm.amount END)
                    FROM cash_movements cm 
                    WHERE cm.cash_box_id = cb.id) as calculated_balance
            FROM cash_boxes cb
            ORDER BY cb.id
        ");
        
        if ($verifyResult) {
            echo "<p><strong>الأرصدة بعد التصحيح:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>ID</th><th>اسم الصندوق</th><th>الرصيد الحالي</th><th>الرصيد المحسوب</th><th>الحالة</th>\n";
            echo "</tr>\n";
            
            while ($row = $verifyResult->fetch_assoc()) {
                $initialBalance = (float)$row['initial_balance'];
                $currentBalance = (float)$row['current_balance'];
                $calculatedBalance = $initialBalance + (float)($row['calculated_balance'] ?? 0);
                $difference = abs($currentBalance - $calculatedBalance);
                
                $status = ($difference < 0.01) ? '✓ صحيح' : '✗ خطأ';
                $statusColor = ($difference < 0.01) ? 'green' : 'red';
                
                echo "<tr>\n";
                echo "<td>{$row['id']}</td>\n";
                echo "<td>{$row['name']}</td>\n";
                echo "<td>" . number_format($currentBalance, 2) . "</td>\n";
                echo "<td>" . number_format($calculatedBalance, 2) . "</td>\n";
                echo "<td style='color: $statusColor;'>$status</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
        
        echo "<h3>4. إنشاء دالة تحديث الرصيد التلقائي</h3>\n";
        
        $updateFunction = '
/**
 * Update cash box balance based on movements
 */
public function updateCashBoxBalance(int $cashBoxId): bool
{
    try {
        $conn = Database::getConnection();
        
        // Calculate correct balance
        $sql = "SELECT 
                    cb.initial_balance,
                    COALESCE(SUM(CASE WHEN cm.movement_type = \'deposit\' THEN cm.amount ELSE -cm.amount END), 0) as movements_total
                FROM cash_boxes cb
                LEFT JOIN cash_movements cm ON cm.cash_box_id = cb.id
                WHERE cb.id = ?
                GROUP BY cb.id, cb.initial_balance";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param(\'i\', $cashBoxId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            $correctBalance = $row[\'initial_balance\'] + $row[\'movements_total\'];
            
            // Update the balance
            $updateSql = "UPDATE cash_boxes SET current_balance = ?, updated_at = NOW() WHERE id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param(\'di\', $correctBalance, $cashBoxId);
            
            return $updateStmt->execute();
        }
        
        return false;
    } catch (Exception $e) {
        error_log("Error updating cash box balance: " . $e->getMessage());
        return false;
    }
}
';
        
        echo "<p><strong>دالة مقترحة لإضافتها إلى CashManager:</strong></p>\n";
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-size: 12px;'>";
        echo htmlspecialchars($updateFunction);
        echo "</pre>\n";
        
    } else {
        echo "<p style='color: red;'>✗ فشل في جلب بيانات الصناديق</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح أرصدة الصناديق!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>تحقق من صفحة الصناديق</a></li>\n";
    echo "<li><a href='../dashboard/cash_box_history.php?id=1' target='_blank'>تحقق من تاريخ الصندوق</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

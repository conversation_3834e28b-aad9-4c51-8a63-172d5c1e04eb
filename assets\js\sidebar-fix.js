/**
 * إصلاح مشكلة النص المشوه في الشريط الجانبي عند الإخفاء
 * Fix Sidebar Text Corruption Issue When Collapsed
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // إصلاح مشكلة النص المشوه
    fixSidebarTextCorruption();
    
    // مراقبة تغييرات الشريط الجانبي
    observeSidebarChanges();
    
    // إضافة زر إخفاء/إظهار للشاشات الكبيرة
    addDesktopSidebarToggle();
    
    console.log('🔧 Sidebar text corruption fix loaded successfully!');
});

// إصلاح النص المشوه
function fixSidebarTextCorruption() {
    // البحث عن جميع النصوص المشوهة وإصلاحها
    const corruptedTexts = [
        { selector: '.paypal-text', correctText: 'حوالات PayPal' },
        { selector: '.sidebar-link span', patterns: [
            { corrupted: /ط§ظ„ط¹ظ…ظ„ط§ط،/, correct: 'العملاء' },
            { corrupted: /ط§ظ„ط­ظˆط§ظ„ط§طھ/, correct: 'الحوالات' },
            { corrupted: /ط§ظ„طµط±ط§ظپط©/, correct: 'الصرافة' },
            { corrupted: /ط§ظ„طھظ‚ط§ط±ظٹط±/, correct: 'التقارير' },
            { corrupted: /ط§ظ„ظ…ظƒط§طھط¨/, correct: 'المكاتب' },
            { corrupted: /ط§ظ„ظ…ط³طھط®ط¯ظ…ظٹظ†/, correct: 'المستخدمين' },
            { corrupted: /ط§ظ„ط£ط¯ظˆط§ط±/, correct: 'الأدوار' },
            { corrupted: /ط§ظ„طµظ„ط§ط­ظٹط§طھ/, correct: 'الصلاحيات' },
            { corrupted: /ظ„ظˆط­ط© ط§ظ„طھط­ظƒظ…/, correct: 'لوحة التحكم' },
            { corrupted: /PayPal.*ط­ظˆط§ظ„ط§طھ/, correct: 'حوالات PayPal' },
            { corrupted: /PayPal.*طھظ‚ط§ط±ظٹط±/, correct: 'تقارير PayPal' }
        ]}
    ];
    
    corruptedTexts.forEach(item => {
        const elements = document.querySelectorAll(item.selector);
        elements.forEach(element => {
            if (item.correctText) {
                // نص محدد
                if (element.textContent.includes('ط') || element.textContent.includes('ظ')) {
                    element.textContent = item.correctText;
                }
            } else if (item.patterns) {
                // أنماط متعددة
                let text = element.textContent;
                item.patterns.forEach(pattern => {
                    if (pattern.corrupted.test(text) || text.includes('ط') || text.includes('ظ')) {
                        text = pattern.correct;
                    }
                });
                if (text !== element.textContent) {
                    element.textContent = text;
                }
            }
        });
    });
    
    // إصلاح خاص لقائمة PayPal
    fixPayPalMenuText();
}

// إصلاح نصوص قائمة PayPal
function fixPayPalMenuText() {
    const paypalTexts = {
        '.paypal-text': 'حوالات PayPal',
        '.dashboard-link span': 'لوحة تحكم PayPal',
        '.incoming-link span': 'الحوالات الواردة',
        '.create-link span': 'إضافة حوالة جديدة',
        '.withdrawals-link span': 'إدارة السحوبات',
        '.reports-link span': 'تقارير PayPal'
    };
    
    Object.entries(paypalTexts).forEach(([selector, correctText]) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (element.textContent.includes('ط') || 
                element.textContent.includes('ظ') || 
                element.textContent.trim() === '' ||
                element.textContent.includes('PayPal') && element.textContent.length > 20) {
                element.textContent = correctText;
            }
        });
    });
}

// مراقبة تغييرات الشريط الجانبي
function observeSidebarChanges() {
    const sidebar = document.querySelector('.sidebar-container');
    if (!sidebar) return;
    
    // مراقب التغييرات
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                handleSidebarStateChange();
            }
        });
    });
    
    // بدء المراقبة
    observer.observe(sidebar, {
        attributes: true,
        attributeFilter: ['class']
    });
    
    // مراقبة تغييرات النصوص أيضاً
    const textObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'characterData') {
                setTimeout(fixSidebarTextCorruption, 100);
            }
        });
    });
    
    textObserver.observe(sidebar, {
        childList: true,
        subtree: true,
        characterData: true
    });
}

// التعامل مع تغيير حالة الشريط الجانبي
function handleSidebarStateChange() {
    const sidebar = document.querySelector('.sidebar-container');
    const isCollapsed = sidebar.classList.contains('collapsed') || 
                       sidebar.classList.contains('sidebar-collapsed');
    
    if (isCollapsed) {
        // إخفاء النصوص عند الطي
        hideSidebarTexts();
    } else {
        // إظهار النصوص عند التوسيع
        showSidebarTexts();
        // إصلاح النصوص المشوهة
        setTimeout(fixSidebarTextCorruption, 200);
    }
}

// إخفاء نصوص الشريط الجانبي
function hideSidebarTexts() {
    const textElements = document.querySelectorAll(`
        .sidebar-link span:not(.sidebar-icon),
        .paypal-text,
        .paypal-sublink span,
        .sidebar-sublink span
    `);
    
    textElements.forEach(element => {
        element.style.display = 'none';
        element.style.visibility = 'hidden';
        element.style.opacity = '0';
        element.style.width = '0';
        element.style.overflow = 'hidden';
    });
    
    // إخفاء الأسهم والمؤشرات
    const indicators = document.querySelectorAll(`
        .dropdown-icon,
        .paypal-chevron,
        .paypal-indicator
    `);
    
    indicators.forEach(element => {
        element.style.display = 'none';
    });
}

// إظهار نصوص الشريط الجانبي
function showSidebarTexts() {
    const textElements = document.querySelectorAll(`
        .sidebar-link span:not(.sidebar-icon),
        .paypal-text,
        .paypal-sublink span,
        .sidebar-sublink span
    `);
    
    textElements.forEach(element => {
        element.style.display = '';
        element.style.visibility = '';
        element.style.opacity = '';
        element.style.width = '';
        element.style.overflow = '';
    });
    
    // إظهار الأسهم والمؤشرات
    const indicators = document.querySelectorAll(`
        .dropdown-icon,
        .paypal-chevron,
        .paypal-indicator
    `);
    
    indicators.forEach(element => {
        element.style.display = '';
    });
}

// إضافة زر إخفاء/إظهار للشاشات الكبيرة
function addDesktopSidebarToggle() {
    // التحقق من وجود الزر
    if (document.querySelector('.desktop-sidebar-toggle')) return;
    
    const toggleButton = document.createElement('button');
    toggleButton.className = 'btn btn-outline-secondary desktop-sidebar-toggle d-none d-lg-block';
    toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
    toggleButton.title = 'إخفاء/إظهار القائمة الجانبية';
    toggleButton.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    `;
    
    // إضافة الزر للصفحة
    document.body.appendChild(toggleButton);
    
    // إضافة وظيفة التبديل
    toggleButton.addEventListener('click', function() {
        const sidebar = document.querySelector('.sidebar-container');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar && mainContent) {
            sidebar.classList.toggle('collapsed');
            
            if (sidebar.classList.contains('collapsed')) {
                // عند الإخفاء
                mainContent.style.marginRight = '0';
                toggleButton.innerHTML = '<i class="fas fa-chevron-left"></i>';
                toggleButton.title = 'إظهار القائمة الجانبية';
            } else {
                // عند الإظهار
                mainContent.style.marginRight = '';
                toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
                toggleButton.title = 'إخفاء القائمة الجانبية';
            }
        }
    });
    
    // تأثيرات التمرير
    toggleButton.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1)';
        this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.2)';
    });
    
    toggleButton.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
    });
}

// إصلاح دوري للنصوص المشوهة
setInterval(function() {
    // فحص وجود نصوص مشوهة
    const corruptedElements = document.querySelectorAll('*');
    let foundCorruption = false;
    
    corruptedElements.forEach(element => {
        if (element.textContent && 
            (element.textContent.includes('ط') || element.textContent.includes('ظ')) &&
            !element.textContent.includes('نقطة') && // استثناء للكلمات الصحيحة
            !element.textContent.includes('ضغط')) {
            foundCorruption = true;
        }
    });
    
    if (foundCorruption) {
        fixSidebarTextCorruption();
    }
}, 5000); // كل 5 ثوان

// إضافة CSS للإصلاحات
const style = document.createElement('style');
style.textContent = `
    /* إصلاحات CSS للشريط الجانبي المطوي */
    .sidebar-container.collapsed .sidebar-link span:not(.sidebar-icon),
    .sidebar-container.collapsed .paypal-text,
    .sidebar-container.collapsed .paypal-sublink span {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        width: 0 !important;
        overflow: hidden !important;
    }
    
    .sidebar-container.collapsed .sidebar-link,
    .sidebar-container.collapsed .paypal-main-link {
        justify-content: center !important;
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
    
    .sidebar-container.collapsed .dropdown-icon,
    .sidebar-container.collapsed .paypal-chevron,
    .sidebar-container.collapsed .paypal-indicator {
        display: none !important;
    }
    
    .sidebar-container.collapsed .sidebar-icon,
    .sidebar-container.collapsed .paypal-icon-wrapper,
    .sidebar-container.collapsed .paypal-subicon-wrapper {
        margin: 0 !important;
    }
    
    /* تحسين زر التبديل */
    .desktop-sidebar-toggle:hover {
        background-color: #0070ba !important;
        color: white !important;
        border-color: #0070ba !important;
    }
    
    /* إصلاح المحتوى الرئيسي عند إخفاء الشريط */
    .main-content {
        transition: margin-right 0.3s ease;
    }
    
    /* منع النص المشوه */
    .sidebar-link span,
    .paypal-text,
    .paypal-sublink span {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        direction: rtl !important;
        unicode-bidi: embed !important;
    }
`;

document.head.appendChild(style);

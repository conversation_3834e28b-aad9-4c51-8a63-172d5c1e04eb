-- Remove transfer_commissions table and related data
-- This script removes the transfer commission system completely

-- Drop the transfer_commissions table
DROP TABLE IF EXISTS transfer_commissions;

-- Remove commission-related permissions from role_permissions
DELETE FROM role_permissions WHERE permission_id IN (
    SELECT id FROM permissions WHERE name LIKE 'transfers.commissions%'
);

-- Remove commission-related permissions
DELETE FROM permissions WHERE name LIKE 'transfers.commissions%';

-- Update permission IDs to be sequential (optional)
-- This ensures no gaps in permission IDs
SET @rank = 0;
UPDATE permissions SET id = (@rank:=@rank+1) ORDER BY id;

-- Reset auto increment for permissions table
ALTER TABLE permissions AUTO_INCREMENT = 1;

-- Update role_permissions to use new permission IDs
-- This is handled automatically by foreign key constraints

-- Clean up any orphaned role_permissions
DELETE rp FROM role_permissions rp 
LEFT JOIN permissions p ON rp.permission_id = p.id 
WHERE p.id IS NULL;

COMMIT; 
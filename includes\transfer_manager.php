<?php
/**
 * TransferManager – handles transfer commission rules and basic transfer creation.
 */
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/functions.php';

class TransferManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }



    /* ---------------- Transfers ---------------- */
    /**
     * Add a new transfer to the database
     *
     * @param array $data Transfer data containing all required fields
     * @return int|false Transfer ID on success, false on failure
     *
     * Expected $data structure:
     * - transfer_type: 'صادرة' or 'واردة'
     * - sender_name: string
     * - sender_id_type: string (optional)
     * - sender_id_number: string (optional)
     * - sender_phone: string (optional)
     * - sender_address: string (optional)
     * - beneficiary_id: int (optional, customer ID)
     * - beneficiary_name: string
     * - beneficiary_country: string (optional)
     * - beneficiary_bank: string (optional)
     * - beneficiary_account: string (optional)
     * - sending_currency_id: int
     * - sending_amount: decimal
     * - receiving_currency_id: int
     * - receiving_amount: decimal
     * - exchange_rate_used: decimal
     * - transfer_fee: decimal
     * - profit: decimal
     * - delivery_method: 'بنكي', 'نقدي', or 'شركة تحويل'
     * - status: 'معلقة', 'مكتملة', 'ملغاة', 'مرسلة', 'مستلمة'
     * - notes: string (optional)
     * - branch_id: int
     * - created_by: int (user ID)
     */
    public function addTransfer(array $data)
    {
        $txNumber = $this->generateTransactionNumber();

        // SQL INSERT statement with all required columns from transfers table schema
        // Note: tracking_number and completed_at are set to NULL initially
        // created_at uses DEFAULT CURRENT_TIMESTAMP from table definition
        $sql = 'INSERT INTO transfers (
            transaction_number, transfer_type, sender_name, sender_id_type, sender_id_number,
            sender_phone, sender_address, beneficiary_id, beneficiary_name, beneficiary_country,
            beneficiary_bank, beneficiary_account, sending_currency_id, sending_amount,
            receiving_currency_id, receiving_amount, exchange_rate_used, transfer_fee, profit,
            delivery_method, status, tracking_number, notes, branch_id, created_by, completed_at
        ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            error_log("Failed to prepare transfer insert statement: " . $this->db->error);
            return false;
        }

        // Prepare variables for bind_param (all parameters must be variables, not direct values)
        $senderIdType = $data['sender_id_type'] ?? null;
        $senderIdNumber = $data['sender_id_number'] ?? null;
        $senderPhone = $data['sender_phone'] ?? null;
        $senderAddress = $data['sender_address'] ?? null;
        $beneficiaryId = $data['beneficiary_id'] ?? null;
        $beneficiaryCountry = $data['beneficiary_country'] ?? null;
        $beneficiaryBank = $data['beneficiary_bank'] ?? null;
        $beneficiaryAccount = $data['beneficiary_account'] ?? null;
        $trackingNumber = null; // Set to NULL initially
        $notes = $data['notes'] ?? null;
        $completedAt = null; // Set to NULL initially

        // Bind parameters: 26 parameters total
        // s=string, i=integer, d=decimal/double
        $stmt->bind_param('sssssssissssididdddssssiis',
            $txNumber,                          // transaction_number (s)
            $data['transfer_type'],             // transfer_type (s)
            $data['sender_name'],               // sender_name (s)
            $senderIdType,                      // sender_id_type (s)
            $senderIdNumber,                    // sender_id_number (s)
            $senderPhone,                       // sender_phone (s)
            $senderAddress,                     // sender_address (s)
            $beneficiaryId,                     // beneficiary_id (i)
            $data['beneficiary_name'],          // beneficiary_name (s)
            $beneficiaryCountry,                // beneficiary_country (s)
            $beneficiaryBank,                   // beneficiary_bank (s)
            $beneficiaryAccount,                // beneficiary_account (s)
            $data['sending_currency_id'],       // sending_currency_id (i)
            $data['sending_amount'],            // sending_amount (d)
            $data['receiving_currency_id'],     // receiving_currency_id (i)
            $data['receiving_amount'],          // receiving_amount (d)
            $data['exchange_rate_used'],        // exchange_rate_used (d)
            $data['transfer_fee'],              // transfer_fee (d)
            $data['profit'],                    // profit (d)
            $data['delivery_method'],           // delivery_method (s)
            $data['status'],                    // status (s)
            $trackingNumber,                    // tracking_number (s) - set to NULL initially
            $notes,                             // notes (s)
            $data['branch_id'],                 // branch_id (i)
            $data['created_by'],                // created_by (i)
            $completedAt                        // completed_at (s) - set to NULL initially
        );
        $this->db->begin_transaction();
        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            // Status history
            $hist = $this->db->prepare('INSERT INTO transfer_status_history (transfer_id, old_status, new_status, status_date, changed_by_user_id) VALUES (?,?,?,?,?)');
            $old = null;
            $statusDate = date('Y-m-d H:i:s');
            $hist->bind_param('isssi', $newId, $old, $data['status'], $statusDate, $data['created_by']);
            if ($hist->execute()) {
                $hist->close();
                $this->db->commit();
                return $newId;
            }
            $hist->close();
        }
        $this->db->rollback();
        return false;
    }

    public function getRecentTransfers(int $limit = 20): array
    {
        $stmt = $this->db->prepare('SELECT t.*, c.full_name AS beneficiary_name FROM transfers t LEFT JOIN customers c ON c.id = t.beneficiary_id ORDER BY t.created_at DESC LIMIT ?');
        if (!$stmt) return [];
        $stmt->bind_param('i', $limit);
        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Generate a unique transaction number for transfers
     * Format: TX + 8 hex characters + timestamp
     *
     * @return string Unique transaction number
     */
    private function generateTransactionNumber(): string
    {
        do {
            $number = 'TX' . strtoupper(bin2hex(random_bytes(4))) . time();
            $stmt = $this->db->prepare('SELECT COUNT(*) FROM transfers WHERE transaction_number = ?');
            $stmt->bind_param('s', $number);
            $stmt->execute();
            $stmt->bind_result($cnt);
            $stmt->fetch();
            $stmt->close();
        } while ($cnt > 0);
        return $number;
    }

    /**
     * Generate a unique tracking number for transfers
     * Format: TK + 6 hex characters + timestamp
     *
     * @return string Unique tracking number
     */
    private function generateTrackingNumber(): string
    {
        do {
            $number = 'TK' . strtoupper(bin2hex(random_bytes(3))) . substr(time(), -6);
            $stmt = $this->db->prepare('SELECT COUNT(*) FROM transfers WHERE tracking_number = ?');
            $stmt->bind_param('s', $number);
            $stmt->execute();
            $stmt->bind_result($cnt);
            $stmt->fetch();
            $stmt->close();
        } while ($cnt > 0);
        return $number;
    }

    /**
     * Update transfer with tracking number (usually when transfer is sent)
     *
     * @param int $transferId Transfer ID
     * @return array Result with success status and tracking number
     */
    public function assignTrackingNumber(int $transferId): array
    {
        // Check if transfer exists and doesn't already have a tracking number
        $stmt = $this->db->prepare('SELECT tracking_number, status FROM transfers WHERE id = ?');
        $stmt->bind_param('i', $transferId);
        $stmt->execute();
        $result = $stmt->get_result();
        $transfer = $result->fetch_assoc();
        $stmt->close();

        if (!$transfer) {
            return ['success' => false, 'error' => 'Transfer not found'];
        }

        if (!empty($transfer['tracking_number'])) {
            return ['success' => true, 'tracking_number' => $transfer['tracking_number']];
        }

        // Generate new tracking number
        $trackingNumber = $this->generateTrackingNumber();

        // Update transfer with tracking number
        $stmt = $this->db->prepare('UPDATE transfers SET tracking_number = ? WHERE id = ?');
        $stmt->bind_param('si', $trackingNumber, $transferId);

        if ($stmt->execute()) {
            $stmt->close();
            return ['success' => true, 'tracking_number' => $trackingNumber];
        } else {
            $error = $stmt->error;
            $stmt->close();
            return ['success' => false, 'error' => 'Failed to assign tracking number: ' . $error];
        }
    }

    /* ------- Advanced Retrieval ------- */
    public function getTransferById(int $id): ?array
    {
        $sql = 'SELECT t.*, b.name AS branch_name, u.full_name AS created_by_name, c.full_name AS beneficiary_full_name
                FROM transfers t
                LEFT JOIN branches b ON b.id = t.branch_id
                LEFT JOIN users u    ON u.id = t.created_by
                LEFT JOIN customers c ON c.id = t.beneficiary_id
                WHERE t.id = ? LIMIT 1';
        $stmt = $this->db->prepare($sql);
        if(!$stmt) return null;
        $stmt->bind_param('i',$id);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    public function getTransferHistory(int $transferId): array
    {
        $sql = 'SELECT h.*, u.full_name AS changer_name FROM transfer_status_history h LEFT JOIN users u ON u.id = h.changed_by_user_id WHERE h.transfer_id = ? ORDER BY h.status_date ASC';
        $stmt = $this->db->prepare($sql);
        if(!$stmt) return [];
        $stmt->bind_param('i',$transferId);
        $stmt->execute();
        $res=$stmt->get_result();
        $rows=$res? $res->fetch_all(MYSQLI_ASSOC):[];
        $stmt->close();
        return $rows;
    }

    public function getAllTransfers(array $filters = [], string $orderBy='created_at DESC'): array
    {
        $sql='SELECT t.*, c.full_name AS beneficiary_full_name, b.name AS branch_name FROM transfers t LEFT JOIN customers c ON c.id = t.beneficiary_id LEFT JOIN branches b ON b.id = t.branch_id WHERE 1=1';
        $types=''; $params=[];
        if(!empty($filters['status'])){ $sql.=' AND t.status = ?'; $types.='s'; $params[]=$filters['status']; }
        if(!empty($filters['branch_id'])){ $sql.=' AND t.branch_id = ?'; $types.='i'; $params[]=$filters['branch_id']; }
        if(!empty($filters['search'])){ $term='%'.$filters['search'].'%'; $sql.=' AND (t.transaction_number LIKE ? OR t.sender_name LIKE ? OR c.full_name LIKE ?)'; $types.='sss'; $params[]=$term;$params[]=$term;$params[]=$term; }
        $sql.=' ORDER BY '.$orderBy;
        $stmt=$this->db->prepare($sql);
        if(!$stmt) return [];
        if($params){ $stmt->bind_param($types,...$params); }
        $stmt->execute();
        $res=$stmt->get_result();
        $rows=$res? $res->fetch_all(MYSQLI_ASSOC):[];
        $stmt->close();
        return $rows;
    }

    /**
     * Advanced calculation helper (simplified; uses direct pair rate if exists else 1).
     */
    public function calculateTransferDetails(string $type,int $fromCurr,int $toCurr,float $amount,string $inputField,array $rates=[],array $rules=[]): array
    {
        $rate=1.0; $receiving=$amount; $sending=$amount;
        foreach($rates as $r){
            if((int)$r['from_currency_id']===$fromCurr && (int)$r['to_currency_id']===$toCurr){
                $rate=$r['sell_rate']; break;
            }
        }
        if($fromCurr!==$toCurr){
            if($inputField==='sending'){
                $receiving=$amount*$rate;
            }else{
                $sending=$amount; // treat amount as sending if unknown
                $receiving=$amount;
            }
        }
        // Commission rule match
        $fee=0;$profit=0;
        foreach($rules as $rule){
            if($rule['transfer_type']===$type && (int)$rule['from_currency_id']===$fromCurr && (int)$rule['to_currency_id']===$toCurr){
                if($sending>=$rule['min_amount'] && ($sending<=$rule['max_amount'] || $rule['max_amount']==0)){
                    $fee=$rule['fixed_fee'] + ($sending*$rule['percentage_fee']/100);
                    if($fee < $rule['min_profit']) $fee=$rule['min_profit'];
                    break;
                }
            }
        }
        $profit=$fee; // Simplified – spread profit omitted
        return [
            'sending_amount'=>$sending,
            'receiving_amount'=>$receiving,
            'exchange_rate_used'=>$rate,
            'transfer_fee'=>$fee,
            'profit'=>$profit
        ];
    }

    /**
     * Update status and perform financial integration.
     */
    public function updateTransferStatus(int $transferId,string $newStatus,int $userId,?string $notes=null,?string $location=null,?array $fundSrc=null,?array $dispSrc=null): array
    {
        // allowed statuses
        $allowed=['معلقة','مقبولة','مرفوضة','مرسلة','مستلمة','ملغاة','مكتملة'];
        if(!in_array($newStatus,$allowed,true)) return ['success'=>false,'error'=>'Invalid status'];
        $this->db->begin_transaction();
        $transfer=$this->getTransferById($transferId);
        if(!$transfer){ $this->db->rollback(); return ['success'=>false,'error'=>'Transfer not found']; }
        $oldStatus=$transfer['status'];
        if($oldStatus===$newStatus){ $this->db->rollback(); return ['success'=>false,'error'=>'No status change']; }
        // basic state machine – prevent invalid transitions
        $validNext=['معلقة'=>['مرسلة','ملغاة'],'مرسلة'=>['مكتملة','ملغاة']];
        if(isset($validNext[$oldStatus]) && !in_array($newStatus,$validNext[$oldStatus],true)){
            $this->db->rollback(); return ['success'=>false,'error'=>'غير مسموح بالانتقال من '.$oldStatus.' الى '.$newStatus]; }

        // Update transfers table
        $stmt=$this->db->prepare('UPDATE transfers SET status = ?, completed_at = IF(?="مكتملة", NOW(), completed_at) WHERE id = ?');
        if(!$stmt){ $this->db->rollback(); return ['success'=>false,'error'=>'Prepare failed']; }
        $stmt->bind_param('ssi',$newStatus,$newStatus,$transferId);
        if(!$stmt->execute()){ $stmt->close(); $this->db->rollback(); return ['success'=>false,'error'=>'Update failed']; }
        $stmt->close();
        // Insert history
        $stmt=$this->db->prepare('INSERT INTO transfer_status_history (transfer_id, old_status, new_status, status_date, changed_by_user_id, notes, location) VALUES (?,?,?,?,?,?,?)');
        $stmt->bind_param('isssiss',$transferId,$oldStatus,$newStatus,date('Y-m-d H:i:s'),$userId,$notes,$location);
        $stmt->execute(); $stmt->close();

        // Financial integration on completion
        if($newStatus==='مكتملة'){
            require_once __DIR__.'/cash_manager.php';
            require_once __DIR__.'/bank_manager.php';
            require_once __DIR__.'/accounting_manager.php';
            $cashMgr=new CashManager(new Database());
            $bankMgr=new BankManager(new Database());
            $acctMgr=new AccountingManager($this->db);
            // Determine flows – simplistic: sending currency goes into system (deposit), receiving currency leaves system (withdrawal)
            if(!$fundSrc || !$dispSrc){ $this->db->rollback(); return ['success'=>false,'error'=>'Funding/Disbursement sources required']; }
            // Fund deposit
            if($fundSrc['type']==='cash'){
                $res=$cashMgr->addCashMovementForTransfer($fundSrc['id'],'deposit',(float)$transfer['sending_amount'],$transfer['sending_currency_id'],$transferId,$userId,'Transfer funding');
            }else{
                $res=$bankMgr->addBankMovementForTransfer($fundSrc['id'],'deposit',(float)$transfer['sending_amount'],$transfer['sending_currency_id'],$transferId,$userId,'Transfer funding');
            }
            if(!$res['success']){ $this->db->rollback(); return ['success'=>false,'error'=>$res['error']]; }
            $fundMovId=$res['movement_id'];
            // Disburse withdrawal
            if($dispSrc['type']==='cash'){
                $res=$cashMgr->addCashMovementForTransfer($dispSrc['id'],'withdrawal',(float)$transfer['receiving_amount'],$transfer['receiving_currency_id'],$transferId,$userId,'Transfer disbursement');
            }else{
                $res=$bankMgr->addBankMovementForTransfer($dispSrc['id'],'withdrawal',(float)$transfer['receiving_amount'],$transfer['receiving_currency_id'],$transferId,$userId,'Transfer disbursement');
            }
            if(!$res['success']){ $this->db->rollback(); return ['success'=>false,'error'=>$res['error']]; }
            $dispMovId=$res['movement_id'];
            // Accounting entries (very simplified)
            $acctMgr->createEntry([
                'branch_id'=>$transfer['branch_id'],
                'account_debit'=>'Cash',
                'account_credit'=>'Transfers Payable',
                'amount'=>$transfer['sending_amount'],
                'currency_id'=>$transfer['sending_currency_id'],
                'created_by'=>$userId,
                'transfer_id'=>$transferId,
                'cash_movement_id'=>$fundSrc['type']==='cash'? $fundMovId:null,
                'bank_movement_id'=>$fundSrc['type']==='bank'? $fundMovId:null,
                'description'=>'Funding for transfer'
            ]);
            $acctMgr->createEntry([
                'branch_id'=>$transfer['branch_id'],
                'account_debit'=>'Transfers Payable',
                'account_credit'=>'Cash',
                'amount'=>$transfer['receiving_amount'],
                'currency_id'=>$transfer['receiving_currency_id'],
                'created_by'=>$userId,
                'transfer_id'=>$transferId,
                'cash_movement_id'=>$dispSrc['type']==='cash'? $dispMovId:null,
                'bank_movement_id'=>$dispSrc['type']==='bank'? $dispMovId:null,
                'description'=>'Disbursement of transfer'
            ]);
        }
        // commit
        $this->db->commit();
        // Log
        AuditManager::quickLog($userId,'transfer.update_status','transfers',$transferId,['old'=>$oldStatus],['new'=>$newStatus]);
        return ['success'=>true];
    }

    /**
     * Simple status update for UI operations
     *
     * @param int $transferId Transfer ID
     * @param array $data Update data (status, notes, completed_at)
     * @return bool Success status
     */
    public function updateTransferStatusSimple(int $transferId, array $data): bool
    {
        $allowedStatuses = ['معلقة', 'مقبولة', 'مرفوضة', 'مرسلة', 'مستلمة', 'ملغاة', 'مكتملة'];

        if (!isset($data['status']) || !in_array($data['status'], $allowedStatuses)) {
            return false;
        }

        $sql = 'UPDATE transfers SET status = ?';
        $params = [$data['status']];
        $types = 's';

        // Add notes if provided
        if (isset($data['notes']) && !empty($data['notes'])) {
            $sql .= ', notes = ?';
            $params[] = $data['notes'];
            $types .= 's';
        }

        // Add completed_at if provided
        if (isset($data['completed_at'])) {
            $sql .= ', completed_at = ?';
            $params[] = $data['completed_at'];
            $types .= 's';
        }

        $sql .= ' WHERE id = ?';
        $params[] = $transferId;
        $types .= 'i';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $stmt->bind_param($types, ...$params);
        $result = $stmt->execute();
        $stmt->close();

        return $result;
    }

    /**
     * Update transfer delivery status
     *
     * @param int $transferId Transfer ID
     * @param array $data Delivery data (delivery_status, delivery_notes, status, completed_at)
     * @return bool Success status
     */
    public function updateTransferDelivery(int $transferId, array $data): bool
    {
        // First check if delivery columns exist
        $checkQuery = "SHOW COLUMNS FROM transfers LIKE 'delivery_status'";
        $result = $this->db->query($checkQuery);
        $deliveryColumnsExist = $result && $result->num_rows > 0;

        if (!$deliveryColumnsExist) {
            // If delivery columns don't exist, just update the main status
            if (isset($data['status'])) {
                return $this->updateTransferStatusSimple($transferId, ['status' => $data['status']]);
            }
            return false; // No delivery columns and no status to update
        }

        $sql = 'UPDATE transfers SET';
        $setParts = [];
        $params = [];
        $types = '';

        // Add delivery_status if provided
        if (isset($data['delivery_status'])) {
            $setParts[] = 'delivery_status = ?';
            $params[] = $data['delivery_status'];
            $types .= 's';
        }

        // Add delivery_notes if provided
        if (isset($data['delivery_notes'])) {
            $setParts[] = 'delivery_notes = ?';
            $params[] = $data['delivery_notes'];
            $types .= 's';
        }

        // Add status if provided (when delivery affects main status)
        if (isset($data['status'])) {
            $setParts[] = 'status = ?';
            $params[] = $data['status'];
            $types .= 's';
        }

        // Add completed_at if provided
        if (isset($data['completed_at'])) {
            $setParts[] = 'completed_at = ?';
            $params[] = $data['completed_at'];
            $types .= 's';
        }

        if (empty($setParts)) {
            return false; // No data to update
        }

        $sql .= ' ' . implode(', ', $setParts) . ' WHERE id = ?';
        $params[] = $transferId;
        $types .= 'i';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $stmt->bind_param($types, ...$params);
        $result = $stmt->execute();
        $stmt->close();

        return $result;
    }



    /**
     * Get today's transfer count
     */
    public function getTodayTransferCount(): int {
        $sql = "SELECT COUNT(*) as count FROM transfers WHERE DATE(created_at) = CURDATE()";
        $result = $this->db->query($sql);
        if ($result && $row = $result->fetch_assoc()) {
            return (int)$row['count'];
        }
        return 0;
    }

    public function getTransferCountChange(): float {
        $sql = "SELECT 
                (SELECT COUNT(*) FROM transfers 
                 WHERE DATE(created_at) = CURDATE()) as today,
                (SELECT COUNT(*) FROM transfers 
                 WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) as yesterday";
        
        $result = $this->db->query($sql);
        if (!$result) return 0.0;
        
        $row = $result->fetch_assoc();
        if (!$row || $row['yesterday'] == 0) return 0.0;
        
        return (($row['today'] - $row['yesterday']) / $row['yesterday']) * 100;
    }

    public function getWeeklyOperations(): array {
        $sql = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as count
                FROM transfers 
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC";
        
        $result = $this->db->query($sql);
        if (!$result) return [];
        
        $data = [];
        while ($row = $result->fetch_assoc()) {
            $data[$row['date']] = (int)$row['count'];
        }
        
        // Fill in missing dates with 0
        $end = new DateTime();
        $start = new DateTime('-6 days');
        $interval = new DateInterval('P1D');
        $period = new DatePeriod($start, $interval, $end);
        
        $complete_data = [];
        foreach ($period as $date) {
            $date_str = $date->format('Y-m-d');
            $complete_data[$date_str] = $data[$date_str] ?? 0;
        }
        $complete_data[$end->format('Y-m-d')] = $data[$end->format('Y-m-d')] ?? 0;
        
        return array_values($complete_data);
    }
} 
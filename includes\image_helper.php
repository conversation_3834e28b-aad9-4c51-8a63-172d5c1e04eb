<?php

class ImageHelper
{
    private static $uploadDir = 'uploads/profiles/';
    private static $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    private static $maxSize = 5 * 1024 * 1024; // 5MB

    /**
     * Upload user profile image (simple upload without resizing)
     */
    public static function uploadProfileImage($file, $userId): ?string
    {
        // Check if upload directory exists, create if not
        $uploadPath = __DIR__ . '/../' . self::$uploadDir;
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Validate file
        if (!self::validateImage($file)) {
            return null;
        }

        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'user_' . $userId . '_' . time() . '.' . $extension;
        $filepath = $uploadPath . $filename;

        // Upload file (no resizing)
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return self::$uploadDir . $filename;
        }

        return null;
    }

    /**
     * Validate uploaded image
     */
    private static function validateImage($file): bool
    {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return false;
        }

        // Check file size
        if ($file['size'] > self::$maxSize) {
            return false;
        }

        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, self::$allowedTypes)) {
            return false;
        }

        return true;
    }

    /**
     * Delete profile image file
     */
    public static function deleteProfileImage($imagePath): bool
    {
        if (empty($imagePath)) {
            return true;
        }

        $fullPath = __DIR__ . '/../' . $imagePath;
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }

        return true;
    }

    /**
     * Get profile image URL or default avatar
     */
    public static function getProfileImageUrl($imagePath, $userId = null): string
    {
        if (!empty($imagePath)) {
            $fullPath = __DIR__ . '/../' . $imagePath;
            if (file_exists($fullPath)) {
                return BASE_URL . $imagePath;
            }
        }

        // Return default avatar
        return BASE_URL . 'assets/images/default-avatar.png';
    }

    /**
     * Get profile image HTML with CSS classes
     */
    public static function getProfileImageHtmlWithClasses($imagePath, $userId = null, $sizeClass = 'profile-image-md', $borderClass = '', $additionalClass = ''): string
    {
        $imageUrl = self::getProfileImageUrl($imagePath, $userId);
        $classes = "profile-image {$sizeClass} {$borderClass} {$additionalClass}";
        
        return "<img src=\"{$imageUrl}\" alt=\"صورة المستخدم\" class=\"{$classes}\">";
    }

    /**
     * Get default avatar HTML
     */
    public static function getDefaultAvatarHtml($sizeClass = 'default-avatar-md', $additionalClass = ''): string
    {
        $classes = "default-avatar {$sizeClass} {$additionalClass}";
        return "<div class=\"{$classes}\"><i class=\"fas fa-user\"></i></div>";
    }

    /**
     * Get profile image HTML with improved CSS sizing
     */
    public static function getProfileImageHtml($imagePath, $userId = null, $size = '40px', $class = ''): string
    {
        $imageUrl = self::getProfileImageUrl($imagePath, $userId);
        $style = "width: {$size}; height: {$size}; object-fit: cover; object-position: center; border-radius: 50%; display: block;";
        
        return "<img src=\"{$imageUrl}\" alt=\"صورة المستخدم\" style=\"{$style}\" class=\"{$class}\">";
    }

    /**
     * Get profile image HTML with custom positioning
     */
    public static function getProfileImageHtmlWithPosition($imagePath, $userId = null, $size = '40px', $position = 'center', $class = ''): string
    {
        $imageUrl = self::getProfileImageUrl($imagePath, $userId);
        $style = "width: {$size}; height: {$size}; object-fit: cover; object-position: {$position}; border-radius: 50%; display: block;";
        
        return "<img src=\"{$imageUrl}\" alt=\"صورة المستخدم\" style=\"{$style}\" class=\"{$class}\">";
    }

    /**
     * Get profile image HTML with border
     */
    public static function getProfileImageHtmlWithBorder($imagePath, $userId = null, $size = '40px', $borderColor = 'transparent', $borderWidth = '0px', $class = ''): string
    {
        $imageUrl = self::getProfileImageUrl($imagePath, $userId);
        $style = "width: {$size}; height: {$size}; object-fit: cover; object-position: center; border-radius: 50%; display: block; border: {$borderWidth} solid {$borderColor};";
        
        return "<img src=\"{$imageUrl}\" alt=\"صورة المستخدم\" style=\"{$style}\" class=\"{$class}\">";
    }

    /**
     * Check if image resizing is available (always false now)
     */
    public static function isResizingAvailable(): bool
    {
        return false; // We don't use resizing anymore
    }
} 
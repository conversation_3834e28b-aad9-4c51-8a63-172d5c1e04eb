<?php
/**
 * Migration Runner for Transfer Delivery Status
 * This script adds the missing delivery_status and delivery_notes columns to the transfers table
 */

// Include database connection
require_once __DIR__ . '/../includes/database.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تحديث قاعدة البيانات - إضافة حالة التسليم</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-8'>
            <div class='card shadow'>
                <div class='card-header bg-primary text-white'>
                    <h4 class='mb-0'>
                        <i class='fas fa-database me-2'></i>
                        تحديث قاعدة البيانات - إضافة حالة التسليم
                    </h4>
                </div>
                <div class='card-body'>";

try {
    // Initialize database connection
    $db = Database::getConnection();
    
    echo "<div class='alert alert-info'>
            <i class='fas fa-info-circle me-2'></i>
            بدء عملية تحديث قاعدة البيانات...
          </div>";
    
    // Start transaction
    $db->begin_transaction();
    
    // Check if columns already exist
    $checkQuery = "SHOW COLUMNS FROM transfers LIKE 'delivery_status'";
    $result = $db->query($checkQuery);
    
    if ($result && $result->num_rows > 0) {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                العمود delivery_status موجود بالفعل. سيتم تخطي هذه الخطوة.
              </div>";
    } else {
        // Add delivery_status column
        echo "<div class='alert alert-info'>
                <i class='fas fa-plus me-2'></i>
                إضافة عمود delivery_status...
              </div>";
        
        $sql1 = "ALTER TABLE transfers 
                 ADD COLUMN delivery_status ENUM('غير مستلمة', 'في الطريق', 'وصلت للوجهة', 'مستلمة', 'مرتجعة') 
                 DEFAULT 'غير مستلمة' 
                 AFTER status";
        
        if ($db->query($sql1)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة عمود delivery_status بنجاح
                  </div>";
        } else {
            throw new Exception("خطأ في إضافة عمود delivery_status: " . $db->error);
        }
    }
    
    // Check if delivery_notes column exists
    $checkQuery2 = "SHOW COLUMNS FROM transfers LIKE 'delivery_notes'";
    $result2 = $db->query($checkQuery2);
    
    if ($result2 && $result2->num_rows > 0) {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                العمود delivery_notes موجود بالفعل. سيتم تخطي هذه الخطوة.
              </div>";
    } else {
        // Add delivery_notes column
        echo "<div class='alert alert-info'>
                <i class='fas fa-plus me-2'></i>
                إضافة عمود delivery_notes...
              </div>";
        
        $sql2 = "ALTER TABLE transfers 
                 ADD COLUMN delivery_notes TEXT NULL 
                 AFTER delivery_status";
        
        if ($db->query($sql2)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة عمود delivery_notes بنجاح
                  </div>";
        } else {
            throw new Exception("خطأ في إضافة عمود delivery_notes: " . $db->error);
        }
    }
    
    // Update status enum to include new statuses
    echo "<div class='alert alert-info'>
            <i class='fas fa-edit me-2'></i>
            تحديث قائمة حالات الحوالة...
          </div>";
    
    $sql3 = "ALTER TABLE transfers 
             MODIFY COLUMN status ENUM('معلقة', 'مقبولة', 'مرفوضة', 'مرسلة', 'مستلمة', 'ملغاة', 'مكتملة') 
             DEFAULT 'معلقة'";
    
    if ($db->query($sql3)) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                تم تحديث قائمة حالات الحوالة بنجاح
              </div>";
    } else {
        throw new Exception("خطأ في تحديث قائمة حالات الحوالة: " . $db->error);
    }
    
    // Update transfer_status_history table
    echo "<div class='alert alert-info'>
            <i class='fas fa-edit me-2'></i>
            تحديث جدول تاريخ حالات الحوالة...
          </div>";
    
    $sql4 = "ALTER TABLE transfer_status_history 
             MODIFY COLUMN old_status ENUM('معلقة', 'مقبولة', 'مرفوضة', 'مرسلة', 'مستلمة', 'ملغاة', 'مكتملة') NULL";
    
    $sql5 = "ALTER TABLE transfer_status_history 
             MODIFY COLUMN new_status ENUM('معلقة', 'مقبولة', 'مرفوضة', 'مرسلة', 'مستلمة', 'ملغاة', 'مكتملة') NOT NULL";
    
    if ($db->query($sql4) && $db->query($sql5)) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                تم تحديث جدول تاريخ حالات الحوالة بنجاح
              </div>";
    } else {
        throw new Exception("خطأ في تحديث جدول تاريخ حالات الحوالة: " . $db->error);
    }
    
    // Add indexes for better performance
    echo "<div class='alert alert-info'>
            <i class='fas fa-database me-2'></i>
            إضافة فهارس لتحسين الأداء...
          </div>";
    
    // Check if indexes exist before creating them
    $indexCheck1 = "SHOW INDEX FROM transfers WHERE Key_name = 'idx_transfers_delivery_status'";
    $indexResult1 = $db->query($indexCheck1);
    
    if (!$indexResult1 || $indexResult1->num_rows == 0) {
        $sql6 = "CREATE INDEX idx_transfers_delivery_status ON transfers(delivery_status)";
        if ($db->query($sql6)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة فهرس delivery_status بنجاح
                  </div>";
        }
    }
    
    $indexCheck2 = "SHOW INDEX FROM transfers WHERE Key_name = 'idx_transfers_status'";
    $indexResult2 = $db->query($indexCheck2);
    
    if (!$indexResult2 || $indexResult2->num_rows == 0) {
        $sql7 = "CREATE INDEX idx_transfers_status ON transfers(status)";
        if ($db->query($sql7)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة فهرس status بنجاح
                  </div>";
        }
    }
    
    // Commit transaction
    $db->commit();
    
    echo "<div class='alert alert-success'>
            <i class='fas fa-check-circle me-2'></i>
            <strong>تم تحديث قاعدة البيانات بنجاح!</strong>
            <br>
            تم إضافة الأعمدة والفهارس المطلوبة لدعم حالة التسليم.
          </div>";
    
    echo "<div class='alert alert-info'>
            <i class='fas fa-info-circle me-2'></i>
            <strong>الأعمدة المضافة:</strong>
            <ul class='mb-0 mt-2'>
                <li><code>delivery_status</code> - حالة التسليم</li>
                <li><code>delivery_notes</code> - ملاحظات التسليم</li>
            </ul>
          </div>";
    
    echo "<div class='alert alert-info'>
            <i class='fas fa-list me-2'></i>
            <strong>حالات الحوالة المحدثة:</strong>
            <ul class='mb-0 mt-2'>
                <li>معلقة</li>
                <li>مقبولة</li>
                <li>مرفوضة</li>
                <li>مرسلة</li>
                <li>مستلمة</li>
                <li>ملغاة</li>
                <li>مكتملة</li>
            </ul>
          </div>";
    
    echo "<div class='alert alert-info'>
            <i class='fas fa-truck me-2'></i>
            <strong>حالات التسليم المتاحة:</strong>
            <ul class='mb-0 mt-2'>
                <li>غير مستلمة</li>
                <li>في الطريق</li>
                <li>وصلت للوجهة</li>
                <li>مستلمة</li>
                <li>مرتجعة</li>
            </ul>
          </div>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    $db->rollback();
    
    echo "<div class='alert alert-danger'>
            <i class='fas fa-exclamation-circle me-2'></i>
            <strong>خطأ في تحديث قاعدة البيانات:</strong>
            <br>
            " . htmlspecialchars($e->getMessage()) . "
          </div>";
    
    echo "<div class='alert alert-warning'>
            <i class='fas fa-undo me-2'></i>
            تم التراجع عن جميع التغييرات.
          </div>";
}

echo "                </div>
                <div class='card-footer'>
                    <a href='../dashboard/transfers.php' class='btn btn-primary'>
                        <i class='fas fa-arrow-left me-2'></i>
                        العودة لصفحة الحوالات
                    </a>
                    <a href='../dashboard/' class='btn btn-secondary ms-2'>
                        <i class='fas fa-home me-2'></i>
                        الصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>

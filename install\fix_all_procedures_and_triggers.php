<?php
/**
 * إصلاح شامل للإجراءات المخزنة والمشغلات
 * Complete Fix for Stored Procedures and Triggers
 */

require_once __DIR__ . '/../config/database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح شامل للإجراءات المخزنة والمشغلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-tools text-primary"></i>
            إصلاح شامل للإجراءات المخزنة والمشغلات
        </h1>

        <?php
        try {
            // الاتصال بقاعدة البيانات
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            
            echo "<div class='result success'>";
            echo "<i class='fas fa-check-circle me-2'></i>";
            echo "<strong>✓ تم الاتصال بقاعدة البيانات بنجاح</strong>";
            echo "</div>";
            
            // 1. حذف الإجراءات والمشغلات الموجودة
            echo "<h3>حذف الإجراءات والمشغلات الموجودة:</h3>";
            
            $procedures_to_drop = ['UpdateDailyTransaction', 'DeleteDailyTransaction', 'GetDeletedDailyTransactions'];
            
            foreach ($procedures_to_drop as $procedure) {
                try {
                    $pdo->exec("DROP PROCEDURE IF EXISTS {$procedure}");
                    echo "<div class='result info'>";
                    echo "<i class='fas fa-trash me-2'></i>";
                    echo "تم حذف الإجراء {$procedure} (إن وجد)";
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='result warning'>";
                    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                    echo "تحذير في حذف الإجراء {$procedure}: " . $e->getMessage();
                    echo "</div>";
                }
            }
            
            // حذف المشغل
            try {
                $pdo->exec("DROP TRIGGER IF EXISTS before_daily_transaction_delete");
                echo "<div class='result info'>";
                echo "<i class='fas fa-trash me-2'></i>";
                echo "تم حذف المشغل before_daily_transaction_delete (إن وجد)";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='result warning'>";
                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                echo "تحذير في حذف المشغل: " . $e->getMessage();
                echo "</div>";
            }
            
            // 2. إنشاء إجراء UpdateDailyTransaction
            echo "<h3>إنشاء إجراء UpdateDailyTransaction:</h3>";
            
            $updateProcedure = "
            CREATE PROCEDURE UpdateDailyTransaction(
                IN p_transaction_id INT UNSIGNED,
                IN p_country_id INT UNSIGNED,
                IN p_base_amount DECIMAL(18,2),
                IN p_customer_rate DECIMAL(18,6),
                IN p_operation_type ENUM('multiply', 'divide'),
                IN p_exchange_rate DECIMAL(18,6),
                IN p_delivery_type ENUM('cash', 'bank', 'usdt'),
                IN p_transfer_amount DECIMAL(18,2),
                IN p_recipient_name VARCHAR(255),
                IN p_notes TEXT,
                IN p_updated_by INT UNSIGNED,
                OUT p_result_message VARCHAR(500)
            )
            BEGIN
                DECLARE v_transaction_exists INT DEFAULT 0;
                DECLARE v_country_exists INT DEFAULT 0;
                DECLARE v_calculated_amount DECIMAL(18,2);
                DECLARE v_recipient_amount DECIMAL(18,2);
                
                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    ROLLBACK;
                    GET DIAGNOSTICS CONDITION 1
                        p_result_message = MESSAGE_TEXT;
                END;
                
                START TRANSACTION;
                
                -- التحقق من وجود المعاملة
                SELECT COUNT(*) INTO v_transaction_exists 
                FROM daily_transactions 
                WHERE id = p_transaction_id;
                
                IF v_transaction_exists = 0 THEN
                    SET p_result_message = 'المعاملة المحددة غير موجودة';
                    ROLLBACK;
                    LEAVE UpdateDailyTransaction;
                END IF;
                
                -- التحقق من وجود الدولة
                SELECT COUNT(*) INTO v_country_exists 
                FROM countries 
                WHERE id = p_country_id AND is_active = 1;
                
                IF v_country_exists = 0 THEN
                    SET p_result_message = 'الدولة المحددة غير موجودة أو غير نشطة';
                    ROLLBACK;
                    LEAVE UpdateDailyTransaction;
                END IF;
                
                -- التحقق من صحة المبالغ
                IF p_base_amount <= 0 THEN
                    SET p_result_message = 'مبلغ الحوالة الأساسي يجب أن يكون أكبر من صفر';
                    ROLLBACK;
                    LEAVE UpdateDailyTransaction;
                END IF;
                
                -- حساب المبلغ الناتج
                IF p_operation_type = 'multiply' THEN
                    SET v_calculated_amount = p_base_amount * p_customer_rate;
                ELSE
                    SET v_calculated_amount = p_base_amount / p_customer_rate;
                END IF;
                
                -- حساب المبلغ للمستلم
                SET v_recipient_amount = FLOOR(v_calculated_amount * p_exchange_rate);
                
                -- تحديث المعاملة
                UPDATE daily_transactions SET
                    country_id = p_country_id,
                    base_amount = p_base_amount,
                    customer_rate = p_customer_rate,
                    operation_type = p_operation_type,
                    calculated_amount = v_calculated_amount,
                    exchange_rate = p_exchange_rate,
                    recipient_amount = v_recipient_amount,
                    delivery_type = p_delivery_type,
                    transfer_amount = p_transfer_amount,
                    recipient_name = p_recipient_name,
                    notes = p_notes,
                    updated_by = p_updated_by,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = p_transaction_id;
                
                SET p_result_message = 'تم تحديث المعاملة بنجاح';
                
                COMMIT;
            END
            ";
            
            try {
                $pdo->exec($updateProcedure);
                echo "<div class='result success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ تم إنشاء إجراء UpdateDailyTransaction بنجاح</strong>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='result error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ خطأ في إنشاء إجراء UpdateDailyTransaction:</strong> " . $e->getMessage();
                echo "</div>";
            }
            
            // 3. إنشاء إجراء DeleteDailyTransaction
            echo "<h3>إنشاء إجراء DeleteDailyTransaction:</h3>";
            
            $deleteProcedure = "
            CREATE PROCEDURE DeleteDailyTransaction(
                IN p_transaction_id INT UNSIGNED,
                IN p_deleted_by INT UNSIGNED,
                OUT p_result_message VARCHAR(500)
            )
            BEGIN
                DECLARE v_transaction_exists INT DEFAULT 0;
                
                DECLARE EXIT HANDLER FOR SQLEXCEPTION
                BEGIN
                    ROLLBACK;
                    GET DIAGNOSTICS CONDITION 1
                        p_result_message = MESSAGE_TEXT;
                END;
                
                START TRANSACTION;
                
                -- التحقق من وجود المعاملة
                SELECT COUNT(*) INTO v_transaction_exists 
                FROM daily_transactions 
                WHERE id = p_transaction_id;
                
                IF v_transaction_exists = 0 THEN
                    SET p_result_message = 'المعاملة المحددة غير موجودة';
                    ROLLBACK;
                    LEAVE DeleteDailyTransaction;
                END IF;
                
                -- تعيين المستخدم الحالي للاستخدام في الـ Trigger
                SET @current_user_id = p_deleted_by;
                
                -- حذف المعاملة (سيتم تنفيذ الـ Trigger تلقائياً)
                DELETE FROM daily_transactions WHERE id = p_transaction_id;
                
                SET p_result_message = 'تم حذف المعاملة بنجاح';
                
                COMMIT;
            END
            ";
            
            try {
                $pdo->exec($deleteProcedure);
                echo "<div class='result success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ تم إنشاء إجراء DeleteDailyTransaction بنجاح</strong>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='result error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ خطأ في إنشاء إجراء DeleteDailyTransaction:</strong> " . $e->getMessage();
                echo "</div>";
            }

            // 4. إنشاء إجراء GetDeletedDailyTransactions
            echo "<h3>إنشاء إجراء GetDeletedDailyTransactions:</h3>";

            $getDeletedProcedure = "
            CREATE PROCEDURE GetDeletedDailyTransactions(
                IN p_date_from DATE,
                IN p_date_to DATE,
                IN p_limit_count INT,
                IN p_offset_count INT
            )
            BEGIN
                DECLARE v_sql TEXT DEFAULT '';
                DECLARE v_limit_clause TEXT DEFAULT '';

                SET v_sql = 'SELECT
                    ddt.id,
                    ddt.original_id,
                    ddt.transaction_number,
                    ddt.deleted_at,
                    c.name_ar as country_name,
                    c.currency_code,
                    ddt.base_amount,
                    ddt.customer_rate,
                    ddt.operation_type,
                    ddt.calculated_amount,
                    ddt.exchange_rate,
                    ddt.recipient_amount,
                    ddt.delivery_type,
                    ddt.transfer_amount,
                    ddt.recipient_name,
                    ddt.notes,
                    b.name as branch_name,
                    u_created.full_name as created_by_name,
                    u_updated.full_name as updated_by_name,
                    u_deleted.full_name as deleted_by_name,
                    ddt.created_at,
                    ddt.updated_at,
                    ddt.deleted_at
                FROM deleted_daily_transactions ddt
                LEFT JOIN countries c ON ddt.country_id = c.id
                LEFT JOIN branches b ON ddt.branch_id = b.id
                LEFT JOIN users u_created ON ddt.created_by = u_created.id
                LEFT JOIN users u_updated ON ddt.updated_by = u_updated.id
                LEFT JOIN users u_deleted ON ddt.deleted_by = u_deleted.id
                WHERE 1=1';

                IF p_date_from IS NOT NULL THEN
                    SET v_sql = CONCAT(v_sql, ' AND DATE(ddt.deleted_at) >= ''', p_date_from, '''');
                END IF;

                IF p_date_to IS NOT NULL THEN
                    SET v_sql = CONCAT(v_sql, ' AND DATE(ddt.deleted_at) <= ''', p_date_to, '''');
                END IF;

                SET v_sql = CONCAT(v_sql, ' ORDER BY ddt.deleted_at DESC');

                IF p_limit_count IS NOT NULL AND p_limit_count > 0 THEN
                    SET v_limit_clause = CONCAT(' LIMIT ', p_limit_count);

                    IF p_offset_count IS NOT NULL AND p_offset_count > 0 THEN
                        SET v_limit_clause = CONCAT(v_limit_clause, ' OFFSET ', p_offset_count);
                    END IF;

                    SET v_sql = CONCAT(v_sql, v_limit_clause);
                END IF;

                SET @sql_statement = v_sql;
                PREPARE stmt FROM @sql_statement;
                EXECUTE stmt;
                DEALLOCATE PREPARE stmt;
            END
            ";

            try {
                $pdo->exec($getDeletedProcedure);
                echo "<div class='result success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ تم إنشاء إجراء GetDeletedDailyTransactions بنجاح</strong>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='result error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ خطأ في إنشاء إجراء GetDeletedDailyTransactions:</strong> " . $e->getMessage();
                echo "</div>";
            }

            // 5. إنشاء المشغل
            echo "<h3>إنشاء المشغل before_daily_transaction_delete:</h3>";

            $trigger_sql = "
            CREATE TRIGGER before_daily_transaction_delete
            BEFORE DELETE ON daily_transactions
            FOR EACH ROW
            BEGIN
                INSERT INTO deleted_daily_transactions (
                    original_id,
                    transaction_number,
                    country_id,
                    base_amount,
                    customer_rate,
                    operation_type,
                    calculated_amount,
                    exchange_rate,
                    recipient_amount,
                    delivery_type,
                    transfer_amount,
                    recipient_name,
                    notes,
                    branch_id,
                    created_by,
                    created_at,
                    updated_by,
                    updated_at,
                    deleted_by
                ) VALUES (
                    OLD.id,
                    OLD.transaction_number,
                    OLD.country_id,
                    OLD.base_amount,
                    OLD.customer_rate,
                    OLD.operation_type,
                    OLD.calculated_amount,
                    OLD.exchange_rate,
                    OLD.recipient_amount,
                    OLD.delivery_type,
                    OLD.transfer_amount,
                    OLD.recipient_name,
                    OLD.notes,
                    OLD.branch_id,
                    OLD.created_by,
                    OLD.created_at,
                    OLD.updated_by,
                    OLD.updated_at,
                    COALESCE(@current_user_id, 1)
                );
            END
            ";

            try {
                $pdo->exec($trigger_sql);
                echo "<div class='result success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ تم إنشاء المشغل before_daily_transaction_delete بنجاح</strong>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='result error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ خطأ في إنشاء المشغل:</strong> " . $e->getMessage();
                echo "</div>";
            }

            // 6. التحقق من النتائج النهائية
            echo "<h3>التحقق من النتائج النهائية:</h3>";

            // فحص الإجراءات المخزنة
            $procedures = ['UpdateDailyTransaction', 'DeleteDailyTransaction', 'GetDeletedDailyTransactions'];
            $procedures_success = 0;

            foreach ($procedures as $procedure) {
                try {
                    $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = '{$procedure}' AND Db = '" . DB_NAME . "'");
                    $exists = $stmt->rowCount() > 0;

                    if ($exists) {
                        echo "<div class='result success'>";
                        echo "<i class='fas fa-check-circle me-2'></i>";
                        echo "<strong>✓ إجراء {$procedure} موجود ويعمل</strong>";
                        echo "</div>";
                        $procedures_success++;
                    } else {
                        echo "<div class='result error'>";
                        echo "<i class='fas fa-times-circle me-2'></i>";
                        echo "<strong>✗ إجراء {$procedure} غير موجود</strong>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='result error'>";
                    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                    echo "<strong>خطأ في فحص إجراء {$procedure}:</strong> " . $e->getMessage();
                    echo "</div>";
                }
            }

            // فحص المشغل
            try {
                $stmt = $pdo->query("SHOW TRIGGERS FROM " . DB_NAME);
                $triggers = $stmt->fetchAll();
                $trigger_found = false;

                foreach ($triggers as $trigger) {
                    if ($trigger['Trigger'] == 'before_daily_transaction_delete') {
                        $trigger_found = true;
                        break;
                    }
                }

                if ($trigger_found) {
                    echo "<div class='result success'>";
                    echo "<i class='fas fa-check-circle me-2'></i>";
                    echo "<strong>✓ المشغل before_daily_transaction_delete موجود ويعمل</strong>";
                    echo "</div>";
                } else {
                    echo "<div class='result error'>";
                    echo "<i class='fas fa-times-circle me-2'></i>";
                    echo "<strong>✗ المشغل before_daily_transaction_delete غير موجود</strong>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='result error'>";
                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                echo "<strong>خطأ في فحص المشغل:</strong> " . $e->getMessage();
                echo "</div>";
            }

            echo "<hr>";

            if ($procedures_success == 3) {
                echo "<div class='alert alert-success text-center'>";
                echo "<h4><i class='fas fa-trophy me-2'></i>تم الإصلاح بنجاح!</h4>";
                echo "<p>جميع الإجراءات المخزنة والمشغلات تعمل بشكل صحيح</p>";
                echo "</div>";
            } else {
                echo "<div class='alert alert-warning text-center'>";
                echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>إصلاح جزئي</h4>";
                echo "<p>تم إنشاء {$procedures_success} من أصل 3 إجراءات مخزنة</p>";
                echo "</div>";
            }

        } catch (Exception $e) {
            echo "<div class='result error'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>خطأ عام:</strong> " . $e->getMessage();
            echo "</div>";
        }
        ?>

        <div class="text-center mt-4">
            <a href="test_daily_transactions_system.php" class="btn btn-primary btn-lg">
                <i class="fas fa-vial me-2"></i>
                اختبار النظام مرة أخرى
            </a>
            <a href="../dashboard/daily_transactions.php" class="btn btn-success btn-lg ms-2">
                <i class="fas fa-list me-2"></i>
                الذهاب إلى المعاملات اليومية
            </a>
            <a href="../dashboard/edit_daily_transaction.php?id=1" class="btn btn-warning btn-lg ms-2">
                <i class="fas fa-edit me-2"></i>
                اختبار التعديل
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

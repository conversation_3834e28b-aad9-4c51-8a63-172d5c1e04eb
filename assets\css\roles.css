/* تنسيقات خاصة بصفحة الأدوار */

/* بطاقات الإحصائيات */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* تحسين الجدول */
#rolesTable {
    font-size: 0.9rem;
}

#rolesTable th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    border: none;
    padding: 15px 10px;
    position: sticky;
    top: 0;
    z-index: 10;
}

#rolesTable td {
    vertical-align: middle;
    padding: 12px 10px;
    border-bottom: 1px solid #dee2e6;
}

#rolesTable tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* تحسين الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
    border-radius: 8px;
    font-weight: 500;
}

/* شارات الحالة التفاعلية */
.status-badge {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-badge:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    z-index: 10;
}

.status-badge:active {
    transform: scale(0.95);
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.status-badge:hover::before {
    left: 100%;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: #000 !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

/* تحسين الأزرار */
.btn-group-sm .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
    border-radius: 6px;
    margin: 0 2px;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    transform: scale(1.05);
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    transform: scale(1.05);
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    transform: scale(1.05);
}

/* تحسين الفلاتر */
.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    border-radius: 12px 12px 0 0 !important;
}

.form-select, .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

/* تحسين الإحصائيات */
.card.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

/* تحسين الأيقونات */
.fas {
    margin-left: 8px;
}

/* تحسين Modal */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    border-radius: 15px 15px 0 0;
    padding: 20px;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px;
    border-top: 2px solid #dee2e6;
}

/* تحسين الجدول المتجاوب */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* تحسين عداد الأدوار */
#roleCount {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* تحسين النوافذ المنبثقة للصلاحيات */
#permissionsModal .modal-dialog {
    max-width: 900px;
}

#availablePermissions, #rolePermissions {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.form-check {
    padding: 10px;
    margin: 5px 0;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.form-check:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
    transform: translateX(5px);
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    cursor: pointer;
    width: 100%;
}

/* تحسين التخطيط العام */
.content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

/* تحسين العناوين */
h2, h5 {
    color: #495057;
    font-weight: 600;
}

/* تحسين الروابط والأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    border: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
}

/* تحسين الجدول للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.3em 0.5em;
    }
    
    .modal-dialog {
        margin: 10px;
    }
    
    #permissionsModal .modal-dialog {
        max-width: 95%;
    }
}

/* تحسين التمرير */
.table-responsive::-webkit-scrollbar,
#availablePermissions::-webkit-scrollbar,
#rolePermissions::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track,
#availablePermissions::-webkit-scrollbar-track,
#rolePermissions::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb,
#availablePermissions::-webkit-scrollbar-thumb,
#rolePermissions::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover,
#availablePermissions::-webkit-scrollbar-thumb:hover,
#rolePermissions::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

/* تأثيرات الحركة */
.card, .btn, .badge, .form-check {
    transition: all 0.3s ease;
}

/* تحسين النصوص */
.text-primary {
    color: #007bff !important;
    font-weight: 600;
}

.text-dark {
    color: #495057 !important;
}

/* تحسين الحدود */
.border-start {
    border-left: 4px solid #007bff !important;
}

/* تحسين المسافات */
.mb-4 {
    margin-bottom: 2rem !important;
}

.me-2 {
    margin-left: 0.75rem !important;
}

.ms-2 {
    margin-right: 0.75rem !important;
}

/* تحسين التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

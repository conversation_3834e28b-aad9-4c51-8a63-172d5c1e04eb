<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/transfer_manager.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';
require_once __DIR__ . '/../includes/exchange_manager.php';
require_once __DIR__ . '/../includes/cash_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('transfers.view');

$currentUser = $auth->getCurrentUser();
$userId   = $currentUser['id'];
$branchId = $currentUser['branch_id'] ?? null;

$transferMgr = new TransferManager(new Database());
$customerMgr = new CustomerManager(new Database());
$currencyMgr = new CurrencyManager(new Database());
$exManager   = new ExchangeManager(new Database());
$cashMgr     = new CashManager(new Database());

$errors = [];

// Handle transfer status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && ($_POST['action'] ?? '') === 'update_status') {
    $auth->requirePermission('transfers.edit');

    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز الحماية غير صالح';
    } else {
        $transferId = (int)($_POST['transfer_id'] ?? 0);
        $newStatus = sanitize_input($_POST['status'] ?? '');
        $notes = sanitize_input($_POST['notes'] ?? '');

        // Validate status
        $validStatuses = ['معلقة', 'مقبولة', 'مرفوضة', 'مرسلة', 'مستلمة', 'ملغاة'];
        if (!in_array($newStatus, $validStatuses)) {
            $errors[] = 'حالة غير صالحة';
        }

        if ($transferId <= 0) {
            $errors[] = 'معرف الحوالة غير صالح';
        }

        if (empty($errors)) {
            try {
                $updateData = [
                    'status' => $newStatus,
                    'notes' => $notes
                ];

                // If status is completed, set completed_at
                if ($newStatus === 'مستلمة') {
                    $updateData['completed_at'] = date('Y-m-d H:i:s');
                }

                $success = $transferMgr->updateTransferStatusSimple($transferId, $updateData);

                if ($success) {
                    // Log the activity
                    log_activity($userId, 'transfer.status_update', [
                        'transfer_id' => $transferId,
                        'new_status' => $newStatus,
                        'notes' => $notes
                    ]);

                    set_flash('success', 'تم تحديث حالة الحوالة بنجاح');
                } else {
                    set_flash('danger', 'فشل في تحديث حالة الحوالة');
                }
            } catch (Exception $e) {
                set_flash('danger', 'خطأ في تحديث الحالة: ' . $e->getMessage());
                error_log("Error updating transfer status: " . $e->getMessage());
            }
        }
    }

    // Redirect to prevent form resubmission
    header('Location: transfers.php');
    exit;
}

// Handle delivery status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && ($_POST['action'] ?? '') === 'update_delivery') {
    $auth->requirePermission('transfers.edit');

    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز الحماية غير صالح';
    } else {
        $transferId = (int)($_POST['transfer_id'] ?? 0);
        $deliveryStatus = sanitize_input($_POST['delivery_status'] ?? '');
        $deliveryNotes = sanitize_input($_POST['delivery_notes'] ?? '');

        if ($transferId <= 0) {
            $errors[] = 'معرف الحوالة غير صالح';
        }

        if (empty($errors)) {
            try {
                $updateData = [
                    'delivery_status' => $deliveryStatus,
                    'delivery_notes' => $deliveryNotes
                ];

                // If delivered, update status and completed_at
                if ($deliveryStatus === 'مستلمة') {
                    $updateData['status'] = 'مستلمة';
                    $updateData['completed_at'] = date('Y-m-d H:i:s');
                }

                $success = $transferMgr->updateTransferDelivery($transferId, $updateData);

                if ($success) {
                    // تسجيل عملية تحديث حالة التسليم
                    ActivityHelper::logStatusChange(
                        'transfers',
                        "حوالة #$transferId",
                        'غير محدد',
                        $deliveryStatus,
                        $transferId
                    );

                    set_flash('success', 'تم تحديث حالة التسليم بنجاح');
                } else {
                    set_flash('danger', 'فشل في تحديث حالة التسليم');
                }
            } catch (Exception $e) {
                set_flash('danger', 'خطأ في تحديث التسليم: ' . $e->getMessage());
                error_log("Error updating delivery status: " . $e->getMessage());
            }
        }
    }

    // Redirect to prevent form resubmission
    header('Location: transfers.php');
    exit;
}

// Handle adding new transfer
if ($_SERVER['REQUEST_METHOD'] === 'POST' && ($_POST['action'] ?? '') === 'add_transfer') {
    $auth->requirePermission('transfers.create');
    
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز الحماية غير صالح';
    }

    $transferCountry        = sanitize_input($_POST['transfer_country'] ?? '');
    $trackingNumber         = sanitize_input($_POST['tracking_number'] ?? '');
    $beneficiaryId          = (int)($_POST['beneficiary_id'] ?? 0);
    $baseAmount             = (float)($_POST['base_amount'] ?? 0);
    $deliveryAmount         = (float)($_POST['delivery_amount'] ?? 0);
    $deliveryCashBoxId      = (int)($_POST['delivery_cash_box_id'] ?? 0);
    $deliveryMethod         = sanitize_input($_POST['delivery_method'] ?? 'نقدي');
    $notes                  = sanitize_input($_POST['notes'] ?? '');

    // Get cash box information to determine currency
    $cashBox = $cashMgr->getCashBoxById($deliveryCashBoxId);
    
    // Find currency ID from currency code in cash box
    $currencyId = 0;
    if ($cashBox && !empty($cashBox['currency_code'])) {
        // Find currency ID from code
        $currencySql = "SELECT id FROM currencies WHERE code = ?";
        $currencyStmt = Database::getConnection()->prepare($currencySql);
        if ($currencyStmt) {
            $currencyStmt->bind_param('s', $cashBox['currency_code']);
            $currencyStmt->execute();
            $currencyStmt->bind_result($currencyId);
            $currencyStmt->fetch();
            $currencyStmt->close();
        }
    }

    // Validation
    if (!$transferCountry || !$trackingNumber || $beneficiaryId === 0 || $baseAmount <= 0 || $deliveryAmount <= 0) {
        $errors[] = 'الرجاء إدخال جميع الحقول المطلوبة.';
    }

    if ($deliveryCashBoxId <= 0) {
        $errors[] = 'الرجاء اختيار صندوق التسليم.';
    }

    if ($currencyId <= 0) {
        $errors[] = 'لم يتم العثور على عملة صالحة لصندوق التسليم.';
    }

    $beneficiary = $customerMgr->getCustomerById($beneficiaryId);
    if (!$beneficiary) {
        $errors[] = 'المستفيد غير موجود.';
    }

    // Check cash box availability
    if (!$cashBox) {
        $errors[] = 'صندوق التسليم غير موجود.';
    } elseif ($cashBox['current_balance'] < $deliveryAmount) {
        $errors[] = 'رصيد الصندوق غير كافي لمبلغ التسليم.';
    }

    // Calculate profit (simplified without exchange rates)
    $sendingAmount = $baseAmount;
    $receivingAmount = $deliveryAmount;
    $profit = $baseAmount - $deliveryAmount; // Profit = Base Amount - Delivery Amount

    if (!$errors) {
        try {
            $db = Database::getConnection();
            $db->begin_transaction();

            // Prepare transfer data
        $payload = [
                'transfer_type'        => 'واردة', // Default to incoming
                'sender_name'          => 'غير محدد', // Default value since sender info removed
                'sender_id_type'       => null,
                'sender_id_number'     => null,
                'sender_phone'         => null,
                'sender_address'       => null,
                'beneficiary_id'       => $beneficiaryId,
                'beneficiary_name'     => $beneficiary['full_name'],
                'beneficiary_country'  => $transferCountry,
                'beneficiary_bank'     => '',
                'beneficiary_account'  => '',
                'sending_currency_id'  => $currencyId, // Use cash box currency
                'sending_amount'       => $sendingAmount,
                'receiving_currency_id'=> $currencyId, // Use cash box currency
                'receiving_amount'     => $receivingAmount,
                'exchange_rate_used'   => 1.0, // Default to 1.0 as we're not using exchange rates
                'transfer_fee'         => 0,
                'profit'               => $profit,
                'delivery_method'      => $deliveryMethod,
                'status'               => 'معلقة',
                'tracking_number'      => $trackingNumber ?: null,
                'notes'                => $notes,
                'branch_id'            => $branchId,
                'created_by'           => $userId,
            ];

            // Create the transfer
            $newId = $transferMgr->addTransfer($payload);
            if (!$newId) {
                throw new Exception('فشل في إنشاء الحوالة');
            }

            // Deduct delivery amount from cash box
            $cashResult = $cashMgr->addCashMovementForTransfer(
                (int)$deliveryCashBoxId,
                'withdrawal',
                (float)$deliveryAmount,
                (int)$currencyId,
                (int)$newId,
                (int)$userId,
                "تسليم حوالة رقم: " . $newId
            );

            if (!$cashResult['success']) {
                throw new Exception('فشل في خصم مبلغ التسليم من الصندوق: ' . $cashResult['error']);
            }

            $db->commit();

            // تسجيل عملية إنشاء الحوالة
            ActivityHelper::logTransfer('create', $payload, $newId);

            set_flash('success', 'تم إنشاء الحوالة بنجاح وخصم مبلغ التسليم من الصندوق');
            redirect('transfers.php');
        } catch (Exception $e) {
            $db->rollback();
            $errors[] = 'خطأ في إنشاء الحوالة: ' . $e->getMessage();
            error_log("Error creating transfer: " . $e->getMessage());
        }
    }
}

// Calculate total profits
$totalProfits = 0;
$transferProfits = [];
try {
    $db = Database::getConnection();
    $profitQuery = "SELECT id, transaction_number, beneficiary_name, profit, base_amount, delivery_amount 
                   FROM transfers 
                   WHERE profit > 0 
                   ORDER BY created_at DESC";
    $result = $db->query($profitQuery);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $transferProfits[] = $row;
            $totalProfits += $row['profit'];
        }
    }
} catch (Exception $e) {
    error_log("Error calculating profits: " . $e->getMessage());
}

$csrf       = get_csrf_token();
$customers  = $customerMgr->getAllCustomers(['status' => 'active']);
$currencies = $currencyMgr->getAllCurrencies(true);
$cashBoxes  = $cashMgr->getAllCashBoxes($branchId);
$transfers  = $transferMgr->getRecentTransfers(20);

// Add currency information to transfers
foreach ($transfers as &$transfer) {
    foreach ($currencies as $currency) {
        if ($currency['id'] == $transfer['sending_currency_id']) {
            $transfer['sending_currency_code'] = $currency['code'];
            $transfer['sending_currency_symbol'] = $currency['symbol'];
        }
        if ($currency['id'] == $transfer['receiving_currency_id']) {
            $transfer['receiving_currency_code'] = $currency['code'];
            $transfer['receiving_currency_symbol'] = $currency['symbol'];
        }
    }
}

$pageTitle = 'الحوالات';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="main-content">
<div class="container-fluid p-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="page-title mb-1">
                                <i class="fas fa-paper-plane text-primary me-2"></i>
                                إدارة الحوالات
                            </h2>
                            <p class="text-muted mb-0">عرض وإدارة جميع الحوالات المالية</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-info" onclick="showProfitsReport()">
                                <i class="fas fa-chart-line me-2"></i>
                                تقرير الأرباح
                            </button>
                            <a href="add_transfer.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة حوالة جديدة
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Status Check -->
    <?php
    try {
        $db_check = Database::getConnection();
        $checkQuery = "SHOW COLUMNS FROM transfers LIKE 'delivery_status'";
        $result = $db_check->query($checkQuery);
        $deliveryColumnsExist = $result && $result->num_rows > 0;

        if (!$deliveryColumnsExist) {
            echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحديث مطلوب:</strong> قاعدة البيانات تحتاج تحديث لدعم حالة التسليم.
                    <a href="fix_database.php" class="btn btn-sm btn-warning ms-2">
                        <i class="fas fa-tools me-1"></i>
                        إصلاح الآن
                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                  </div>';
        }
    } catch (Exception $e) {
        // Ignore database check errors
    }
    ?>

    <?php foreach(get_flash() as $t=>$m): ?>
        <div class="alert alert-<?php echo $t; ?> alert-dismissible fade show" role="alert">
            <?php echo $m; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endforeach; ?>

    <?php foreach($errors as $e): ?>
        <div class="alert alert-danger"><?php echo $e; ?></div>
    <?php endforeach; ?>

    <?php if ($auth->hasPermission('transfers.create')): ?>
    <div class="card p-3 mb-4">
        <h5 class="mb-3">إنشاء حوالة جديدة</h5>
        <form method="post" id="transferForm">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
            <input type="hidden" name="action" value="add_transfer">
            <input type="hidden" name="beneficiary_id" id="selectedBeneficiaryId">
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">دولة الحوالة <span class="text-danger">*</span></label>
                    <input type="text" name="transfer_country" class="form-control" required 
                           placeholder="اسم الدولة" value="<?php echo htmlspecialchars($_POST['transfer_country'] ?? ''); ?>">
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">رقم التتبع <span class="text-danger">*</span></label>
                    <input type="text" name="tracking_number" class="form-control" required
                           placeholder="رقم التتبع" value="<?php echo htmlspecialchars($_POST['tracking_number'] ?? ''); ?>">
                </div>
            </div>

            <div class="row">
                <div class="col-md-10 mb-3">
                    <label class="form-label">المستفيد <span class="text-danger">*</span></label>
                    <input type="text" id="beneficiarySearch" class="form-control" 
                           placeholder="اكتب اسم المستفيد أو رقم الهوية للبحث..." required>
                    <div id="beneficiaryResults" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto;"></div>
                    <div id="selectedBeneficiary" class="mt-2 d-none">
                        <div class="alert alert-info">
                            <strong>المستفيد المحدد:</strong> <span id="selectedBeneficiaryName"></span>
            </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-success w-100" onclick="showAddBeneficiaryModal()">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">المبلغ الأساسي <span class="text-danger">*</span></label>
                    <input type="number" step="0.01" name="base_amount" class="form-control" required
                           placeholder="0.00" value="<?php echo htmlspecialchars($_POST['base_amount'] ?? ''); ?>"
                           onchange="calculateProfit()">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">مبلغ التسليم <span class="text-danger">*</span></label>
                    <input type="number" step="0.01" name="delivery_amount" class="form-control" required
                           placeholder="0.00" value="<?php echo htmlspecialchars($_POST['delivery_amount'] ?? ''); ?>"
                           onchange="calculateProfit()">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">صندوق التسليم <span class="text-danger">*</span></label>
                    <select name="delivery_cash_box_id" class="form-select" required>
                        <option value="">-- اختر الصندوق --</option>
                        <?php foreach($cashBoxes as $box): ?>
                            <option value="<?php echo $box['id']; ?>" 
                                    data-balance="<?php echo $box['current_balance']; ?>"
                                    data-currency="<?php echo $box['currency_code']; ?>">
                                <?php echo htmlspecialchars($box['name']); ?> 
                                (<?php echo number_format($box['current_balance'], 2); ?> <?php echo $box['currency_code']; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">الربح المتوقع</label>
                    <input type="text" id="calculatedProfit" class="form-control" readonly 
                           placeholder="0.00" style="background-color: #f8f9fa;">
            </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                <label class="form-label">طريقة التسليم</label>
                <select name="delivery_method" class="form-select">
                    <option value="نقدي">نقدي</option>
                    <option value="بنكي">بنكي</option>
                    <option value="شركة تحويل">شركة تحويل</option>
                </select>
            </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">الملاحظات</label>
                    <textarea name="notes" class="form-control" rows="1" placeholder="ملاحظات إضافية"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                </div>
            </div>

            <button type="submit" class="btn btn-success">
                <i class="fas fa-save me-2"></i>حفظ الحوالة
            </button>
        </form>
    </div>
    <?php endif; ?>

    <!-- Transfers Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0 text-dark fw-bold">
                                <i class="fas fa-list text-primary me-2"></i>
                                قائمة الحوالات
                            </h5>
                            <small class="text-muted">إجمالي الحوالات: <span class="badge bg-primary"><?php echo count($transfers); ?></span></small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshTransfers()">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="showProfitsModal()">
                                <i class="fas fa-chart-line me-1"></i>
                                الأرباح (<?php echo number_format($totalProfits, 2); ?>)
                            </button>
                            <?php if ($auth->hasPermission('transfers.export')): ?>
                                <button class="btn btn-sm btn-outline-info" onclick="exportTransfers()">
                                    <i class="fas fa-file-excel me-1"></i>
                                    تصدير
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0 transfers-table">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center" style="width: 80px;">
                                        <i class="fas fa-hashtag text-muted"></i>
                                    </th>
                                    <th style="width: 140px;">
                                        <i class="fas fa-globe text-muted me-1"></i>
                                        الدولة
                                    </th>
                                    <th style="width: 120px;" class="d-mobile-none">
                                        <i class="fas fa-barcode text-muted me-1"></i>
                                        رقم التتبع
                                    </th>
                                    <th style="width: 180px;">
                                        <i class="fas fa-user text-muted me-1"></i>
                                        المستفيد
                                    </th>
                                    <th style="width: 120px;" class="text-center">
                                        <i class="fas fa-money-bill text-muted me-1"></i>
                                        المبلغ الأساسي
                                    </th>
                                    <th style="width: 120px;" class="text-center d-tablet-none">
                                        <i class="fas fa-hand-holding-usd text-muted me-1"></i>
                                        مبلغ التسليم
                                    </th>
                                    <th style="width: 100px;" class="text-center d-mobile-none">
                                        <i class="fas fa-chart-line text-muted me-1"></i>
                                        الربح
                                    </th>
                                    <th style="width: 140px;" class="text-center">
                                        <i class="fas fa-info-circle text-muted me-1"></i>
                                        حالة الحوالة
                                    </th>
                                    <th style="width: 140px;" class="text-center d-mobile-none">
                                        <i class="fas fa-truck text-muted me-1"></i>
                                        حالة التسليم
                                    </th>
                                    <th style="width: 120px;" class="text-center d-tablet-none">
                                        <i class="fas fa-calendar text-muted me-1"></i>
                                        التاريخ
                                    </th>
                                    <th style="width: 180px;" class="text-center">
                                        <i class="fas fa-cogs text-muted me-1"></i>
                                        الإجراءات
                                    </th>
                                </tr>
                            </thead>
            <tbody>
                <?php foreach($transfers as $t): ?>
                    <tr class="transfer-row">
                        <td class="text-center">
                            <div class="transaction-badge">
                                <span class="badge bg-light text-dark fw-bold">
                                    #<?php echo htmlspecialchars($t['transaction_number']); ?>
                                </span>
                            </div>
                        </td>
                        <td>
                            <div class="country-info">
                                <i class="fas fa-flag text-primary me-2"></i>
                                <span class="fw-bold"><?php echo htmlspecialchars($t['beneficiary_country'] ?: 'غير محدد'); ?></span>
                            </div>
                        </td>
                        <td class="d-mobile-none">
                            <div class="tracking-info">
                                <span class="badge bg-secondary">
                                    <?php echo htmlspecialchars($t['tracking_number'] ?: 'غير محدد'); ?>
                                </span>
                            </div>
                        </td>
                        <td>
                            <div class="beneficiary-info">
                                <div class="fw-bold text-dark">
                                    <i class="fas fa-user text-muted me-1"></i>
                                    <?php echo htmlspecialchars($t['beneficiary_name']); ?>
                                </div>
                                <?php if (!empty($t['beneficiary_phone'])): ?>
                                <small class="text-muted">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($t['beneficiary_phone']); ?>
                                </small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="amount-info">
                                <div class="fw-bold text-success fs-6">
                                    <?php echo number_format($t['sending_amount'], 2); ?>
                                </div>
                                <small class="badge bg-success bg-opacity-10 text-success">
                                    <?php echo htmlspecialchars($t['sending_currency_code'] ?? 'USD'); ?>
                                </small>
                            </div>
                        </td>
                        <td class="text-center d-tablet-none">
                            <div class="amount-info">
                                <div class="fw-bold text-primary fs-6">
                                    <?php echo number_format($t['receiving_amount'], 2); ?>
                                </div>
                                <small class="badge bg-primary bg-opacity-10 text-primary">
                                    <?php echo htmlspecialchars($t['receiving_currency_code'] ?? 'USD'); ?>
                                </small>
                            </div>
                        </td>
                        <td class="text-center d-mobile-none">
                            <div class="profit-info">
                                <span class="badge bg-warning text-dark fw-bold">
                                    <i class="fas fa-chart-line me-1"></i>
                                    <?php echo number_format($t['profit'], 2); ?>
                                </span>
                            </div>
                        </td>
                        <td class="text-center">
                            <?php
                            $statusClass = '';
                            $statusIcon = '';
                            $currentStatus = $t['status'] ?? 'معلقة';

                            switch ($currentStatus) {
                                case 'معلقة':
                                    $statusClass = 'bg-warning text-dark';
                                    $statusIcon = 'fas fa-clock';
                                    break;
                                case 'مقبولة':
                                    $statusClass = 'bg-success';
                                    $statusIcon = 'fas fa-check';
                                    break;
                                case 'مرفوضة':
                                    $statusClass = 'bg-danger';
                                    $statusIcon = 'fas fa-times';
                                    break;
                                case 'مرسلة':
                                    $statusClass = 'bg-info';
                                    $statusIcon = 'fas fa-paper-plane';
                                    break;
                                case 'مستلمة':
                                    $statusClass = 'bg-primary';
                                    $statusIcon = 'fas fa-check-double';
                                    break;
                                case 'ملغاة':
                                    $statusClass = 'bg-secondary';
                                    $statusIcon = 'fas fa-ban';
                                    break;
                                case 'مكتملة':
                                    $statusClass = 'bg-primary';
                                    $statusIcon = 'fas fa-check-circle';
                                    break;
                                default:
                                    $statusClass = 'bg-light text-dark';
                                    $statusIcon = 'fas fa-question';
                                    $currentStatus = 'غير محدد';
                            }
                            ?>
                            <div class="status-badge">
                                <span class="badge <?php echo $statusClass; ?> px-3 py-2">
                                    <i class="<?php echo $statusIcon; ?> me-1"></i>
                                    <?php echo htmlspecialchars($currentStatus); ?>
                                </span>
                            </div>
                        </td>
                        <td class="d-mobile-none">
                            <?php
                            // Check if delivery_status column exists and has value
                            $deliveryStatus = 'غير مستلمة'; // Default value
                            if (isset($t['delivery_status']) && !empty($t['delivery_status'])) {
                                $deliveryStatus = $t['delivery_status'];
                            }

                            $deliveryClass = '';
                            $deliveryIcon = '';

                            switch ($deliveryStatus) {
                                case 'مستلمة':
                                    $deliveryClass = 'bg-success';
                                    $deliveryIcon = 'fas fa-check-circle';
                                    break;
                                case 'في الطريق':
                                    $deliveryClass = 'bg-info';
                                    $deliveryIcon = 'fas fa-truck';
                                    break;
                                case 'وصلت للوجهة':
                                    $deliveryClass = 'bg-warning text-dark';
                                    $deliveryIcon = 'fas fa-map-marker-alt';
                                    break;
                                case 'مرتجعة':
                                    $deliveryClass = 'bg-danger';
                                    $deliveryIcon = 'fas fa-undo';
                                    break;
                                case 'غير مستلمة':
                                default:
                                    $deliveryClass = 'bg-secondary';
                                    $deliveryIcon = 'fas fa-hourglass-half';
                                    $deliveryStatus = 'غير مستلمة';
                            }
                            ?>
                            <span class="badge <?php echo $deliveryClass; ?>">
                                <i class="<?php echo $deliveryIcon; ?> me-1"></i>
                                <?php echo htmlspecialchars($deliveryStatus); ?>
                            </span>
                        </td>
                        <td class="text-center d-tablet-none">
                            <div class="date-info">
                                <div class="fw-bold text-dark">
                                    <i class="fas fa-calendar text-muted me-1"></i>
                                    <?php echo date('Y-m-d', strtotime($t['created_at'])); ?>
                                </div>
                                <small class="text-muted">
                                    <i class="fas fa-clock text-muted me-1"></i>
                                    <?php echo date('H:i', strtotime($t['created_at'])); ?>
                                </small>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="action-buttons">
                                <div class="btn-group" role="group">
                                    <?php if ($auth->hasPermission('transfers.view')): ?>
                                        <button class="btn btn-outline-info btn-sm"
                                                onclick="viewTransferDetails(<?php echo $t['id']; ?>)"
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    <?php endif; ?>

                                    <?php if ($auth->hasPermission('transfers.edit')): ?>
                                        <button class="btn btn-outline-primary btn-sm"
                                                data-bs-toggle="modal"
                                                data-bs-target="#statusModal"
                                                data-transfer-id="<?php echo $t['id']; ?>"
                                                data-current-status="<?php echo htmlspecialchars($t['status']); ?>"
                                                data-transfer-number="<?php echo htmlspecialchars($t['transaction_number']); ?>"
                                                title="تغيير حالة الحوالة">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <button class="btn btn-outline-success btn-sm"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deliveryModal"
                                                data-transfer-id="<?php echo $t['id']; ?>"
                                                data-current-delivery="<?php echo htmlspecialchars($t['delivery_status'] ?? 'غير مستلمة'); ?>"
                                                data-transfer-number="<?php echo htmlspecialchars($t['transaction_number']); ?>"
                                                title="تحديث حالة التسليم">
                                            <i class="fas fa-truck"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                <?php if(!$transfers): ?>
                    <tr>
                        <td colspan="11" class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد حوالات</h5>
                                <p class="text-muted mb-3">لم يتم العثور على أي حوالات مالية</p>
                                <a href="add_transfer.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة حوالة جديدة
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade transfer-modal" id="statusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content status-update-form">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تحديث حالة الحوالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="update_status">
                <input type="hidden" name="transfer_id" id="statusTransferId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">

                <div class="mb-3">
                    <label class="form-label fw-bold">رقم الحوالة:</label>
                    <span id="statusTransferNumber" class="text-primary"></span>
                </div>

                <div class="mb-3">
                    <label class="form-label">الحالة الحالية:</label>
                    <span id="currentStatus" class="badge bg-secondary"></span>
                </div>

                <div class="mb-3">
                    <label for="newStatus" class="form-label">
                        <i class="fas fa-flag me-1"></i>
                        الحالة الجديدة <span class="text-danger">*</span>
                    </label>
                    <select name="status" id="newStatus" class="form-select" required>
                        <option value="">اختر الحالة الجديدة</option>
                        <option value="معلقة">
                            <i class="fas fa-clock"></i> معلقة
                        </option>
                        <option value="مقبولة">
                            <i class="fas fa-check"></i> مقبولة
                        </option>
                        <option value="مرفوضة">
                            <i class="fas fa-times"></i> مرفوضة
                        </option>
                        <option value="مرسلة">
                            <i class="fas fa-paper-plane"></i> مرسلة
                        </option>
                        <option value="مستلمة">
                            <i class="fas fa-check-double"></i> مستلمة
                        </option>
                        <option value="ملغاة">
                            <i class="fas fa-ban"></i> ملغاة
                        </option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="statusNotes" class="form-label">
                        <i class="fas fa-sticky-note me-1"></i>
                        ملاحظات التحديث
                    </label>
                    <textarea name="notes" id="statusNotes" class="form-control" rows="3"
                              placeholder="أدخل ملاحظات حول تغيير الحالة (اختياري)"></textarea>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> تغيير الحالة إلى "مستلمة" سيؤدي إلى تحديث تاريخ الإكمال تلقائياً.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    تحديث الحالة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delivery Status Modal -->
<div class="modal fade transfer-modal" id="deliveryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content delivery-update-form">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-truck me-2"></i>
                    تحديث حالة التسليم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="update_delivery">
                <input type="hidden" name="transfer_id" id="deliveryTransferId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">

                <div class="mb-3">
                    <label class="form-label fw-bold">رقم الحوالة:</label>
                    <span id="deliveryTransferNumber" class="text-primary"></span>
                </div>

                <div class="mb-3">
                    <label class="form-label">حالة التسليم الحالية:</label>
                    <span id="currentDelivery" class="badge bg-secondary"></span>
                </div>

                <div class="mb-3">
                    <label for="deliveryStatus" class="form-label">
                        <i class="fas fa-shipping-fast me-1"></i>
                        حالة التسليم الجديدة <span class="text-danger">*</span>
                    </label>
                    <select name="delivery_status" id="deliveryStatus" class="form-select" required>
                        <option value="">اختر حالة التسليم</option>
                        <option value="غير مستلمة">
                            <i class="fas fa-hourglass-half"></i> غير مستلمة
                        </option>
                        <option value="في الطريق">
                            <i class="fas fa-truck"></i> في الطريق
                        </option>
                        <option value="وصلت للوجهة">
                            <i class="fas fa-map-marker-alt"></i> وصلت للوجهة
                        </option>
                        <option value="مستلمة">
                            <i class="fas fa-check-circle"></i> مستلمة
                        </option>
                        <option value="مرتجعة">
                            <i class="fas fa-undo"></i> مرتجعة
                        </option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="deliveryNotes" class="form-label">
                        <i class="fas fa-clipboard me-1"></i>
                        ملاحظات التسليم
                    </label>
                    <textarea name="delivery_notes" id="deliveryNotes" class="form-control" rows="3"
                              placeholder="أدخل ملاحظات حول حالة التسليم (اختياري)"></textarea>
                </div>

                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>ملاحظة:</strong> تغيير حالة التسليم إلى "مستلمة" سيؤدي إلى تحديث حالة الحوالة إلى "مستلمة" تلقائياً.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-save me-1"></i>
                    تحديث التسليم
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Beneficiary Modal -->
<div class="modal fade" id="addBeneficiaryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستفيد جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addBeneficiaryForm">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                        <input type="text" name="full_name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهوية</label>
                        <input type="text" name="id_number" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الهاتف</label>
                        <input type="text" name="phone" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الجنسية</label>
                        <input type="text" name="nationality" class="form-control">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="addBeneficiary()">
                    <i class="fas fa-save me-1"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Profits Modal -->
<div class="modal fade" id="profitsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line me-2"></i>
                    تقرير أرباح الحوالات
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>إجمالي الأرباح:</strong> 
                    <span class="fw-bold text-success"><?php echo number_format($totalProfits, 2); ?></span>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-mobile-stack">
                        <thead>
                            <tr>
                                <th>رقم الحوالة</th>
                                <th>اسم المستفيد</th>
                                <th>المبلغ الأساسي</th>
                                <th>مبلغ التسليم</th>
                                <th>الربح</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($transferProfits as $tp): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($tp['transaction_number']); ?></td>
                                <td><?php echo htmlspecialchars($tp['beneficiary_name']); ?></td>
                                <td class="text-success"><?php echo number_format($tp['sending_amount'] ?? 0, 2); ?></td>
                                <td class="text-primary"><?php echo number_format($tp['receiving_amount'] ?? 0, 2); ?></td>
                                <td class="text-warning fw-bold"><?php echo number_format($tp['profit'], 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if(empty($transferProfits)): ?>
                            <tr>
                                <td colspan="5" class="text-center">لا توجد حوالات بأرباح</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الحوالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="transferDetailsContent">
                <!-- Content will be loaded via AJAX -->
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Status Modal Event Handlers
    const statusModal = document.getElementById('statusModal');
    if (statusModal) {
        statusModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const transferId = button.getAttribute('data-transfer-id');
            const currentStatus = button.getAttribute('data-current-status');
            const transferNumber = button.getAttribute('data-transfer-number');

            // Update modal content
            document.getElementById('statusTransferId').value = transferId;
            document.getElementById('statusTransferNumber').textContent = transferNumber;
            document.getElementById('currentStatus').textContent = currentStatus;

            // Set current status badge class
            const statusBadge = document.getElementById('currentStatus');
            statusBadge.className = 'badge ' + getStatusClass(currentStatus);

            // Clear previous selections
            document.getElementById('newStatus').value = '';
            document.getElementById('statusNotes').value = '';
        });
    }

    // Delivery Modal Event Handlers
    const deliveryModal = document.getElementById('deliveryModal');
    if (deliveryModal) {
        deliveryModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const transferId = button.getAttribute('data-transfer-id');
            const currentDelivery = button.getAttribute('data-current-delivery');
            const transferNumber = button.getAttribute('data-transfer-number');

            // Update modal content
            document.getElementById('deliveryTransferId').value = transferId;
            document.getElementById('deliveryTransferNumber').textContent = transferNumber;
            document.getElementById('currentDelivery').textContent = currentDelivery;

            // Set current delivery badge class
            const deliveryBadge = document.getElementById('currentDelivery');
            deliveryBadge.className = 'badge ' + getDeliveryClass(currentDelivery);

            // Clear previous selections
            document.getElementById('deliveryStatus').value = '';
            document.getElementById('deliveryNotes').value = '';
        });
    }

    // Form submission confirmations
    const statusForm = statusModal?.querySelector('form');
    if (statusForm) {
        statusForm.addEventListener('submit', function(e) {
            const newStatus = document.getElementById('newStatus').value;
            if (!confirm(`هل أنت متأكد من تغيير حالة الحوالة إلى "${newStatus}"؟`)) {
                e.preventDefault();
            }
        });
    }

    const deliveryForm = deliveryModal?.querySelector('form');
    if (deliveryForm) {
        deliveryForm.addEventListener('submit', function(e) {
            const deliveryStatus = document.getElementById('deliveryStatus').value;
            if (!confirm(`هل أنت متأكد من تحديث حالة التسليم إلى "${deliveryStatus}"؟`)) {
                e.preventDefault();
            }
        });
    }

    // Beneficiary Search Functionality
    const beneficiarySearch = document.getElementById('beneficiarySearch');
    const beneficiaryResults = document.getElementById('beneficiaryResults');
    const selectedBeneficiary = document.getElementById('selectedBeneficiary');
    const selectedBeneficiaryName = document.getElementById('selectedBeneficiaryName');
    const selectedBeneficiaryId = document.getElementById('selectedBeneficiaryId');

    if (beneficiarySearch) {
        beneficiarySearch.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length < 2) {
                beneficiaryResults.style.display = 'none';
                return;
            }

            // Show loading
            beneficiaryResults.innerHTML = '<div class="dropdown-item"><i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...</div>';
            beneficiaryResults.style.display = 'block';

            // Search for beneficiaries
            // Use window.location.origin for more robustness
            const baseUrl = window.location.origin + '/TrustPlus';
            fetch(baseUrl + '/dashboard/ajax/search_beneficiaries.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `search=${encodeURIComponent(searchTerm)}`
            })
            .then(response => response.json())
            .then(data => {
                console.log('Search response:', data);
                if (data.success && data.customers) {
                    beneficiaryResults.innerHTML = '';
                    data.customers.forEach(customer => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.style.cursor = 'pointer';
                        item.innerHTML = `
                            <strong>${customer.full_name}</strong>
                            <br><small class="text-muted">${customer.id_number || 'لا يوجد رقم هوية'} | ${customer.phone || 'لا يوجد هاتف'}</small>
                        `;
                        item.addEventListener('click', function() {
                            selectBeneficiary(customer);
                        });
                        beneficiaryResults.appendChild(item);
                    });
                    
                    if (data.customers.length === 0) {
                        beneficiaryResults.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                    }
                } else {
                    console.error('Search error:', data.error || 'خطأ في البحث');
                    beneficiaryResults.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                }
            })
            .catch(error => {
                console.error('Search fetch error:', error);
                beneficiaryResults.innerHTML = '<div class="dropdown-item text-danger">خطأ في البحث</div>';
            });
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!beneficiarySearch.contains(e.target) && !beneficiaryResults.contains(e.target)) {
                beneficiaryResults.style.display = 'none';
            }
        });
    }

    // Transfer form validation
    const transferForm = document.getElementById('transferForm');
    if (transferForm) {
        transferForm.addEventListener('submit', function(e) {
            const beneficiaryId = document.getElementById('selectedBeneficiaryId').value;
            if (!beneficiaryId) {
                e.preventDefault();
                alert('الرجاء اختيار المستفيد');
                return;
            }

            const baseAmount = parseFloat(document.querySelector('[name="base_amount"]').value) || 0;
            const deliveryAmount = parseFloat(document.querySelector('[name="delivery_amount"]').value) || 0;
            const cashBoxId = document.querySelector('[name="delivery_cash_box_id"]').value;

            if (baseAmount <= 0 || deliveryAmount <= 0) {
                e.preventDefault();
                alert('الرجاء إدخال المبالغ بشكل صحيح');
                return;
            }

            if (!cashBoxId) {
                e.preventDefault();
                alert('الرجاء اختيار صندوق التسليم');
                return;
            }

            // Check cash box balance
            const selectedOption = document.querySelector(`[name="delivery_cash_box_id"] option[value="${cashBoxId}"]`);
            if (selectedOption) {
                const balance = parseFloat(selectedOption.getAttribute('data-balance')) || 0;
                if (balance < deliveryAmount) {
                    e.preventDefault();
                    alert('رصيد الصندوق غير كافي لمبلغ التسليم');
                    return;
                }
            }
        });
    }
});

// Helper function to get status badge class
function getStatusClass(status) {
    switch (status) {
        case 'معلقة': return 'bg-warning text-dark';
        case 'مقبولة': return 'bg-success';
        case 'مرفوضة': return 'bg-danger';
        case 'مرسلة': return 'bg-info';
        case 'مستلمة': return 'bg-primary';
        case 'ملغاة': return 'bg-secondary';
        default: return 'bg-light text-dark';
    }
}

// Helper function to get delivery badge class
function getDeliveryClass(status) {
    switch (status) {
        case 'مستلمة': return 'bg-success';
        case 'في الطريق': return 'bg-info';
        case 'وصلت للوجهة': return 'bg-warning text-dark';
        case 'مرتجعة': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

// Select Beneficiary Function
function selectBeneficiary(customer) {
    document.getElementById('selectedBeneficiaryId').value = customer.id;
    document.getElementById('selectedBeneficiaryName').textContent = customer.full_name;
    document.getElementById('selectedBeneficiary').classList.remove('d-none');
    document.getElementById('beneficiarySearch').value = customer.full_name;
    document.getElementById('beneficiaryResults').style.display = 'none';
}

// Show Add Beneficiary Modal
function showAddBeneficiaryModal() {
    const modal = new bootstrap.Modal(document.getElementById('addBeneficiaryModal'));
    modal.show();
}

// Add Beneficiary Function
function addBeneficiary() {
    const form = document.getElementById('addBeneficiaryForm');
    const fullName = form.full_name.value.trim();
    const idNumber = form.id_number.value.trim();
    const phone = form.phone.value.trim();
    const nationality = form.nationality.value.trim();

    if (!fullName) {
        alert('الرجاء إدخال الاسم الكامل للمستفيد.');
        return;
    }

    const submitBtn = document.querySelector('#addBeneficiaryModal .btn-success');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';

    // Use window.location.origin for more robustness
    const baseUrl = window.location.origin + '/TrustPlus';
    fetch(baseUrl + '/dashboard/ajax/add_beneficiary.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `full_name=${encodeURIComponent(fullName)}&id_number=${encodeURIComponent(idNumber)}&phone=${encodeURIComponent(phone)}&nationality=${encodeURIComponent(nationality)}&csrf_token=${encodeURIComponent(document.querySelector('[name="csrf_token"]').value)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Select the newly added beneficiary
            selectBeneficiary({
                id: data.id,
                full_name: data.full_name,
                id_number: data.id_number,
                phone: data.phone
            });
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('addBeneficiaryModal'));
            modal.hide();
            
            alert('تم إضافة المستفيد بنجاح!');
        } else {
            alert('فشل في إضافة المستفيد: ' + (data.error || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        alert('خطأ في الاتصال بالخادم أثناء إضافة المستفيد.');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-1"></i>حفظ';
    });
}

// Show Profits Modal
function showProfitsModal() {
    const modal = new bootstrap.Modal(document.getElementById('profitsModal'));
    modal.show();
}

// Show Profits Report Function (AJAX)
function showProfitsReport() {
    showProfitsModal();
}

// Calculate Profit Function
function calculateProfit() {
    const baseAmount = parseFloat(document.querySelector('[name="base_amount"]').value) || 0;
    const deliveryAmount = parseFloat(document.querySelector('[name="delivery_amount"]').value) || 0;
    const profit = baseAmount - deliveryAmount;
    document.getElementById('calculatedProfit').value = profit.toFixed(2);
}

// View transfer details function
function viewTransferDetails(transferId) {
    const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
    const content = document.getElementById('transferDetailsContent');

    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري تحميل تفاصيل الحوالة...</div>
        </div>
    `;

    modal.show();

    // Load transfer details via AJAX
    // Use window.location.origin for more robustness
    const baseUrl = window.location.origin + '/TrustPlus';
    fetch(`${baseUrl}/dashboard/ajax/get_transfer_details.php?id=${transferId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${data.error || 'خطأ في تحميل التفاصيل'}
                    </div>
                `;
            }
        })
        .catch(error => {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    خطأ في الاتصال بالخادم
                </div>
            `;
        });
}

// Refresh transfers function
function refreshTransfers() {
    location.reload();
}

// Export transfers function
function exportTransfers() {
    window.open('export_transfers.php', '_blank');
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if no modals are open
    if (!document.querySelector('.modal.show')) {
        const currentTime = new Date().toLocaleTimeString();
        console.log('Auto-refresh at:', currentTime);
        // You can implement a silent AJAX refresh here instead of full page reload
        // refreshTransfersAjax();
    }
}, 30000);

// Show loading state on form submissions
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري المعالجة...';

                // Re-enable after 10 seconds as fallback
                setTimeout(function() {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 10000);
            }
        });
    });
});
</script>

</div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
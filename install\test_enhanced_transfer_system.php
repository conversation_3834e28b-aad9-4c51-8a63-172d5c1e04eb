<?php
/**
 * Test Enhanced Transfer System
 * اختبار نظام الحوالات المحسن
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';

echo "<h2>اختبار نظام الحوالات المحسن</h2>\n";

try {
    $db = new Database();
    $enhancedManager = new EnhancedTransferManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الجداول الجديدة</h3>\n";
    
    $newTables = [
        'transfer_activity_log' => 'سجل أنشطة الحوالات',
        'transfer_delivery_history' => 'تاريخ التسليم'
    ];
    
    foreach ($newTables as $table => $description) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ جدول $description موجود</p>\n";
            
            // Get record count
            $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
            if ($countResult) {
                $count = $countResult->fetch_assoc()['count'];
                echo "<p>&nbsp;&nbsp;&nbsp;📊 عدد السجلات: $count</p>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ جدول $description غير موجود</p>\n";
        }
    }
    
    echo "<h3>2. اختبار تسجيل الأنشطة</h3>\n";
    
    // Get a test transfer
    $transferResult = $conn->query("SELECT id FROM transfers LIMIT 1");
    if ($transferResult && $transferResult->num_rows > 0) {
        $transfer = $transferResult->fetch_assoc();
        $transferId = $transfer['id'];
        
        // Test activity logging
        $testActivities = [
            [
                'type' => 'viewed',
                'description' => 'تم عرض تفاصيل الحوالة من صفحة الاختبار',
                'old_value' => null,
                'new_value' => ['view_source' => 'test_page', 'timestamp' => date('Y-m-d H:i:s')]
            ],
            [
                'type' => 'notes_added',
                'description' => 'تم إضافة ملاحظة اختبار',
                'old_value' => ['notes' => ''],
                'new_value' => ['notes' => 'ملاحظة اختبار من النظام المحسن']
            ],
            [
                'type' => 'system_update',
                'description' => 'تحديث تلقائي من النظام',
                'old_value' => null,
                'new_value' => ['system_version' => '2.0', 'update_type' => 'enhancement']
            ]
        ];
        
        foreach ($testActivities as $activity) {
            $result = $enhancedManager->logActivity(
                $transferId,
                $activity['type'],
                $activity['description'],
                $activity['old_value'],
                $activity['new_value'],
                1, // Test user ID
                ['location' => 'صفحة الاختبار']
            );
            
            if ($result) {
                echo "<p style='color: green;'>✓ تم تسجيل نشاط: {$activity['description']}</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في تسجيل نشاط: {$activity['description']}</p>\n";
            }
        }
        
        echo "<h3>3. اختبار تحديث الحالة مع التسجيل</h3>\n";
        
        // Test status update with logging
        $statusResult = $enhancedManager->updateStatusWithLogging(
            $transferId,
            'مرسلة',
            'تم إرسال الحوالة للاختبار',
            1,
            ['location' => 'صفحة الاختبار المحسن']
        );
        
        if ($statusResult['success']) {
            echo "<p style='color: green;'>✓ تم تحديث الحالة مع التسجيل: {$statusResult['message']}</p>\n";
            echo "<p>&nbsp;&nbsp;&nbsp;📝 من: {$statusResult['old_status']} إلى: {$statusResult['new_status']}</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في تحديث الحالة: {$statusResult['error']}</p>\n";
        }
        
        echo "<h3>4. اختبار تحديث حالة التسليم</h3>\n";
        
        // Test delivery status update
        $deliveryResult = $enhancedManager->updateDeliveryStatus(
            $transferId,
            'in_transit',
            [
                'delivery_agent' => 'أحمد محمد - سائق التوصيل',
                'delivery_location' => 'الرياض - حي النخيل',
                'delivery_notes' => 'الحوالة في الطريق إلى المستفيد',
                'delivery_date' => date('Y-m-d H:i:s')
            ],
            1
        );
        
        if ($deliveryResult['success']) {
            echo "<p style='color: green;'>✓ تم تحديث حالة التسليم: {$deliveryResult['message']}</p>\n";
            echo "<p>&nbsp;&nbsp;&nbsp;🚚 الحالة: {$deliveryResult['delivery_status']}</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في تحديث حالة التسليم: {$deliveryResult['error']}</p>\n";
        }
        
        echo "<h3>5. اختبار استرجاع سجل الأنشطة</h3>\n";
        
        // Test activity log retrieval
        $activityLog = $enhancedManager->getTransferActivityLog($transferId, 10);
        
        if (!empty($activityLog)) {
            echo "<p style='color: green;'>✓ تم استرجاع سجل الأنشطة: " . count($activityLog) . " نشاط</p>\n";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>النوع</th><th>الوصف</th><th>المستخدم</th><th>التاريخ</th><th>IP</th>\n";
            echo "</tr>\n";
            
            foreach (array_slice($activityLog, 0, 5) as $activity) {
                echo "<tr>\n";
                echo "<td>{$activity['activity_type']}</td>\n";
                echo "<td>" . htmlspecialchars(substr($activity['description'], 0, 50)) . "...</td>\n";
                echo "<td>" . htmlspecialchars($activity['user_name'] ?? 'غير محدد') . "</td>\n";
                echo "<td>" . date('Y-m-d H:i', strtotime($activity['created_at'])) . "</td>\n";
                echo "<td>{$activity['ip_address']}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p style='color: red;'>✗ لم يتم العثور على أنشطة</p>\n";
        }
        
        echo "<h3>6. اختبار استرجاع تاريخ التسليم</h3>\n";
        
        // Test delivery history retrieval
        $deliveryHistory = $enhancedManager->getDeliveryHistory($transferId);
        
        if (!empty($deliveryHistory)) {
            echo "<p style='color: green;'>✓ تم استرجاع تاريخ التسليم: " . count($deliveryHistory) . " حدث</p>\n";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>الحالة</th><th>الملاحظات</th><th>المسؤول</th><th>التاريخ</th>\n";
            echo "</tr>\n";
            
            foreach ($deliveryHistory as $delivery) {
                echo "<tr>\n";
                echo "<td>{$delivery['delivery_status']}</td>\n";
                echo "<td>" . htmlspecialchars($delivery['delivery_notes'] ?? 'لا توجد') . "</td>\n";
                echo "<td>" . htmlspecialchars($delivery['created_by_name'] ?? 'غير محدد') . "</td>\n";
                echo "<td>" . date('Y-m-d H:i', strtotime($delivery['created_at'])) . "</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p style='color: orange;'>⚠ لا يوجد تاريخ تسليم</p>\n";
        }
        
        echo "<h3>7. اختبار تعيين رقم التتبع مع التسجيل</h3>\n";
        
        // Test tracking number assignment with logging
        $trackingResult = $enhancedManager->assignTrackingNumberWithLogging($transferId, 1);
        
        if ($trackingResult['success']) {
            echo "<p style='color: green;'>✓ تم تعيين رقم التتبع مع التسجيل: {$trackingResult['tracking_number']}</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ {$trackingResult['error']}</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ لا توجد حوالات للاختبار</p>\n";
    }
    
    echo "<h3>8. إحصائيات النظام المحسن</h3>\n";
    
    // Get system statistics
    $stats = [];
    
    $tables = [
        'transfers' => 'الحوالات',
        'transfer_activity_log' => 'سجل الأنشطة',
        'transfer_delivery_history' => 'تاريخ التسليم',
        'transfer_status_history' => 'تاريخ الحالات'
    ];
    
    foreach ($tables as $table => $description) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            $stats[$table] = $count;
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h4>📊 إحصائيات النظام</h4>\n";
    echo "<ul>\n";
    foreach ($tables as $table => $description) {
        echo "<li><strong>$description:</strong> " . ($stats[$table] ?? 0) . " سجل</li>\n";
    }
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>9. اختبار الأداء</h3>\n";
    
    // Performance test
    $startTime = microtime(true);
    
    for ($i = 0; $i < 10; $i++) {
        $enhancedManager->logActivity(
            $transferId ?? 1,
            'system_update',
            "اختبار الأداء - التكرار $i",
            null,
            ['iteration' => $i, 'timestamp' => microtime(true)],
            1
        );
    }
    
    $endTime = microtime(true);
    $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
    
    echo "<p style='color: green;'>✓ تم تسجيل 10 أنشطة في " . number_format($executionTime, 2) . " ميلي ثانية</p>\n";
    echo "<p>&nbsp;&nbsp;&nbsp;⚡ متوسط الوقت لكل نشاط: " . number_format($executionTime / 10, 2) . " ميلي ثانية</p>\n";
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى اختبار النظام المحسن!</h3>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 النتيجة النهائية: النظام المحسن يعمل بكفاءة عالية!</h4>\n";
    echo "<p style='margin: 0; color: #155724;'>جميع الميزات الجديدة تعمل بشكل مثالي:</p>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li>✅ تسجيل شامل لجميع الأنشطة</li>\n";
    echo "<li>✅ تتبع تفصيلي لحالات التسليم</li>\n";
    echo "<li>✅ حفظ القيم القديمة والجديدة</li>\n";
    echo "<li>✅ تسجيل معلومات المستخدم والجهاز</li>\n";
    echo "<li>✅ أداء سريع ومحسن</li>\n";
    echo "<li>✅ واجهة محسنة لعرض التاريخ</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4>🔗 اختبار الصفحات المحدثة</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/all_transfers.php' target='_blank' style='color: #007bff;'>📋 صفحة جميع الحوالات المحدثة</a></li>\n";
    echo "<li><a href='../dashboard/transfer_details.php?id=" . ($transferId ?? 1) . "' target='_blank' style='color: #007bff;'>📄 صفحة تفاصيل الحوالة المحسنة</a></li>\n";
    echo "<li><a href='../dashboard/transfers.php' target='_blank' style='color: #007bff;'>➕ إضافة حوالة جديدة</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

<?php
require_once __DIR__ . '/database.php';

class CustomerManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Fetch list of customers with optional filters/search.
     * @param array $filters
     * @param string $orderBy
     * @return array<int, array<string,mixed>>
     */
    public function getAllCustomers(array $filters = [], string $orderBy = 'created_at DESC'): array
    {
        $where  = [];
        $params = [];
        $types  = '';

        // Search term across name, id_number, phone
        if (!empty($filters['search'])) {
            $term = '%' . $filters['search'] . '%';
            $where[] = '(c.full_name LIKE ? OR c.id_number LIKE ? OR c.phone LIKE ?)';
            $params  = array_merge($params, [$term, $term, $term]);
            $types  .= 'sss';
        }
        if (!empty($filters['status'])) {
            $where[] = 'c.status = ?';
            $params[] = $filters['status'];
            $types   .= 's';
        }
        if (!empty($filters['risk_level'])) {
            $where[] = 'c.risk_level = ?';
            $params[] = $filters['risk_level'];
            $types   .= 's';
        }

        $sql = "SELECT c.*, u.full_name AS created_by_name
                FROM customers c
                LEFT JOIN users u ON u.id = c.created_by";
        if ($where) {
            $sql .= ' WHERE ' . implode(' AND ', $where);
        }
        $sql .= ' ORDER BY ' . $orderBy;

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        if ($params) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result    = $stmt->get_result();
        $customers = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $customers;
    }

    /**
     * Fetch a single customer by ID.
     */
    public function getCustomerById(int $id): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM customers WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $res   = $stmt->get_result();
        $cust  = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $cust;
    }

    /**
     * Check if ID number already exists (excluding given ID).
     */
    public function idNumberExists(string $idNumber, int $excludeId = null): bool
    {
        if ($idNumber === '') return false; // allow empty/NULL duplicates
        $sql = 'SELECT COUNT(*) FROM customers WHERE id_number = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($excludeId !== null) {
            $stmt->bind_param('si', $idNumber, $excludeId);
        } else {
            $stmt->bind_param('s', $idNumber);
        }
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    /**
     * Add new customer.
     * @param array $data
     * @return int|false
     */
    public function addCustomer(array $data)
    {
        $sql = 'INSERT INTO customers (full_name, id_number, id_type, nationality, birth_date, phone, email, address, occupation, risk_level, status, kyc_status, created_by, created_at, updated_at)
                VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?, NOW(), NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $stmt->bind_param(
            'sssssssssssis',
            $data['full_name'],
            $data['id_number'],
            $data['id_type'],
            $data['nationality'],
            $data['birth_date'],
            $data['phone'],
            $data['email'],
            $data['address'],
            $data['occupation'],
            $data['risk_level'],
            $data['status'],
            $data['kyc_status'],
            $data['created_by']
        );
        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }

    /** Update customer */
    public function updateCustomer(int $id, array $data): bool
    {
        if (empty($data)) return false;
        $fields = [];
        $params = [];
        $types  = '';
        foreach ($data as $k => $v) {
            $fields[] = "$k = ?";
            $params[] = $v;
            $types   .= 's';
        }
        $sql = 'UPDATE customers SET ' . implode(',', $fields) . ', updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $types .= 'i';
        $params[] = $id;
        $stmt->bind_param($types, ...$params);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /** Delete customer (documents cascade) */
    public function deleteCustomer(int $id): bool
    {
        // delete notes & compliance checks explicit (cascade on customer_id will handle but safeguard)
        $this->db->query("DELETE FROM customer_notes WHERE customer_id = {$id}");
        $this->db->query("DELETE FROM compliance_checks WHERE customer_id = {$id}");
        $stmt = $this->db->prepare('DELETE FROM customers WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $id);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /** Document list */
    public function getCustomerDocuments(int $cid): array
    {
        $sql = "SELECT d.*, u.full_name AS verifier_name FROM documents d LEFT JOIN users u ON u.id = d.verified_by WHERE d.customer_id = ? ORDER BY d.uploaded_at DESC";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->bind_param('i', $cid);
        $stmt->execute();
        $res = $stmt->get_result();
        $docs = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $docs;
    }

    public function getDocumentById(int $docId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM documents WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $docId);
        $stmt->execute();
        $res = $stmt->get_result();
        $doc = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $doc;
    }

    /** Add document */
    public function addDocument(int $custId, array $processed, string $type, int $userId)
    {
        if (!$processed['success']) return false;
        // ensure customer-specific dir exists and move already placed file if not inside customerDir
        $custDir = get_upload_path((string)$custId);
        $destRel = (string)$custId . '/' . basename($processed['path']);
        $destAbs = get_upload_path($custId) . '/' . basename($processed['path']);
        // move file into customer directory if not already
        $srcAbs = __DIR__ . '/../' . $processed['path'];
        if (!file_exists($destAbs)) {
            rename($srcAbs, $destAbs);
        }
        $relPathFull = 'uploads/documents/' . $destRel;
        $stmt = $this->db->prepare('INSERT INTO documents (customer_id, type, file_path, uploaded_at) VALUES (?,?,?, NOW())');
        if (!$stmt) return false;
        $stmt->bind_param('iss', $custId, $type, $relPathFull);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok ? $this->db->insert_id : false;
    }

    public function deleteDocument(int $docId): bool
    {
        $doc = $this->getDocumentById($docId);
        if (!$doc) return false;
        // remove file
        $filePath = __DIR__ . '/../' . $doc['file_path'];
        if (file_exists($filePath)) {
            unlink($filePath);
        }
        $stmt = $this->db->prepare('DELETE FROM documents WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $docId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function updateDocumentStatus(int $docId, string $status, int $verifierId): bool
    {
        $stmt = $this->db->prepare('UPDATE documents SET status = ?, verified_by = ?, verified_at = NOW() WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('sii', $status, $verifierId, $docId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function updateCustomerKycStatus(int $custId, string $status): bool
    {
        $stmt = $this->db->prepare('UPDATE customers SET kyc_status = ?, updated_at = NOW() WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('si', $status, $custId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function updateCustomerRiskLevel(int $custId, string $level): bool
    {
        $stmt = $this->db->prepare('UPDATE customers SET risk_level = ?, updated_at = NOW() WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('si', $level, $custId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function addCustomerNote(int $custId, string $note, int $userId): bool
    {
        $stmt = $this->db->prepare('INSERT INTO customer_notes (customer_id, note, created_by) VALUES (?,?,?)');
        if (!$stmt) return false;
        $stmt->bind_param('isi', $custId, $note, $userId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function getCustomerNotes(int $custId): array
    {
        $sql = "SELECT n.*, u.full_name AS author FROM customer_notes n LEFT JOIN users u ON u.id = n.created_by WHERE n.customer_id = ? ORDER BY n.created_at DESC";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->bind_param('i', $custId);
        $stmt->execute();
        $res = $stmt->get_result();
        $notes = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $notes;
    }

    /**
     * Search customers by name, ID number, or phone.
     */
    public function searchCustomers(string $term, int $limit = 10, int $offset = 0, bool $activeOnly = true): array
    {
        $like = '%' . $term . '%';
        $sql = "SELECT id, full_name, id_number, phone, status, kyc_status, risk_level
                FROM customers
                WHERE (full_name LIKE ? OR id_number LIKE ? OR phone LIKE ?)";
        if ($activeOnly) {
            $sql .= " AND status = 'active'";
        }
        $sql .= " ORDER BY full_name LIMIT ? OFFSET ?";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->bind_param('sssii', $like, $like, $like, $limit, $offset);
        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get count of new customers added today
     */
    public function getTodayCustomerCount(): int {
        $sql = "SELECT COUNT(*) as count FROM customers WHERE DATE(created_at) = CURDATE()";
        $result = $this->db->query($sql);
        if ($result && $row = $result->fetch_assoc()) {
            return (int)$row['count'];
        }
        return 0;
    }

    public function getCustomerCountChange(): float {
        $sql = "SELECT 
                (SELECT COUNT(*) FROM customers 
                 WHERE DATE(created_at) = CURDATE()) as today,
                (SELECT COUNT(*) FROM customers 
                 WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) as yesterday";
        
        $result = $this->db->query($sql);
        if (!$result) return 0.0;
        
        $row = $result->fetch_assoc();
        if (!$row || $row['yesterday'] == 0) return 0.0;
        
        return (($row['today'] - $row['yesterday']) / $row['yesterday']) * 100;
    }

    /**
     * Get customer operations history (exchanges and transfers)
     */
    public function getCustomerOperations(int $customerId, int $limit = 50): array
    {
        $operations = [];
        
        // Get exchanges
        $sql = "SELECT e.*, 
                    c1.name as from_currency_name, c1.symbol as from_currency_symbol,
                    c2.name as to_currency_name, c2.symbol as to_currency_symbol,
                    u.full_name as created_by_name,
                    'exchange' as operation_type
                FROM exchanges e
                LEFT JOIN currencies c1 ON c1.id = e.from_currency_id
                LEFT JOIN currencies c2 ON c2.id = e.to_currency_id
                LEFT JOIN users u ON u.id = e.created_by
                WHERE e.customer_id = ?
                ORDER BY e.created_at DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        if ($stmt) {
            $stmt->bind_param('ii', $customerId, $limit);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result) {
                $exchanges = $result->fetch_all(MYSQLI_ASSOC);
                $operations = array_merge($operations, $exchanges);
            }
            $stmt->close();
        }
        
        // Get transfers if transfers table exists
        $sql = "SELECT t.*, 
                    c1.name as sending_currency_name, c1.symbol as sending_currency_symbol,
                    c2.name as receiving_currency_name, c2.symbol as receiving_currency_symbol,
                    u.full_name as created_by_name,
                    'transfer' as operation_type
                FROM transfers t
                LEFT JOIN currencies c1 ON c1.id = t.sending_currency_id
                LEFT JOIN currencies c2 ON c2.id = t.receiving_currency_id
                LEFT JOIN users u ON u.id = t.created_by
                WHERE t.beneficiary_id = ?
                ORDER BY t.created_at DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        if ($stmt) {
            $stmt->bind_param('ii', $customerId, $limit);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result) {
                $transfers = $result->fetch_all(MYSQLI_ASSOC);
                $operations = array_merge($operations, $transfers);
            }
            $stmt->close();
        }
        
        // Sort by created_at desc
        usort($operations, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return array_slice($operations, 0, $limit);
    }

    /**
     * Get customer statistics
     */
    public function getCustomerStats(int $customerId): array
    {
        $stats = [
            'total_exchanges' => 0,
            'total_transfers' => 0,
            'total_documents' => 0,
            'total_volume' => 0,
            'last_activity' => null
        ];
        
        // Count exchanges
        $stmt = $this->db->prepare('SELECT COUNT(*), SUM(amount_from) FROM exchanges WHERE customer_id = ?');
        if ($stmt) {
            $stmt->bind_param('i', $customerId);
            $stmt->execute();
            $stmt->bind_result($count, $volume);
            $stmt->fetch();
            $stats['total_exchanges'] = $count ?? 0;
            $stats['total_volume'] = $volume ?? 0;
            $stmt->close();
        }
        
        // Count transfers
        $stmt = $this->db->prepare('SELECT COUNT(*) FROM transfers WHERE beneficiary_id = ?');
        if ($stmt) {
            $stmt->bind_param('i', $customerId);
            $stmt->execute();
            $stmt->bind_result($count);
            $stmt->fetch();
            $stats['total_transfers'] = $count ?? 0;
            $stmt->close();
        }
        
        // Count documents
        $stmt = $this->db->prepare('SELECT COUNT(*) FROM documents WHERE customer_id = ?');
        if ($stmt) {
            $stmt->bind_param('i', $customerId);
            $stmt->execute();
            $stmt->bind_result($count);
            $stmt->fetch();
            $stats['total_documents'] = $count ?? 0;
            $stmt->close();
        }
        
        // Get last activity from both exchanges and transfers
        $lastExchangeActivity = null;
        $lastTransferActivity = null;
        
        $stmt = $this->db->prepare('SELECT MAX(created_at) FROM exchanges WHERE customer_id = ?');
        if ($stmt) {
            $stmt->bind_param('i', $customerId);
            $stmt->execute();
            $stmt->bind_result($lastExchangeActivity);
            $stmt->fetch();
            $stmt->close();
        }
        
        $stmt = $this->db->prepare('SELECT MAX(created_at) FROM transfers WHERE beneficiary_id = ?');
        if ($stmt) {
            $stmt->bind_param('i', $customerId);
            $stmt->execute();
            $stmt->bind_result($lastTransferActivity);
            $stmt->fetch();
            $stmt->close();
        }
        
        // Get the most recent activity
        $stats['last_activity'] = null;
        if ($lastExchangeActivity && $lastTransferActivity) {
            $stats['last_activity'] = max($lastExchangeActivity, $lastTransferActivity);
        } elseif ($lastExchangeActivity) {
            $stats['last_activity'] = $lastExchangeActivity;
        } elseif ($lastTransferActivity) {
            $stats['last_activity'] = $lastTransferActivity;
        }
        
        return $stats;
    }
} 
<?php
/**
 * صفحة تقارير PayPal
 * PayPal Reports Page
 */

require_once '../includes/auth.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    header('Location: ../login.php');
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('paypal.reports')) {
    header('Location: ../dashboard/index.php?error=access_denied');
    exit;
}

$db = new Database();
$conn = Database::getConnection();

// معالجة الفلاتر
$dateFrom = $_GET['date_from'] ?? date('Y-m-01'); // بداية الشهر الحالي
$dateTo = $_GET['date_to'] ?? date('Y-m-d'); // اليوم الحالي
$status = $_GET['status'] ?? '';
$transactionCode = $_GET['transaction_code'] ?? '';

// بناء شروط الاستعلام
$whereConditions = ["DATE(created_at) BETWEEN ? AND ?"];
$params = [$dateFrom, $dateTo];
$types = 'ss';

if (!empty($status)) {
    $whereConditions[] = "status = ?";
    $params[] = $status;
    $types .= 's';
}

if (!empty($transactionCode)) {
    $whereConditions[] = "transaction_code LIKE ?";
    $params[] = '%' . $transactionCode . '%';
    $types .= 's';
}

$whereClause = implode(' AND ', $whereConditions);

// الإحصائيات العامة
$statsQuery = "
    SELECT
        COUNT(*) as total_transfers,
        SUM(CASE WHEN status = 'مستلم' THEN amount ELSE 0 END) as completed_amount,
        SUM(CASE WHEN status = 'لم يستلم' THEN amount ELSE 0 END) as pending_amount,
        0 as cancelled_amount,
        COUNT(CASE WHEN status = 'مستلم' THEN 1 END) as completed_count,
        COUNT(CASE WHEN status = 'لم يستلم' THEN 1 END) as pending_count,
        0 as cancelled_count,
        SUM(amount) as incoming_total,
        0 as outgoing_total,
        COUNT(*) as incoming_count,
        0 as outgoing_count
    FROM paypal_transfers
    WHERE $whereClause
";

$stmt = $conn->prepare($statsQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$stats = $result->fetch_assoc();
$stmt->close();

// إحصائيات يومية للرسم البياني
$dailyStatsQuery = "
    SELECT
        DATE(created_at) as date,
        COUNT(*) as count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 'مستلم' THEN amount ELSE 0 END) as completed_amount
    FROM paypal_transfers
    WHERE $whereClause
    GROUP BY DATE(created_at)
    ORDER BY date ASC
";

$stmt = $conn->prepare($dailyStatsQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$dailyStats = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// أفضل العملاء
$topCustomersQuery = "
    SELECT
        sender_name,
        '' as sender_email,
        COUNT(*) as transfer_count,
        SUM(amount) as total_amount
    FROM paypal_transfers
    WHERE $whereClause
    GROUP BY sender_name
    ORDER BY total_amount DESC
    LIMIT 10
";

$stmt = $conn->prepare($topCustomersQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$topCustomers = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// إحصائيات شهرية
$monthlyStatsQuery = "
    SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as completed_amount
    FROM paypal_transfers 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
    ORDER BY month ASC
";

$result = $conn->query($monthlyStatsQuery);
$monthlyStats = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];

$pageTitle = 'تقارير PayPal';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar-container">
            <?php include '../includes/sidebar.php'; ?>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-info me-2"></i>
                    تقارير PayPal
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="exportReport('excel')">
                        <i class="fas fa-file-excel me-1"></i>
                        تصدير Excel
                    </button>
                    <button class="btn btn-outline-secondary" onclick="exportReport('pdf')">
                        <i class="fas fa-file-pdf me-1"></i>
                        تصدير PDF
                    </button>
                    <button class="btn btn-primary" onclick="printReport()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>

            <!-- فلاتر التقرير -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر التقرير
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($dateFrom); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($dateTo); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="لم يستلم" <?php echo $status === 'لم يستلم' ? 'selected' : ''; ?>>لم يستلم</option>
                                <option value="مستلم" <?php echo $status === 'مستلم' ? 'selected' : ''; ?>>مستلم</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="transaction_code" class="form-label">رمز المعاملة</label>
                            <input type="text" class="form-control" id="transaction_code" name="transaction_code"
                                   placeholder="البحث برمز المعاملة" value="<?php echo htmlspecialchars($_GET['transaction_code'] ?? ''); ?>">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>
                                تطبيق الفلاتر
                            </button>
                            <a href="paypal_reports.php" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-1"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- الإحصائيات العامة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary bg-gradient rounded-circle p-3">
                                        <i class="fas fa-exchange-alt text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="text-muted mb-1">إجمالي الحوالات</h6>
                                    <h4 class="mb-0"><?php echo number_format($stats['total_transfers']); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-success bg-gradient rounded-circle p-3">
                                        <i class="fas fa-check-circle text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="text-muted mb-1">المبلغ المستلم</h6>
                                    <h4 class="mb-0">$<?php echo number_format($stats['completed_amount'], 2); ?></h4>
                                    <small class="text-success"><?php echo $stats['completed_count']; ?> حوالة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning bg-gradient rounded-circle p-3">
                                        <i class="fas fa-clock text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="text-muted mb-1">لم يستلم بعد</h6>
                                    <h4 class="mb-0">$<?php echo number_format($stats['pending_amount'], 2); ?></h4>
                                    <small class="text-warning"><?php echo $stats['pending_count']; ?> حوالة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="bg-info bg-gradient rounded-circle p-3">
                                        <i class="fab fa-paypal text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="text-muted mb-1">إجمالي PayPal</h6>
                                    <h4 class="mb-0">$<?php echo number_format($stats['incoming_total'], 2); ?></h4>
                                    <small class="text-info"><?php echo $stats['incoming_count']; ?> حوالة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row mb-4">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                الحوالات اليومية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyChart" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>
                                توزيع الحالات
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أفضل العملاء -->
            <div class="row mb-4">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-users me-2"></i>
                                أفضل العملاء
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($topCustomers)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <p>لا توجد بيانات عملاء في الفترة المحددة</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>العميل</th>
                                                <th>عدد الحوالات</th>
                                                <th>إجمالي المبلغ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($topCustomers as $index => $customer): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="bg-primary rounded-circle text-white d-flex align-items-center justify-content-center me-2" 
                                                             style="width: 32px; height: 32px; font-size: 14px;">
                                                            <?php echo $index + 1; ?>
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold"><?php echo htmlspecialchars($customer['sender_name']); ?></div>
                                                            <small class="text-muted"><?php echo htmlspecialchars($customer['sender_email']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $customer['transfer_count']; ?></span>
                                                </td>
                                                <td>
                                                    <strong>$<?php echo number_format($customer['total_amount'], 2); ?></strong>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                الاتجاه الشهري
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="monthlyChart" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// بيانات الرسوم البيانية
const dailyData = <?php echo json_encode($dailyStats); ?>;
const monthlyData = <?php echo json_encode($monthlyStats); ?>;
const stats = <?php echo json_encode($stats); ?>;

// الرسم البياني اليومي
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: dailyData.map(item => item.date),
        datasets: [{
            label: 'عدد الحوالات',
            data: dailyData.map(item => item.count),
            borderColor: '#0070ba',
            backgroundColor: 'rgba(0, 112, 186, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'المبلغ المكتمل ($)',
            data: dailyData.map(item => item.completed_amount),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});

// رسم بياني دائري للحالات
const statusCtx = document.getElementById('statusChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['مستلم', 'لم يستلم'],
        datasets: [{
            data: [stats.completed_count, stats.pending_count],
            backgroundColor: ['#28a745', '#ffc107'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// الرسم البياني الشهري
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: monthlyData.map(item => item.month),
        datasets: [{
            label: 'إجمالي المبلغ ($)',
            data: monthlyData.map(item => item.total_amount),
            backgroundColor: 'rgba(0, 112, 186, 0.8)',
            borderColor: '#0070ba',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// دوال التصدير والطباعة
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.open('export_paypal_report.php?' + params.toString(), '_blank');
}

function printReport() {
    window.print();
}
</script>

<?php include '../includes/footer.php'; ?>

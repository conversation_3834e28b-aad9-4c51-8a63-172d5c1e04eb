/*
 * Trust Plus Financial System - Dashboard JavaScript
 * JavaScript specific to dashboard views (index, user lists, role lists, etc.),
 * excluding specialized modules like transfers/exchange (they have their own)
 */

// ===== DASHBOARD NAMESPACE =====
if (!window.TrustPlus) {
  window.TrustPlus = { utils: {}, ui: {} };
}

TrustPlus.dashboard = {};

// ===== COLLAPSIBLE SIDEBAR SECTIONS =====
function initCollapsibleSections() {
  console.log('Initializing collapsible sections...');
  
  const collapsibleHeaders = document.querySelectorAll('.sidebar-section-header.collapsible');
  console.log('Found headers:', collapsibleHeaders.length);
  
  collapsibleHeaders.forEach((header, index) => {
    const targetId = header.getAttribute('data-target');
    const targetSection = document.getElementById(targetId);
    
    console.log(`Header ${index + 1}:`, targetId, targetSection);
    
    if (!targetSection) {
      console.error('Target section not found:', targetId);
      return;
    }
    
    // Check if section should be collapsed by default
    const isActivePage = targetSection.querySelector('.sidebar-sub-link.active');
    const savedState = localStorage.getItem(`sidebar-section-${targetId}`);
    
    console.log(`Section ${targetId}:`, {
      isActivePage: !!isActivePage,
      savedState: savedState,
      shouldCollapse: savedState ? savedState === 'collapsed' : !isActivePage
    });
    
    // If there's an active page in this section, expand it by default
    // Otherwise, use saved state or collapse by default
    const shouldCollapse = savedState ? savedState === 'collapsed' : !isActivePage;
    
    if (shouldCollapse) {
      header.classList.add('collapsed');
      targetSection.classList.add('collapsed');
      console.log(`Collapsed section: ${targetId}`);
    }
    
    // Add click event listener
    header.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      console.log(`Clicked on header: ${targetId}`);
      
      const isCollapsed = targetSection.classList.contains('collapsed');
      console.log(`Section ${targetId} is collapsed:`, isCollapsed);
      
      if (isCollapsed) {
        // Expand section
        header.classList.remove('collapsed');
        targetSection.classList.remove('collapsed');
        localStorage.setItem(`sidebar-section-${targetId}`, 'expanded');
        console.log(`Expanded section: ${targetId}`);
      } else {
        // Collapse section
        header.classList.add('collapsed');
        targetSection.classList.add('collapsed');
        localStorage.setItem(`sidebar-section-${targetId}`, 'collapsed');
        console.log(`Collapsed section: ${targetId}`);
      }
    });
    
    console.log(`Event listener added for: ${targetId}`);
  });
  
  console.log('Collapsible sections initialization complete');
}

// ===== TABLE FUNCTIONALITY =====
TrustPlus.dashboard.initTableSorting = function() {
  const tables = document.querySelectorAll('.sortable-table');

  tables.forEach(table => {
    const headers = table.querySelectorAll('th[data-sort]');

    headers.forEach(header => {
      header.style.cursor = 'pointer';
      header.addEventListener('click', function() {
        const column = this.getAttribute('data-sort');
        const currentOrder = this.getAttribute('data-order') || 'asc';
        const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';

        // Remove sorting indicators from other headers
        headers.forEach(h => {
          h.classList.remove('sort-asc', 'sort-desc');
          h.removeAttribute('data-order');
        });

        // Add sorting indicator to current header
        this.setAttribute('data-order', newOrder);
        this.classList.add(newOrder === 'asc' ? 'sort-asc' : 'sort-desc');

        // Sort table rows
        sortTable(table, column, newOrder);
      });
    });
  });

  function sortTable(table, column, order) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    rows.sort((a, b) => {
      const aValue = a.querySelector(`[data-value="${column}"]`)?.textContent.trim() ||
                     a.cells[getColumnIndex(table, column)]?.textContent.trim() || '';
      const bValue = b.querySelector(`[data-value="${column}"]`)?.textContent.trim() ||
                     b.cells[getColumnIndex(table, column)]?.textContent.trim() || '';

      // Try to parse as numbers first
      const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
      const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));

      if (!isNaN(aNum) && !isNaN(bNum)) {
        return order === 'asc' ? aNum - bNum : bNum - aNum;
      }

      // Fall back to string comparison
      return order === 'asc' ?
        aValue.localeCompare(bValue, 'ar') :
        bValue.localeCompare(aValue, 'ar');
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
  }

  function getColumnIndex(table, column) {
    const headers = table.querySelectorAll('th');
    for (let i = 0; i < headers.length; i++) {
      if (headers[i].getAttribute('data-sort') === column) {
        return i;
      }
    }
    return 0;
  }
};

// ===== TABLE FILTERING =====
TrustPlus.dashboard.initTableFiltering = function() {
  const filterInputs = document.querySelectorAll('.table-filter');

  filterInputs.forEach(input => {
    const targetTable = document.querySelector(input.getAttribute('data-target'));
    if (!targetTable) return;

    input.addEventListener('input', TrustPlus.utils.debounce(function() {
      const filterValue = this.value.toLowerCase().trim();
      const rows = targetTable.querySelectorAll('tbody tr');

      rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(filterValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });

      // Update row count if there's a counter
      updateRowCount(targetTable);
    }, 300));
  });

  function updateRowCount(table) {
    const counter = document.querySelector(`[data-count-for="${table.id}"]`);
    if (counter) {
      const visibleRows = table.querySelectorAll('tbody tr:not([style*="display: none"])');
      counter.textContent = visibleRows.length;
    }
  }
};

// ===== CONFIRMATION DIALOGS =====
TrustPlus.dashboard.initConfirmationDialogs = function() {
  const deleteButtons = document.querySelectorAll('.btn-delete, [data-action="delete"]');

  deleteButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      e.preventDefault();

      const message = this.getAttribute('data-confirm-message') || 'هل أنت متأكد من حذف هذا العنصر؟';
      const title = this.getAttribute('data-confirm-title') || 'تأكيد الحذف';

      showConfirmationModal(title, message, () => {
        // If it's a form button, submit the form
        if (this.form) {
          this.form.submit();
        }
        // If it has a href, navigate to it
        else if (this.href) {
          window.location.href = this.href;
        }
        // If it has a data-url, navigate to it
        else if (this.getAttribute('data-url')) {
          window.location.href = this.getAttribute('data-url');
        }
      });
    });
  });

  function showConfirmationModal(title, message, onConfirm) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('confirmationModal');
    if (!modal) {
      modal = createConfirmationModal();
      document.body.appendChild(modal);
    }

    // Update modal content
    modal.querySelector('.modal-title').textContent = title;
    modal.querySelector('.modal-body p').textContent = message;

    // Set up confirm button
    const confirmBtn = modal.querySelector('.btn-confirm');
    confirmBtn.onclick = function() {
      onConfirm();
      if (typeof bootstrap !== 'undefined') {
        bootstrap.Modal.getInstance(modal).hide();
      }
    };

    // Show modal
    if (typeof bootstrap !== 'undefined') {
      new bootstrap.Modal(modal).show();
    }
  }

  function createConfirmationModal() {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'confirmationModal';
    modal.innerHTML = `
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">تأكيد</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <p>هل أنت متأكد؟</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            <button type="button" class="btn btn-danger btn-confirm">تأكيد</button>
          </div>
        </div>
      </div>
    `;
    return modal;
  }
};

// ===== MODAL FORMS =====
TrustPlus.dashboard.initModalForms = function() {
  const editButtons = document.querySelectorAll('[data-bs-toggle="modal"][data-action="edit"]');

  editButtons.forEach(button => {
    button.addEventListener('click', function() {
      const modalId = this.getAttribute('data-bs-target').substring(1);
      const modal = document.getElementById(modalId);

      if (modal) {
        // Populate form fields with data attributes
        const form = modal.querySelector('form');
        if (form) {
          // Get all data attributes from the button
          const data = this.dataset;

          Object.keys(data).forEach(key => {
            if (key.startsWith('field')) {
              const fieldName = key.replace('field', '').toLowerCase();
              const fieldValue = data[key];
              const input = form.querySelector(`[name="${fieldName}"]`);

              if (input) {
                input.value = fieldValue;
              }
            }
          });
        }
      }
    });
  });
};

// ===== SIDEBAR FUNCTIONALITY =====
TrustPlus.dashboard.initSidebar = function() {
  console.log('Initializing sidebar...');
  
  // Initialize collapsible sections
  initCollapsibleSections();

  // Initialize sidebar toggle functionality
  const sidebarToggleBtn = document.querySelector('.sidebar-toggle-btn');
  if (sidebarToggleBtn) {
    sidebarToggleBtn.addEventListener('click', toggleSidebarVisibility);
  }

  // Apply saved sidebar state
  initializeSidebarState();
};

// ===== SIDEBAR TOGGLE FUNCTIONALITY =====
function toggleSidebarVisibility() {
  const body = document.body;
  const currentState = getSidebarState();
  const newState = currentState === 'collapsed' ? 'expanded' : 'collapsed';
  
  setSidebarState(newState);
  applySidebarState(newState);
  updateToggleButtonIcon(newState);
}

function isSmallScreen() {
  return window.innerWidth < 992;
}

function getSidebarState() {
  if (isSmallScreen()) {
    return localStorage.getItem('sidebar-mobile-state') || 'collapsed';
  } else {
    return localStorage.getItem('sidebar-desktop-state') || 'expanded';
  }
}

function setSidebarState(state) {
  if (isSmallScreen()) {
    localStorage.setItem('sidebar-mobile-state', state);
  } else {
    localStorage.setItem('sidebar-desktop-state', state);
  }
}

function applySidebarState(state) {
  const body = document.body;
  
  if (isSmallScreen()) {
    // Mobile behavior
    if (state === 'expanded') {
      body.classList.add('sidebar-open');
      body.classList.remove('sidebar-collapsed');
    } else {
      body.classList.remove('sidebar-open');
      body.classList.remove('sidebar-collapsed');
    }
  } else {
    // Desktop behavior
    if (state === 'collapsed') {
      body.classList.add('sidebar-collapsed');
    } else {
      body.classList.remove('sidebar-collapsed');
    }
  }
}

function updateToggleButtonIcon(state) {
  const toggleBtn = document.querySelector('.sidebar-toggle-btn');
  if (!toggleBtn) return;
  
  const icon = toggleBtn.querySelector('i');
  if (!icon) return;
  
  if (state === 'collapsed') {
    icon.className = 'fas fa-bars';
  } else {
    icon.className = 'fas fa-times';
  }
}

function initializeSidebarState() {
  const savedState = getSidebarState();
  applySidebarState(savedState);
  updateToggleButtonIcon(savedState);
}

// ===== UTILITY FUNCTIONS =====
TrustPlus.utils.debounce = function(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, initializing dashboard...');
  
  // Initialize sidebar functionality
  TrustPlus.dashboard.initSidebar();

  // Initialize table sorting
  TrustPlus.dashboard.initTableSorting();

  // Initialize table filtering
  TrustPlus.dashboard.initTableFiltering();

  // Initialize confirmation dialogs
  TrustPlus.dashboard.initConfirmationDialogs();

  // Initialize modal forms
  TrustPlus.dashboard.initModalForms();
  
  console.log('Dashboard initialization complete');
});
<?php
/**
 * Quick Database Fix for Transfer Delivery Status
 * This page quickly adds missing columns and fixes the database
 */

// Required includes
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Initialize auth
$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('transfers.edit'); // Basic permission check

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح سريع لقاعدة البيانات</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
<div class='container mt-4'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card shadow'>
                <div class='card-header bg-primary text-white'>
                    <h4 class='mb-0'>
                        <i class='fas fa-tools me-2'></i>
                        إصلاح سريع لقاعدة البيانات - حالة التسليم
                    </h4>
                </div>
                <div class='card-body'>";

try {
    // Initialize database connection
    $db = Database::getConnection();
    
    echo "<div class='alert alert-info'>
            <i class='fas fa-info-circle me-2'></i>
            بدء فحص وإصلاح قاعدة البيانات...
          </div>";
    
    $fixesApplied = [];
    $errors = [];
    
    // Start transaction
    $db->begin_transaction();
    
    // 1. Check and add delivery_status column
    echo "<h5 class='text-primary mt-4'><i class='fas fa-1 me-2'></i> فحص عمود delivery_status</h5>";
    $checkQuery1 = "SHOW COLUMNS FROM transfers LIKE 'delivery_status'";
    $result1 = $db->query($checkQuery1);
    
    if ($result1 && $result1->num_rows > 0) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                عمود delivery_status موجود بالفعل
              </div>";
    } else {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                عمود delivery_status غير موجود - جاري الإضافة...
              </div>";
        
        $sql1 = "ALTER TABLE transfers 
                 ADD COLUMN delivery_status ENUM('غير مستلمة', 'في الطريق', 'وصلت للوجهة', 'مستلمة', 'مرتجعة') 
                 DEFAULT 'غير مستلمة' 
                 AFTER status";
        
        if ($db->query($sql1)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة عمود delivery_status بنجاح
                  </div>";
            $fixesApplied[] = "إضافة عمود delivery_status";
        } else {
            $error = "خطأ في إضافة عمود delivery_status: " . $db->error;
            echo "<div class='alert alert-danger'>
                    <i class='fas fa-times me-2'></i>
                    $error
                  </div>";
            $errors[] = $error;
        }
    }
    
    // 2. Check and add delivery_notes column
    echo "<h5 class='text-primary mt-4'><i class='fas fa-2 me-2'></i> فحص عمود delivery_notes</h5>";
    $checkQuery2 = "SHOW COLUMNS FROM transfers LIKE 'delivery_notes'";
    $result2 = $db->query($checkQuery2);
    
    if ($result2 && $result2->num_rows > 0) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                عمود delivery_notes موجود بالفعل
              </div>";
    } else {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                عمود delivery_notes غير موجود - جاري الإضافة...
              </div>";
        
        $sql2 = "ALTER TABLE transfers 
                 ADD COLUMN delivery_notes TEXT NULL 
                 AFTER delivery_status";
        
        if ($db->query($sql2)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة عمود delivery_notes بنجاح
                  </div>";
            $fixesApplied[] = "إضافة عمود delivery_notes";
        } else {
            $error = "خطأ في إضافة عمود delivery_notes: " . $db->error;
            echo "<div class='alert alert-danger'>
                    <i class='fas fa-times me-2'></i>
                    $error
                  </div>";
            $errors[] = $error;
        }
    }
    
    // 3. Update status enum
    echo "<h5 class='text-primary mt-4'><i class='fas fa-3 me-2'></i> تحديث قائمة حالات الحوالة</h5>";
    $checkQuery3 = "SHOW COLUMNS FROM transfers LIKE 'status'";
    $result3 = $db->query($checkQuery3);
    $statusColumn = $result3 ? $result3->fetch_assoc() : null;
    
    if ($statusColumn && strpos($statusColumn['Type'], 'مقبولة') !== false) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                قائمة حالات الحوالة محدثة بالفعل
              </div>";
    } else {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                قائمة حالات الحوالة تحتاج تحديث - جاري التحديث...
              </div>";
        
        $sql3 = "ALTER TABLE transfers 
                 MODIFY COLUMN status ENUM('معلقة', 'مقبولة', 'مرفوضة', 'مرسلة', 'مستلمة', 'ملغاة', 'مكتملة') 
                 DEFAULT 'معلقة'";
        
        if ($db->query($sql3)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم تحديث قائمة حالات الحوالة بنجاح
                  </div>";
            $fixesApplied[] = "تحديث قائمة حالات الحوالة";
        } else {
            $error = "خطأ في تحديث قائمة حالات الحوالة: " . $db->error;
            echo "<div class='alert alert-danger'>
                    <i class='fas fa-times me-2'></i>
                    $error
                  </div>";
            $errors[] = $error;
        }
    }
    
    // 4. Add indexes if they don't exist
    echo "<h5 class='text-primary mt-4'><i class='fas fa-4 me-2'></i> إضافة فهارس الأداء</h5>";
    
    // Check delivery_status index
    $indexCheck1 = "SHOW INDEX FROM transfers WHERE Key_name = 'idx_transfers_delivery_status'";
    $indexResult1 = $db->query($indexCheck1);
    
    if ($indexResult1 && $indexResult1->num_rows > 0) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                فهرس delivery_status موجود بالفعل
              </div>";
    } else {
        $sql4 = "CREATE INDEX idx_transfers_delivery_status ON transfers(delivery_status)";
        if ($db->query($sql4)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check me-2'></i>
                    تم إضافة فهرس delivery_status بنجاح
                  </div>";
            $fixesApplied[] = "إضافة فهرس delivery_status";
        } else {
            echo "<div class='alert alert-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    تعذر إضافة فهرس delivery_status (غير ضروري للعمل)
                  </div>";
        }
    }
    
    // Commit transaction if no critical errors
    if (empty($errors)) {
        $db->commit();
        echo "<div class='alert alert-success mt-4'>
                <i class='fas fa-check-circle me-2'></i>
                <strong>تم إصلاح قاعدة البيانات بنجاح!</strong>
              </div>";
    } else {
        $db->rollback();
        echo "<div class='alert alert-danger mt-4'>
                <i class='fas fa-exclamation-circle me-2'></i>
                <strong>فشل في إصلاح قاعدة البيانات - تم التراجع عن التغييرات</strong>
              </div>";
    }
    
    // Summary
    if (!empty($fixesApplied)) {
        echo "<div class='alert alert-info mt-4'>
                <i class='fas fa-list me-2'></i>
                <strong>الإصلاحات المطبقة:</strong>
                <ul class='mb-0 mt-2'>";
        foreach ($fixesApplied as $fix) {
            echo "<li>$fix</li>";
        }
        echo "</ul></div>";
    }
    
    if (!empty($errors)) {
        echo "<div class='alert alert-danger mt-4'>
                <i class='fas fa-exclamation-circle me-2'></i>
                <strong>الأخطاء:</strong>
                <ul class='mb-0 mt-2'>";
        foreach ($errors as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul></div>";
    }
    
    // Test query
    echo "<h5 class='text-primary mt-4'><i class='fas fa-vial me-2'></i> اختبار قاعدة البيانات</h5>";
    $testQuery = "SELECT id, transaction_number, status, delivery_status FROM transfers LIMIT 1";
    $testResult = $db->query($testQuery);
    
    if ($testResult) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check me-2'></i>
                اختبار قاعدة البيانات نجح - الأعمدة الجديدة تعمل بشكل صحيح
              </div>";
    } else {
        echo "<div class='alert alert-danger'>
                <i class='fas fa-times me-2'></i>
                اختبار قاعدة البيانات فشل: " . $db->error . "
              </div>";
    }
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($db)) {
        $db->rollback();
    }
    
    echo "<div class='alert alert-danger'>
            <i class='fas fa-exclamation-circle me-2'></i>
            <strong>خطأ عام في إصلاح قاعدة البيانات:</strong>
            <br>
            " . htmlspecialchars($e->getMessage()) . "
          </div>";
}

echo "                </div>
                <div class='card-footer'>
                    <a href='transfers.php' class='btn btn-primary'>
                        <i class='fas fa-arrow-left me-2'></i>
                        العودة لصفحة الحوالات
                    </a>
                    <a href='../dashboard/' class='btn btn-secondary ms-2'>
                        <i class='fas fa-home me-2'></i>
                        الصفحة الرئيسية
                    </a>
                    <button class='btn btn-info ms-2' onclick='location.reload()'>
                        <i class='fas fa-sync-alt me-2'></i>
                        إعادة الفحص
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>

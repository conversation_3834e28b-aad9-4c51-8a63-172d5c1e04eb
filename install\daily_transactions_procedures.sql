-- =====================================================
-- إجراءات مخزنة لنظام المعاملات اليومية
-- Stored Procedures for Daily Transactions System
-- =====================================================

DELIMITER //

-- إجراء لإنشاء معاملة جديدة مع التحقق من صحة البيانات
CREATE PROCEDURE IF NOT EXISTS CreateDailyTransaction(
    IN p_country_id INT UNSIGNED,
    IN p_base_amount DECIMAL(18,2),
    IN p_customer_rate DECIMAL(18,6),
    IN p_operation_type ENUM('multiply', 'divide'),
    IN p_exchange_rate DECIMAL(18,6),
    IN p_delivery_type ENUM('cash', 'bank', 'usdt'),
    IN p_transfer_amount DECIMAL(18,2),
    IN p_recipient_name VARCHAR(255),
    IN p_notes TEXT,
    IN p_branch_id INT UNSIGNED,
    IN p_created_by INT UNSIGNED,
    OUT p_transaction_id INT UNSIGNED,
    OUT p_transaction_number VARCHAR(50),
    OUT p_result_message VARCHAR(500)
)
BEGIN
    DECLARE v_error_count INT DEFAULT 0;
    DECLARE v_country_exists INT DEFAULT 0;
    DECLARE v_branch_exists INT DEFAULT 0;
    DECLARE v_user_exists INT DEFAULT 0;
    DECLARE v_transaction_number VARCHAR(50);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            p_result_message = MESSAGE_TEXT;
        SET p_transaction_id = 0;
        SET p_transaction_number = '';
    END;
    
    START TRANSACTION;
    
    -- التحقق من صحة البيانات
    -- التحقق من وجود الدولة
    SELECT COUNT(*) INTO v_country_exists 
    FROM countries 
    WHERE id = p_country_id AND is_active = 1;
    
    IF v_country_exists = 0 THEN
        SET p_result_message = 'الدولة المحددة غير موجودة أو غير نشطة';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    -- التحقق من وجود الفرع (إذا تم تحديده)
    IF p_branch_id IS NOT NULL THEN
        SELECT COUNT(*) INTO v_branch_exists 
        FROM branches 
        WHERE id = p_branch_id AND status = 'active';
        
        IF v_branch_exists = 0 THEN
            SET p_result_message = 'الفرع المحدد غير موجود أو غير نشط';
            SET v_error_count = v_error_count + 1;
        END IF;
    END IF;
    
    -- التحقق من وجود المستخدم (إذا تم تحديده)
    IF p_created_by IS NOT NULL THEN
        SELECT COUNT(*) INTO v_user_exists 
        FROM users 
        WHERE id = p_created_by AND status = 'active';
        
        IF v_user_exists = 0 THEN
            SET p_result_message = 'المستخدم المحدد غير موجود أو غير نشط';
            SET v_error_count = v_error_count + 1;
        END IF;
    END IF;
    
    -- التحقق من صحة المبالغ
    IF p_base_amount <= 0 THEN
        SET p_result_message = 'مبلغ الحوالة الأساسي يجب أن يكون أكبر من صفر';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    IF p_customer_rate <= 0 THEN
        SET p_result_message = 'سعر القص للزبون يجب أن يكون أكبر من صفر';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    IF p_exchange_rate <= 0 THEN
        SET p_result_message = 'سعر الصرف يجب أن يكون أكبر من صفر';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    -- التحقق من البيانات المطلوبة للتحويل البنكي أو USDT
    IF p_delivery_type IN ('bank', 'usdt') THEN
        IF p_transfer_amount IS NULL OR p_transfer_amount <= 0 THEN
            SET p_result_message = 'المبلغ المراد تحويله مطلوب للتحويل البنكي أو USDT';
            SET v_error_count = v_error_count + 1;
        END IF;
        
        IF p_recipient_name IS NULL OR TRIM(p_recipient_name) = '' THEN
            SET p_result_message = 'اسم المستلم مطلوب للتحويل البنكي أو USDT';
            SET v_error_count = v_error_count + 1;
        END IF;
    END IF;
    
    -- إذا كانت هناك أخطاء، إلغاء العملية
    IF v_error_count > 0 THEN
        ROLLBACK;
        SET p_transaction_id = 0;
        SET p_transaction_number = '';
        -- p_result_message تم تعيينها بالفعل
    ELSE
        -- إنشاء رقم المعاملة
        SET v_transaction_number = CONCAT('DT', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(FLOOR(RAND() * 10000), 4, '0'));
        
        -- التأكد من عدم تكرار رقم المعاملة
        WHILE EXISTS(SELECT 1 FROM daily_transactions WHERE transaction_number = v_transaction_number) DO
            SET v_transaction_number = CONCAT('DT', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(FLOOR(RAND() * 10000), 4, '0'));
        END WHILE;
        
        -- إدراج المعاملة الجديدة
        INSERT INTO daily_transactions (
            transaction_number,
            country_id,
            base_amount,
            customer_rate,
            operation_type,
            exchange_rate,
            delivery_type,
            transfer_amount,
            recipient_name,
            notes,
            branch_id,
            created_by
        ) VALUES (
            v_transaction_number,
            p_country_id,
            p_base_amount,
            p_customer_rate,
            p_operation_type,
            p_exchange_rate,
            p_delivery_type,
            p_transfer_amount,
            p_recipient_name,
            p_notes,
            p_branch_id,
            p_created_by
        );
        
        SET p_transaction_id = LAST_INSERT_ID();
        SET p_transaction_number = v_transaction_number;
        SET p_result_message = 'تم إنشاء المعاملة بنجاح';
        
        -- إضافة سجل في تاريخ المعاملات
        INSERT INTO daily_transaction_history (
            transaction_id,
            action_type,
            new_values,
            changed_by,
            ip_address
        ) VALUES (
            p_transaction_id,
            'created',
            JSON_OBJECT(
                'transaction_number', v_transaction_number,
                'country_id', p_country_id,
                'base_amount', p_base_amount,
                'delivery_type', p_delivery_type
            ),
            p_created_by,
            NULL
        );
        
        COMMIT;
    END IF;
    
END //

-- إجراء للبحث في المعاملات مع فلترة متقدمة
CREATE PROCEDURE IF NOT EXISTS SearchDailyTransactions(
    IN p_date_from DATE,
    IN p_date_to DATE,
    IN p_country_id INT UNSIGNED,
    IN p_delivery_type VARCHAR(20),
    IN p_min_amount DECIMAL(18,2),
    IN p_max_amount DECIMAL(18,2),
    IN p_recipient_name VARCHAR(255),
    IN p_branch_id INT UNSIGNED,
    IN p_limit_count INT,
    IN p_offset_count INT
)
BEGIN
    DECLARE v_sql TEXT DEFAULT '';
    DECLARE v_where_clause TEXT DEFAULT '';
    DECLARE v_conditions TEXT DEFAULT '';
    
    -- بناء استعلام البحث ديناميكياً
    SET v_sql = 'SELECT 
        dt.id,
        dt.transaction_number,
        dt.created_at,
        c.name_ar as country_name,
        c.currency_code,
        dt.base_amount,
        dt.customer_rate,
        dt.operation_type,
        dt.calculated_amount,
        dt.exchange_rate,
        dt.recipient_amount,
        dt.delivery_type,
        dt.transfer_amount,
        dt.recipient_name,
        dt.notes,
        b.name as branch_name,
        u.full_name as created_by_name
    FROM daily_transactions dt
    LEFT JOIN countries c ON dt.country_id = c.id
    LEFT JOIN branches b ON dt.branch_id = b.id
    LEFT JOIN users u ON dt.created_by = u.id';
    
    -- إضافة شروط البحث
    IF p_date_from IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND DATE(dt.created_at) >= ''', p_date_from, '''');
    END IF;
    
    IF p_date_to IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND DATE(dt.created_at) <= ''', p_date_to, '''');
    END IF;
    
    IF p_country_id IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND dt.country_id = ', p_country_id);
    END IF;
    
    IF p_delivery_type IS NOT NULL AND p_delivery_type != '' THEN
        SET v_conditions = CONCAT(v_conditions, ' AND dt.delivery_type = ''', p_delivery_type, '''');
    END IF;
    
    IF p_min_amount IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND dt.base_amount >= ', p_min_amount);
    END IF;
    
    IF p_max_amount IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND dt.base_amount <= ', p_max_amount);
    END IF;
    
    IF p_recipient_name IS NOT NULL AND p_recipient_name != '' THEN
        SET v_conditions = CONCAT(v_conditions, ' AND dt.recipient_name LIKE ''%', p_recipient_name, '%''');
    END IF;
    
    IF p_branch_id IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND dt.branch_id = ', p_branch_id);
    END IF;
    
    -- إضافة WHERE إذا كانت هناك شروط
    IF LENGTH(v_conditions) > 0 THEN
        SET v_where_clause = CONCAT(' WHERE 1=1', v_conditions);
    END IF;
    
    -- إضافة الترتيب والحد
    SET v_sql = CONCAT(v_sql, v_where_clause, ' ORDER BY dt.created_at DESC');
    
    IF p_limit_count IS NOT NULL AND p_limit_count > 0 THEN
        SET v_sql = CONCAT(v_sql, ' LIMIT ', p_limit_count);
        
        IF p_offset_count IS NOT NULL AND p_offset_count > 0 THEN
            SET v_sql = CONCAT(v_sql, ' OFFSET ', p_offset_count);
        END IF;
    END IF;
    
    -- تنفيذ الاستعلام
    SET @sql_statement = v_sql;
    PREPARE stmt FROM @sql_statement;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
END //

-- إجراء لحساب إحصائيات المعاملات اليومية
CREATE PROCEDURE IF NOT EXISTS GetDailyTransactionStats(
    IN p_date_from DATE,
    IN p_date_to DATE,
    IN p_branch_id INT UNSIGNED
)
BEGIN
    DECLARE v_where_clause TEXT DEFAULT '';
    
    -- بناء شرط WHERE
    SET v_where_clause = ' WHERE 1=1';
    
    IF p_date_from IS NOT NULL THEN
        SET v_where_clause = CONCAT(v_where_clause, ' AND DATE(dt.created_at) >= ''', p_date_from, '''');
    END IF;
    
    IF p_date_to IS NOT NULL THEN
        SET v_where_clause = CONCAT(v_where_clause, ' AND DATE(dt.created_at) <= ''', p_date_to, '''');
    END IF;
    
    IF p_branch_id IS NOT NULL THEN
        SET v_where_clause = CONCAT(v_where_clause, ' AND dt.branch_id = ', p_branch_id);
    END IF;
    
    -- إحصائيات عامة
    SET @sql_stats = CONCAT('
        SELECT 
            COUNT(*) as total_transactions,
            SUM(dt.base_amount) as total_base_amount,
            AVG(dt.base_amount) as avg_base_amount,
            SUM(dt.recipient_amount) as total_recipient_amount,
            COUNT(CASE WHEN dt.delivery_type = ''cash'' THEN 1 END) as cash_transactions,
            COUNT(CASE WHEN dt.delivery_type = ''bank'' THEN 1 END) as bank_transactions,
            COUNT(CASE WHEN dt.delivery_type = ''usdt'' THEN 1 END) as usdt_transactions
        FROM daily_transactions dt', v_where_clause);
    
    PREPARE stmt FROM @sql_stats;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
END //

DELIMITER ;

-- إنشاء فهارس إضافية لتحسين أداء البحث
CREATE INDEX IF NOT EXISTS idx_daily_trans_date_country ON daily_transactions(created_at, country_id);
CREATE INDEX IF NOT EXISTS idx_daily_trans_delivery_amount ON daily_transactions(delivery_type, base_amount);
CREATE INDEX IF NOT EXISTS idx_daily_trans_recipient_name ON daily_transactions(recipient_name(50));
CREATE INDEX IF NOT EXISTS idx_daily_trans_branch_date ON daily_transactions(branch_id, created_at);

-- إنشاء view لعرض المعاملات مع تفاصيل كاملة
CREATE OR REPLACE VIEW v_daily_transactions_full AS
SELECT 
    dt.id,
    dt.transaction_number,
    dt.created_at,
    dt.updated_at,
    c.name_ar as country_name_ar,
    c.name_en as country_name_en,
    c.currency_code,
    c.currency_symbol,
    dt.base_amount,
    dt.customer_rate,
    dt.operation_type,
    dt.calculated_amount,
    dt.exchange_rate,
    dt.recipient_amount,
    dt.delivery_type,
    dt.transfer_amount,
    dt.recipient_name,
    dt.notes,
    b.name as branch_name,
    b.code as branch_code,
    u.full_name as created_by_name,
    u.username as created_by_username,
    -- حساب الربح المتوقع (يمكن تخصيصه حسب الحاجة)
    CASE 
        WHEN dt.operation_type = 'multiply' THEN 
            (dt.base_amount * dt.customer_rate * dt.exchange_rate) - dt.recipient_amount
        ELSE 
            dt.recipient_amount - (dt.base_amount / dt.customer_rate * dt.exchange_rate)
    END as estimated_profit
FROM daily_transactions dt
LEFT JOIN countries c ON dt.country_id = c.id
LEFT JOIN branches b ON dt.branch_id = b.id
LEFT JOIN users u ON dt.created_by = u.id;

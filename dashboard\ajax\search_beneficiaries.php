<?php
// Enable error display for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../../includes/database.php';

// Set JSON response headers
header('Content-Type: application/json');

// Debug response
$response = [
    'success' => false,
    'debug' => [
        'script' => 'search_beneficiaries.php',
        'time' => date('Y-m-d H:i:s'),
        'post' => $_POST,
    ]
];

try {
    // Get search term
    $searchTerm = isset($_POST['search']) ? trim($_POST['search']) : '';
    
    if (empty($searchTerm) || strlen($searchTerm) < 2) {
        $response['error'] = 'يجب إدخال على الأقل حرفين للبحث';
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // Initialize database directly
    $db = Database::getConnection();
    $response['debug']['db_connected'] = ($db !== null);
    
    // Direct database query
    $like = '%' . $searchTerm . '%';
    $sql = "SELECT id, full_name, id_number, phone, nationality 
            FROM customers 
            WHERE (full_name LIKE ? OR id_number LIKE ? OR phone LIKE ?) 
            AND status = 'active' 
            ORDER BY full_name LIMIT 10";
            
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        throw new Exception("Database prepare error: " . $db->error);
    }
    
    $stmt->bind_param('sss', $like, $like, $like);
    $executed = $stmt->execute();
    
    if (!$executed) {
        throw new Exception("Execute error: " . $stmt->error);
    }
    
    $result = $stmt->get_result();
    if (!$result) {
        throw new Exception("Result error: " . $stmt->error);
    }
    
    $customers = [];
    while ($row = $result->fetch_assoc()) {
        $customers[] = [
            'id' => (int)$row['id'],
            'full_name' => $row['full_name'],
            'id_number' => $row['id_number'] ?? '',
            'phone' => $row['phone'] ?? '',
            'nationality' => $row['nationality'] ?? ''
        ];
    }
    
    $stmt->close();
    
    $response['success'] = true;
    $response['customers'] = $customers;
    $response['count'] = count($customers);

} catch (Exception $e) {
    $response['success'] = false;
    $response['error'] = $e->getMessage();
    $response['error_trace'] = $e->getTraceAsString();
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?> 
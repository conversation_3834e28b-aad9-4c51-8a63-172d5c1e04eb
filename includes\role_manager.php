<?php
require_once __DIR__ . '/database.php';

class RoleManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Fetch all roles.
     *
     * @return array<int, array<string, mixed>>
     */
    public function getAllRoles(): array
    {
        $sql = "SELECT id, name, description, status FROM roles ORDER BY id ASC";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return [];
        }
        $stmt->execute();
        $result = $stmt->get_result();
        $roles  = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $roles;
    }

    /**
     * Retrieve role by ID.
     */
    public function getRoleById(int $roleId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM roles WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $roleId);
        $stmt->execute();
        $result = $stmt->get_result();
        $role   = $result->fetch_assoc() ?: null;
        $stmt->close();
        return $role;
    }

    /**
     * Get role permissions
     */
    public function getRolePermissions(int $roleId): array
    {
        $sql = "SELECT p.id, p.name, p.description, p.module
                FROM permissions p
                INNER JOIN role_permissions rp ON p.id = rp.permission_id
                WHERE rp.role_id = ?
                ORDER BY p.module, p.name";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->bind_param('i', $roleId);
        $stmt->execute();
        $result = $stmt->get_result();
        $permissions = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $permissions;
    }

    /**
     * Get count of users with this role
     */
    public function getRoleUserCount(int $roleId): int
    {
        $sql = "SELECT COUNT(*) as count FROM users WHERE role_id = ?";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 0;
        $stmt->bind_param('i', $roleId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();
        return $row ? (int)$row['count'] : 0;
    }

    /**
     * Check if a role name already exists.
     */
    public function roleNameExists(string $name, int $excludeId = 0): bool
    {
        $sql = "SELECT COUNT(*) as count FROM roles WHERE name = ?";
        $params = [$name];
        $types = 's';

        if ($excludeId > 0) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
            $types .= 'i';
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();
        return $row && $row['count'] > 0;
    }

    /**
     * Update role permissions
     */
    public function updateRolePermissions(int $roleId, array $permissionIds): bool
    {
        // بدء المعاملة
        $this->db->autocommit(false);

        try {
            // حذف الصلاحيات الحالية
            $stmt = $this->db->prepare('DELETE FROM role_permissions WHERE role_id = ?');
            if (!$stmt) {
                throw new Exception('فشل في تحضير استعلام الحذف');
            }
            $stmt->bind_param('i', $roleId);
            $stmt->execute();
            $stmt->close();

            // إضافة الصلاحيات الجديدة
            if (!empty($permissionIds)) {
                $stmt = $this->db->prepare('INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)');
                if (!$stmt) {
                    throw new Exception('فشل في تحضير استعلام الإدراج');
                }

                foreach ($permissionIds as $permissionId) {
                    $stmt->bind_param('ii', $roleId, $permissionId);
                    $stmt->execute();
                }
                $stmt->close();
            }

            // تأكيد المعاملة
            $this->db->commit();
            $this->db->autocommit(true);
            return true;

        } catch (Exception $e) {
            // إلغاء المعاملة
            $this->db->rollback();
            $this->db->autocommit(true);
            return false;
        }
    }

    /**
     * Check if a role name already exists.
     */
    public function roleExists(string $name, int $excludeId = null): bool
    {
        $sql = 'SELECT COUNT(*) FROM roles WHERE name = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($excludeId !== null) {
            $stmt->bind_param('si', $name, $excludeId);
        } else {
            $stmt->bind_param('s', $name);
        }
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    /**
     * Add a role – returns ID or false.
     */
    public function addRole(array $data)
    {
        $stmt = $this->db->prepare('INSERT INTO roles (name, description, status, created_at) VALUES (?,?,?, NOW())');
        if (!$stmt) return false;
        $stmt->bind_param('sss', $data['name'], $data['description'], $data['status']);
        if ($stmt->execute()) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        $stmt->close();
        return false;
    }

    /**
     * Update role.
     */
    public function updateRole(int $roleId, array $data): bool
    {
        if (empty($data)) return false;
        $fields = [];
        $params = [];
        $types  = '';

        foreach ($data as $k => $v) {
            $fields[] = "$k = ?";
            $params[] = $v;
            $types   .= 's';
        }
        $sql = 'UPDATE roles SET ' . implode(', ', $fields) . ' WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $types .= 'i';
        $params[] = $roleId;
        $stmt->bind_param($types, ...$params);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Delete role (except system admin role).
     */
    public function deleteRole(int $roleId): bool
    {
        if ($roleId === 1) {
            return false; // Prevent deleting system admin role
        }
        $stmt = $this->db->prepare('DELETE FROM roles WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $roleId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }
} 
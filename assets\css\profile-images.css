/* Profile Images Styling */
.profile-image {
    display: block;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s ease;
}

.profile-image:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Different sizes for different contexts */
.profile-image-sm {
    width: 32px;
    height: 32px;
}

.profile-image-md {
    width: 40px;
    height: 40px;
}

.profile-image-lg {
    width: 50px;
    height: 50px;
}

.profile-image-xl {
    width: 80px;
    height: 80px;
}

.profile-image-xxl {
    width: 120px;
    height: 120px;
}

/* Border styles - All borders removed */
.profile-image-border {
    border: none;
}

.profile-image-border-white {
    border: none;
}

.profile-image-border-primary {
    border: none;
}

.profile-image-border-success {
    border: none;
}

/* Container for profile images */
.profile-image-container {
    position: relative;
    display: inline-block;
}

.profile-image-container .profile-image {
    display: block;
}

/* Status indicator for profile images */
.profile-image-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.profile-image-status.online {
    background-color: #28a745;
}

.profile-image-status.offline {
    background-color: #6c757d;
}

.profile-image-status.busy {
    background-color: #ffc107;
}

/* Default avatar styling */
.default-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 50%;
    color: #6c757d;
    font-size: 1.2em;
    border: none;
}

.default-avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.8em;
}

.default-avatar-md {
    width: 40px;
    height: 40px;
    font-size: 1em;
}

.default-avatar-lg {
    width: 50px;
    height: 50px;
    font-size: 1.2em;
}

.default-avatar-xl {
    width: 80px;
    height: 80px;
    font-size: 1.8em;
}

.default-avatar-xxl {
    width: 120px;
    height: 120px;
    font-size: 2.5em;
}

/* Image upload preview */
.image-preview {
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    border: none;
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.02);
}

/* Sidebar specific styling */
.sidebar .profile-image-lg {
    width: 50px !important;
    height: 50px !important;
    border: none !important;
}

.sidebar .default-avatar-lg {
    width: 50px !important;
    height: 50px !important;
    background-color: rgba(255,255,255,0.1) !important;
    color: rgba(255,255,255,0.8) !important;
    border: none !important;
}

/* Profile avatar container in sidebar */
.profile-avatar {
    flex-shrink: 0;
}

.profile-avatar img,
.profile-avatar .default-avatar {
    display: block;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    border: none;
}

/* Specific styling for profile avatar image */
.profile-avatar-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    object-position: center;
    border: none;
    transition: all 0.3s ease;
}

.profile-avatar-img:hover {
    transform: scale(1.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-image-lg {
        width: 40px;
        height: 40px;
    }
    
    .profile-image-xl {
        width: 60px;
        height: 60px;
    }
    
    .profile-image-xxl {
        width: 80px;
        height: 80px;
    }
    
    .sidebar .profile-image-lg {
        width: 40px !important;
        height: 40px !important;
    }
    
    .sidebar .default-avatar-lg {
        width: 40px !important;
        height: 40px !important;
    }
} 
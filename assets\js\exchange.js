/*
 * Trust Plus Financial System - Exchange Module JavaScript
 * Handles currency exchange calculations and form interactions
 */

// ===== EXCHANGE MODULE =====
window.TrustPlus = window.TrustPlus || {};
TrustPlus.Exchange = {
    rates: {},
    currencies: {},
    
    init: function() {
        this.bindEvents();
        this.loadExchangeRates();
    },
    
    bindEvents: function() {
        // Currency selection change events
        const fromCurrency = document.querySelector('select[name="from_currency_id"]');
        const toCurrency = document.querySelector('select[name="to_currency_id"]');
        const amountFrom = document.querySelector('input[name="amount_from"]');
        const amountTo = document.querySelector('input[name="amount_to"]');
        
        if (fromCurrency) {
            fromCurrency.addEventListener('change', () => this.calculateExchange());
        }
        
        if (toCurrency) {
            toCurrency.addEventListener('change', () => this.calculateExchange());
        }
        
        if (amountFrom) {
            amountFrom.addEventListener('input', () => this.calculateFromAmount());
        }
        
        if (amountTo) {
            amountTo.addEventListener('input', () => this.calculateToAmount());
        }
    },
    
    loadExchangeRates: function() {
        // Load exchange rates from server
        fetch('ajax/get_exchange_rates.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.rates = data.rates;
                    this.currencies = data.currencies;
                }
            })
            .catch(error => {
                console.error('Error loading exchange rates:', error);
            });
    },
    
    calculateExchange: function() {
        const fromCurrencyId = document.querySelector('select[name="from_currency_id"]')?.value;
        const toCurrencyId = document.querySelector('select[name="to_currency_id"]')?.value;
        
        if (!fromCurrencyId || !toCurrencyId || fromCurrencyId === toCurrencyId) {
            this.clearAmounts();
            return;
        }
        
        // Update rate display if available
        this.updateRateDisplay(fromCurrencyId, toCurrencyId);
    },
    
    calculateFromAmount: function() {
        const fromCurrencyId = document.querySelector('select[name="from_currency_id"]')?.value;
        const toCurrencyId = document.querySelector('select[name="to_currency_id"]')?.value;
        const amountFrom = parseFloat(document.querySelector('input[name="amount_from"]')?.value || 0);
        const amountToField = document.querySelector('input[name="amount_to"]');
        
        if (!fromCurrencyId || !toCurrencyId || !amountFrom || fromCurrencyId === toCurrencyId) {
            if (amountToField) amountToField.value = '';
            return;
        }
        
        const rate = this.getExchangeRate(fromCurrencyId, toCurrencyId);
        if (rate && amountToField) {
            const amountTo = amountFrom * rate.sell_rate;
            amountToField.value = amountTo.toFixed(2);
        }
    },
    
    calculateToAmount: function() {
        const fromCurrencyId = document.querySelector('select[name="from_currency_id"]')?.value;
        const toCurrencyId = document.querySelector('select[name="to_currency_id"]')?.value;
        const amountTo = parseFloat(document.querySelector('input[name="amount_to"]')?.value || 0);
        const amountFromField = document.querySelector('input[name="amount_from"]');
        
        if (!fromCurrencyId || !toCurrencyId || !amountTo || fromCurrencyId === toCurrencyId) {
            if (amountFromField) amountFromField.value = '';
            return;
        }
        
        const rate = this.getExchangeRate(fromCurrencyId, toCurrencyId);
        if (rate && amountFromField) {
            const amountFrom = amountTo / rate.sell_rate;
            amountFromField.value = amountFrom.toFixed(2);
        }
    },
    
    getExchangeRate: function(fromCurrencyId, toCurrencyId) {
        const rateKey = `${fromCurrencyId}-${toCurrencyId}`;
        return this.rates[rateKey] || null;
    },
    
    updateRateDisplay: function(fromCurrencyId, toCurrencyId) {
        const rate = this.getExchangeRate(fromCurrencyId, toCurrencyId);
        const rateDisplay = document.getElementById('rate-display');
        
        if (rateDisplay) {
            if (rate) {
                const fromCurrency = this.currencies[fromCurrencyId];
                const toCurrency = this.currencies[toCurrencyId];
                rateDisplay.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-exchange-alt me-2"></i>
                        سعر الصرف: 1 ${fromCurrency?.code || ''} = ${rate.sell_rate} ${toCurrency?.code || ''}
                    </div>
                `;
            } else {
                rateDisplay.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        سعر الصرف غير متوفر لهذا الزوج
                    </div>
                `;
            }
        }
    },
    
    clearAmounts: function() {
        const amountFromField = document.querySelector('input[name="amount_from"]');
        const amountToField = document.querySelector('input[name="amount_to"]');
        const rateDisplay = document.getElementById('rate-display');
        
        if (amountFromField) amountFromField.value = '';
        if (amountToField) amountToField.value = '';
        if (rateDisplay) rateDisplay.innerHTML = '';
    },
    
    formatCurrency: function(amount, currencyCode) {
        if (typeof TrustPlus !== 'undefined' && TrustPlus.utils && TrustPlus.utils.formatCurrency) {
            return TrustPlus.utils.formatCurrency(amount, currencyCode);
        }
        return parseFloat(amount).toFixed(2) + ' ' + currencyCode;
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    TrustPlus.Exchange.init();
});

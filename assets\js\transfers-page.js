// transfers-page.js
// وظائف خاصة بصفحة الحوالات فقط

document.addEventListener('DOMContentLoaded', function() {
    console.log('Transfers page JavaScript loaded');

    // التأكد من عدم تضارب مع النظام العام للاستجابة
    if (window.ResponsiveSystem) {
        console.log('Using global responsive system');
        return; // استخدام النظام العام بدلاً من الكود المكرر
    }

    // وظائف خاصة بصفحة الحوالات فقط
    function initTransfersSpecificFeatures() {
        // تحسينات خاصة بالجداول في صفحة الحوالات
        enhanceTransfersTables();

        // تحسينات خاصة بالنماذج في صفحة الحوالات
        enhanceTransfersForms();

        // تحسينات خاصة بالبحث في صفحة الحوالات
        enhanceTransfersSearch();
    }

    // تحسين جداول الحوالات
    function enhanceTransfersTables() {
        const transfersTable = document.querySelector('.transfers-table');
        if (transfersTable) {
            // إضافة تأثيرات خاصة بجدول الحوالات
            transfersTable.addEventListener('click', function(e) {
                if (e.target.closest('tr')) {
                    const row = e.target.closest('tr');
                    // تأثير تمييز الصف المحدد
                    document.querySelectorAll('.transfers-table tr').forEach(r => r.classList.remove('selected'));
                    row.classList.add('selected');
                }
            });
        }
    }

    // تحسين نماذج الحوالات
    function enhanceTransfersForms() {
        const transferForm = document.querySelector('#transferForm');
        if (transferForm) {
            // تحسينات خاصة بنموذج الحوالات
            const beneficiarySearch = document.getElementById('beneficiarySearch');
            if (beneficiarySearch) {
                // تحسين البحث عن المستفيدين
                beneficiarySearch.addEventListener('focus', function() {
                    this.parentElement.classList.add('search-focused');
                });

                beneficiarySearch.addEventListener('blur', function() {
                    setTimeout(() => {
                        this.parentElement.classList.remove('search-focused');
                    }, 200);
                });
            }
        }
    }

    // تحسين البحث في الحوالات
    function enhanceTransfersSearch() {
        const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="بحث"]');

        searchInputs.forEach(input => {
            // إضافة تأثيرات بصرية للبحث
            input.addEventListener('input', function() {
                const hasValue = this.value.trim().length > 0;
                this.classList.toggle('has-value', hasValue);
            });
        });
    }

    // تحسين الإحصائيات في صفحة الحوالات
    function enhanceTransfersStats() {
        const statsCards = document.querySelectorAll('.stats-card, .stat-card');

        statsCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '';
            });
        });
    }

    // تحسين أزرار الإجراءات
    function enhanceActionButtons() {
        const actionButtons = document.querySelectorAll('.btn-group .btn, .table-actions .btn');

        actionButtons.forEach(button => {
            button.addEventListener('click', function() {
                // إضافة تأثير تحميل للأزرار
                if (this.href || this.type === 'submit') {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    this.disabled = true;

                    // إعادة تعيين النص بعد 3 ثوان في حالة عدم تحديث الصفحة
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 3000);
                }
            });
        });
    }

    // تشغيل جميع التحسينات الخاصة بصفحة الحوالات
    function runTransfersEnhancements() {
        try {
            initTransfersSpecificFeatures();
            enhanceTransfersStats();
            enhanceActionButtons();

            console.log('Transfers page enhancements applied successfully');
        } catch (error) {
            console.error('Error applying transfers page enhancements:', error);
        }
    }

    // تشغيل التحسينات
    runTransfersEnhancements();
});
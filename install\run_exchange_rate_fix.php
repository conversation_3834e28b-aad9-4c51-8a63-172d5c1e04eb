<?php
/**
 * Fix Exchange Rate Decimal Precision
 * This script modifies the office_exchange_rates table to support larger values
 */

require_once __DIR__ . '/../config/database.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    echo "=== Fixing Exchange Rate Decimal Precision ===\n";
    
    // Read the SQL file
    $sqlFile = __DIR__ . '/fix_exchange_rate_decimal.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Execute the SQL
    $result = $pdo->exec($sql);
    
    if ($result !== false) {
        echo "✅ Successfully modified office_exchange_rates table\n";
        echo "✅ Exchange rate percentage now supports values up to 1,000,000\n";
        echo "✅ Column type changed from DECIMAL(5,2) to DECIMAL(10,2)\n";
    } else {
        throw new Exception("Failed to execute SQL");
    }
    
    // Verify the change
    $stmt = $pdo->query("DESCRIBE office_exchange_rates exchange_rate_percentage");
    $columnInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "\n=== Verification ===\n";
    echo "Column: " . $columnInfo['Field'] . "\n";
    echo "Type: " . $columnInfo['Type'] . "\n";
    echo "Null: " . $columnInfo['Null'] . "\n";
    echo "Default: " . $columnInfo['Default'] . "\n";
    
    if (strpos($columnInfo['Type'], 'decimal(10,2)') !== false) {
        echo "✅ Column type successfully updated to DECIMAL(10,2)\n";
    } else {
        echo "❌ Column type not updated correctly\n";
    }
    
    echo "\n=== Test Insert ===\n";
    
    // Test inserting a large value
    $testValue = 50000.50;
    $stmt = $pdo->prepare("INSERT INTO office_exchange_rates (office_id, operation_name, exchange_rate_percentage, is_active) VALUES (1, 'TEST_LARGE_VALUE', ?, 1)");
    
    if ($stmt->execute([$testValue])) {
        echo "✅ Successfully inserted test value: $testValue\n";
        
        // Clean up test data
        $pdo->exec("DELETE FROM office_exchange_rates WHERE operation_name = 'TEST_LARGE_VALUE'");
        echo "✅ Test data cleaned up\n";
    } else {
        echo "❌ Failed to insert test value\n";
    }
    
    echo "\n=== Fix Complete ===\n";
    echo "The exchange rate percentage field now supports values from 1 to 1,000,000\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
} 
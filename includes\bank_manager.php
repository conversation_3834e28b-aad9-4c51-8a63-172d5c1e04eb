<?php
require_once __DIR__ . '/database.php';

/**
 * Class BankManager
 *
 * Provides CRUD operations for bank accounts and handles deposit/withdrawal movements.
 */
class BankManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Retrieve all bank accounts (optionally filtered by branch).
     */
    public function getAllBankAccounts(int $branchId = null): array
    {
        $sql = "SELECT ba.*,
                       cur.id AS currency_id,
                       cur.code AS currency_code,
                       cur.symbol AS currency_symbol,
                       b.name AS branch_name
                FROM bank_accounts ba
                LEFT JOIN currencies cur ON cur.code = ba.currency_code
                LEFT JOIN branches b ON b.id = ba.branch_id";
        if ($branchId !== null) {
            $sql .= ' WHERE ba.branch_id = ?';
        }
        $sql .= ' ORDER BY ba.account_name';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        if ($branchId !== null) {
            $stmt->bind_param('i', $branchId);
        }
        $stmt->execute();
        $res  = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Fetch single bank account by ID with detailed information including currency and branch details
     *
     * @param int $accId Bank account ID
     * @return array|null Bank account data with related information or null if not found
     */
    public function getBankAccountById(int $accId): ?array
    {
        $sql = 'SELECT ba.*, curr.name as currency_name, curr.code as currency_code,
                       curr.symbol as currency_symbol, b.name as branch_name
                FROM bank_accounts ba
                LEFT JOIN currencies curr ON curr.code = ba.currency_code
                LEFT JOIN branches b ON b.id = ba.branch_id
                WHERE ba.id = ? LIMIT 1';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return null;

        $stmt->bind_param('i', $accId);
        $stmt->execute();
        $res = $stmt->get_result();
        $acc = $res->fetch_assoc() ?: null;
        $stmt->close();

        return $acc;
    }

    /** Check if given IBAN exists (excluding certain ID) */
    public function bankAccountExists(string $iban, int $excludeId = null): bool
    {
        if ($iban === '') return false;
        $sql = 'SELECT COUNT(*) FROM bank_accounts WHERE iban = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($excludeId !== null) {
            $stmt->bind_param('si', $iban, $excludeId);
        } else {
            $stmt->bind_param('s', $iban);
        }
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    /** Add new bank account */
    public function addBankAccount(array $data)
    {
        $sql = 'INSERT INTO bank_accounts (account_name, bank_name, account_number, iban, swift_code, currency_code, initial_balance, current_balance, branch_id, is_active, created_at, updated_at)
                VALUES (?,?,?,?,?,?,?,?,?,?, NOW(), NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $isActive = isset($data['is_active']) ? (int)$data['is_active'] : 1;
        $stmt->bind_param(
            'ssssssddii',
            $data['account_name'],
            $data['bank_name'],
            $data['account_number'],
            $data['iban'],
            $data['swift_code'],
            $data['currency_code'],
            $data['initial_balance'],
            $data['initial_balance'], // current balance
            $data['branch_id'],
            $isActive
        );
        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }

    /** Update bank account */
    public function updateBankAccount(int $accId, array $data): bool
    {
        if (empty($data)) return false;
        $fields = [];
        $params = [];
        $types  = '';
        foreach ($data as $k => $v) {
            $fields[] = "$k = ?";
            $params[] = $v;
            $types   .= is_int($v) || is_float($v) ? 'd' : 's';
        }
        $sql = 'UPDATE bank_accounts SET ' . implode(',', $fields) . ', updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $types .= 'i';
        $params[] = $accId;
        $stmt->bind_param($types, ...$params);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /** Delete bank account */
    public function deleteBankAccount(int $accId): bool
    {
        $stmt = $this->db->prepare('DELETE FROM bank_accounts WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $accId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /** Activate/deactivate */
    public function updateBankAccountStatus(int $accId, bool $isActive): bool
    {
        $activeInt = $isActive ? 1 : 0;
        $stmt = $this->db->prepare('UPDATE bank_accounts SET is_active = ?, updated_at = NOW() WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('ii', $activeInt, $accId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /** Add deposit/withdrawal movement */
    public function addBankMovement(int $accId, string $type, float $amount, ?string $description, ?string $referenceNumber, int $userId): array
    {
        $type = strtolower($type);
        if (!in_array($type, ['deposit', 'withdrawal'], true)) {
            return ['success' => false, 'error' => 'Invalid movement type'];
        }
        $this->db->begin_transaction();
        try {
            // Lock row
            $stmt = $this->db->prepare('SELECT current_balance FROM bank_accounts WHERE id = ? FOR UPDATE');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('i', $accId);
            $stmt->execute();
            $stmt->bind_result($balance);
            if (!$stmt->fetch()) {
                $stmt->close();
                throw new Exception('Account not found');
            }
            $stmt->close();

            if ($type === 'withdrawal' && $balance < $amount) {
                $this->db->rollback();
                return ['success' => false, 'error' => 'Insufficient funds'];
            }

            $newBalance = $type === 'deposit' ? $balance + $amount : $balance - $amount;
            $stmt = $this->db->prepare('UPDATE bank_accounts SET current_balance = ?, updated_at = NOW() WHERE id = ?');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('di', $newBalance, $accId);
            if (!$stmt->execute()) throw new Exception('Balance update failed');
            $stmt->close();

            $stmt = $this->db->prepare('INSERT INTO bank_movements (bank_account_id, movement_type, amount, description, reference_number, user_id, created_at)
                                        VALUES (?,?,?,?,?,?, NOW())');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('isdssi', $accId, $type, $amount, $description, $referenceNumber, $userId);
            if (!$stmt->execute()) throw new Exception('Movement insert failed');
            $movId = $stmt->insert_id;
            $stmt->close();

            $this->db->commit();
            return ['success' => true, 'movement_id' => $movId];
        } catch (Exception $ex) {
            $this->db->rollback();
            return ['success' => false, 'error' => $ex->getMessage()];
        }
    }

    /**
     * Create a bank movement tied to an exchange. Does **NOT** manage its own
     * transaction – caller must have started one already.
     *
     * @param int    $accountId      Bank account ID affected
     * @param string $type           deposit|withdrawal
     * @param float  $amount         Amount in account currency
     * @param int    $currencyId     Currency of the movement (redundant)
     * @param int|null $exchangeId   Related exchange ID
     * @param int    $userId         User performing
     * @param string $description    Optional description
     * @return array{success:bool, movement_id?:int, error?:string}
     */
    public function addBankMovementForExchange(int $accountId, string $type, float $amount, ?int $exchangeId, int $userId, string $description = ''): array
    {
        $type = strtolower($type);
        if (!in_array($type, ['deposit', 'withdrawal'], true)) {
            return ['success' => false, 'error' => 'Invalid movement type'];
        }
        try {
            // Set shorter lock timeout and use NOWAIT to avoid long waits
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 3");

            // Lock account balance row with NOWAIT
            $stmt = $this->db->prepare('SELECT current_balance FROM bank_accounts WHERE id = ? FOR UPDATE NOWAIT');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('i', $accountId);
            $stmt->execute();
            $stmt->bind_result($balance);
            if (!$stmt->fetch()) {
                $stmt->close();
                throw new Exception('Bank account not found or locked');
            }
            $stmt->close();

            if ($type === 'withdrawal' && $balance < $amount) {
                return ['success' => false, 'error' => 'Insufficient funds'];
            }
            $newBalance = $type === 'deposit' ? $balance + $amount : $balance - $amount;

            // Update account balance
            $stmt = $this->db->prepare('UPDATE bank_accounts SET current_balance = ?, updated_at = NOW() WHERE id = ?');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('di', $newBalance, $accountId);
            if (!$stmt->execute()) throw new Exception('Balance update failed');
            $stmt->close();

            // Insert movement row
            $stmt = $this->db->prepare('INSERT INTO bank_movements (bank_account_id, movement_type, amount, description, reference_number, exchange_id, user_id, created_at)
                                        VALUES (?,?,?,?,?,?, ?, NOW())');
            if (!$stmt) throw new Exception('Prepare failed');
            $ref = null;
            $stmt->bind_param('isdssi', $accountId, $type, $amount, $description, $ref, $exchangeId, $userId);
            if (!$stmt->execute()) throw new Exception('Movement insert failed');
            $movementId = $stmt->insert_id;
            $stmt->close();

            // Reset lock timeout
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");
            return ['success' => true, 'movement_id' => $movementId];
        } catch (Exception $ex) {
            // Reset lock timeout on error
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");
            return ['success' => false, 'error' => $ex->getMessage()];
        }
    }

    /**
     * Create a bank movement tied to a transfer. Caller manages transaction.
     */
    public function addBankMovementForTransfer(int $accountId, string $type, float $amount, ?int $transferId, int $userId, string $description=''): array
    {
        $type = strtolower($type);
        if (!in_array($type,['deposit','withdrawal'],true)) {
            return ['success'=>false,'error'=>'Invalid movement type'];
        }
        try {
            $stmt = $this->db->prepare('SELECT current_balance FROM bank_accounts WHERE id = ? FOR UPDATE');
            if(!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('i',$accountId);
            $stmt->execute();
            $stmt->bind_result($balance);
            if(!$stmt->fetch()) { $stmt->close(); throw new Exception('Bank account not found'); }
            $stmt->close();
            if($type==='withdrawal' && $balance<$amount) {
                return ['success'=>false,'error'=>'Insufficient funds'];
            }
            $newBal = $type==='deposit' ? $balance+$amount : $balance-$amount;
            $stmt=$this->db->prepare('UPDATE bank_accounts SET current_balance = ?, updated_at = NOW() WHERE id = ?');
            if(!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('di',$newBal,$accountId);
            if(!$stmt->execute()) throw new Exception('Balance update failed');
            $stmt->close();
            $ref=null;
            $stmt=$this->db->prepare('INSERT INTO bank_movements (bank_account_id, movement_type, amount, description, reference_number, transfer_id, user_id, created_at) VALUES (?,?,?,?,?,?, ?, NOW())');
            if(!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('isdssi',$accountId,$type,$amount,$description,$ref,$transferId,$userId);
            if(!$stmt->execute()) throw new Exception('Insert failed');
            $mid=$stmt->insert_id;
            $stmt->close();
            return ['success'=>true,'movement_id'=>$mid];
        } catch(Exception $e) {
            return ['success'=>false,'error'=>$e->getMessage()];
        }
    }

    /** Retrieve movements for a bank account */
    public function getBankAccountMovements(int $accId, array $filters = []): array
    {
        $sql = 'SELECT bm.*, u.full_name AS user_name FROM bank_movements bm LEFT JOIN users u ON u.id = bm.user_id WHERE bm.bank_account_id = ?';
        $types = 'i';
        $params= [$accId];
        if (!empty($filters['from'])) {
            $sql .= ' AND bm.created_at >= ?';
            $types .= 's';
            $params[] = $filters['from'];
        }
        if (!empty($filters['to'])) {
            $sql .= ' AND bm.created_at <= ?';
            $types .= 's';
            $params[] = $filters['to'];
        }
        $sql .= ' ORDER BY bm.created_at DESC';

        // Add pagination if limit is specified
        if (!empty($filters['limit'])) {
            $sql .= ' LIMIT ?';
            $types .= 'i';
            $params[] = $filters['limit'];

            if (!empty($filters['offset'])) {
                $sql .= ' OFFSET ?';
                $types .= 'i';
                $params[] = $filters['offset'];
            }
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $res = $stmt->get_result();
        $rows= $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get count of bank account movements with optional filters
     *
     * @param int $accountId Bank account ID
     * @param array $filters Supported filters: date_from, date_to, type, from, to
     * @return int Total count of movements
     */
    public function getBankAccountMovementsCount(int $accountId, array $filters = []): int
    {
        $sql = 'SELECT COUNT(*) as count FROM bank_movements WHERE bank_account_id = ?';
        $types = 'i';
        $params = [$accountId];

        // Support both old and new filter formats
        $dateFrom = $filters['date_from'] ?? $filters['from'] ?? null;
        $dateTo = $filters['date_to'] ?? $filters['to'] ?? null;

        if (!empty($dateFrom)) {
            $sql .= ' AND DATE(created_at) >= ?';
            $types .= 's';
            $params[] = $dateFrom;
        }
        if (!empty($dateTo)) {
            $sql .= ' AND DATE(created_at) <= ?';
            $types .= 's';
            $params[] = $dateTo;
        }
        if (!empty($filters['type']) && in_array($filters['type'], ['deposit', 'withdrawal'])) {
            $sql .= ' AND movement_type = ?';
            $types .= 's';
            $params[] = $filters['type'];
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 0;

        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return (int)($row['count'] ?? 0);
    }



    /**
     * Get bank account summary statistics
     *
     * @param int $accountId Bank account ID
     * @param array $filters Optional filters for date range and type
     * @return array Summary statistics
     */
    public function getBankAccountSummary(int $accountId, array $filters = []): array
    {
        $sql = 'SELECT
                    SUM(CASE WHEN movement_type = "deposit" THEN amount ELSE 0 END) as total_deposits,
                    SUM(CASE WHEN movement_type = "withdrawal" THEN amount ELSE 0 END) as total_withdrawals,
                    COUNT(*) as movement_count
                FROM bank_movements
                WHERE bank_account_id = ?';
        $types = 'i';
        $params = [$accountId];

        // Support both old and new filter formats
        $dateFrom = $filters['date_from'] ?? $filters['from'] ?? null;
        $dateTo = $filters['date_to'] ?? $filters['to'] ?? null;

        if (!empty($dateFrom)) {
            $sql .= ' AND DATE(created_at) >= ?';
            $types .= 's';
            $params[] = $dateFrom;
        }
        if (!empty($dateTo)) {
            $sql .= ' AND DATE(created_at) <= ?';
            $types .= 's';
            $params[] = $dateTo;
        }
        if (!empty($filters['type']) && in_array($filters['type'], ['deposit', 'withdrawal'])) {
            $sql .= ' AND movement_type = ?';
            $types .= 's';
            $params[] = $filters['type'];
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return [
                'total_deposits' => 0,
                'total_withdrawals' => 0,
                'net_change' => 0,
                'movement_count' => 0
            ];
        }

        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        $totalDeposits = (float)($row['total_deposits'] ?? 0);
        $totalWithdrawals = (float)($row['total_withdrawals'] ?? 0);

        return [
            'total_deposits' => $totalDeposits,
            'total_withdrawals' => $totalWithdrawals,
            'net_change' => $totalDeposits - $totalWithdrawals,
            'movement_count' => (int)($row['movement_count'] ?? 0)
        ];
    }

    /**
     * Get a specific bank movement by ID
     *
     * @param int $movementId Movement ID
     * @return array|null Movement data or null if not found
     */
    public function getBankMovementById(int $movementId): ?array
    {
        $sql = 'SELECT bm.*, u.full_name AS user_name, ba.account_name, ba.account_number,
                       ba.bank_name, curr.code as currency_code, curr.symbol as currency_symbol,
                       bm.movement_type as type, ba.branch_id, b.name as branch_name
                FROM bank_movements bm
                LEFT JOIN users u ON u.id = bm.user_id
                LEFT JOIN bank_accounts ba ON ba.id = bm.bank_account_id
                LEFT JOIN currencies curr ON curr.code = ba.currency_code
                LEFT JOIN branches b ON b.id = ba.branch_id
                WHERE bm.id = ?';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return null;

        $stmt->bind_param('i', $movementId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return $row;
    }

    /**
     * Delete bank movement
     *
     * @param int $movementId Movement ID
     * @return bool Success status
     */
    public function deleteBankMovement(int $movementId): bool
    {
        try {
            $conn = Database::getConnection();
            $conn->begin_transaction();

            // Get movement details first
            $movement = $this->getBankMovementById($movementId);
            if (!$movement) {
                throw new Exception('Movement not found');
            }

            // Check if movement is linked to exchange or transfer
            if (!empty($movement['exchange_id']) || !empty($movement['transfer_id'])) {
                throw new Exception('Cannot delete movement linked to exchange or transfer');
            }

            $bankAccountId = $movement['bank_account_id'];
            $amount = $movement['amount'];
            $type = $movement['movement_type'];

            // Delete the movement
            $deleteSql = 'DELETE FROM bank_movements WHERE id = ?';
            $deleteStmt = $conn->prepare($deleteSql);
            if (!$deleteStmt) {
                throw new Exception('Failed to prepare delete statement');
            }

            $deleteStmt->bind_param('i', $movementId);
            if (!$deleteStmt->execute()) {
                throw new Exception('Failed to delete movement');
            }
            $deleteStmt->close();

            // Update bank account balance
            $balanceChange = $type === 'deposit' ? -$amount : $amount; // Reverse the movement
            $updateBalanceSql = 'UPDATE bank_accounts SET
                                   current_balance = current_balance + ?,
                                   updated_at = NOW()
                                 WHERE id = ?';

            $balanceStmt = $conn->prepare($updateBalanceSql);
            if (!$balanceStmt) {
                throw new Exception('Failed to prepare balance update statement');
            }

            $balanceStmt->bind_param('di', $balanceChange, $bankAccountId);
            if (!$balanceStmt->execute()) {
                throw new Exception('Failed to update bank account balance');
            }
            $balanceStmt->close();

            $conn->commit();
            return true;

        } catch (Exception $e) {
            $conn->rollback();
            error_log("Error deleting bank movement: " . $e->getMessage());
            return false;
        }
    }

}
<?php
/**
 * Create a test cash movement for testing edit functionality
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h2>إنشاء حركة مالية تجريبية للاختبار</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الصناديق المتاحة</h3>\n";
    
    // Check for available cash boxes
    $result = $conn->query("SELECT id, name, current_balance, currency_id FROM cash_boxes WHERE is_active = 1 LIMIT 1");
    
    if (!$result || $result->num_rows === 0) {
        echo "<p style='color: red;'>✗ لا توجد صناديق نشطة</p>\n";
        echo "<p>يرجى إنشاء صندوق نشط أولاً من <a href='../dashboard/cash.php' target='_blank'>صفحة الصناديق</a></p>\n";
        exit;
    }
    
    $cashBox = $result->fetch_assoc();
    echo "<p style='color: green;'>✓ صندوق متاح: {$cashBox['name']} (رصيد: {$cashBox['current_balance']})</p>\n";
    
    echo "<h3>2. إنشاء حركة مالية تجريبية</h3>\n";
    
    // Create a test movement
    $testAmount = 100.00;
    $testDescription = 'حركة تجريبية للاختبار - ' . date('Y-m-d H:i:s');
    $testReference = 'TEST_' . time();
    $testUserId = 1; // Assuming user ID 1 exists
    
    $result = $cashManager->addCashMovement(
        $cashBox['id'],
        'deposit',
        $testAmount,
        $testDescription,
        $testReference,
        $testUserId
    );
    
    if ($result['success']) {
        $movementId = $result['movement_id'];
        echo "<p style='color: green;'>✓ تم إنشاء حركة مالية تجريبية بنجاح</p>\n";
        echo "<p><strong>تفاصيل الحركة:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>رقم الحركة: $movementId</li>\n";
        echo "<li>نوع الحركة: إيداع</li>\n";
        echo "<li>المبلغ: $testAmount</li>\n";
        echo "<li>الوصف: $testDescription</li>\n";
        echo "<li>رقم المرجع: $testReference</li>\n";
        echo "</ul>\n";
        
        echo "<h3>3. اختبار جلب بيانات الحركة</h3>\n";
        
        $movementData = $cashManager->getCashMovementById($movementId);
        if ($movementData) {
            echo "<p style='color: green;'>✓ تم جلب بيانات الحركة بنجاح</p>\n";
            echo "<p>البيانات المسترجعة:</p>\n";
            echo "<ul>\n";
            echo "<li>رقم الحركة: {$movementData['id']}</li>\n";
            echo "<li>اسم الصندوق: " . ($movementData['cash_box_name'] ?? 'غير محدد') . "</li>\n";
            echo "<li>نوع الحركة: {$movementData['movement_type']}</li>\n";
            echo "<li>المبلغ: {$movementData['amount']}</li>\n";
            echo "<li>الوصف: {$movementData['description']}</li>\n";
            echo "<li>رقم المرجع: {$movementData['reference_number']}</li>\n";
            echo "<li>تاريخ الإنشاء: {$movementData['created_at']}</li>\n";
            echo "</ul>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في جلب بيانات الحركة</p>\n";
        }
        
        echo "<h3>4. اختبار صفحة التعديل</h3>\n";
        
        $editUrl = "../dashboard/edit_movement.php?id=$movementId";
        echo "<p><a href='$editUrl' target='_blank' style='color: #007bff; font-weight: bold; font-size: 18px;'>🔗 اختبر تعديل الحركة الآن</a></p>\n";
        
        echo "<h3>5. اختبار دالة updateCashMovement</h3>\n";
        
        // Test update function with sample data
        $updateData = [
            'movement_type' => 'deposit',
            'amount' => 150.00,
            'description' => 'حركة محدثة للاختبار - ' . date('Y-m-d H:i:s'),
            'reference_number' => 'UPDATED_' . time()
        ];
        
        // Calculate balance change (old: 100, new: 150, difference: +50)
        $balanceChange = 50.00;
        
        echo "<p><strong>بيانات التحديث التجريبية:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>المبلغ الجديد: {$updateData['amount']}</li>\n";
        echo "<li>الوصف الجديد: {$updateData['description']}</li>\n";
        echo "<li>رقم المرجع الجديد: {$updateData['reference_number']}</li>\n";
        echo "<li>تغيير الرصيد: +$balanceChange</li>\n";
        echo "</ul>\n";
        
        $updateResult = $cashManager->updateCashMovement($movementId, $updateData, $balanceChange);
        
        if ($updateResult) {
            echo "<p style='color: green;'>✓ تم تحديث الحركة بنجاح</p>\n";
            
            // Verify the update
            $updatedMovement = $cashManager->getCashMovementById($movementId);
            if ($updatedMovement) {
                echo "<p><strong>البيانات بعد التحديث:</strong></p>\n";
                echo "<ul>\n";
                echo "<li>المبلغ: {$updatedMovement['amount']}</li>\n";
                echo "<li>الوصف: {$updatedMovement['description']}</li>\n";
                echo "<li>رقم المرجع: {$updatedMovement['reference_number']}</li>\n";
                echo "</ul>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ فشل في تحديث الحركة</p>\n";
        }
        
        echo "<hr>\n";
        echo "<h3 style='color: green;'>✅ تم إنشاء واختبار الحركة المالية بنجاح!</h3>\n";
        echo "<p><strong>الخطوات التالية:</strong></p>\n";
        echo "<ul>\n";
        echo "<li><a href='$editUrl' target='_blank'>اختبر صفحة تعديل الحركة</a></li>\n";
        echo "<li><a href='../dashboard/cash_box_history.php?id={$cashBox['id']}' target='_blank'>عرض تاريخ الصندوق</a></li>\n";
        echo "<li><a href='../dashboard/cash.php' target='_blank'>العودة لصفحة الصناديق</a></li>\n";
        echo "</ul>\n";
        
    } else {
        echo "<p style='color: red;'>✗ فشل في إنشاء الحركة المالية: " . ($result['error'] ?? 'خطأ غير معروف') . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

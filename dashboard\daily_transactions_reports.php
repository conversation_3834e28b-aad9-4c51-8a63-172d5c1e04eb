<?php
/**
 * صفحة تقارير المعاملات اليومية
 * Daily Transactions Reports Page
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

// إنشاء كائن المصادقة والتحقق من تسجيل الدخول
$auth = new Auth();
$auth->requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = $auth->getCurrentUser();

// التحقق من الصلاحيات
if (!$auth->hasPermission('daily_transactions.view')) {
    header('Location: ' . BASE_URL . 'dashboard/index.php?error=no_permission');
    exit;
}

$pageTitle = 'تقارير المعاملات اليومية';

// معاملات التقرير
$report_params = [
    'date_from' => $_GET['date_from'] ?? date('Y-m-01'), // بداية الشهر الحالي
    'date_to' => $_GET['date_to'] ?? date('Y-m-d'), // اليوم الحالي
    'country_id' => $_GET['country_id'] ?? '',
    'delivery_type' => $_GET['delivery_type'] ?? '',
    'branch_id' => $_GET['branch_id'] ?? ''
];

// جلب الإحصائيات العامة
try {
    // بناء استعلام الإحصائيات
    $stats_sql = "SELECT 
        COUNT(*) as total_transactions,
        SUM(base_amount) as total_base_amount,
        SUM(calculated_amount) as total_calculated_amount,
        SUM(recipient_amount) as total_recipient_amount,
        AVG(base_amount) as avg_base_amount,
        AVG(customer_rate) as avg_customer_rate,
        AVG(exchange_rate) as avg_exchange_rate
    FROM daily_transactions dt
    LEFT JOIN countries c ON dt.country_id = c.id
    WHERE 1=1";
    
    $stats_params = [];
    
    if ($report_params['date_from']) {
        $stats_sql .= " AND DATE(dt.created_at) >= ?";
        $stats_params[] = $report_params['date_from'];
    }
    if ($report_params['date_to']) {
        $stats_sql .= " AND DATE(dt.created_at) <= ?";
        $stats_params[] = $report_params['date_to'];
    }
    if ($report_params['country_id']) {
        $stats_sql .= " AND dt.country_id = ?";
        $stats_params[] = $report_params['country_id'];
    }
    if ($report_params['delivery_type']) {
        $stats_sql .= " AND dt.delivery_type = ?";
        $stats_params[] = $report_params['delivery_type'];
    }
    if ($report_params['branch_id']) {
        $stats_sql .= " AND dt.branch_id = ?";
        $stats_params[] = $report_params['branch_id'];
    }
    
    $stats_stmt = $pdo->prepare($stats_sql);
    $stats_stmt->execute($stats_params);
    $general_stats = $stats_stmt->fetch();
    
} catch (Exception $e) {
    $general_stats = [
        'total_transactions' => 0,
        'total_base_amount' => 0,
        'total_calculated_amount' => 0,
        'total_recipient_amount' => 0,
        'avg_base_amount' => 0,
        'avg_customer_rate' => 0,
        'avg_exchange_rate' => 0
    ];
}

// جلب إحصائيات حسب نوع التسليم
try {
    $delivery_stats_sql = "SELECT
        delivery_type,
        COUNT(*) as count,
        SUM(base_amount) as total_amount,
        AVG(base_amount) as avg_amount
    FROM daily_transactions dt
    WHERE 1=1";

    $delivery_params = [];

    if ($report_params['date_from']) {
        $delivery_stats_sql .= " AND DATE(dt.created_at) >= ?";
        $delivery_params[] = $report_params['date_from'];
    }
    if ($report_params['date_to']) {
        $delivery_stats_sql .= " AND DATE(dt.created_at) <= ?";
        $delivery_params[] = $report_params['date_to'];
    }
    if ($report_params['country_id']) {
        $delivery_stats_sql .= " AND dt.country_id = ?";
        $delivery_params[] = $report_params['country_id'];
    }
    if ($report_params['delivery_type']) {
        $delivery_stats_sql .= " AND dt.delivery_type = ?";
        $delivery_params[] = $report_params['delivery_type'];
    }
    if ($report_params['branch_id']) {
        $delivery_stats_sql .= " AND dt.branch_id = ?";
        $delivery_params[] = $report_params['branch_id'];
    }

    $delivery_stats_sql .= " GROUP BY delivery_type ORDER BY count DESC";

    $delivery_stmt = $pdo->prepare($delivery_stats_sql);
    $delivery_stmt->execute($delivery_params);
    $delivery_stats = $delivery_stmt->fetchAll();

} catch (Exception $e) {
    $delivery_stats = [];
}

// جلب إحصائيات حسب الدولة
try {
    $country_stats_sql = "SELECT
        c.name_ar as country_name,
        c.currency_code,
        COUNT(*) as count,
        SUM(dt.base_amount) as total_amount,
        AVG(dt.base_amount) as avg_amount
    FROM daily_transactions dt
    LEFT JOIN countries c ON dt.country_id = c.id
    WHERE 1=1";

    $country_params = [];

    if ($report_params['date_from']) {
        $country_stats_sql .= " AND DATE(dt.created_at) >= ?";
        $country_params[] = $report_params['date_from'];
    }
    if ($report_params['date_to']) {
        $country_stats_sql .= " AND DATE(dt.created_at) <= ?";
        $country_params[] = $report_params['date_to'];
    }
    if ($report_params['country_id']) {
        $country_stats_sql .= " AND dt.country_id = ?";
        $country_params[] = $report_params['country_id'];
    }
    if ($report_params['delivery_type']) {
        $country_stats_sql .= " AND dt.delivery_type = ?";
        $country_params[] = $report_params['delivery_type'];
    }
    if ($report_params['branch_id']) {
        $country_stats_sql .= " AND dt.branch_id = ?";
        $country_params[] = $report_params['branch_id'];
    }

    $country_stats_sql .= " GROUP BY dt.country_id, c.name_ar, c.currency_code ORDER BY count DESC";

    $country_stmt = $pdo->prepare($country_stats_sql);
    $country_stmt->execute($country_params);
    $country_stats = $country_stmt->fetchAll();

} catch (Exception $e) {
    $country_stats = [];
}

// جلب إحصائيات يومية
try {
    $daily_stats_sql = "SELECT
        DATE(created_at) as transaction_date,
        COUNT(*) as count,
        SUM(base_amount) as total_amount
    FROM daily_transactions dt
    WHERE 1=1";

    $daily_params = [];

    if ($report_params['date_from']) {
        $daily_stats_sql .= " AND DATE(dt.created_at) >= ?";
        $daily_params[] = $report_params['date_from'];
    }
    if ($report_params['date_to']) {
        $daily_stats_sql .= " AND DATE(dt.created_at) <= ?";
        $daily_params[] = $report_params['date_to'];
    }
    if ($report_params['country_id']) {
        $daily_stats_sql .= " AND dt.country_id = ?";
        $daily_params[] = $report_params['country_id'];
    }
    if ($report_params['delivery_type']) {
        $daily_stats_sql .= " AND dt.delivery_type = ?";
        $daily_params[] = $report_params['delivery_type'];
    }
    if ($report_params['branch_id']) {
        $daily_stats_sql .= " AND dt.branch_id = ?";
        $daily_params[] = $report_params['branch_id'];
    }

    $daily_stats_sql .= " GROUP BY DATE(created_at) ORDER BY transaction_date DESC LIMIT 30";

    $daily_stmt = $pdo->prepare($daily_stats_sql);
    $daily_stmt->execute($daily_params);
    $daily_stats = $daily_stmt->fetchAll();

} catch (Exception $e) {
    $daily_stats = [];
}

// جلب قائمة الدول للفلترة
try {
    $countries_stmt = $pdo->query("SELECT id, name_ar, currency_code FROM countries WHERE is_active = 1 ORDER BY name_ar");
    $countries = $countries_stmt->fetchAll();
} catch (Exception $e) {
    $countries = [];
}

// جلب قائمة الفروع للفلترة
try {
    $branches_stmt = $pdo->query("SELECT id, name FROM branches WHERE is_active = 1 ORDER BY name");
    $branches = $branches_stmt->fetchAll();
} catch (Exception $e) {
    $branches = [];
}

require_once __DIR__ . '/../includes/header.php';
?>

<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-auto p-0">
                <?php require_once __DIR__ . '/../includes/sidebar.php'; ?>
            </div>

            <!-- Main Content -->
            <div class="col">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h3 mb-0">
                            <i class="fas fa-chart-bar text-primary me-2"></i>
                            تقارير المعاملات اليومية
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php">المعاملات اليومية</a></li>
                                <li class="breadcrumb-item active">التقارير</li>
                            </ol>
                        </nav>
                    </div>

                    <!-- فلاتر التقرير -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-filter me-2"></i>
                                فلاتر التقرير
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <!-- الفترة الزمنية -->
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo htmlspecialchars($report_params['date_from']); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo htmlspecialchars($report_params['date_to']); ?>">
                                </div>

                                <!-- الدولة -->
                                <div class="col-md-2">
                                    <label for="country_id" class="form-label">الدولة</label>
                                    <select class="form-select" id="country_id" name="country_id">
                                        <option value="">جميع الدول</option>
                                        <?php foreach ($countries as $country): ?>
                                            <option value="<?php echo $country['id']; ?>" 
                                                    <?php echo ($report_params['country_id'] == $country['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($country['name_ar']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- نوع التسليم -->
                                <div class="col-md-2">
                                    <label for="delivery_type" class="form-label">نوع التسليم</label>
                                    <select class="form-select" id="delivery_type" name="delivery_type">
                                        <option value="">جميع الأنواع</option>
                                        <option value="cash" <?php echo ($report_params['delivery_type'] == 'cash') ? 'selected' : ''; ?>>كاش</option>
                                        <option value="bank" <?php echo ($report_params['delivery_type'] == 'bank') ? 'selected' : ''; ?>>بنكي</option>
                                        <option value="usdt" <?php echo ($report_params['delivery_type'] == 'usdt') ? 'selected' : ''; ?>>USDT</option>
                                    </select>
                                </div>

                                <!-- أزرار -->
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>
                                            تطبيق الفلاتر
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- البطاقات الإحصائية العامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($general_stats['total_transactions']); ?></h4>
                                            <p class="mb-0">إجمالي المعاملات</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-list-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($general_stats['total_base_amount'], 2); ?></h4>
                                            <p class="mb-0">إجمالي المبلغ الأساسي</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-money-bill fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($general_stats['total_recipient_amount']); ?></h4>
                                            <p class="mb-0">إجمالي المبلغ للمستلم</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-hand-holding-usd fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($general_stats['avg_base_amount'], 2); ?></h4>
                                            <p class="mb-0">متوسط المبلغ</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calculator fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول صافي الأرباح -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-coins me-2"></i>
                                صافي الأرباح (المبلغ الناتج للمكتب - الناتج للزبون)
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php
                            // حساب مجموع المبلغ الناتج للمكتب والناتج للزبون
                            $stmt = $pdo->prepare("SELECT SUM(office_amount) as total_office, SUM(calculated_amount) as total_customer FROM daily_transactions WHERE DATE(created_at) >= ? AND DATE(created_at) <= ?" .
                                ($report_params['country_id'] ? " AND country_id = " . intval($report_params['country_id']) : '') .
                                ($report_params['delivery_type'] ? " AND delivery_type = '" . $report_params['delivery_type'] . "'" : '') .
                                ($report_params['branch_id'] ? " AND branch_id = " . intval($report_params['branch_id']) : ''));
                            $stmt->execute([$report_params['date_from'], $report_params['date_to']]);
                            $profit_row = $stmt->fetch();
                            $total_office = $profit_row['total_office'] ?? 0;
                            $total_customer = $profit_row['total_customer'] ?? 0;
                            $net_profit = $total_office - $total_customer;
                            ?>
                            <div class="table-responsive">
                                <table class="table table-bordered text-center mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>المبلغ الناتج للمكتب</th>
                                            <th>المبلغ الناتج للزبون</th>
                                            <th>صافي الأرباح</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="fw-bold text-info"><?php echo number_format($total_office, 2); ?></td>
                                            <td class="fw-bold text-info"><?php echo number_format($total_customer, 2); ?></td>
                                            <td class="fw-bold text-success"><?php echo number_format($net_profit, 2); ?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <?php if ($total_office == 0 && $total_customer == 0): ?>
                                <p class="text-muted text-center mt-3">لا توجد بيانات لصافي الأرباح في الفترة المحددة.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row">
                        <!-- إحصائيات نوع التسليم (جدول جديد) -->
                        <div class="col-md-6 mb-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-truck me-2"></i>
                                        إحصائيات نوع التسليم
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($delivery_stats)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-bordered text-center mb-0">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>نوع التسليم</th>
                                                        <th>عدد المعاملات</th>
                                                        <th>إجمالي المبلغ الأساسي</th>
                                                        <th>متوسط المبلغ</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($delivery_stats as $stat): ?>
                                                        <?php
                                                        $delivery_names = [
                                                            'cash' => 'كاش',
                                                            'bank' => 'بنكي',
                                                            'usdt' => 'USDT'
                                                        ];
                                                        $badge = $stat['delivery_type'] == 'cash' ? 'bg-success' : ($stat['delivery_type'] == 'bank' ? 'bg-primary' : 'bg-warning');
                                                        ?>
                                                        <tr>
                                                            <td><span class="badge <?php echo $badge; ?>"> <?php echo $delivery_names[$stat['delivery_type']] ?? $stat['delivery_type']; ?> </span></td>
                                                            <td><span class="fw-bold text-info"><?php echo number_format($stat['count']); ?></span></td>
                                                            <td><span class="fw-bold text-success"><?php echo number_format($stat['total_amount'], 2); ?></span></td>
                                                            <td><span class="fw-bold text-muted"><?php echo number_format($stat['avg_amount'], 2); ?></span></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted text-center">لا توجد بيانات للعرض</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات الدول -->
                        <div class="col-md-6 mb-4">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-globe me-2"></i>
                                        إحصائيات الدول
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($country_stats)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>الدولة</th>
                                                        <th>العدد</th>
                                                        <th>إجمالي المبلغ</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach (array_slice($country_stats, 0, 10) as $stat): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($stat['country_name']); ?></td>
                                                            <td><span class="badge bg-info"><?php echo number_format($stat['count']); ?></span></td>
                                                            <td><?php echo number_format($stat['total_amount'], 2); ?></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted text-center">لا توجد بيانات للعرض</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإحصائيات اليومية -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-calendar-day me-2"></i>
                                الإحصائيات اليومية (آخر 30 يوم)
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($daily_stats)): ?>
                                <canvas id="dailyChart" width="400" height="150"></canvas>
                            <?php else: ?>
                                <p class="text-muted text-center">لا توجد بيانات للعرض</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- أزرار التصدير -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-download me-2"></i>
                                تصدير التقارير
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> سيتم تصدير البيانات حسب الفلاتر المطبقة أعلاه.
                                <?php if ($general_stats['total_transactions'] > 0): ?>
                                    سيتم تصدير <strong><?php echo number_format($general_stats['total_transactions']); ?></strong> معاملة.
                                <?php endif; ?>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <a href="export_daily_transactions.php?<?php echo http_build_query($report_params); ?>&format=excel"
                                       class="btn btn-success btn-lg w-100" target="_blank">
                                        <i class="fas fa-file-excel me-2"></i>
                                        تصدير إلى Excel
                                        <small class="d-block">ملف .xls للتحليل</small>
                                    </a>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <a href="export_daily_transactions.php?<?php echo http_build_query($report_params); ?>&format=pdf"
                                       class="btn btn-danger btn-lg w-100" target="_blank">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        تصدير إلى PDF
                                        <small class="d-block">تقرير جاهز للطباعة</small>
                                    </a>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <a href="export_daily_transactions.php?<?php echo http_build_query($report_params); ?>&format=csv"
                                       class="btn btn-info btn-lg w-100" target="_blank">
                                        <i class="fas fa-file-csv me-2"></i>
                                        تصدير إلى CSV
                                        <small class="d-block">بيانات خام للاستيراد</small>
                                    </a>
                                </div>
                            </div>

                            <hr>

                            <div class="text-center">
                                <a href="daily_transactions.php<?php echo !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : ''; ?>"
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>
                                    عرض المعاملات التفصيلية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // رسم بياني للإحصائيات اليومية
        <?php if (!empty($daily_stats)): ?>
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        const dailyChart = new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: [
                    <?php foreach (array_reverse($daily_stats) as $stat): ?>
                        '<?php echo date('m/d', strtotime($stat['transaction_date'])); ?>',
                    <?php endforeach; ?>
                ],
                datasets: [{
                    label: 'عدد المعاملات',
                    data: [
                        <?php foreach (array_reverse($daily_stats) as $stat): ?>
                            <?php echo $stat['count']; ?>,
                        <?php endforeach; ?>
                    ],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        <?php endif; ?>

        // تحديث تلقائي للتواريخ
        document.addEventListener('DOMContentLoaded', function() {
            const dateFromInput = document.getElementById('date_from');
            const dateToInput = document.getElementById('date_to');

            // إضافة أزرار سريعة للفترات
            const quickFilters = document.createElement('div');
            quickFilters.className = 'mt-2';
            quickFilters.innerHTML = `
                <small class="text-muted">فترات سريعة:</small>
                <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="setDateRange('today')">اليوم</button>
                <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="setDateRange('week')">هذا الأسبوع</button>
                <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="setDateRange('month')">هذا الشهر</button>
                <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="setDateRange('year')">هذا العام</button>
            `;

            dateToInput.parentNode.appendChild(quickFilters);
        });

        function setDateRange(period) {
            const today = new Date();
            const dateFrom = document.getElementById('date_from');
            const dateTo = document.getElementById('date_to');

            switch(period) {
                case 'today':
                    dateFrom.value = today.toISOString().split('T')[0];
                    dateTo.value = today.toISOString().split('T')[0];
                    break;
                case 'week':
                    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                    dateFrom.value = weekStart.toISOString().split('T')[0];
                    dateTo.value = new Date().toISOString().split('T')[0];
                    break;
                case 'month':
                    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                    dateFrom.value = monthStart.toISOString().split('T')[0];
                    dateTo.value = new Date().toISOString().split('T')[0];
                    break;
                case 'year':
                    const yearStart = new Date(today.getFullYear(), 0, 1);
                    dateFrom.value = yearStart.toISOString().split('T')[0];
                    dateTo.value = new Date().toISOString().split('T')[0];
                    break;
            }
        }
    </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

<?php
/**
 * Create Customer Activity System
 * إنشاء نظام سجل العمليات للعملاء
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إنشاء نظام سجل العمليات للعملاء</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. إنشاء جدول سجل أنشطة العملاء</h3>\n";
    
    // Create customer activity log table
    $createActivityLogSql = "
    CREATE TABLE IF NOT EXISTS customer_activity_log (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        customer_id INT UNSIGNED NOT NULL,
        activity_type ENUM(
            'created','updated','status_changed','document_uploaded','document_verified',
            'risk_level_changed','contact_updated','address_updated','viewed','deleted',
            'transfer_created','transfer_received','kyc_updated','notes_added',
            'verification_requested','verification_completed','account_locked','account_unlocked'
        ) NOT NULL,
        description TEXT NOT NULL,
        old_value JSON NULL,
        new_value JSON NULL,
        user_id INT UNSIGNED NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        location VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_cal_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_cal_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
        INDEX idx_customer_activity (customer_id, created_at),
        INDEX idx_activity_type (activity_type),
        INDEX idx_user_activity (user_id, created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($createActivityLogSql)) {
        echo "<p style='color: green;'>✓ تم إنشاء جدول customer_activity_log</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ جدول customer_activity_log موجود بالفعل أو حدث خطأ: " . $conn->error . "</p>\n";
    }
    
    echo "<h3>2. إنشاء جدول مستندات العملاء</h3>\n";
    
    // Create customer documents table
    $createDocumentsSql = "
    CREATE TABLE IF NOT EXISTS customer_documents (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        customer_id INT UNSIGNED NOT NULL,
        document_type ENUM('id_card','passport','driving_license','utility_bill','bank_statement','other') NOT NULL,
        document_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT UNSIGNED NULL,
        mime_type VARCHAR(100) NULL,
        verification_status ENUM('pending','verified','rejected') DEFAULT 'pending',
        verified_by INT UNSIGNED NULL,
        verified_at TIMESTAMP NULL,
        verification_notes TEXT NULL,
        uploaded_by INT UNSIGNED NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT fk_cd_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_cd_verified_by FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
        CONSTRAINT fk_cd_uploaded_by FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
        INDEX idx_customer_documents (customer_id),
        INDEX idx_document_type (document_type),
        INDEX idx_verification_status (verification_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($createDocumentsSql)) {
        echo "<p style='color: green;'>✓ تم إنشاء جدول customer_documents</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ جدول customer_documents موجود بالفعل أو حدث خطأ: " . $conn->error . "</p>\n";
    }
    
    echo "<h3>3. إضافة أعمدة جديدة لجدول العملاء</h3>\n";
    
    // Check customers table structure
    $customersResult = $conn->query("DESCRIBE customers");
    $customersColumns = [];
    if ($customersResult) {
        while ($row = $customersResult->fetch_assoc()) {
            $customersColumns[] = $row['Field'];
        }
    }
    
    // Add new columns to customers table
    $newCustomerColumns = [
        'verification_status' => "ENUM('pending','partial','verified','rejected') DEFAULT 'pending'",
        'kyc_status' => "ENUM('not_started','in_progress','completed','rejected') DEFAULT 'not_started'",
        'last_activity_at' => "TIMESTAMP NULL",
        'notes' => "TEXT NULL",
        'created_by' => "INT UNSIGNED NULL",
        'updated_by' => "INT UNSIGNED NULL"
    ];
    
    foreach ($newCustomerColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $customersColumns)) {
            $sql = "ALTER TABLE customers ADD COLUMN $columnName $columnDefinition";
            if ($conn->query($sql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود $columnName إلى جدول customers</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عمود $columnName: " . $conn->error . "</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>ℹ عمود $columnName موجود بالفعل في جدول customers</p>\n";
        }
    }
    
    echo "<h3>4. إنشاء بيانات تجريبية</h3>\n";
    
    // Get sample customers
    $customersResult = $conn->query("SELECT id, full_name FROM customers LIMIT 3");
    if ($customersResult && $customersResult->num_rows > 0) {
        while ($customer = $customersResult->fetch_assoc()) {
            $customerId = $customer['id'];
            $customerName = $customer['full_name'];
            
            // Insert sample activities
            $sampleActivities = [
                [
                    'activity_type' => 'created',
                    'description' => 'تم إنشاء حساب العميل',
                    'new_value' => json_encode(['customer_name' => $customerName, 'status' => 'active'], JSON_UNESCAPED_UNICODE)
                ],
                [
                    'activity_type' => 'document_uploaded',
                    'description' => 'تم رفع مستند الهوية',
                    'new_value' => json_encode(['document_type' => 'id_card', 'status' => 'pending'], JSON_UNESCAPED_UNICODE)
                ],
                [
                    'activity_type' => 'verification_requested',
                    'description' => 'تم طلب التحقق من الهوية',
                    'new_value' => json_encode(['verification_status' => 'pending'], JSON_UNESCAPED_UNICODE)
                ],
                [
                    'activity_type' => 'kyc_updated',
                    'description' => 'تم تحديث حالة KYC',
                    'old_value' => json_encode(['kyc_status' => 'not_started'], JSON_UNESCAPED_UNICODE),
                    'new_value' => json_encode(['kyc_status' => 'in_progress'], JSON_UNESCAPED_UNICODE)
                ]
            ];
            
            foreach ($sampleActivities as $activity) {
                $stmt = $conn->prepare("
                    INSERT INTO customer_activity_log (
                        customer_id, activity_type, description, old_value, new_value, user_id, ip_address, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $userId = 1;
                $ip = '***********';
                $stmt->bind_param('issssis', 
                    $customerId, 
                    $activity['activity_type'], 
                    $activity['description'],
                    $activity['old_value'] ?? null,
                    $activity['new_value'],
                    $userId,
                    $ip
                );
                
                if ($stmt->execute()) {
                    echo "<p style='color: green;'>✓ تم إضافة نشاط للعميل $customerName: {$activity['description']}</p>\n";
                }
                $stmt->close();
            }
            
            // Insert sample documents
            $sampleDocuments = [
                [
                    'document_type' => 'id_card',
                    'document_name' => 'بطاقة الهوية الوطنية',
                    'file_path' => '/uploads/documents/id_card_' . $customerId . '.pdf',
                    'verification_status' => 'pending'
                ],
                [
                    'document_type' => 'utility_bill',
                    'document_name' => 'فاتورة كهرباء',
                    'file_path' => '/uploads/documents/utility_' . $customerId . '.pdf',
                    'verification_status' => 'verified'
                ]
            ];
            
            foreach ($sampleDocuments as $doc) {
                $stmt = $conn->prepare("
                    INSERT INTO customer_documents (
                        customer_id, document_type, document_name, file_path, verification_status, uploaded_by, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $uploadedBy = 1;
                $stmt->bind_param('issssi', 
                    $customerId, 
                    $doc['document_type'], 
                    $doc['document_name'],
                    $doc['file_path'],
                    $doc['verification_status'],
                    $uploadedBy
                );
                
                if ($stmt->execute()) {
                    echo "<p style='color: green;'>✓ تم إضافة مستند للعميل $customerName: {$doc['document_name']}</p>\n";
                }
                $stmt->close();
            }
        }
    }
    
    echo "<h3>5. إنشاء stored procedures للعملاء</h3>\n";
    
    // Create stored procedures
    $proceduresSql = [
        "DROP PROCEDURE IF EXISTS LogCustomerActivity",
        "
        CREATE PROCEDURE LogCustomerActivity(
            IN p_customer_id INT UNSIGNED,
            IN p_activity_type VARCHAR(50),
            IN p_description TEXT,
            IN p_old_value JSON,
            IN p_new_value JSON,
            IN p_user_id INT UNSIGNED,
            IN p_ip_address VARCHAR(45)
        )
        BEGIN
            INSERT INTO customer_activity_log (
                customer_id, activity_type, description, old_value, new_value,
                user_id, ip_address, created_at
            ) VALUES (
                p_customer_id, p_activity_type, p_description, p_old_value, p_new_value,
                p_user_id, p_ip_address, NOW()
            );
            
            -- Update last activity timestamp
            UPDATE customers SET last_activity_at = NOW() WHERE id = p_customer_id;
        END
        "
    ];
    
    foreach ($proceduresSql as $sql) {
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ تم إنشاء stored procedure للعملاء</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ خطأ في إنشاء stored procedure: " . $conn->error . "</p>\n";
        }
    }
    
    echo "<h3>6. التحقق من النتائج</h3>\n";
    
    // Verify results
    $tables = [
        'customers' => 'العملاء',
        'customer_activity_log' => 'سجل أنشطة العملاء',
        'customer_documents' => 'مستندات العملاء'
    ];
    
    foreach ($tables as $table => $description) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p><strong>جدول $description:</strong> $count سجل</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إنشاء نظام سجل العمليات للعملاء بنجاح!</h3>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 الميزات الجديدة:</h4>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li>✅ سجل شامل لأنشطة العملاء</li>\n";
    echo "<li>✅ نظام إدارة المستندات</li>\n";
    echo "<li>✅ تتبع حالات التحقق</li>\n";
    echo "<li>✅ سجل KYC والامتثال</li>\n";
    echo "<li>✅ تسجيل تلقائي للعمليات</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4>🔗 الخطوات التالية:</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='create_enhanced_customer_manager.php' target='_blank'>إنشاء CustomerManager محسن</a></li>\n";
    echo "<li><a href='update_customers_page.php' target='_blank'>تحديث صفحة العملاء</a></li>\n";
    echo "<li><a href='../dashboard/customers.php' target='_blank'>اختبار الصفحة المحدثة</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

<?php
require_once __DIR__ . '/database.php';

/**
 * Class CashManager
 *
 * Provides CRUD operations for cash boxes and handles deposit/withdrawal movements.
 */
class CashManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        // Obtain MySQLi connection from our Database helper
        $this->db = $database::getConnection();
    }

    /**
     * Retrieve all cash boxes (optionally filtered by branch).
     *
     * @param int|null $branchId
     * @return array<int, array<string, mixed>>
     */
    public function getAllCashBoxes(int $branchId = null): array
    {
        $sql = "SELECT cb.*,
                       cur.id          AS currency_id,
                       cur.code        AS currency_code,
                       cur.symbol      AS currency_symbol,
                       b.name          AS branch_name,
                       u.full_name     AS responsible_name
                FROM cash_boxes cb
                LEFT JOIN currencies cur ON cur.code = cb.currency_code
                LEFT JOIN branches   b   ON b.id   = cb.branch_id
                LEFT JOIN users      u   ON u.id   = cb.responsible_user_id";
        if ($branchId !== null) {
            $sql .= " WHERE cb.branch_id = ?";
        }
        $sql .= " ORDER BY cb.name";

        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return [];
        }
        if ($branchId !== null) {
            $stmt->bind_param('i', $branchId);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        $boxes  = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $boxes;
    }

    /**
     * Retrieve single cash box by ID with detailed information including currency and branch details.
     *
     * @param int $boxId Cash box ID
     * @return array|null Cash box data with related information or null if not found
     */
    public function getCashBoxById(int $boxId): ?array
    {
        $sql = 'SELECT cb.*, curr.name as currency_name, curr.code as currency_code,
                       curr.symbol as currency_symbol, b.name as branch_name,
                       u.full_name as responsible_name
                FROM cash_boxes cb
                LEFT JOIN currencies curr ON curr.code = cb.currency_code
                LEFT JOIN branches b ON b.id = cb.branch_id
                LEFT JOIN users u ON u.id = cb.responsible_user_id
                WHERE cb.id = ? LIMIT 1';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return null;

        $stmt->bind_param('i', $boxId);
        $stmt->execute();
        $res = $stmt->get_result();
        $box = $res->fetch_assoc() ?: null;
        $stmt->close();

        return $box;
    }

    /**
     * Add a new cash box.
     * Returns the new record ID on success or false on failure.
     */
    public function addCashBox(array $data)
    {
        $sql = 'INSERT INTO cash_boxes (name, currency_code, initial_balance, current_balance, branch_id, responsible_user_id, is_active, created_at, updated_at)
                VALUES (?,?,?,?,?,?,?, NOW(), NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $isActive = isset($data['is_active']) ? (int) $data['is_active'] : 1;
        $stmt->bind_param(
            'ssddiis',
            $data['name'],
            $data['currency_code'],
            $data['initial_balance'],
            $data['initial_balance'], // current_balance equals initial on creation
            $data['branch_id'],
            $data['responsible_user_id'],
            $isActive
        );
        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }

    /**
     * Update existing cash box (does NOT affect balances).
     */
    public function updateCashBox(int $boxId, array $data): bool
    {
        if (empty($data)) return false;
        $fields = [];
        $params = [];
        $types  = '';

        foreach ($data as $k => $v) {
            $fields[] = "$k = ?";
            $params[] = $v;
            // Determine bind type dynamically
            $types .= is_int($v) || is_float($v) ? 'd' : 's';
        }
        $sql = 'UPDATE cash_boxes SET ' . implode(',', $fields) . ', updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $types .= 'i';
        $params[] = $boxId;
        $stmt->bind_param($types, ...$params);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Delete cash box by ID.
     */
    public function deleteCashBox(int $boxId): bool
    {
        $stmt = $this->db->prepare('DELETE FROM cash_boxes WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $boxId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Activate / deactivate cash box.
     */
    public function updateCashBoxStatus(int $boxId, bool $isActive): bool
    {
        $activeInt = $isActive ? 1 : 0;
        $stmt = $this->db->prepare('UPDATE cash_boxes SET is_active = ?, updated_at = NOW() WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('ii', $activeInt, $boxId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Record manual deposit / withdrawal.
     * Performs balance update atomically.
     *
     * @return array{success:bool, movement_id?:int, error?:string}
     */
    public function addCashMovement(int $boxId, string $type, float $amount, ?string $description, ?string $referenceNumber, int $userId): array
    {
        $type = strtolower($type);
        if (!in_array($type, ['deposit', 'withdrawal'], true)) {
            return ['success' => false, 'error' => 'Invalid movement type'];
        }

        $this->db->begin_transaction();
        try {
            // Lock cash box row for update to avoid race conditions
            $stmt = $this->db->prepare('SELECT current_balance FROM cash_boxes WHERE id = ? FOR UPDATE');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('i', $boxId);
            $stmt->execute();
            $stmt->bind_result($balance);
            if (!$stmt->fetch()) {
                $stmt->close();
                throw new Exception('Cash box not found');
            }
            $stmt->close();

            if ($type === 'withdrawal' && $balance < $amount) {
                $this->db->rollback();
                return ['success' => false, 'error' => 'Insufficient funds'];
            }

            $newBalance = $type === 'deposit' ? $balance + $amount : $balance - $amount;

            // Update balance
            $stmt = $this->db->prepare('UPDATE cash_boxes SET current_balance = ?, updated_at = NOW() WHERE id = ?');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('di', $newBalance, $boxId);
            if (!$stmt->execute()) throw new Exception('Balance update failed');
            $stmt->close();

            // Insert movement row
            $stmt = $this->db->prepare('INSERT INTO cash_movements (cash_box_id, movement_type, amount, description, reference_number, user_id, created_at)
                                        VALUES (?,?,?,?,?,?, NOW())');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('isdssi', $boxId, $type, $amount, $description, $referenceNumber, $userId);
            if (!$stmt->execute()) throw new Exception('Movement insert failed');
            $movementId = $stmt->insert_id;
            $stmt->close();

            $this->db->commit();
            return ['success' => true, 'movement_id' => $movementId];
        } catch (Exception $ex) {
            $this->db->rollback();
            return ['success' => false, 'error' => $ex->getMessage()];
        }
    }

    /**
     * Fetch movements related to a given cash box with pagination support
     *
     * @param int $boxId Cash box ID
     * @param array $filters Supported filters: date_from, date_to, type, from, to (for backward compatibility)
     * @param int $limit Number of records to return (0 = no limit)
     * @param int $offset Starting offset
     * @return array<int, array<string, mixed>>
     */
    public function getCashBoxMovements(int $boxId, array $filters = [], int $limit = 0, int $offset = 0): array
    {
        $sql = 'SELECT cm.*, u.full_name AS user_name, cb.name as cash_box_name,
                       curr.code as currency_code, curr.symbol as currency_symbol,
                       cm.movement_type as type, cm.exchange_id, cm.transfer_id,
                       (
                           SELECT
                               cb.initial_balance +
                               COALESCE(SUM(CASE
                                   WHEN cm2.movement_type = "deposit" THEN cm2.amount
                                   ELSE -cm2.amount
                               END), 0)
                           FROM cash_movements cm2
                           WHERE cm2.cash_box_id = cm.cash_box_id
                           AND (cm2.created_at < cm.created_at OR (cm2.created_at = cm.created_at AND cm2.id <= cm.id))
                       ) as balance_after
                FROM cash_movements cm
                LEFT JOIN users u ON u.id = cm.user_id
                LEFT JOIN cash_boxes cb ON cb.id = cm.cash_box_id
                LEFT JOIN currencies curr ON curr.code = cb.currency_code
                WHERE cm.cash_box_id = ?';
        $types = 'i';
        $params = [$boxId];

        // Support both old and new filter formats
        $dateFrom = $filters['date_from'] ?? $filters['from'] ?? null;
        $dateTo = $filters['date_to'] ?? $filters['to'] ?? null;

        if (!empty($dateFrom)) {
            $sql .= ' AND DATE(cm.created_at) >= ?';
            $types .= 's';
            $params[] = $dateFrom;
        }
        if (!empty($dateTo)) {
            $sql .= ' AND DATE(cm.created_at) <= ?';
            $types .= 's';
            $params[] = $dateTo;
        }
        if (!empty($filters['type']) && in_array($filters['type'], ['deposit', 'withdrawal'])) {
            $sql .= ' AND cm.movement_type = ?';
            $types .= 's';
            $params[] = $filters['type'];
        }
        if (!empty($filters['operation_type'])) {
            switch ($filters['operation_type']) {
                case 'exchange':
                    $sql .= ' AND cm.exchange_id IS NOT NULL';
                    break;
                case 'transfer':
                    $sql .= ' AND cm.transfer_id IS NOT NULL';
                    break;
                case 'normal':
                    $sql .= ' AND cm.exchange_id IS NULL AND cm.transfer_id IS NULL';
                    break;
            }
        }

        $sql .= ' ORDER BY cm.created_at DESC';

        // Add pagination if limit is specified
        if ($limit > 0) {
            $sql .= ' LIMIT ? OFFSET ?';
            $types .= 'ii';
            $params[] = $limit;
            $params[] = $offset;
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];

        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $rows = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get count of cash box movements with optional filters
     *
     * @param int $boxId Cash box ID
     * @param array $filters Supported filters: date_from, date_to, type
     * @return int Total count of movements
     */
    public function getCashBoxMovementsCount(int $boxId, array $filters = []): int
    {
        $sql = 'SELECT COUNT(*) as count FROM cash_movements WHERE cash_box_id = ?';
        $types = 'i';
        $params = [$boxId];

        // Apply date filters
        if (!empty($filters['date_from'])) {
            $sql .= ' AND DATE(created_at) >= ?';
            $types .= 's';
            $params[] = $filters['date_from'];
        }
        if (!empty($filters['date_to'])) {
            $sql .= ' AND DATE(created_at) <= ?';
            $types .= 's';
            $params[] = $filters['date_to'];
        }
        if (!empty($filters['type']) && in_array($filters['type'], ['deposit', 'withdrawal'])) {
            $sql .= ' AND movement_type = ?';
            $types .= 's';
            $params[] = $filters['type'];
        }
        if (!empty($filters['operation_type'])) {
            switch ($filters['operation_type']) {
                case 'exchange':
                    $sql .= ' AND exchange_id IS NOT NULL';
                    break;
                case 'transfer':
                    $sql .= ' AND transfer_id IS NOT NULL';
                    break;
                case 'normal':
                    $sql .= ' AND exchange_id IS NULL AND transfer_id IS NULL';
                    break;
            }
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 0;

        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return (int)($row['count'] ?? 0);
    }



    /**
     * Get cash box summary statistics
     *
     * @param int $boxId Cash box ID
     * @param array $filters Optional filters for date range and type
     * @return array Summary statistics
     */
    public function getCashBoxSummary(int $boxId, array $filters = []): array
    {
        $sql = 'SELECT
                    SUM(CASE WHEN movement_type = "deposit" THEN amount ELSE 0 END) as total_deposits,
                    SUM(CASE WHEN movement_type = "withdrawal" THEN amount ELSE 0 END) as total_withdrawals,
                    COUNT(*) as movement_count
                FROM cash_movements
                WHERE cash_box_id = ?';
        $types = 'i';
        $params = [$boxId];

        // Apply date filters
        if (!empty($filters['date_from'])) {
            $sql .= ' AND DATE(created_at) >= ?';
            $types .= 's';
            $params[] = $filters['date_from'];
        }
        if (!empty($filters['date_to'])) {
            $sql .= ' AND DATE(created_at) <= ?';
            $types .= 's';
            $params[] = $filters['date_to'];
        }
        if (!empty($filters['type']) && in_array($filters['type'], ['deposit', 'withdrawal'])) {
            $sql .= ' AND movement_type = ?';
            $types .= 's';
            $params[] = $filters['type'];
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return [
                'total_deposits' => 0,
                'total_withdrawals' => 0,
                'net_change' => 0,
                'movement_count' => 0
            ];
        }

        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        $totalDeposits = (float)($row['total_deposits'] ?? 0);
        $totalWithdrawals = (float)($row['total_withdrawals'] ?? 0);

        return [
            'total_deposits' => $totalDeposits,
            'total_withdrawals' => $totalWithdrawals,
            'net_change' => $totalDeposits - $totalWithdrawals,
            'movement_count' => (int)($row['movement_count'] ?? 0)
        ];
    }

    /**
     * Get a specific cash movement by ID
     *
     * @param int $movementId Movement ID
     * @return array|null Movement data or null if not found
     */
    public function getCashMovementById(int $movementId): ?array
    {
        $sql = 'SELECT cm.*, u.full_name AS user_name, cb.name as cash_box_name,
                       curr.code as currency_code, curr.symbol as currency_symbol,
                       cm.movement_type as type, cm.exchange_id, cm.transfer_id
                FROM cash_movements cm
                LEFT JOIN users u ON u.id = cm.user_id
                LEFT JOIN cash_boxes cb ON cb.id = cm.cash_box_id
                LEFT JOIN currencies curr ON curr.code = cb.currency_code
                WHERE cm.id = ?';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return null;

        $stmt->bind_param('i', $movementId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return $row;
    }



    /**
     * Create a cash movement that is tied to an exchange transaction. The
     * caller is responsible for wrapping the call in a database transaction
     * – this method will NOT start/commit/rollback by itself. That way the
     * exchange workflow can combine multiple steps atomically.
     *
     * @param int    $boxId          Cash box affected
     * @param string $type           'deposit' or 'withdrawal'
     * @param float  $amount         Movement amount (in box currency)
     * @param int    $currencyId     Currency ID of the cash box (redundant but
     *                               useful for validation, ignored for now)
     * @param int    $exchangeId     Related exchange record ID (nullable)
     * @param int    $userId         User performing the action
     * @param string $description    Optional free-text description
     * @return array{success:bool, movement_id?:int, error?:string}
     */
    public function addCashMovementForExchange(int $boxId, string $type, float $amount, ?int $exchangeId, int $userId, string $description = ''): array
    {
        $type = strtolower($type);
        if (!in_array($type, ['deposit', 'withdrawal'], true)) {
            return ['success' => false, 'error' => 'Invalid movement type'];
        }
        // The outer layer manages the transaction – we only lock/modify
        try {
            // Set shorter lock timeout and use NOWAIT to avoid long waits
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 3");

            // Lock balance with NOWAIT
            $stmt = $this->db->prepare('SELECT current_balance FROM cash_boxes WHERE id = ? FOR UPDATE NOWAIT');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('i', $boxId);
            $stmt->execute();
            $stmt->bind_result($balance);
            if (!$stmt->fetch()) {
                $stmt->close();
                throw new Exception('Cash box not found or locked');
            }
            $stmt->close();

            if ($type === 'withdrawal' && $balance < $amount) {
                return ['success' => false, 'error' => 'Insufficient funds'];
            }
            $newBalance = $type === 'deposit' ? $balance + $amount : $balance - $amount;

            // Update balance
            $stmt = $this->db->prepare('UPDATE cash_boxes SET current_balance = ?, updated_at = NOW() WHERE id = ?');
            if (!$stmt) throw new Exception('Prepare failed');
            $stmt->bind_param('di', $newBalance, $boxId);
            if (!$stmt->execute()) throw new Exception('Balance update failed');
            $stmt->close();

            // Insert movement
            $stmt = $this->db->prepare('INSERT INTO cash_movements (cash_box_id, movement_type, amount, description, reference_number, exchange_id, user_id, created_at)
                                        VALUES (?,?,?,?,?,?, ?, NOW())');
            if (!$stmt) throw new Exception('Prepare failed');
            $ref = null; // No reference number in this context
            $stmt->bind_param('isdssi', $boxId, $type, $amount, $description, $ref, $exchangeId, $userId);
            if (!$stmt->execute()) throw new Exception('Movement insert failed');
            $movementId = $stmt->insert_id;
            $stmt->close();

            // Reset lock timeout
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");
            return ['success' => true, 'movement_id' => $movementId];
        } catch (Exception $ex) {
            // Reset lock timeout on error
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");
            return ['success' => false, 'error' => $ex->getMessage()];
        }
    }

    /**
     * Create a cash movement tied to a transfer. Must be inside an existing transaction.
     */
    public function addCashMovementForTransfer(int $boxId, string $type, float $amount, int $currencyId, int $transferId, int $userId, string $description = ''): array
    {
        if (!in_array($type, ['deposit', 'withdrawal'], true)) {
            return ['success' => false, 'error' => 'Invalid movement type'];
        }
        
        try {
            // Lock row
            $stmt = $this->db->prepare('SELECT current_balance FROM cash_boxes WHERE id = ? FOR UPDATE');
            if (!$stmt) {
                throw new Exception('Prepare failed');
            }
            
            $stmt->bind_param('i', $boxId);
            $stmt->execute();
            $stmt->bind_result($balance);
            
            if (!$stmt->fetch()) {
            $stmt->close();
                throw new Exception('Cash box not found');
            }
            
            $stmt->close();
            
            // Check if enough funds for withdrawal
            if ($type === 'withdrawal' && $balance < $amount) {
                return ['success' => false, 'error' => 'Insufficient funds'];
            }
            
            // Calculate new balance
            $newBalance = ($type === 'deposit') ? $balance + $amount : $balance - $amount;
            
            // Update cash box balance
            $updateStmt = $this->db->prepare('UPDATE cash_boxes SET current_balance = ?, updated_at = NOW() WHERE id = ?');
            if (!$updateStmt) {
                throw new Exception('Prepare failed for balance update');
            }
            
            $updateStmt->bind_param('di', $newBalance, $boxId);
            
            if (!$updateStmt->execute()) {
                $updateStmt->close();
                throw new Exception('Balance update failed');
            }
            
            $updateStmt->close();
            
            // Insert movement record
            $insertStmt = $this->db->prepare('INSERT INTO cash_movements (cash_box_id, movement_type, amount, description, reference_number, transfer_id, user_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())');
            if (!$insertStmt) {
                throw new Exception('Prepare failed for movement insert');
            }
            
            $referenceNumber = null;
            
            $insertStmt->bind_param('isdssii', 
                $boxId,              // i - integer (cash_box_id)
                $type,               // s - string (movement_type)
                $amount,             // d - double (amount)
                $description,        // s - string (description)
                $referenceNumber,    // s - string (reference_number)
                $transferId,         // i - integer (transfer_id)
                $userId              // i - integer (user_id)
            );
            
            if (!$insertStmt->execute()) {
                $insertStmt->close();
                throw new Exception('Movement insert failed');
            }
            
            $movementId = $insertStmt->insert_id;
            $insertStmt->close();
            
            return ['success' => true, 'movement_id' => $movementId];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get movement by reference number (excluding specific movement ID)
     *
     * @param string $referenceNumber Reference number to check
     * @param int $excludeId Movement ID to exclude from check
     * @return array|null Movement data or null if not found
     */
    public function getMovementByReference(string $referenceNumber, int $excludeId = 0): ?array
    {
        $sql = 'SELECT * FROM cash_movements WHERE reference_number = ?';
        $params = [$referenceNumber];
        $types = 's';

        if ($excludeId > 0) {
            $sql .= ' AND id != ?';
            $params[] = $excludeId;
            $types .= 'i';
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return null;

        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return $row;
    }

    /**
     * Update cash movement
     *
     * @param int $movementId Movement ID
     * @param array $data Movement data
     * @param float $balanceChange Change in balance (positive for increase, negative for decrease)
     * @return bool Success status
     */
    public function updateCashMovement(int $movementId, array $data, float $balanceChange = 0): bool
    {
        try {
            $conn = Database::getConnection();
            $conn->begin_transaction();

            // Update the movement
            $sql = 'UPDATE cash_movements SET
                        movement_type = ?,
                        amount = ?,
                        description = ?,
                        reference_number = ?,
                        updated_at = NOW()
                    WHERE id = ?';

            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                throw new Exception('Failed to prepare movement update statement');
            }

            $stmt->bind_param('sdssi',
                $data['movement_type'],
                $data['amount'],
                $data['description'],
                $data['reference_number'],
                $movementId
            );

            if (!$stmt->execute()) {
                throw new Exception('Failed to update movement');
            }
            $stmt->close();

            // Update cash box balance if there's a balance change
            if ($balanceChange != 0) {
                // Get the cash box ID from the movement
                $movementData = $this->getCashMovementById($movementId);
                if (!$movementData) {
                    throw new Exception('Movement not found after update');
                }

                $cashBoxId = $movementData['cash_box_id'];

                // Update cash box balance
                $updateBalanceSql = 'UPDATE cash_boxes SET
                                       current_balance = current_balance + ?,
                                       updated_at = NOW()
                                     WHERE id = ?';

                $balanceStmt = $conn->prepare($updateBalanceSql);
                if (!$balanceStmt) {
                    throw new Exception('Failed to prepare balance update statement');
                }

                $balanceStmt->bind_param('di', $balanceChange, $cashBoxId);

                if (!$balanceStmt->execute()) {
                    throw new Exception('Failed to update cash box balance');
                }
                $balanceStmt->close();
            }

            $conn->commit();
            return true;

        } catch (Exception $e) {
            $conn->rollback();
            error_log("Error updating cash movement: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Toggle cash box status (active/inactive)
     *
     * @param int $cashBoxId Cash box ID
     * @return array Result with success status and message
     */
    public function toggleCashBoxStatus(int $cashBoxId): array
    {
        try {
            // Get current status
            $cashBox = $this->getCashBoxById($cashBoxId);
            if (!$cashBox) {
                return ['success' => false, 'error' => 'الصندوق غير موجود'];
            }

            // Toggle status
            $newStatus = $cashBox['is_active'] ? 0 : 1;

            $sql = 'UPDATE cash_boxes SET is_active = ?, updated_at = NOW() WHERE id = ?';
            $stmt = $this->db->prepare($sql);

            if (!$stmt) {
                return ['success' => false, 'error' => 'خطأ في إعداد الاستعلام'];
            }

            $stmt->bind_param('ii', $newStatus, $cashBoxId);
            $success = $stmt->execute();
            $stmt->close();

            if ($success) {
                $statusText = $newStatus ? 'تم تفعيل' : 'تم إلغاء تفعيل';
                return ['success' => true, 'message' => $statusText . ' الصندوق بنجاح'];
            } else {
                return ['success' => false, 'error' => 'فشل في تحديث حالة الصندوق'];
            }

        } catch (Exception $e) {
            error_log("Error toggling cash box status: " . $e->getMessage());
            return ['success' => false, 'error' => 'خطأ في النظام: ' . $e->getMessage()];
        }
    }

    /**
     * Update cash box balance based on movements
     *
     * @param int $cashBoxId Cash box ID
     * @return bool Success status
     */
    public function updateCashBoxBalance(int $cashBoxId): bool
    {
        try {
            $conn = Database::getConnection();

            // Calculate correct balance
            $sql = "SELECT
                        cb.initial_balance,
                        COALESCE(SUM(CASE WHEN cm.movement_type = 'deposit' THEN cm.amount ELSE -cm.amount END), 0) as movements_total
                    FROM cash_boxes cb
                    LEFT JOIN cash_movements cm ON cm.cash_box_id = cb.id
                    WHERE cb.id = ?
                    GROUP BY cb.id, cb.initial_balance";

            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                return false;
            }

            $stmt->bind_param('i', $cashBoxId);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($row = $result->fetch_assoc()) {
                $correctBalance = $row['initial_balance'] + $row['movements_total'];

                // Update the balance
                $updateSql = "UPDATE cash_boxes SET current_balance = ?, updated_at = NOW() WHERE id = ?";
                $updateStmt = $conn->prepare($updateSql);
                if (!$updateStmt) {
                    return false;
                }

                $updateStmt->bind_param('di', $correctBalance, $cashBoxId);
                $success = $updateStmt->execute();
                $updateStmt->close();

                return $success;
            }

            return false;
        } catch (Exception $e) {
            error_log("Error updating cash box balance: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete cash movement
     *
     * @param int $movementId Movement ID
     * @return bool Success status
     */
    public function deleteMovement(int $movementId): bool
    {
        try {
            $conn = Database::getConnection();
            $conn->begin_transaction();

            // Get movement details first
            $movement = $this->getCashMovementById($movementId);
            if (!$movement) {
                throw new Exception('Movement not found');
            }

            // Check if movement is linked to exchange or transfer
            if (!empty($movement['exchange_id']) || !empty($movement['transfer_id'])) {
                throw new Exception('Cannot delete movement linked to exchange or transfer');
            }

            $cashBoxId = $movement['cash_box_id'];
            $amount = $movement['amount'];
            $type = $movement['movement_type'];

            // Delete the movement
            $deleteSql = 'DELETE FROM cash_movements WHERE id = ?';
            $deleteStmt = $conn->prepare($deleteSql);
            if (!$deleteStmt) {
                throw new Exception('Failed to prepare delete statement');
            }

            $deleteStmt->bind_param('i', $movementId);
            if (!$deleteStmt->execute()) {
                throw new Exception('Failed to delete movement');
            }
            $deleteStmt->close();

            // Update cash box balance
            $balanceChange = $type === 'deposit' ? -$amount : $amount; // Reverse the movement
            $updateBalanceSql = 'UPDATE cash_boxes SET
                                   current_balance = current_balance + ?,
                                   updated_at = NOW()
                                 WHERE id = ?';

            $balanceStmt = $conn->prepare($updateBalanceSql);
            if (!$balanceStmt) {
                throw new Exception('Failed to prepare balance update statement');
            }

            $balanceStmt->bind_param('di', $balanceChange, $cashBoxId);
            if (!$balanceStmt->execute()) {
                throw new Exception('Failed to update cash box balance');
            }
            $balanceStmt->close();

            $conn->commit();
            return true;

        } catch (Exception $e) {
            $conn->rollback();
            error_log("Error deleting cash movement: " . $e->getMessage());
            return false;
        }
    }

}
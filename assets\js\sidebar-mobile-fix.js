/**
 * Sidebar Mobile Fix
 * إصلاحات إضافية للشريط الجانبي على الأجهزة المحمولة
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Sidebar Mobile Fix loaded');

    // التأكد من وجود العناصر المطلوبة
    function ensureElements() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) {
            console.warn('Sidebar element not found');
            return false;
        }

        // التأكد من وجود الفئات المطلوبة
        if (!sidebar.classList.contains('sidebar')) {
            sidebar.classList.add('sidebar');
        }

        return true;
    }

    // إصلاح مشاكل CSS المحتملة
    function fixCSSIssues() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        // التأكد من الخصائص الأساسية
        const computedStyle = window.getComputedStyle(sidebar);
        
        if (computedStyle.position !== 'fixed') {
            sidebar.style.position = 'fixed';
        }
        
        if (computedStyle.zIndex < 1000) {
            sidebar.style.zIndex = '1000';
        }
    }

    // إصلاح مشاكل الأحداث
    function fixEventIssues() {
        // إزالة المستمعين المكررين
        const existingButtons = document.querySelectorAll('.sidebar-toggle-btn');
        existingButtons.forEach((btn, index) => {
            if (index > 0) { // الاحتفاظ بالأول فقط
                btn.remove();
            }
        });

        // التأكد من عمل زر التبديل
        const toggleBtn = document.querySelector('.sidebar-toggle-btn');
        if (toggleBtn) {
            // إزالة المستمعين السابقين
            const newBtn = toggleBtn.cloneNode(true);
            toggleBtn.parentNode.replaceChild(newBtn, toggleBtn);
            
            // إضافة مستمع جديد
            newBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                if (window.MobileSidebar) {
                    window.MobileSidebar.toggle();
                } else if (window.ResponsiveSystem) {
                    window.ResponsiveSystem.toggleSidebar();
                } else {
                    // fallback
                    const sidebar = document.getElementById('sidebar');
                    if (sidebar) {
                        sidebar.classList.toggle('show');
                    }
                }
            });
        }
    }

    // إصلاح مشاكل التمرير
    function fixScrollIssues() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        // منع التمرير الأفقي
        sidebar.style.overflowX = 'hidden';
        sidebar.style.overflowY = 'auto';
    }

    // إصلاح مشاكل الاتجاه (RTL)
    function fixRTLIssues() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        // التأكد من الاتجاه الصحيح
        if (document.dir === 'rtl' || document.documentElement.dir === 'rtl') {
            sidebar.style.right = sidebar.style.right || '0';
            sidebar.style.left = 'auto';
        }
    }

    // مراقبة التغييرات في DOM
    function observeChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // التحقق من إضافة عناصر جديدة
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && node.classList.contains('sidebar-toggle-btn')) {
                                fixEventIssues();
                            }
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // تشغيل الإصلاحات
    function runFixes() {
        if (!ensureElements()) return;

        fixCSSIssues();
        fixEventIssues();
        fixScrollIssues();
        fixRTLIssues();
        observeChanges();

        console.log('Sidebar mobile fixes applied');
    }

    // تشغيل الإصلاحات بعد تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runFixes);
    } else {
        runFixes();
    }

    // تشغيل الإصلاحات عند تغيير حجم النافذة
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            fixCSSIssues();
            fixRTLIssues();
        }, 100);
    });

    // إتاحة الوظائف للاستخدام الخارجي
    window.SidebarMobileFix = {
        runFixes: runFixes,
        fixCSSIssues: fixCSSIssues,
        fixEventIssues: fixEventIssues
    };
});

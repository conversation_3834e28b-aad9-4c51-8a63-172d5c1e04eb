<?php
/**
 * اختبار صفحة تعديل المعاملات اليومية
 * Test Edit Daily Transaction Page
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة تعديل المعاملات اليومية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-edit text-warning"></i>
            اختبار صفحة تعديل المعاملات اليومية
        </h1>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                echo "<div class='test-result test-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        // اختبار 1: اتصال قاعدة البيانات
        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // اختبار 2: فحص وجود ملف التعديل
        $edit_file = __DIR__ . '/../dashboard/edit_daily_transaction.php';
        $exists = file_exists($edit_file);
        showResult("ملف edit_daily_transaction.php", $exists, $exists ? "الملف موجود" : "الملف غير موجود");

        // اختبار 3: فحص وجود الإجراء المخزن UpdateDailyTransaction
        try {
            $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = 'UpdateDailyTransaction' AND Db = '" . DB_NAME . "'");
            $exists = $stmt->rowCount() > 0;
            showResult("إجراء UpdateDailyTransaction", $exists, $exists ? "الإجراء موجود" : "الإجراء غير موجود");
        } catch (Exception $e) {
            showResult("إجراء UpdateDailyTransaction", false, $e->getMessage());
        }

        // اختبار 4: فحص وجود صلاحية التعديل
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM permissions WHERE name = 'daily_transactions.edit'");
            $count = $stmt->fetch()['count'];
            showResult("صلاحية daily_transactions.edit", $count > 0, "الصلاحية موجودة");
        } catch (Exception $e) {
            showResult("صلاحية daily_transactions.edit", false, $e->getMessage());
        }

        // اختبار 5: فحص وجود معاملات للتعديل
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM daily_transactions LIMIT 1");
            $count = $stmt->fetch()['count'];
            if ($count > 0) {
                showResult("وجود معاملات للتعديل", true, "يوجد {$count} معاملة");
                
                // جلب أول معاملة للاختبار
                $stmt = $pdo->query("SELECT id, transaction_number FROM daily_transactions ORDER BY id DESC LIMIT 1");
                $transaction = $stmt->fetch();
                
                if ($transaction) {
                    showInfo("معاملة للاختبار: #{$transaction['transaction_number']} (ID: {$transaction['id']})");
                    
                    echo "<div class='mt-3'>";
                    echo "<a href='../dashboard/edit_daily_transaction.php?id={$transaction['id']}' class='btn btn-warning btn-lg' target='_blank'>";
                    echo "<i class='fas fa-edit me-2'></i>";
                    echo "اختبار تعديل المعاملة #{$transaction['transaction_number']}";
                    echo "</a>";
                    echo "</div>";
                }
            } else {
                showResult("وجود معاملات للتعديل", false, "لا توجد معاملات في قاعدة البيانات");
                showInfo("يمكنك إضافة معاملة جديدة أولاً من صفحة إضافة المعاملات");
            }
        } catch (Exception $e) {
            showResult("وجود معاملات للتعديل", false, $e->getMessage());
        }

        // اختبار 6: فحص الملفات المطلوبة
        $required_files = [
            'includes/auth.php',
            'includes/database.php', 
            'includes/pdo_database.php',
            'includes/header.php',
            'includes/sidebar.php',
            'includes/footer.php'
        ];
        
        foreach ($required_files as $file) {
            $file_path = __DIR__ . '/../' . $file;
            $exists = file_exists($file_path);
            showResult("ملف {$file}", $exists, $exists ? "الملف موجود" : "الملف غير موجود");
        }

        // اختبار 7: فحص الجداول المطلوبة
        $required_tables = ['daily_transactions', 'countries', 'users', 'permissions'];
        
        foreach ($required_tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM {$table} LIMIT 1");
                showResult("جدول {$table}", true, "الجدول موجود ويمكن الوصول إليه");
            } catch (Exception $e) {
                showResult("جدول {$table}", false, $e->getMessage());
            }
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 90) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> صفحة التعديل جاهزة للاستخدام";
            echo "</div>";
        } elseif ($success_rate >= 70) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> صفحة التعديل تعمل مع بعض المشاكل البسيطة";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج إصلاح!</strong> هناك مشاكل تحتاج إلى حل";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <div class="text-center mt-4">
            <a href="../dashboard/daily_transactions.php" class="btn btn-primary btn-lg">
                <i class="fas fa-list me-2"></i>
                الذهاب إلى قائمة المعاملات
            </a>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-success btn-lg ms-2">
                <i class="fas fa-plus me-2"></i>
                إضافة معاملة جديدة
            </a>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary btn-lg ms-2">
                <i class="fas fa-redo me-2"></i>
                إعادة الاختبار
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

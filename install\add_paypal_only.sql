-- إضافة صلاحيات PayPal فقط
-- Add PayPal Permissions Only

-- إضافة صلاحيات PayPal الأساسية
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('paypal.view', 'عرض حوالات PayPal', 'paypal', NOW()),
('paypal.create', 'إضافة حوالة PayPal جديدة', 'paypal', NOW()),
('paypal.edit', 'تعديل حوالة PayPal', 'paypal', NOW()),
('paypal.delete', 'حذف حوالة PayPal', 'paypal', NOW()),
('paypal.details', 'عرض تفاصيل حوالة PayPal', 'paypal', NOW()),
('paypal.mark_received', 'تأكيد استلام حوالة PayPal', 'paypal', NOW()),
('paypal.incoming.view', 'عرض الحوالات الواردة', 'paypal', NOW()),
('paypal.outgoing.view', 'عرض الحوالات الصادرة', 'paypal', NOW()),
('paypal.export', 'تصدير بيانات PayPal', 'paypal', NOW()),
('paypal.search', 'البحث في حوالات PayPal', 'paypal', NOW()),
('paypal.stats', 'عرض إحصائيات PayPal', 'paypal', NOW()),
('paypal.dashboard', 'الوصول لوحة تحكم PayPal', 'paypal', NOW()),
('paypal.print', 'طباعة حوالات PayPal', 'paypal', NOW()),
('paypal.filter', 'فلترة حوالات PayPal', 'paypal', NOW()),
('paypal.status_change', 'تغيير حالة حوالة PayPal', 'paypal', NOW()),
('paypal.withdrawals.view', 'عرض سحوبات PayPal', 'paypal', NOW()),
('paypal.withdrawals.create', 'إنشاء سحب من PayPal', 'paypal', NOW()),
('paypal.withdrawals.edit', 'تعديل سحب PayPal', 'paypal', NOW()),
('paypal.withdrawals.approve', 'الموافقة على سحب PayPal', 'paypal', NOW()),
('paypal.reports', 'تقارير PayPal', 'paypal', NOW());

-- ربط صلاحيات PayPal بدور مدير النظام (role_id = 1)
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 1, id FROM permissions WHERE module = 'paypal';

-- ربط صلاحيات PayPal بدور مدير الفرع (role_id = 2)
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 2, id FROM permissions WHERE name IN (
    'paypal.view', 'paypal.create', 'paypal.edit', 'paypal.details', 'paypal.mark_received',
    'paypal.incoming.view', 'paypal.outgoing.view', 'paypal.search', 'paypal.stats',
    'paypal.dashboard', 'paypal.print', 'paypal.filter', 'paypal.status_change',
    'paypal.withdrawals.view', 'paypal.withdrawals.create', 'paypal.withdrawals.edit', 
    'paypal.withdrawals.approve', 'paypal.reports', 'paypal.export'
);

-- ربط صلاحيات PayPal بدور الصراف (role_id = 3)
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 3, id FROM permissions WHERE name IN (
    'paypal.view', 'paypal.create', 'paypal.details', 'paypal.mark_received', 
    'paypal.incoming.view', 'paypal.search', 'paypal.dashboard', 'paypal.print',
    'paypal.filter', 'paypal.stats', 'paypal.withdrawals.view', 'paypal.withdrawals.create'
);

-- ربط صلاحيات PayPal بدور موظف العمليات (role_id = 4)
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 4, id FROM permissions WHERE name IN (
    'paypal.view', 'paypal.details', 'paypal.incoming.view', 'paypal.outgoing.view', 
    'paypal.search', 'paypal.stats', 'paypal.dashboard', 'paypal.filter',
    'paypal.status_change', 'paypal.withdrawals.view', 'paypal.reports'
);

-- ربط صلاحيات PayPal بدور المحاسب (role_id = 5)
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 5, id FROM permissions WHERE name IN (
    'paypal.view', 'paypal.details', 'paypal.incoming.view', 'paypal.outgoing.view', 
    'paypal.export', 'paypal.stats', 'paypal.search', 'paypal.dashboard',
    'paypal.filter', 'paypal.withdrawals.view', 'paypal.withdrawals.approve',
    'paypal.reports', 'paypal.print'
);

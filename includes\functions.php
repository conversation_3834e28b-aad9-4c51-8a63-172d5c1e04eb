<?php
/**
 * Common utility functions used across the system.
 */

/**
 * Redirect to a specified URL then exit.
 *
 * @param string $url
 */
function redirect($url)
{
    header('Location: ' . $url);
    exit;
}

/**
 * Sanitize user input to prevent XSS and other common issues.
 *
 * @param string $data
 * @return string
 */
function sanitize_input($data)
{
    $data = trim($data);
    $data = stripslashes($data);
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

require_once __DIR__ . '/database.php';
require_once __DIR__ . '/audit_manager.php';

/**
 * Record an activity into audit_logs table.
 *
 * @param int|null $user_id
 * @param string   $action
 * @param array    $details
 * @param string|null $module
 * @param int|null $record_id
 * @return void
 */
function log_activity($user_id, $action, $details = [], $module = null, $record_id = null)
{
    $audit = new AuditManager(new Database());
    $audit->logAction(
        $user_id,
        $action,
        $module,
        $record_id,
        null,
        !empty($details) ? $details : null
    );
}

/**
 * Generate a cryptographically secure CSRF token, store it in session and return it.
 *
 * @return string
 */
function generate_csrf_token(): string
{
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    return $token;
}

/**
 * Retrieve the current CSRF token from session or generate a new one if not set.
 *
 * @return string
 */
function get_csrf_token(): string
{
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    return $_SESSION['csrf_token'] ?? generate_csrf_token();
}

/**
 * Verify incoming CSRF token and clear it afterwards (single-use).
 *
 * @param string|null $token
 * @return bool True if valid, otherwise false.
 */
function verify_csrf_token(?string $token): bool
{
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    $valid = isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], (string) $token);

    // Invalidate token after check (single use)
    unset($_SESSION['csrf_token']);

    return $valid;
}

/**
 * Generate a secure random filename preserving original extension.
 */
function generate_secure_filename(string $original): string
{
    $ext = pathinfo($original, PATHINFO_EXTENSION);
    return bin2hex(random_bytes(16)) . ($ext ? '.' . $ext : '');
}

/**
 * Check if MIME type is allowed for document uploads.
 */
function is_allowed_file_type(string $mime): bool
{
    $allowed = [
        'application/pdf',
        'image/jpeg',
        'image/png',
    ];
    return in_array($mime, $allowed, true);
}

/**
 * Build upload path (creates directory if missing).
 */
function get_upload_path(string $subdir = ''): string
{
    $base = __DIR__ . '/../uploads/documents';
    if (!is_dir($base)) {
        mkdir($base, 0755, true);
    }
    if ($subdir) {
        $target = $base . '/' . $subdir;
        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }
        return $target;
    }
    return $base;
}

// Document upload constants
if (!defined('UPLOAD_DIR_DOCUMENTS')) {
    define('UPLOAD_DIR_DOCUMENTS', __DIR__ . '/../uploads/documents');
    if (!is_dir(UPLOAD_DIR_DOCUMENTS)) {
        mkdir(UPLOAD_DIR_DOCUMENTS, 0755, true);
    }
}
if (!defined('ALLOWED_DOC_TYPES')) {
    define('ALLOWED_DOC_TYPES', [
        'jpg' => 'image/jpeg',
        'jpeg'=> 'image/jpeg',
        'png' => 'image/png',
        'pdf' => 'application/pdf',
    ]);
}
if (!defined('MAX_DOC_SIZE')) {
    define('MAX_DOC_SIZE', 5 * 1024 * 1024); // 5MB
}

/**
 * Handle uploaded file validation & move. Returns array with success flag.
 */
function handle_uploaded_file(string $input): array
{
    if (!isset($_FILES[$input]) || $_FILES[$input]['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'error' => 'لم يتم رفع ملف'];
    }
    $file = $_FILES[$input];
    if ($file['size'] > MAX_DOC_SIZE) {
        return ['success' => false, 'error' => 'الملف يتجاوز الحجم المسموح'];
    }
    $mime = mime_content_type($file['tmp_name']);
    if (!in_array($mime, ALLOWED_DOC_TYPES, true)) {
        return ['success' => false, 'error' => 'نوع ملف غير مسموح'];
    }
    $ext = array_search($mime, ALLOWED_DOC_TYPES, true);
    $secureName = generate_secure_filename('doc.' . $ext);
    $destDir = get_upload_path();
    $dest = $destDir . '/' . $secureName;
    if (!move_uploaded_file($file['tmp_name'], $dest)) {
        return ['success' => false, 'error' => 'فشل نقل الملف'];
    }
    return [
        'success'   => true,
        'path'      => 'uploads/documents/' . $secureName,
        'name'      => $file['name'],
        'size'      => $file['size'],
        'mime_type' => $mime,
    ];
}

/**
 * Delete physical file.
 */
function delete_file(string $filePath): bool
{
    $abs = __DIR__ . '/../' . ltrim($filePath, '/');
    if (file_exists($abs)) {
        return unlink($abs);
    }
    return false;
}

/**
 * Set a flash message in session.
 *
 * @param string $type Message type (success, danger, warning, info)
 * @param string $message The message content
 */
function set_flash(string $type, string $message): void
{
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['flash'][$type] = $message;
}

/**
 * Get flash messages from session and clear them.
 *
 * @param string|null $type Specific type to get, or null for all
 * @return array|string|null
 */
function get_flash(?string $type = null)
{
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if ($type === null) {
        // Return all flash messages and clear them
        $all = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $all;
    }

    // Return specific type and clear it
    $message = $_SESSION['flash'][$type] ?? null;
    unset($_SESSION['flash'][$type]);
    return $message;
}
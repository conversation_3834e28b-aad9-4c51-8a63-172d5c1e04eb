<?php
/**
 * اختبار شامل لنظام تسجيل العمليات
 * Comprehensive Activity Logging System Test
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/system_activity_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

echo "<h1>اختبار نظام تسجيل العمليات الشامل</h1>\n";

$testResults = [];
$totalTests = 0;
$passedTests = 0;

function runTest($testName, $testFunction) {
    global $testResults, $totalTests, $passedTests;
    
    $totalTests++;
    echo "<h3>اختبار: $testName</h3>\n";
    
    try {
        $result = $testFunction();
        if ($result) {
            echo "<p style='color: green;'>✅ نجح الاختبار</p>\n";
            $passedTests++;
            $testResults[$testName] = 'PASS';
        } else {
            echo "<p style='color: red;'>❌ فشل الاختبار</p>\n";
            $testResults[$testName] = 'FAIL';
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $testResults[$testName] = 'ERROR';
    }
    
    echo "<hr>\n";
}

// اختبار 1: التحقق من وجود الجدول
runTest('وجود جدول system_activity_logs', function() {
    $db = Database::getConnection();
    $result = $db->query("SHOW TABLES LIKE 'system_activity_logs'");
    return $result && $result->num_rows > 0;
});

// اختبار 2: التحقق من هيكل الجدول
runTest('هيكل جدول system_activity_logs', function() {
    $db = Database::getConnection();
    $result = $db->query("DESCRIBE system_activity_logs");
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    $requiredColumns = [
        'id', 'user_id', 'username', 'action_type', 'module', 
        'operation', 'description', 'created_at'
    ];
    
    foreach ($requiredColumns as $col) {
        if (!in_array($col, $columns)) {
            echo "<p>عمود مفقود: $col</p>\n";
            return false;
        }
    }
    
    echo "<p>تم العثور على " . count($columns) . " عمود</p>\n";
    return true;
});

// اختبار 3: التحقق من الفهارس
runTest('فهارس قاعدة البيانات', function() {
    $db = Database::getConnection();
    $result = $db->query("SHOW INDEX FROM system_activity_logs");
    $indexes = [];
    while ($row = $result->fetch_assoc()) {
        $indexes[] = $row['Key_name'];
    }
    
    $requiredIndexes = ['PRIMARY', 'idx_user_id', 'idx_action_type', 'idx_module'];
    $foundIndexes = 0;
    
    foreach ($requiredIndexes as $idx) {
        if (in_array($idx, $indexes)) {
            $foundIndexes++;
        }
    }
    
    echo "<p>تم العثور على $foundIndexes من " . count($requiredIndexes) . " فهارس مطلوبة</p>\n";
    return $foundIndexes >= 3; // على الأقل 3 فهارس
});

// اختبار 4: اختبار SystemActivityManager
runTest('SystemActivityManager - تسجيل عملية أساسية', function() {
    $manager = new SystemActivityManager();
    return $manager->logActivity(
        'CREATE',
        'test',
        'test_operation',
        'اختبار تسجيل عملية أساسية',
        ['test_data' => 'test_value']
    );
});

// اختبار 5: اختبار تسجيل عملية إنشاء
runTest('SystemActivityManager - تسجيل عملية إنشاء', function() {
    $manager = new SystemActivityManager();
    return $manager->logCreate(
        'test_module',
        'test_create',
        'اختبار تسجيل عملية إنشاء',
        ['name' => 'اختبار', 'value' => 123],
        ['target_id' => 999]
    );
});

// اختبار 6: اختبار تسجيل عملية تحديث
runTest('SystemActivityManager - تسجيل عملية تحديث', function() {
    $manager = new SystemActivityManager();
    return $manager->logUpdate(
        'test_module',
        'test_update',
        'اختبار تسجيل عملية تحديث',
        ['old_name' => 'قديم', 'old_value' => 100],
        ['new_name' => 'جديد', 'new_value' => 200],
        ['target_id' => 999]
    );
});

// اختبار 7: اختبار ActivityHelper
runTest('ActivityHelper - تسجيل سريع', function() {
    ActivityHelper::logCreate('test_helper', 'عنصر اختبار', ['test' => 'data'], 888);
    ActivityHelper::logView('test_helper', 'عرض اختبار', ['filter' => 'test']);
    return true; // إذا لم تحدث أخطاء
});

// اختبار 8: اختبار تسجيل خطأ
runTest('تسجيل الأخطاء', function() {
    $manager = new SystemActivityManager();
    return $manager->logError(
        'test_module',
        'test_error',
        'رسالة خطأ اختبارية',
        ['error_context' => 'test context']
    );
});

// اختبار 9: قراءة السجلات المضافة
runTest('قراءة السجلات المضافة', function() {
    $db = Database::getConnection();
    $result = $db->query("SELECT COUNT(*) as count FROM system_activity_logs WHERE module LIKE 'test%'");
    $count = $result->fetch_assoc()['count'];
    
    echo "<p>تم العثور على $count سجل اختبار</p>\n";
    return $count > 0;
});

// اختبار 10: اختبار الفلترة
runTest('فلترة السجلات', function() {
    $db = Database::getConnection();
    
    // اختبار فلترة حسب نوع العملية
    $result1 = $db->query("SELECT COUNT(*) as count FROM system_activity_logs WHERE action_type = 'CREATE'");
    $createCount = $result1->fetch_assoc()['count'];
    
    // اختبار فلترة حسب الوحدة
    $result2 = $db->query("SELECT COUNT(*) as count FROM system_activity_logs WHERE module LIKE 'test%'");
    $testCount = $result2->fetch_assoc()['count'];
    
    echo "<p>سجلات CREATE: $createCount</p>\n";
    echo "<p>سجلات الاختبار: $testCount</p>\n";
    
    return $createCount > 0 && $testCount > 0;
});

// اختبار 11: اختبار الأداء
runTest('اختبار الأداء - إدراج متعدد', function() {
    $startTime = microtime(true);
    $manager = new SystemActivityManager();
    
    for ($i = 1; $i <= 10; $i++) {
        $manager->logActivity(
            'INFO',
            'performance_test',
            'bulk_insert',
            "اختبار أداء - السجل رقم $i",
            ['iteration' => $i]
        );
    }
    
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 3);
    
    echo "<p>وقت إدراج 10 سجلات: {$executionTime} ثانية</p>\n";
    
    return $executionTime < 2.0; // يجب أن يكون أقل من ثانيتين
});

// اختبار 12: اختبار JSON
runTest('اختبار تخزين JSON', function() {
    $manager = new SystemActivityManager();
    $complexData = [
        'user' => ['name' => 'أحمد محمد', 'age' => 30],
        'settings' => ['theme' => 'dark', 'language' => 'ar'],
        'permissions' => ['read', 'write', 'delete']
    ];
    
    $result = $manager->logActivity(
        'UPDATE',
        'json_test',
        'json_storage_test',
        'اختبار تخزين بيانات JSON معقدة',
        [
            'new_values' => $complexData,
            'additional_data' => ['test_type' => 'json_complex']
        ]
    );
    
    if ($result) {
        // التحقق من قراءة البيانات
        $db = Database::getConnection();
        $checkResult = $db->query("SELECT new_values FROM system_activity_logs WHERE operation = 'json_storage_test' ORDER BY id DESC LIMIT 1");
        if ($checkResult && $checkResult->num_rows > 0) {
            $row = $checkResult->fetch_assoc();
            $retrievedData = json_decode($row['new_values'], true);
            return isset($retrievedData['new_values']['user']['name']);
        }
    }
    
    return false;
});

// عرض النتائج النهائية
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
echo "<h2>📊 نتائج الاختبار النهائية</h2>\n";
echo "<p><strong>إجمالي الاختبارات:</strong> $totalTests</p>\n";
echo "<p><strong>نجح:</strong> <span style='color: green;'>$passedTests</span></p>\n";
echo "<p><strong>فشل:</strong> <span style='color: red;'>" . ($totalTests - $passedTests) . "</span></p>\n";

$successRate = round(($passedTests / $totalTests) * 100, 1);
echo "<p><strong>معدل النجاح:</strong> {$successRate}%</p>\n";

if ($successRate >= 90) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h3>🎉 ممتاز! النظام يعمل بشكل مثالي</h3>\n";
    echo "<p>جميع الاختبارات تقريباً نجحت. النظام جاهز للاستخدام الإنتاجي.</p>\n";
    echo "</div>\n";
} elseif ($successRate >= 70) {
    echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h3>⚠️ جيد مع بعض التحسينات</h3>\n";
    echo "<p>معظم الاختبارات نجحت ولكن هناك بعض المشاكل التي تحتاج إلى إصلاح.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h3>❌ يحتاج إلى إصلاحات</h3>\n";
    echo "<p>هناك مشاكل كبيرة في النظام تحتاج إلى إصلاح قبل الاستخدام.</p>\n";
    echo "</div>\n";
}

echo "</div>\n";

// عرض تفاصيل النتائج
echo "<h3>تفاصيل النتائج:</h3>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr><th>الاختبار</th><th>النتيجة</th></tr>\n";
foreach ($testResults as $test => $result) {
    $color = $result === 'PASS' ? 'green' : ($result === 'FAIL' ? 'red' : 'orange');
    $icon = $result === 'PASS' ? '✅' : ($result === 'FAIL' ? '❌' : '⚠️');
    echo "<tr><td>$test</td><td style='color: $color;'>$icon $result</td></tr>\n";
}
echo "</table>\n";

// تنظيف بيانات الاختبار
echo "<h3>تنظيف بيانات الاختبار</h3>\n";
try {
    $db = Database::getConnection();
    $cleanupResult = $db->query("DELETE FROM system_activity_logs WHERE module LIKE 'test%' OR operation LIKE '%test%'");
    if ($cleanupResult) {
        $deletedRows = $db->affected_rows;
        echo "<p style='color: green;'>✅ تم حذف $deletedRows سجل اختبار</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠️ لم يتم تنظيف بيانات الاختبار: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='../dashboard/system_activity_logs.php'>عرض سجل العمليات</a> | ";
echo "<a href='../dashboard/'>العودة إلى لوحة التحكم</a></p>\n";
?>

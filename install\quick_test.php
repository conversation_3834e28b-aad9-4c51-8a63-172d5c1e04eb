<?php
/**
 * Quick Test for Enhanced Transfer System
 * اختبار سريع للنظام المحسن
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>اختبار سريع للنظام المحسن</h2>\n";

try {
    // Test basic database connection
    echo "<h3>1. اختبار الاتصال بقاعدة البيانات</h3>\n";
    $conn = Database::getConnection();
    if ($conn) {
        echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    } else {
        echo "<p style='color: red;'>✗ فشل في الاتصال بقاعدة البيانات</p>\n";
        exit;
    }
    
    // Test TransferManager class
    echo "<h3>2. اختبار كلاس TransferManager</h3>\n";
    require_once __DIR__ . '/../includes/transfer_manager.php';
    
    $db = new Database();
    $transferManager = new TransferManager($db);
    echo "<p style='color: green;'>✓ تم إنشاء TransferManager بنجاح</p>\n";
    
    // Test EnhancedTransferManager class
    echo "<h3>3. اختبار كلاس EnhancedTransferManager</h3>\n";
    require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';
    
    $enhancedManager = new EnhancedTransferManager($db);
    echo "<p style='color: green;'>✓ تم إنشاء EnhancedTransferManager بنجاح</p>\n";
    
    // Test tables existence
    echo "<h3>4. فحص الجداول المطلوبة</h3>\n";
    $requiredTables = [
        'transfers' => 'جدول الحوالات',
        'transfer_activity_log' => 'سجل الأنشطة',
        'transfer_delivery_history' => 'تاريخ التسليم',
        'transfer_status_history' => 'تاريخ الحالات'
    ];
    
    foreach ($requiredTables as $table => $description) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ $description موجود</p>\n";
        } else {
            echo "<p style='color: red;'>✗ $description غير موجود</p>\n";
        }
    }
    
    // Test basic functionality
    echo "<h3>5. اختبار الوظائف الأساسية</h3>\n";
    
    // Get a test transfer
    $transferResult = $conn->query("SELECT id FROM transfers LIMIT 1");
    if ($transferResult && $transferResult->num_rows > 0) {
        $transfer = $transferResult->fetch_assoc();
        $transferId = $transfer['id'];
        
        // Test activity logging
        $logResult = $enhancedManager->logActivity(
            $transferId,
            'viewed',
            'اختبار سريع لتسجيل النشاط',
            null,
            ['test' => true, 'timestamp' => date('Y-m-d H:i:s')],
            1
        );
        
        if ($logResult) {
            echo "<p style='color: green;'>✓ تم تسجيل النشاط بنجاح</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في تسجيل النشاط</p>\n";
        }
        
        // Test activity retrieval
        $activities = $enhancedManager->getTransferActivityLog($transferId, 5);
        if (!empty($activities)) {
            echo "<p style='color: green;'>✓ تم استرجاع الأنشطة: " . count($activities) . " نشاط</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ لا توجد أنشطة مسجلة</p>\n";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠ لا توجد حوالات للاختبار</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى الاختبار السريع بنجاح!</h3>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 النتيجة: النظام يعمل بشكل صحيح!</h4>\n";
    echo "<p style='margin: 0; color: #155724;'>جميع المكونات الأساسية تعمل بشكل مثالي.</p>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4>🔗 اختبار الصفحات:</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/all_transfers.php' target='_blank' style='color: #007bff;'>📋 صفحة جميع الحوالات</a></li>\n";
    echo "<li><a href='../dashboard/transfers.php' target='_blank' style='color: #007bff;'>➕ إضافة حوالة جديدة</a></li>\n";
    echo "<li><a href='test_enhanced_transfer_system.php' target='_blank' style='color: #007bff;'>🧪 اختبار شامل للنظام</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

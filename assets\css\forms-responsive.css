/*
 * تحسينات النماذج المتجاوبة
 * Responsive Forms Enhancements
 */

/* ===== BASE FORM STYLES ===== */
.form-control {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--bg-color);
    color: var(--text-color);
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
    outline: none;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* ===== RESPONSIVE FORM LAYOUTS ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.form-col {
    flex: 1;
    min-width: 250px;
}

/* ===== TABLET RESPONSIVE (768px - 991px) ===== */
@media (max-width: 991.98px) and (min-width: 768px) {
    .form-control {
        padding: 0.6rem 0.875rem;
        font-size: 0.95rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .form-row {
        gap: 0.75rem;
    }
    
    .form-col {
        min-width: 200px;
    }
}

/* ===== MOBILE RESPONSIVE (max-width: 767px) ===== */
@media (max-width: 767.98px) {
    .form-control {
        padding: 0.75rem;
        font-size: 16px; /* منع التكبير في iOS */
        border-radius: 6px;
    }
    
    .form-label {
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .form-text {
        font-size: 0.8rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-col {
        min-width: auto;
        width: 100%;
    }
    
    /* تحسين النماذج الطويلة */
    .form-floating .form-control {
        padding: 1rem 0.75rem 0.25rem;
    }
    
    .form-floating label {
        padding: 1rem 0.75rem;
    }
}

/* ===== VERY SMALL SCREENS (max-width: 575px) ===== */
@media (max-width: 575.98px) {
    .form-control {
        padding: 0.875rem 0.75rem;
        font-size: 16px;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        margin-bottom: 0.375rem;
    }
    
    /* تحسين الأزرار في النماذج */
    .form-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .form-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .form-actions .btn:last-child {
        margin-bottom: 0;
    }
}

/* ===== INPUT GROUPS RESPONSIVE ===== */
.input-group {
    display: flex;
    width: 100%;
}

.input-group .form-control {
    flex: 1;
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

.input-group .form-control:last-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.input-group-text {
    background-color: var(--bg-light);
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    color: var(--text-muted);
    font-weight: 500;
}

@media (max-width: 575.98px) {
    .input-group {
        flex-direction: column;
    }
    
    .input-group .form-control,
    .input-group-text {
        border-radius: 6px !important;
        margin-bottom: 0.5rem;
    }
    
    .input-group .form-control:last-child,
    .input-group-text:last-child {
        margin-bottom: 0;
    }
}

/* ===== SELECT RESPONSIVE ===== */
.form-select {
    border-radius: 8px;
    border: 2px solid var(--border-color);
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    font-size: 1rem;
    background-color: var(--bg-color);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
}

.form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

@media (max-width: 767.98px) {
    .form-select {
        padding: 0.75rem 2.25rem 0.75rem 0.75rem;
        font-size: 16px;
        background-position: left 0.5rem center;
    }
}

/* ===== CHECKBOX AND RADIO RESPONSIVE ===== */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-right: 1.5rem;
}

.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-left: 0.5rem;
    border: 2px solid var(--border-color);
    border-radius: 4px;
}

.form-check-input:checked {
    background-color: var(--primary);
    border-color: var(--primary);
}

.form-check-label {
    font-size: 0.95rem;
    color: var(--text-color);
    cursor: pointer;
}

@media (max-width: 575.98px) {
    .form-check {
        padding-right: 1.25rem;
        margin-bottom: 0.5rem;
    }
    
    .form-check-input {
        width: 1.125rem;
        height: 1.125rem;
    }
    
    .form-check-label {
        font-size: 0.875rem;
    }
}

/* ===== FORM VALIDATION RESPONSIVE ===== */
.is-valid {
    border-color: var(--success);
}

.is-invalid {
    border-color: var(--danger);
}

.valid-feedback {
    color: var(--success);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.invalid-feedback {
    color: var(--danger);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

@media (max-width: 575.98px) {
    .valid-feedback,
    .invalid-feedback {
        font-size: 0.8rem;
    }
}

/* ===== FORM BUTTONS RESPONSIVE ===== */
.btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

@media (max-width: 767.98px) {
    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.95rem;
    }
    
    .btn-sm {
        padding: 0.5rem 0.875rem;
        font-size: 0.8rem;
    }
    
    .btn-lg {
        padding: 0.875rem 1.75rem;
        font-size: 1.05rem;
    }
}

@media (max-width: 575.98px) {
    .btn {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn:last-child {
        margin-bottom: 0;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: 8px !important;
        margin-bottom: 0.5rem;
    }
    
    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* ===== FORM SECTIONS RESPONSIVE ===== */
.form-section {
    background: var(--bg-color);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-light);
}

@media (max-width: 767.98px) {
    .form-section {
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 8px;
    }
    
    .form-section-title {
        font-size: 1.125rem;
        margin-bottom: 0.75rem;
    }
}

@media (max-width: 575.98px) {
    .form-section {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .form-section-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
}

/* ===== FORM PROGRESS RESPONSIVE ===== */
.form-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.form-progress-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.form-progress-step::before {
    content: '';
    position: absolute;
    top: 1rem;
    right: 50%;
    width: 100%;
    height: 2px;
    background: var(--border-light);
    z-index: 1;
}

.form-progress-step:last-child::before {
    display: none;
}

.form-progress-step.active::before {
    background: var(--primary);
}

.form-progress-number {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: var(--border-light);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.form-progress-step.active .form-progress-number {
    background: var(--primary);
    color: white;
}

.form-progress-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.form-progress-step.active .form-progress-label {
    color: var(--primary);
    font-weight: 500;
}

@media (max-width: 575.98px) {
    .form-progress {
        padding: 0 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    .form-progress-number {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.75rem;
    }
    
    .form-progress-label {
        font-size: 0.75rem;
    }
    
    .form-progress-step::before {
        top: 0.75rem;
    }
}

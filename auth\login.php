<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/system_activity_manager.php';

$auth = new Auth();

// If already logged in, redirect to dashboard
if ($auth->checkSession()) {
    redirect('../dashboard/index.php');
}

$error_message = '';
$usernameInput = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $usernameInput = $username;
    $password = $_POST['password'] ?? '';

    // محاولة تسجيل الدخول
    $loginResult = $auth->loginUser($username, $password);

    // تسجيل العملية
    $activityManager = new SystemActivityManager();

    if ($loginResult) {
        // تسجيل دخول ناجح
        $activityManager->logLogin($username, true);
    } else {
        // تسجيل دخول فاشل
        $activityManager->logLogin($username, false, 'اسم المستخدم أو كلمة المرور غير صحيحة');
        $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SYSTEM_NAME; ?> - تسجيل الدخول</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom Login CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/login-luxury.css"></head>
<body class="luxury-login-body">
    <!-- Background Elements -->
    <div class="background-elements">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
        <div class="gradient-overlay"></div>
    </div>

    <!-- Particles Background -->
    <div id="particles-js"></div>

    <!-- Main Login Container -->
    <div class="login-container">
        <div class="container-fluid h-100">
            <div class="row h-100 align-items-center justify-content-center">
                <div class="col-lg-5 col-md-7 col-sm-9">

                    <!-- Login Card -->
                    <div class="login-card animate__animated animate__fadeInUp">

                        <!-- Card Header -->
                        <div class="login-header text-center">
                            <div class="logo-container">
                                <div class="logo-circle">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="logo-glow"></div>
                            </div>
                            <h1 class="login-title">مرحباً بك</h1>
                            <h2 class="system-name"><?php echo SYSTEM_NAME; ?></h2>
                            <p class="login-subtitle">سجل دخولك للوصول إلى لوحة التحكم</p>
                        </div>

                        <!-- Login Form -->
                        <div class="login-form-container">
                            <form action="login.php" method="POST" class="login-form" id="loginForm">

                                <!-- Error Message -->
                                <?php if (!empty($error_message)) : ?>
                                <div class="alert alert-danger error-alert animate__animated animate__shakeX" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error_message; ?>
                                </div>
                                <?php endif; ?>

                                <!-- Username Field -->
                                <div class="form-group">
                                    <div class="input-container">
                                        <div class="input-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <input type="text"
                                               name="username"
                                               id="username"
                                               class="form-control luxury-input"
                                               placeholder="اسم المستخدم"
                                               value="<?php echo htmlspecialchars($usernameInput); ?>"
                                               required
                                               autofocus>
                                        <label for="username" class="floating-label">اسم المستخدم</label>
                                        <div class="input-border"></div>
                                    </div>
                                </div>

                                <!-- Password Field -->
                                <div class="form-group">
                                    <div class="input-container">
                                        <div class="input-icon">
                                            <i class="fas fa-lock"></i>
                                        </div>
                                        <input type="password"
                                               name="password"
                                               id="password"
                                               class="form-control luxury-input"
                                               placeholder="كلمة المرور"
                                               required>
                                        <label for="password" class="floating-label">كلمة المرور</label>
                                        <div class="input-border"></div>
                                        <button type="button" class="password-toggle" id="passwordToggle">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Remember Me -->
                                <div class="form-group remember-group">
                                    <div class="form-check luxury-checkbox">
                                        <input class="form-check-input" type="checkbox" id="rememberMe" name="remember">
                                        <label class="form-check-label" for="rememberMe">
                                            تذكرني
                                        </label>
                                    </div>
                                </div>

                                <!-- Login Button -->
                                <div class="form-group">
                                    <button type="submit" class="btn luxury-btn" id="loginBtn">
                                        <span class="btn-text">
                                            <i class="fas fa-sign-in-alt me-2"></i>
                                            تسجيل الدخول
                                        </span>
                                        <div class="btn-loading">
                                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                            جاري التحقق...
                                        </div>
                                        <div class="btn-ripple"></div>
                                    </button>
                                </div>

                            </form>
                        </div>

                        <!-- Card Footer -->
                        <div class="login-footer text-center">
                            <div class="security-badge">
                                <i class="fas fa-shield-check me-2"></i>
                                محمي بأعلى معايير الأمان
                            </div>
                        </div>

                    </div>

                    <!-- Copyright -->
                    <div class="copyright text-center">
                        <p class="mb-0">
                            © <?php echo date('Y'); ?> <?php echo SYSTEM_NAME; ?>. جميع الحقوق محفوظة
                        </p>
                        <p class="version">الإصدار 2.0</p>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/login-luxury.js"></script>
    </body>
</html> 
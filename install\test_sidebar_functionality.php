<?php
/**
 * اختبار وظائف الشريط الجانبي
 * Test Sidebar Functionality
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';

$auth = new Auth();
$auth->requireLogin();
$current_user = $auth->getCurrentUser();

$pageTitle = 'اختبار الشريط الجانبي';
include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <div class="container-fluid">
        <h2 class="mb-4">🔧 اختبار وظائف الشريط الجانبي</h2>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">تشخيص الشريط الجانبي</h5>
                    </div>
                    <div class="card-body">
                        <h6>الملفات المطلوبة:</h6>
                        <ul class="list-group mb-3">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                sidebar.css
                                <span class="badge bg-<?php echo file_exists(__DIR__ . '/../assets/css/sidebar.css') ? 'success' : 'danger'; ?>">
                                    <?php echo file_exists(__DIR__ . '/../assets/css/sidebar.css') ? '✅ موجود' : '❌ مفقود'; ?>
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                sidebar.js
                                <span class="badge bg-<?php echo file_exists(__DIR__ . '/../assets/js/sidebar.js') ? 'success' : 'danger'; ?>">
                                    <?php echo file_exists(__DIR__ . '/../assets/js/sidebar.js') ? '✅ موجود' : '❌ مفقود'; ?>
                                </span>
                            </li>
                        </ul>
                        
                        <h6>اختبار JavaScript:</h6>
                        <div class="mb-3">
                            <button class="btn btn-primary" onclick="testSidebarJS()">اختبار JavaScript</button>
                            <button class="btn btn-secondary" onclick="toggleTestSection()">اختبار التبديل</button>
                            <button class="btn btn-info" onclick="debugSidebar()">تشخيص الشريط الجانبي</button>
                        </div>
                        
                        <div id="test-results" class="alert alert-info" style="display: none;">
                            <h6>نتائج الاختبار:</h6>
                            <div id="test-output"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">معلومات المستخدم</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الاسم:</strong> <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username']); ?></p>
                        <p><strong>الدور:</strong> <?php echo htmlspecialchars($current_user['role_name'] ?? 'غير محدد'); ?></p>
                        <p><strong>Role ID:</strong> <?php echo htmlspecialchars($current_user['role_id'] ?? 'غير محدد'); ?></p>
                        
                        <?php if (isset($current_user['role_id']) && (int)$current_user['role_id'] === 1): ?>
                        <div class="alert alert-success">
                            ✅ أنت أدمن - يمكنك رؤية قسم إدارة النظام
                        </div>
                        <?php else: ?>
                        <div class="alert alert-warning">
                            ⚠️ أنت لست أدمن - لن ترى قسم إدارة النظام
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">إرشادات الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>تأكد من أن الشريط الجانبي يظهر على اليمين</li>
                            <li>جرب النقر على أسهم القوائم</li>
                            <li>تأكد من أن القوائم تفتح وتغلق</li>
                            <li>تحقق من وحدة تحكم المتصفح للأخطاء</li>
                            <li>اضغط F12 وانتقل إلى Console</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- قسم اختبار إضافي -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">اختبار القوائم المنسدلة</h5>
                    </div>
                    <div class="card-body">
                        <p>استخدم الأزرار أدناه لاختبار القوائم المنسدلة برمجياً:</p>
                        
                        <div class="btn-group mb-3" role="group">
                            <button class="btn btn-outline-primary" onclick="expandSection('users-section')">فتح قائمة المستخدمين</button>
                            <button class="btn btn-outline-primary" onclick="collapseSection('users-section')">إغلاق قائمة المستخدمين</button>
                        </div>
                        
                        <div class="btn-group mb-3" role="group">
                            <button class="btn btn-outline-secondary" onclick="expandSection('transfers-section')">فتح قائمة التحويلات</button>
                            <button class="btn btn-outline-secondary" onclick="collapseSection('transfers-section')">إغلاق قائمة التحويلات</button>
                        </div>
                        
                        <div class="btn-group mb-3" role="group">
                            <button class="btn btn-outline-success" onclick="expandSection('exchange-section')">فتح قائمة الصرافة</button>
                            <button class="btn btn-outline-success" onclick="collapseSection('exchange-section')">إغلاق قائمة الصرافة</button>
                        </div>
                        
                        <?php if (isset($current_user['role_id']) && (int)$current_user['role_id'] === 1): ?>
                        <div class="btn-group mb-3" role="group">
                            <button class="btn btn-outline-warning" onclick="expandSection('system-admin-section')">فتح قائمة إدارة النظام</button>
                            <button class="btn btn-outline-warning" onclick="collapseSection('system-admin-section')">إغلاق قائمة إدارة النظام</button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// دوال اختبار JavaScript
function testSidebarJS() {
    const results = document.getElementById('test-results');
    const output = document.getElementById('test-output');
    
    results.style.display = 'block';
    
    let testOutput = '<ul>';
    
    // اختبار وجود العناصر
    const sidebar = document.getElementById('sidebar');
    testOutput += `<li>الشريط الجانبي: ${sidebar ? '✅ موجود' : '❌ مفقود'}</li>`;
    
    const headers = document.querySelectorAll('.sidebar-section-header.collapsible');
    testOutput += `<li>رؤوس القوائم القابلة للطي: ${headers.length} عنصر</li>`;
    
    const subLinks = document.querySelectorAll('.sidebar-sub-links');
    testOutput += `<li>القوائم الفرعية: ${subLinks.length} عنصر</li>`;
    
    // اختبار الكلاسات
    let collapsedCount = 0;
    headers.forEach(header => {
        if (header.classList.contains('collapsed')) {
            collapsedCount++;
        }
    });
    testOutput += `<li>القوائم المطوية: ${collapsedCount} من ${headers.length}</li>`;
    
    // اختبار localStorage
    const hasLocalStorage = typeof(Storage) !== "undefined";
    testOutput += `<li>دعم localStorage: ${hasLocalStorage ? '✅ مدعوم' : '❌ غير مدعوم'}</li>`;
    
    testOutput += '</ul>';
    output.innerHTML = testOutput;
}

function toggleTestSection() {
    const firstHeader = document.querySelector('.sidebar-section-header.collapsible');
    if (firstHeader) {
        firstHeader.click();
        console.log('تم النقر على أول قائمة');
    } else {
        console.log('لم يتم العثور على قوائم قابلة للطي');
    }
}

function expandSection(sectionId) {
    const header = document.querySelector(`[data-target="${sectionId}"]`);
    const target = document.getElementById(sectionId);
    
    if (header && target) {
        header.classList.remove('collapsed');
        target.classList.remove('collapsed');
        console.log(`تم فتح القسم: ${sectionId}`);
    } else {
        console.log(`لم يتم العثور على القسم: ${sectionId}`);
    }
}

function collapseSection(sectionId) {
    const header = document.querySelector(`[data-target="${sectionId}"]`);
    const target = document.getElementById(sectionId);
    
    if (header && target) {
        header.classList.add('collapsed');
        target.classList.add('collapsed');
        console.log(`تم إغلاق القسم: ${sectionId}`);
    } else {
        console.log(`لم يتم العثور على القسم: ${sectionId}`);
    }
}

function debugSidebar() {
    console.log('🔍 تشخيص الشريط الجانبي:');
    
    const sidebar = document.getElementById('sidebar');
    console.log('الشريط الجانبي:', sidebar);
    
    const headers = document.querySelectorAll('.sidebar-section-header.collapsible');
    console.log(`عدد الرؤوس القابلة للطي: ${headers.length}`);
    
    headers.forEach((header, index) => {
        const targetId = header.getAttribute('data-target');
        const target = document.getElementById(targetId);
        const isCollapsed = header.classList.contains('collapsed');
        
        console.log(`${index + 1}. ${targetId}:`);
        console.log(`   - مطوي: ${isCollapsed}`);
        console.log(`   - الهدف موجود: ${!!target}`);
        console.log(`   - الهدف مطوي: ${target ? target.classList.contains('collapsed') : 'N/A'}`);
    });
    
    // عرض النتائج في الصفحة أيضاً
    testSidebarJS();
}

// تشغيل اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        console.log('🚀 بدء اختبار الشريط الجانبي التلقائي');
        debugSidebar();
    }, 1000);
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>

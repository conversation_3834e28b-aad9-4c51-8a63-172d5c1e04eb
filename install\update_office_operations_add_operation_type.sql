-- Update Office Operations Table - Add Operation Type
-- This script adds the operation_type column to the office_operations table

-- Check and add operation_type column if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'office_operations'
    AND COLUMN_NAME = 'operation_type'
    AND TABLE_SCHEMA = DATABASE()
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE office_operations ADD COLUMN operation_type ENUM(''multiply'', ''divide'') NOT NULL DEFAULT ''multiply'' COMMENT ''multiply = ضرب, divide = قسمة'' AFTER amount',
    'SELECT ''Column operation_type already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add tracking_number column if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'office_operations'
    AND COLUMN_NAME = 'tracking_number'
    AND TABLE_SCHEMA = DATABASE()
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE office_operations ADD COLUMN tracking_number VARCHAR(100) DEFAULT NULL AFTER operation_type',
    'SELECT ''Column tracking_number already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add base_amount column if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'office_operations'
    AND COLUMN_NAME = 'base_amount'
    AND TABLE_SCHEMA = DATABASE()
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE office_operations ADD COLUMN base_amount DECIMAL(18,2) NOT NULL DEFAULT 0.00 AFTER operation_name',
    'SELECT ''Column base_amount already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records to have multiply as default operation type
UPDATE office_operations SET operation_type = 'multiply' WHERE operation_type IS NULL;

-- Update existing records to have base_amount equal to amount if base_amount is 0
UPDATE office_operations SET base_amount = amount WHERE base_amount = 0 AND amount > 0;

COMMIT;

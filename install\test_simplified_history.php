<?php
/**
 * Test Simplified Transfer History Display
 * اختبار عرض تاريخ الحوالات المبسط
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';

echo "<h2>اختبار العرض المبسط لتاريخ الحوالات</h2>\n";

try {
    $db = new Database();
    $enhancedManager = new EnhancedTransferManager($db);
    $conn = Database::getConnection();
    
    // Get a test transfer
    $transferResult = $conn->query("SELECT id FROM transfers LIMIT 1");
    if ($transferResult && $transferResult->num_rows > 0) {
        $transfer = $transferResult->fetch_assoc();
        $transferId = $transfer['id'];
        
        echo "<h3>1. إضافة أنشطة تجريبية مبسطة</h3>\n";
        
        // Clear existing test activities
        $conn->query("DELETE FROM transfer_activity_log WHERE transfer_id = $transferId AND description LIKE '%اختبار%'");
        
        // Add simplified test activities
        $testActivities = [
            [
                'type' => 'created',
                'description' => 'تم إنشاء الحوالة',
                'new_value' => ['status' => 'معلقة']
            ],
            [
                'type' => 'status_changed', 
                'description' => 'تم تغيير الحالة إلى مقبولة',
                'new_value' => ['status' => 'مقبولة']
            ],
            [
                'type' => 'tracking_assigned',
                'description' => 'تم تعيين رقم التتبع',
                'new_value' => ['tracking_number' => 'TK123456789']
            ],
            [
                'type' => 'status_changed',
                'description' => 'تم تغيير الحالة إلى مرسلة', 
                'new_value' => ['status' => 'مرسلة']
            ],
            [
                'type' => 'delivery_updated',
                'description' => 'تم تحديث التسليم: في الطريق',
                'new_value' => ['delivery_status' => 'in_transit']
            ],
            [
                'type' => 'status_changed',
                'description' => 'تم تغيير الحالة إلى مكتملة',
                'new_value' => ['status' => 'مكتملة']
            ]
        ];
        
        foreach ($testActivities as $activity) {
            $result = $enhancedManager->logActivity(
                $transferId,
                $activity['type'],
                $activity['description'],
                null, // لا نحفظ القيم القديمة
                $activity['new_value'],
                1
            );
            
            if ($result) {
                echo "<p style='color: green;'>✓ تم تسجيل: {$activity['description']}</p>\n";
            }
        }
        
        echo "<h3>2. عرض التاريخ المبسط</h3>\n";
        
        // Get simplified activity log
        $activityLog = $enhancedManager->getTransferActivityLog($transferId, 20);
        
        // Filter important activities only
        $importantActivities = array_filter($activityLog, function($activity) {
            $importantTypes = ['created', 'status_changed', 'tracking_assigned', 'delivery_updated'];
            return in_array($activity['activity_type'], $importantTypes);
        });
        
        if (!empty($importantActivities)) {
            echo "<div style='background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>\n";
            echo "<h4>📋 تاريخ الحوالة المبسط</h4>\n";
            
            echo "<div class='timeline' style='position: relative; padding-left: 30px;'>\n";
            
            foreach (array_slice($importantActivities, 0, 10) as $index => $activity) {
                $activityColors = [
                    'created' => '#28a745',
                    'status_changed' => '#ffc107', 
                    'tracking_assigned' => '#17a2b8',
                    'delivery_updated' => '#007bff'
                ];
                
                $color = $activityColors[$activity['activity_type']] ?? '#6c757d';
                
                echo "<div style='position: relative; margin-bottom: 20px; padding-left: 20px;'>\n";
                
                // Timeline marker
                echo "<div style='position: absolute; left: -15px; top: 5px; width: 12px; height: 12px; background: $color; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 0 2px $color;'></div>\n";
                
                // Timeline line (except for last item)
                if ($index < count($importantActivities) - 1) {
                    echo "<div style='position: absolute; left: -9px; top: 17px; width: 2px; height: 30px; background: #dee2e6;'></div>\n";
                }
                
                // Content
                echo "<div style='background: #f8f9fa; padding: 12px; border-radius: 6px; border-left: 3px solid $color;'>\n";
                
                if ($activity['activity_type'] === 'status_changed' && $activity['new_value_decoded']) {
                    $newStatus = $activity['new_value_decoded']['status'] ?? 'غير محدد';
                    echo "<h6 style='margin: 0 0 8px 0; color: $color;'>$newStatus</h6>\n";
                } else {
                    $activityLabels = [
                        'created' => 'تم إنشاء الحوالة',
                        'tracking_assigned' => 'تم تعيين رقم التتبع', 
                        'delivery_updated' => 'تم تحديث التسليم'
                    ];
                    $label = $activityLabels[$activity['activity_type']] ?? $activity['activity_type'];
                    echo "<h6 style='margin: 0 0 8px 0; color: $color;'>$label</h6>\n";
                }
                
                echo "<small style='color: #6c757d;'>\n";
                echo "<i class='fas fa-clock'></i> " . date('Y-m-d H:i', strtotime($activity['created_at']));
                
                if (!empty($activity['user_name'])) {
                    echo " • <i class='fas fa-user'></i> بواسطة: " . htmlspecialchars($activity['user_name']);
                }
                
                echo "</small>\n";
                echo "</div>\n";
                echo "</div>\n";
            }
            
            echo "</div>\n";
            echo "</div>\n";
        }
        
        echo "<h3>3. مقارنة العرض القديم والجديد</h3>\n";
        
        echo "<div style='display: flex; gap: 20px; margin: 20px 0;'>\n";
        
        // Old style
        echo "<div style='flex: 1; background: #fff3cd; padding: 15px; border-radius: 8px;'>\n";
        echo "<h5 style='color: #856404;'>❌ العرض القديم (مفصل جداً)</h5>\n";
        echo "<div style='font-size: 12px; color: #856404;'>\n";
        echo "• تم تغيير حالة الحوالة من 'معلقة' إلى 'مقبولة' - ملاحظات: تم قبول الحوالة<br>\n";
        echo "• 2025-07-11 14:32 بواسطة: أحمد محمد<br>\n";
        echo "• IP: ***********<br>\n";
        echo "• من: [معلقة] → إلى: [مقبولة]<br>\n";
        echo "</div>\n";
        echo "</div>\n";
        
        // New style  
        echo "<div style='flex: 1; background: #d4edda; padding: 15px; border-radius: 8px;'>\n";
        echo "<h5 style='color: #155724;'>✅ العرض الجديد (مبسط)</h5>\n";
        echo "<div style='font-size: 12px; color: #155724;'>\n";
        echo "• مقبولة<br>\n";
        echo "• 2025-07-11 14:32<br>\n";
        echo "• بواسطة: أحمد محمد<br>\n";
        echo "</div>\n";
        echo "</div>\n";
        
        echo "</div>\n";
        
        echo "<h3>4. إحصائيات الأنشطة</h3>\n";
        
        $allActivities = $enhancedManager->getTransferActivityLog($transferId, 100);
        $importantCount = count($importantActivities);
        $totalCount = count($allActivities);
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>\n";
        echo "<h5>📊 إحصائيات التصفية</h5>\n";
        echo "<ul>\n";
        echo "<li><strong>إجمالي الأنشطة:</strong> $totalCount</li>\n";
        echo "<li><strong>الأنشطة المهمة المعروضة:</strong> $importantCount</li>\n";
        echo "<li><strong>نسبة التبسيط:</strong> " . round(($importantCount / max($totalCount, 1)) * 100, 1) . "%</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
    } else {
        echo "<p style='color: red;'>✗ لا توجد حوالات للاختبار</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم تطبيق العرض المبسط بنجاح!</h3>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 الميزات الجديدة:</h4>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li>✅ عرض مبسط للحالات فقط</li>\n";
    echo "<li>✅ إزالة التفاصيل الزائدة</li>\n";
    echo "<li>✅ تركيز على المعلومات المهمة</li>\n";
    echo "<li>✅ تصفية الأنشطة غير المهمة</li>\n";
    echo "<li>✅ عرض أنيق ومنظم</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4>🔗 اختبار الصفحات المحدثة:</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/transfer_details.php?id=" . ($transferId ?? 1) . "' target='_blank' style='color: #007bff;'>📄 صفحة تفاصيل الحوالة المبسطة</a></li>\n";
    echo "<li><a href='../dashboard/all_transfers.php' target='_blank' style='color: #007bff;'>📋 صفحة جميع الحوالات</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
h2, h3, h4, h5 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
.fas { margin-left: 5px; }
</style>

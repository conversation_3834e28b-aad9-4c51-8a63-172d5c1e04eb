/**
 * Mobile Sidebar Styles
 * تصميم الشريط الجانبي للأجهزة المحمولة
 */

/* ===== زر التبديل المحسن ===== */
.sidebar-toggle-btn {
  position: fixed;
  top: 15px;
  right: 15px;
  z-index: 1001;
  display: none;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.sidebar-toggle-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
  background: linear-gradient(135deg, #0056b3, #004085);
}

.sidebar-toggle-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

.sidebar-toggle-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.sidebar-toggle-btn i {
  font-size: 18px;
  transition: transform 0.3s ease;
}

/* تأثير دوران الأيقونة */
.sidebar-toggle-btn.active i {
  transform: rotate(90deg);
}

/* إظهار الزر على الأجهزة المحمولة فقط */
@media (max-width: 991.98px) {
  .sidebar-toggle-btn {
    display: flex !important;
  }
}

/* تحسين موضع الزر على الشاشات الصغيرة جداً */
@media (max-width: 575.98px) {
  .sidebar-toggle-btn {
    top: 10px;
    right: 10px;
    width: 45px;
    height: 45px;
  }
  
  .sidebar-toggle-btn i {
    font-size: 16px;
  }
}

/* ===== الشريط الجانبي المحسن ===== */
#sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 250px;
  height: 100vh;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
}

/* إخفاء الشريط الجانبي على الأجهزة المحمولة بشكل افتراضي */
@media (max-width: 991.98px) {
  #sidebar {
    right: -250px;
    box-shadow: none;
    transform: translateX(0);
  }
  
  #sidebar.show {
    right: 0;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
  }
  
  /* تحسين عرض الشريط الجانبي على الأجهزة اللوحية */
  #sidebar {
    width: 280px;
    right: -280px;
  }
  
  #sidebar.show {
    right: 0;
  }
}

/* تحسين إضافي للشاشات الصغيرة جداً */
@media (max-width: 575.98px) {
  #sidebar {
    width: 100%;
    right: -100%;
  }
  
  #sidebar.show {
    right: 0;
  }
}

/* ===== طبقة التغطية المحسنة ===== */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* ===== تحسينات المحتوى الرئيسي ===== */
.main-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 100vh;
  position: relative;
}

/* Desktop */
@media (min-width: 992px) {
  .main-content {
    margin-right: 250px;
    width: calc(100% - 250px);
    padding: 20px;
  }
}

/* Tablet and Mobile */
@media (max-width: 991.98px) {
  .main-content {
    margin-right: 0;
    width: 100%;
    padding: 15px;
    padding-top: 80px; /* مساحة لزر التبديل */
  }
}

@media (max-width: 575.98px) {
  .main-content {
    padding: 10px;
    padding-top: 70px;
  }
}

/* ===== تحسينات إضافية ===== */

/* منع التمرير عند فتح الشريط الجانبي على الأجهزة المحمولة */
body.sidebar-open {
  overflow: hidden;
}

@media (min-width: 992px) {
  body.sidebar-open {
    overflow: auto; /* السماح بالتمرير على أجهزة سطح المكتب */
  }
}

/* تحسين الانتقالات */
#sidebar * {
  transition: inherit;
}

/* تحسين الأداء */
#sidebar,
.sidebar-overlay,
.sidebar-toggle-btn {
  will-change: transform, opacity;
}

/* إخفاء عناصر الطباعة */
@media print {
  .sidebar-toggle-btn,
  .sidebar-overlay {
    display: none !important;
  }
}

/* تحسينات إمكانية الوصول */
.sidebar-toggle-btn:focus-visible {
  outline: 2px solid #fff;
  outline-offset: 2px;
}

/* تحسين للأجهزة التي تدعم hover */
@media (hover: hover) {
  .sidebar-toggle-btn:hover {
    transform: scale(1.1);
  }
}

/* تحسين للأجهزة التي لا تدعم hover */
@media (hover: none) {
  .sidebar-toggle-btn:hover {
    transform: none;
  }
  
  .sidebar-toggle-btn:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #0056b3, #004085);
  }
}

/* تحسين الحركة للأجهزة التي تفضل تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
  #sidebar,
  .sidebar-overlay,
  .sidebar-toggle-btn,
  .main-content {
    transition: none !important;
  }
  
  .sidebar-toggle-btn i {
    transition: none !important;
  }
}

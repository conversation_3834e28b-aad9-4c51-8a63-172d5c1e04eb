/* تنسيقات خاصة بقائمة PayPal في الشريط الجانبي */

/* القائمة الرئيسية لـ PayPal */
.paypal-menu {
    margin: 8px 0;
    border-radius: 12px;
    overflow: hidden;
    background: linear-gradient(135deg, #0070ba 0%, #003087 100%);
    box-shadow: 0 4px 15px rgba(0, 112, 186, 0.3);
    transition: all 0.3s ease;
}

.paypal-menu:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 112, 186, 0.4);
}

/* الرابط الرئيسي لـ PayPal */
.paypal-main-link {
    background: transparent !important;
    color: white !important;
    padding: 15px 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.paypal-main-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s;
}

.paypal-main-link:hover::before {
    left: 100%;
}

.paypal-main-link:hover {
    background: linear-gradient(135deg, #0079c1 0%, #004494 100%) !important;
    color: white !important;
    transform: scale(1.02);
}

/* أيقونة PayPal الرئيسية */
.paypal-icon-wrapper {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    transition: all 0.3s ease;
}

.paypal-icon {
    font-size: 20px;
    color: #ffffff;
    transition: all 0.3s ease;
}

.paypal-main-link:hover .paypal-icon-wrapper {
    background: rgba(255, 255, 255, 0.25);
    transform: rotate(5deg) scale(1.1);
}

.paypal-main-link:hover .paypal-icon {
    transform: scale(1.2);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* نص PayPal */
.paypal-text {
    font-weight: 600;
    font-size: 16px;
    flex: 1;
    margin: 0 10px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* سهم القائمة المنسدلة */
.paypal-chevron {
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.8);
}

.paypal-main-link[aria-expanded="true"] .paypal-chevron {
    transform: rotate(180deg);
    color: white;
}

/* القائمة الفرعية */
.paypal-submenu {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0 0 12px 12px;
    padding: 8px 0;
    margin-top: -1px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* الروابط الفرعية */
.paypal-sublink {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #495057 !important;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    margin: 2px 8px;
    border-radius: 8px;
    background: transparent;
}

.paypal-sublink:hover {
    background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
    color: #212529 !important;
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.paypal-sublink.active {
    background: linear-gradient(135deg, #0070ba 0%, #003087 100%);
    color: white !important;
    box-shadow: 0 4px 15px rgba(0, 112, 186, 0.3);
}

.paypal-sublink.active:hover {
    background: linear-gradient(135deg, #0079c1 0%, #004494 100%);
    transform: translateX(8px);
}

/* أيقونات الروابط الفرعية */
.paypal-subicon-wrapper {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    transition: all 0.3s ease;
}

/* ألوان مختلفة لكل نوع */
.dashboard-icon {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.incoming-icon {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.create-icon {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.withdrawals-icon {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.reports-icon {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
}

/* تأثيرات التمرير للأيقونات */
.paypal-sublink:hover .paypal-subicon-wrapper {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.paypal-sublink.active .paypal-subicon-wrapper {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* مؤشر النشاط */
.paypal-indicator {
    width: 4px;
    height: 20px;
    background: transparent;
    border-radius: 2px;
    transition: all 0.3s ease;
    margin-right: 8px;
}

.paypal-sublink.active .paypal-indicator {
    background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* تأثيرات الحركة */
@keyframes paypalPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.paypal-menu.active {
    animation: paypalPulse 2s infinite;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .paypal-main-link {
        padding: 12px 15px;
    }

    .paypal-icon-wrapper {
        width: 35px;
        height: 35px;
    }

    .paypal-icon {
        font-size: 18px;
    }

    .paypal-text {
        font-size: 14px;
    }

    .paypal-sublink {
        padding: 10px 15px;
    }

    .paypal-subicon-wrapper {
        width: 28px;
        height: 28px;
    }
}

/* إصلاح مشكلة النص عند إخفاء الشريط الجانبي */
.sidebar-collapsed .paypal-text,
.sidebar-collapsed .paypal-sublink span {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
}

.sidebar-collapsed .paypal-main-link {
    justify-content: center !important;
    padding: 15px 10px !important;
}

.sidebar-collapsed .paypal-icon-wrapper {
    margin: 0 !important;
}

.sidebar-collapsed .paypal-chevron {
    display: none !important;
}

.sidebar-collapsed .paypal-menu {
    margin: 8px 5px !important;
}

.sidebar-collapsed .paypal-sublink {
    justify-content: center !important;
    padding: 12px 10px !important;
}

.sidebar-collapsed .paypal-subicon-wrapper {
    margin: 0 !important;
}

.sidebar-collapsed .paypal-indicator {
    display: none !important;
}

/* إصلاح للشريط الجانبي المطوي */
.sidebar-container.collapsed .paypal-text,
.sidebar-container.collapsed .paypal-sublink span {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
}

.sidebar-container.collapsed .paypal-main-link {
    justify-content: center !important;
    padding: 15px 10px !important;
}

.sidebar-container.collapsed .paypal-icon-wrapper {
    margin: 0 !important;
}

.sidebar-container.collapsed .paypal-chevron {
    display: none !important;
}

.sidebar-container.collapsed .paypal-menu {
    margin: 8px 5px !important;
}

.sidebar-container.collapsed .paypal-sublink {
    justify-content: center !important;
    padding: 12px 10px !important;
}

.sidebar-container.collapsed .paypal-subicon-wrapper {
    margin: 0 !important;
}

.sidebar-container.collapsed .paypal-indicator {
    display: none !important;
}

/* تأثيرات إضافية للتفاعل */
.paypal-sublink::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 112, 186, 0.1) 0%, rgba(0, 48, 135, 0.1) 100%);
    transition: width 0.3s ease;
    border-radius: 8px;
}

.paypal-sublink:hover::before {
    width: 100%;
}

.paypal-sublink.active::before {
    width: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* تحسين النصوص */
.paypal-sublink span {
    font-weight: 500;
    font-size: 14px;
    flex: 1;
    transition: all 0.3s ease;
}

.paypal-sublink:hover span {
    font-weight: 600;
}

.paypal-sublink.active span {
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* تأثير الضوء */
.paypal-menu::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #0070ba, #003087, #0070ba);
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.paypal-menu:hover::after {
    opacity: 0.3;
}

/* تحسين الانتقالات */
.paypal-submenu .paypal-sublink {
    opacity: 0;
    transform: translateY(-10px);
    animation: slideInPaypal 0.3s ease forwards;
}

.paypal-submenu .paypal-sublink:nth-child(1) { animation-delay: 0.1s; }
.paypal-submenu .paypal-sublink:nth-child(2) { animation-delay: 0.2s; }
.paypal-submenu .paypal-sublink:nth-child(3) { animation-delay: 0.3s; }
.paypal-submenu .paypal-sublink:nth-child(4) { animation-delay: 0.4s; }
.paypal-submenu .paypal-sublink:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInPaypal {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

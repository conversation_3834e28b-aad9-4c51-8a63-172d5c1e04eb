<?php
/**
 * إنشاء جدول الدول وإضافة بيانات تجريبية
 * Create Countries Table and Add Sample Data
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

$success_messages = [];
$error_messages = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = PDODatabase::getConnection();
        
        // إنشاء جدول الدول
        $create_table_sql = "
        CREATE TABLE IF NOT EXISTS countries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name_ar VARCHAR(100) NOT NULL COMMENT 'اسم الدولة بالعربية',
            name_en VARCHAR(100) NULL COMMENT 'اسم الدولة بالإنجليزية',
            currency_code VARCHAR(3) NOT NULL COMMENT 'رمز العملة',
            currency_symbol VARCHAR(10) NOT NULL COMMENT 'رمز العملة',
            is_active TINYINT(1) DEFAULT 1 COMMENT 'حالة النشاط',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_is_active (is_active),
            INDEX idx_currency_code (currency_code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الدول والعملات'
        ";
        
        $pdo->exec($create_table_sql);
        $success_messages[] = "تم إنشاء جدول countries بنجاح";
        
        // التحقق من وجود بيانات
        $stmt = $pdo->query("SELECT COUNT(*) FROM countries");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            // إضافة بيانات تجريبية
            $countries_data = [
                ['مصر', 'Egypt', 'EGP', 'ج.م'],
                ['السعودية', 'Saudi Arabia', 'SAR', 'ر.س'],
                ['الإمارات العربية المتحدة', 'UAE', 'AED', 'د.إ'],
                ['الكويت', 'Kuwait', 'KWD', 'د.ك'],
                ['قطر', 'Qatar', 'QAR', 'ر.ق'],
                ['البحرين', 'Bahrain', 'BHD', 'د.ب'],
                ['عمان', 'Oman', 'OMR', 'ر.ع'],
                ['الأردن', 'Jordan', 'JOD', 'د.أ'],
                ['لبنان', 'Lebanon', 'LBP', 'ل.ل'],
                ['العراق', 'Iraq', 'IQD', 'د.ع'],
                ['سوريا', 'Syria', 'SYP', 'ل.س'],
                ['فلسطين', 'Palestine', 'ILS', '₪'],
                ['المغرب', 'Morocco', 'MAD', 'د.م'],
                ['الجزائر', 'Algeria', 'DZD', 'د.ج'],
                ['تونس', 'Tunisia', 'TND', 'د.ت'],
                ['ليبيا', 'Libya', 'LYD', 'د.ل'],
                ['السودان', 'Sudan', 'SDG', 'ج.س'],
                ['اليمن', 'Yemen', 'YER', 'ر.ي'],
                ['الولايات المتحدة', 'United States', 'USD', '$'],
                ['المملكة المتحدة', 'United Kingdom', 'GBP', '£'],
                ['دول الاتحاد الأوروبي', 'European Union', 'EUR', '€'],
                ['كندا', 'Canada', 'CAD', 'C$'],
                ['أستراليا', 'Australia', 'AUD', 'A$'],
                ['اليابان', 'Japan', 'JPY', '¥'],
                ['الصين', 'China', 'CNY', '¥'],
                ['الهند', 'India', 'INR', '₹'],
                ['روسيا', 'Russia', 'RUB', '₽'],
                ['تركيا', 'Turkey', 'TRY', '₺'],
                ['إيران', 'Iran', 'IRR', '﷼'],
                ['باكستان', 'Pakistan', 'PKR', '₨']
            ];
            
            $insert_sql = "INSERT INTO countries (name_ar, name_en, currency_code, currency_symbol, is_active) VALUES (?, ?, ?, ?, 1)";
            $stmt = $pdo->prepare($insert_sql);
            
            $inserted_count = 0;
            foreach ($countries_data as $country) {
                if ($stmt->execute($country)) {
                    $inserted_count++;
                }
            }
            
            $success_messages[] = "تم إضافة {$inserted_count} دولة بنجاح";
        } else {
            $success_messages[] = "الجدول يحتوي على {$count} دولة بالفعل";
        }
        
    } catch (Exception $e) {
        $error_messages[] = "خطأ: " . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جدول الدول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-database text-primary"></i>
            إنشاء جدول الدول
        </h1>
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($success_messages)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تم بنجاح!</h5>
                <ul class="mb-0">
                    <?php foreach ($success_messages as $message): ?>
                        <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error_messages)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-times-circle me-2"></i>حدثت أخطاء!</h5>
                <ul class="mb-0">
                    <?php foreach ($error_messages as $message): ?>
                        <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- معلومات الجدول -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات جدول الدول</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الحقول المطلوبة:</h6>
                        <ul>
                            <li><strong>id:</strong> المعرف الفريد (تلقائي)</li>
                            <li><strong>name_ar:</strong> اسم الدولة بالعربية</li>
                            <li><strong>name_en:</strong> اسم الدولة بالإنجليزية (اختياري)</li>
                            <li><strong>currency_code:</strong> رمز العملة (مثل USD)</li>
                            <li><strong>currency_symbol:</strong> رمز العملة (مثل $)</li>
                            <li><strong>is_active:</strong> حالة النشاط (1 = نشط, 0 = غير نشط)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الدول التي سيتم إضافتها:</h6>
                        <ul class="small">
                            <li>دول الخليج العربي (7 دول)</li>
                            <li>دول المشرق العربي (6 دول)</li>
                            <li>دول المغرب العربي (5 دول)</li>
                            <li>دول عربية أخرى (3 دول)</li>
                            <li>دول عالمية مهمة (9 دول)</li>
                        </ul>
                        <strong>المجموع: 30 دولة</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج الإنشاء -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>إنشاء الجدول وإضافة البيانات</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                        <ul class="mb-0">
                            <li>سيتم إنشاء جدول <code>countries</code> إذا لم يكن موجوداً</li>
                            <li>سيتم إضافة 30 دولة مع عملاتها إذا كان الجدول فارغاً</li>
                            <li>لن يتم حذف أي بيانات موجودة</li>
                            <li>العملية آمنة ويمكن تكرارها</li>
                        </ul>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-database me-2"></i>
                            إنشاء الجدول وإضافة البيانات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- عرض بنية الجدول -->
        <div class="card mt-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-code me-2"></i>بنية الجدول (SQL)</h5>
            </div>
            <div class="card-body">
                <div class="code-block">
                    <code>
                    CREATE TABLE IF NOT EXISTS countries (<br>
                    &nbsp;&nbsp;id INT AUTO_INCREMENT PRIMARY KEY,<br>
                    &nbsp;&nbsp;name_ar VARCHAR(100) NOT NULL COMMENT 'اسم الدولة بالعربية',<br>
                    &nbsp;&nbsp;name_en VARCHAR(100) NULL COMMENT 'اسم الدولة بالإنجليزية',<br>
                    &nbsp;&nbsp;currency_code VARCHAR(3) NOT NULL COMMENT 'رمز العملة',<br>
                    &nbsp;&nbsp;currency_symbol VARCHAR(10) NOT NULL COMMENT 'رمز العملة',<br>
                    &nbsp;&nbsp;is_active TINYINT(1) DEFAULT 1 COMMENT 'حالة النشاط',<br>
                    &nbsp;&nbsp;created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,<br>
                    &nbsp;&nbsp;updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,<br>
                    &nbsp;&nbsp;INDEX idx_is_active (is_active),<br>
                    &nbsp;&nbsp;INDEX idx_currency_code (currency_code)<br>
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                    </code>
                </div>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="text-center mt-4">
            <a href="test_countries_database.php" class="btn btn-info btn-lg">
                <i class="fas fa-search me-2"></i>
                فحص الجدول والبيانات
            </a>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-success btn-lg ms-2" target="_blank">
                <i class="fas fa-plus me-2"></i>
                اختبار البحث التفاعلي
            </a>
        </div>

        <!-- معلومات إضافية -->
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb me-2"></i>ملاحظات مهمة:</h6>
            <ul class="mb-0">
                <li><strong>الترميز:</strong> يستخدم الجدول ترميز UTF-8 لدعم النصوص العربية</li>
                <li><strong>الفهارس:</strong> تم إضافة فهارس لتحسين الأداء</li>
                <li><strong>التواريخ:</strong> يتم حفظ تاريخ الإنشاء والتحديث تلقائياً</li>
                <li><strong>المرونة:</strong> يمكن إضافة أو تعديل الدول لاحقاً</li>
                <li><strong>الأمان:</strong> يتم التحقق من صحة البيانات قبل الإدراج</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

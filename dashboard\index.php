<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/exchange_manager.php';
require_once __DIR__ . '/../includes/transfer_manager.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/office_manager.php';
require_once __DIR__ . '/../includes/paypal_manager.php';

$auth = new Auth();
$auth->requireLogin();
$current_user = $auth->getCurrentUser();

// Instantiate managers
$db = new Database();
$exchangeManager = new ExchangeManager($db);
$transferManager = new TransferManager($db);
$customerManager = new CustomerManager($db);
$officeManager = new OfficeManager($db);
$paypalManager = new PayPalManager($db);

// Get today's statistics
$today = date('Y-m-d');
$todayExchanges = $exchangeManager->getTodayExchangeCount();
$todayTransfers = $transferManager->getTodayTransferCount();
$todayCustomers = $customerManager->getTodayCustomerCount();
$todayProfit = $exchangeManager->getTodayProfit();

// Get percentage changes
$exchangeChange = $exchangeManager->getExchangeCountChange();
$transferChange = $transferManager->getTransferCountChange();
$customerChange = $customerManager->getCustomerCountChange();
$profitChange = $exchangeManager->getProfitChange();

// Get latest exchange rates
$latestRates = $exchangeManager->getLatestRates();

// Get latest activities
$latestActivities = $exchangeManager->getLatestActivities(5);

// Get office statistics
$offices = $officeManager->getAllOffices(['is_active' => 1]);
$totalOffices = count($offices);
$activeOffices = 0;
$totalOfficeBalance = 0;

foreach ($offices as $office) {
    if ($office['is_active']) {
        $activeOffices++;
    }
    $balance = $officeManager->calculateOfficeBalance($office['id']);
    $totalOfficeBalance += $balance['balance'];
}

// Get PayPal statistics
$paypalStats = $paypalManager->getStatistics();
$totalPaypalTransfers = $paypalStats['total_count'];
$totalPaypalAmount = $paypalStats['total_amount'];
$receivedPaypalAmount = $paypalStats['received_amount'];
$pendingPaypalAmount = $paypalStats['pending_amount'];

// Page title
$pageTitle = 'لوحة التحكم';

include __DIR__ . '/../includes/header.php';
?>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Include Enhanced Dashboard JavaScript -->
<script src="<?php echo BASE_URL; ?>assets/js/dashboard-enhanced.js"></script>

<!-- Bootstrap 5 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Enhanced Dashboard Container -->
<div class="dashboard-container">
    <div class="dashboard-content">
        
        <!-- Enhanced Page Header -->
        <div class="dashboard-header">
            <div class="dashboard-header-content">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1>مرحباً، <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username'], ENT_QUOTES, 'UTF-8'); ?>! 👋</h1>
                        <p>نظرة عامة على نشاط النظام وإحصائيات اليوم</p>
                    </div>
                    <div class="dashboard-date">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <?php echo date('l, d F Y'); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Navigation Buttons -->
        <div class="navigation-buttons">
            <?php if ($auth->hasPermission('exchange.create')): ?>
            <a href="exchange.php" class="nav-btn primary">
                <i class="fas fa-coins"></i>
                عملية صرافة جديدة
            </a>
            <?php endif; ?>

            <?php if ($auth->hasPermission('transfers.create')): ?>
            <a href="transfers.php" class="nav-btn success">
                <i class="fas fa-money-bill-wave"></i>
                حوالة جديدة
            </a>
            <?php endif; ?>

            <?php if ($auth->hasPermission('customers.create')): ?>
            <a href="add_customer.php" class="nav-btn warning">
                <i class="fas fa-user-plus"></i>
                إضافة عميل
            </a>
            <?php endif; ?>

            <?php if ($auth->hasPermission('reports.view')): ?>
            <a href="reports.php" class="nav-btn secondary">
                <i class="fas fa-chart-bar"></i>
                التقارير
            </a>
            <?php endif; ?>

            <a href="customers.php" class="nav-btn secondary">
                <i class="fas fa-users"></i>
                العملاء
            </a>

            <a href="exchange_rates.php" class="nav-btn secondary">
                <i class="fas fa-chart-line"></i>
                أسعار العملات
            </a>

            <a href="all_transfers.php" class="nav-btn secondary">
                <i class="fas fa-list"></i>
                جميع الحوالات
            </a>

            <a href="users.php" class="nav-btn secondary">
                <i class="fas fa-user-cog"></i>
                إدارة المستخدمين
            </a>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="stats-grid">
            <!-- Exchange Operations Card -->
            <div class="stats-card primary">
                <div class="stats-content">
                    <div class="stats-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="stats-details">
                        <div class="stats-value"><?php echo number_format($todayExchanges); ?></div>
                        <div class="stats-label">عمليات الصرافة اليوم</div>
                        <div class="stats-change <?php echo $exchangeChange >= 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-<?php echo $exchangeChange >= 0 ? 'arrow-up' : 'arrow-down'; ?>"></i>
                            <?php echo ($exchangeChange >= 0 ? '+' : '') . number_format($exchangeChange, 2); ?>%
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transfers Card -->
            <div class="stats-card info">
                <div class="stats-content">
                    <div class="stats-icon info">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="stats-details">
                        <div class="stats-value"><?php echo number_format($todayTransfers); ?></div>
                        <div class="stats-label">الحوالات اليوم</div>
                        <div class="stats-change <?php echo $transferChange >= 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-<?php echo $transferChange >= 0 ? 'arrow-up' : 'arrow-down'; ?>"></i>
                            <?php echo ($transferChange >= 0 ? '+' : '') . number_format($transferChange, 2); ?>%
                        </div>
                    </div>
                </div>
            </div>

            <!-- New Customers Card -->
            <div class="stats-card success">
                <div class="stats-content">
                    <div class="stats-icon success">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="stats-details">
                        <div class="stats-value"><?php echo number_format($todayCustomers); ?></div>
                        <div class="stats-label">العملاء الجدد</div>
                        <div class="stats-change <?php echo $customerChange >= 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-<?php echo $customerChange >= 0 ? 'arrow-up' : 'arrow-down'; ?>"></i>
                            <?php echo ($customerChange >= 0 ? '+' : '') . number_format($customerChange, 2); ?>%
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profits Card -->
            <div class="stats-card warning">
                <div class="stats-content">
                    <div class="stats-icon warning">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stats-details">
                        <div class="stats-value">$<?php echo number_format($todayProfit, 2); ?></div>
                        <div class="stats-label">الأرباح اليوم</div>
                        <div class="stats-change <?php echo $profitChange >= 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-<?php echo $profitChange >= 0 ? 'arrow-up' : 'arrow-down'; ?>"></i>
                            <?php echo ($profitChange >= 0 ? '+' : '') . number_format($profitChange, 2); ?>%
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Offices and PayPal Section -->
        <div class="charts-section">
            <!-- Offices Statistics -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">إحصائيات المكاتب</h3>
                    <a href="offices.php" class="nav-btn secondary" style="padding: 6px 12px; font-size: 0.8rem;">
                        <i class="fas fa-external-link-alt"></i>
                        إدارة المكاتب
                    </a>
                </div>
                <div class="offices-stats">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-building"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value"><?php echo $totalOffices; ?></div>
                                    <div class="stat-label">إجمالي المكاتب</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="stat-item">
                                <div class="stat-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value"><?php echo $activeOffices; ?></div>
                                    <div class="stat-label">المكاتب النشطة</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="stat-item">
                                <div class="stat-icon warning">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">$<?php echo number_format($totalOfficeBalance, 2); ?></div>
                                    <div class="stat-label">إجمالي رصيد المكاتب</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PayPal Statistics -->
            <div class="exchange-rates-card">
                <div class="chart-header">
                    <h3 class="chart-title">إحصائيات PayPal</h3>
                    <a href="paypal_transfers.php" class="nav-btn secondary" style="padding: 6px 12px; font-size: 0.8rem;">
                        <i class="fas fa-external-link-alt"></i>
                        إدارة PayPal
                    </a>
                </div>
                <div class="paypal-stats">
                    <div class="stat-item">
                        <div class="stat-icon info">
                            <i class="fab fa-paypal"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo number_format($totalPaypalTransfers); ?></div>
                            <div class="stat-label">إجمالي حوالات PayPal</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value">$<?php echo number_format($totalPaypalAmount, 2); ?></div>
                            <div class="stat-label">إجمالي مبالغ PayPal</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon warning">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value">$<?php echo number_format($receivedPaypalAmount, 2); ?></div>
                            <div class="stat-label">المبالغ المستلمة</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon danger">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-value">$<?php echo number_format($pendingPaypalAmount, 2); ?></div>
                            <div class="stat-label">المبالغ المعلقة</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exchange Rates Section -->
        <div class="charts-section">
            <!-- Exchange Rates -->
            <div class="exchange-rates-card">
                <div class="chart-header">
                    <h3 class="chart-title">أسعار العملات الحالية</h3>
                    <a href="exchange_rates.php" class="nav-btn secondary" style="padding: 6px 12px; font-size: 0.8rem;">
                        <i class="fas fa-external-link-alt"></i>
                        عرض الكل
                    </a>
                </div>
                <div class="rates-list">
                    <?php foreach ($latestRates as $rate): ?>
                    <div class="rate-item">
                        <div class="rate-info">
                            <div class="rate-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="rate-details">
                                <h4><?php echo htmlspecialchars($rate['from_currency']); ?> / <?php echo htmlspecialchars($rate['to_currency']); ?></h4>
                                <p>تحديث <?php echo date('H:i'); ?></p>
                            </div>
                        </div>
                        <div class="rate-value">
                            <div class="amount"><?php echo number_format($rate['rate'], 4); ?></div>
                            <div class="change">+0.12%</div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Latest Activities -->
            <div class="activity-card">
                <div class="chart-header">
                    <h3 class="chart-title">آخر النشاطات</h3>
                    <button class="nav-btn secondary" style="padding: 6px 12px; font-size: 0.8rem;">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
                <div class="activities-list">
                    <?php foreach ($latestActivities as $activity): ?>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="activity-details">
                            <div class="activity-title">
                                <?php echo htmlspecialchars($activity['customer_name']); ?> - 
                                <?php echo number_format($activity['amount_from'], 2) . ' ' . $activity['from_currency']; ?> 
                                <i class="fas fa-arrow-right mx-1"></i> 
                                <?php echo number_format($activity['amount_to'], 2) . ' ' . $activity['to_currency']; ?>
                            </div>
                            <div class="activity-time">
                                <?php 
                                $timestamp = strtotime($activity['created_at']);
                                $diff = time() - $timestamp;
                                if ($diff < 60) {
                                    echo 'منذ ' . $diff . ' ثانية';
                                } elseif ($diff < 3600) {
                                    echo 'منذ ' . floor($diff/60) . ' دقيقة';
                                } elseif ($diff < 86400) {
                                    echo 'منذ ' . floor($diff/3600) . ' ساعة';
                                } else {
                                    echo date('Y-m-d H:i', $timestamp);
                                }
                                ?>
                            </div>
                        </div>
                        <div class="activity-amount">
                            <?php echo number_format($activity['amount_from'], 2); ?> <?php echo $activity['from_currency']; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php if (empty($latestActivities)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-3"></i>
                        <p>لا توجد نشاطات حديثة</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Enhanced Activity Section -->
        <div class="activity-section">
            <!-- Quick Actions -->
            <div class="activity-card">
                <div class="chart-header">
                    <h3 class="chart-title">إجراءات سريعة</h3>
                </div>
                <div class="quick-actions-grid">
                    <?php if ($auth->hasPermission('exchange.create')): ?>
                    <a href="exchange.php" class="quick-action-card exchange">
                        <div class="quick-action-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="quick-action-title">عملية صرافة</div>
                        <div class="quick-action-desc">إضافة عملية صرافة جديدة</div>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('transfers.create')): ?>
                    <a href="transfers.php" class="quick-action-card transfer">
                        <div class="quick-action-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="quick-action-title">حوالة جديدة</div>
                        <div class="quick-action-desc">إنشاء حوالة مالية</div>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('customers.create')): ?>
                    <a href="add_customer.php" class="quick-action-card customer">
                        <div class="quick-action-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="quick-action-title">إضافة عميل</div>
                        <div class="quick-action-desc">تسجيل عميل جديد</div>
                    </a>
                    <?php endif; ?>

                    <?php if ($auth->hasPermission('reports.view')): ?>
                    <a href="reports.php" class="quick-action-card reports">
                        <div class="quick-action-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="quick-action-title">التقارير</div>
                        <div class="quick-action-desc">عرض التقارير والإحصائيات</div>
                    </a>
                    <?php endif; ?>

                    <a href="customers.php" class="quick-action-card customer">
                        <div class="quick-action-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="quick-action-title">إدارة العملاء</div>
                        <div class="quick-action-desc">عرض وإدارة العملاء</div>
                    </a>

                    <a href="exchange_rates.php" class="quick-action-card reports">
                        <div class="quick-action-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="quick-action-title">أسعار العملات</div>
                        <div class="quick-action-desc">إدارة أسعار العملات</div>
                    </a>

                    <a href="offices.php" class="quick-action-card office">
                        <div class="quick-action-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="quick-action-title">إدارة المكاتب</div>
                        <div class="quick-action-desc">إدارة المكاتب والعمليات</div>
                    </a>

                    <a href="paypal_transfers.php" class="quick-action-card paypal">
                        <div class="quick-action-icon">
                            <i class="fab fa-paypal"></i>
                        </div>
                        <div class="quick-action-title">حوالات PayPal</div>
                        <div class="quick-action-desc">إدارة حوالات PayPal</div>
                    </a>
                </div>
            </div>

            <!-- System Status -->
            <div class="activity-card">
                <div class="chart-header">
                    <h3 class="chart-title">حالة النظام</h3>
                </div>
                <div class="system-status">
                    <div class="status-item">
                        <div class="status-icon success">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-title">قاعدة البيانات</div>
                            <div class="status-value">متصل</div>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon success">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-title">الأمان</div>
                            <div class="status-value">مفعل</div>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon success">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="status-info">
                            <div class="status-title">التحديثات</div>
                            <div class="status-value">آخر تحديث: <?php echo date('H:i'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?php include __DIR__ . '/../includes/footer.php'; ?>
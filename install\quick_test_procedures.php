<?php
/**
 * اختبار سريع للإجراءات المخزنة
 * Quick Test for Stored Procedures
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للإجراءات المخزنة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bolt text-warning"></i>
            اختبار سريع للإجراءات المخزنة
        </h1>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                echo "<div class='test-result test-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
            
            // اختبار الإجراءات المخزنة
            $procedures = [
                'UpdateDailyTransaction' => 'إجراء تحديث المعاملات',
                'DeleteDailyTransaction' => 'إجراء حذف المعاملات', 
                'GetDeletedDailyTransactions' => 'إجراء جلب المعاملات المحذوفة'
            ];
            
            foreach ($procedures as $procedure => $description) {
                try {
                    $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = '{$procedure}' AND Db = '" . DB_NAME . "'");
                    $exists = $stmt->rowCount() > 0;
                    showResult($description, $exists, $exists ? "الإجراء موجود" : "الإجراء غير موجود");
                } catch (Exception $e) {
                    showResult($description, false, $e->getMessage());
                }
            }
            
            // اختبار المشغل
            try {
                $stmt = $pdo->query("SHOW TRIGGERS FROM " . DB_NAME);
                $triggers = $stmt->fetchAll();
                $trigger_found = false;
                
                foreach ($triggers as $trigger) {
                    if ($trigger['Trigger'] == 'before_daily_transaction_delete') {
                        $trigger_found = true;
                        break;
                    }
                }
                
                showResult("مشغل حفظ المعاملات المحذوفة", $trigger_found, $trigger_found ? "المشغل موجود" : "المشغل غير موجود");
            } catch (Exception $e) {
                showResult("مشغل حفظ المعاملات المحذوفة", false, $e->getMessage());
            }
            
            // اختبار وجود معاملة للتعديل
            try {
                $stmt = $pdo->query("SELECT id, transaction_number FROM daily_transactions ORDER BY id DESC LIMIT 1");
                $transaction = $stmt->fetch();
                
                if ($transaction) {
                    showResult("وجود معاملة للاختبار", true, "المعاملة #{$transaction['transaction_number']} (ID: {$transaction['id']})");
                    
                    // اختبار إجراء التحديث
                    try {
                        $stmt = $pdo->prepare("CALL UpdateDailyTransaction(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @result_message)");
                        $stmt->execute([
                            $transaction['id'], // transaction_id
                            1, // country_id
                            1000.00, // base_amount
                            1.5, // customer_rate
                            'multiply', // operation_type
                            250.0, // exchange_rate
                            'cash', // delivery_type
                            null, // transfer_amount
                            'اختبار التحديث', // recipient_name
                            'اختبار إجراء التحديث', // notes
                            1 // updated_by
                        ]);
                        
                        // إغلاق النتائج المعلقة
                        while ($stmt->nextRowset()) {
                            // استهلاك جميع مجموعات النتائج
                        }
                        $stmt->closeCursor();
                        
                        // الحصول على النتائج
                        $result_stmt = $pdo->query("SELECT @result_message as result_message");
                        $result = $result_stmt->fetch();
                        $result_stmt->closeCursor();
                        
                        if (strpos($result['result_message'], 'بنجاح') !== false) {
                            showResult("اختبار إجراء التحديث", true, $result['result_message']);
                        } else {
                            showResult("اختبار إجراء التحديث", false, $result['result_message']);
                        }
                    } catch (Exception $e) {
                        showResult("اختبار إجراء التحديث", false, $e->getMessage());
                    }
                } else {
                    showResult("وجود معاملة للاختبار", false, "لا توجد معاملات في قاعدة البيانات");
                }
            } catch (Exception $e) {
                showResult("وجود معاملة للاختبار", false, $e->getMessage());
            }
            
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 90) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> جميع الإجراءات تعمل بشكل صحيح";
            echo "</div>";
        } elseif ($success_rate >= 70) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> معظم الإجراءات تعمل";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج إصلاح!</strong> هناك مشاكل في الإجراءات";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <div class="text-center mt-4">
            <a href="../dashboard/daily_transactions.php" class="btn btn-primary btn-lg">
                <i class="fas fa-list me-2"></i>
                قائمة المعاملات
            </a>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-success btn-lg ms-2">
                <i class="fas fa-plus me-2"></i>
                إضافة معاملة
            </a>
            <a href="../dashboard/deleted_daily_transactions.php" class="btn btn-warning btn-lg ms-2">
                <i class="fas fa-trash me-2"></i>
                المعاملات المحذوفة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * Bank Account History Page
 * Display movement history for a specific bank account
 */

// Required includes
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/bank_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

// Initialize classes
$auth = new Auth();
$db = new Database();
$bankManager = new BankManager($db);

// Authentication and permission checks
$auth->requireLogin();
$auth->requirePermission('cash.view');

// Get current user
$current_user_id = $auth->getCurrentUser()['id'] ?? null;

// Get bank account ID from URL
$bankAccountId = (int)($_GET['id'] ?? 0);

if ($bankAccountId <= 0) {
    set_flash('danger', 'معرف الحساب البنكي غير صالح');
    redirect('cash.php?tab=bank');
    exit;
}

// Get bank account details
try {
    $bankAccount = $bankManager->getBankAccountById($bankAccountId);
    if (!$bankAccount) {
        set_flash('danger', 'الحساب البنكي غير موجود');
        redirect('cash.php?tab=bank');
        exit;
    }
} catch (Exception $e) {
    set_flash('danger', 'خطأ في تحميل بيانات الحساب البنكي: ' . $e->getMessage());
    redirect('cash.php?tab=bank');
    exit;
}

// Pagination settings
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Date filters
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$movementType = $_GET['type'] ?? '';

// Build filter conditions
$filters = [];
if ($dateFrom) {
    $filters['date_from'] = $dateFrom;
}
if ($dateTo) {
    $filters['date_to'] = $dateTo;
}
if ($movementType && in_array($movementType, ['deposit', 'withdrawal'])) {
    $filters['type'] = $movementType;
}

// Get movements with pagination
try {
    $movements = $bankManager->getBankAccountMovements($bankAccountId, $filters, $limit, $offset);
    $totalMovements = $bankManager->getBankAccountMovementsCount($bankAccountId, $filters);
    $totalPages = ceil($totalMovements / $limit);
} catch (Exception $e) {
    $movements = [];
    $totalMovements = 0;
    $totalPages = 0;
    $error_message = 'خطأ في تحميل الحركات: ' . $e->getMessage();
}

// Calculate summary statistics
try {
    $summary = $bankManager->getBankAccountSummary($bankAccountId, $filters);
} catch (Exception $e) {
    $summary = [
        'total_deposits' => 0,
        'total_withdrawals' => 0,
        'net_change' => 0,
        'movement_count' => 0
    ];
}

// تسجيل عملية عرض تاريخ الحركات البنكية
ActivityHelper::logView('bank_movements', 'عرض تاريخ حركات الحساب البنكي: ' . $bankAccount['account_name'], [
    'bank_account_id' => $bankAccountId,
    'filters' => $filters,
    'page' => $page
]);

// Set page title
$page_title = 'تاريخ حركات الحساب البنكي - ' . htmlspecialchars($bankAccount['account_name']);

// Include header
include __DIR__ . '/../includes/header.php';
?>

<!-- Dashboard content starts here -->
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-university me-2"></i>
                    تاريخ حركات الحساب البنكي
                </h1>
                <p class="text-muted mb-0">
                    عرض جميع الحركات المالية للحساب: 
                    <strong><?php echo htmlspecialchars($bankAccount['account_name']); ?></strong>
                    في بنك <strong><?php echo htmlspecialchars($bankAccount['bank_name']); ?></strong>
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="cash.php?tab=bank" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للحسابات
                </a>
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>

    <!-- Bank Account Info Card -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الحساب البنكي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الحساب:</strong> <?php echo htmlspecialchars($bankAccount['account_name']); ?></p>
                            <p><strong>رقم الحساب:</strong> 
                                <code><?php echo htmlspecialchars($bankAccount['account_number']); ?></code>
                            </p>
                            <p><strong>البنك:</strong> <?php echo htmlspecialchars($bankAccount['bank_name']); ?></p>
                            <p><strong>العملة:</strong> <?php echo htmlspecialchars($bankAccount['currency_name'] . ' (' . $bankAccount['currency_code'] . ')'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الفرع:</strong> <?php echo htmlspecialchars($bankAccount['branch_name']); ?></p>
                            <p><strong>الرصيد الحالي:</strong> 
                                <span class="cash-balance <?php 
                                    $balance = (float)$bankAccount['current_balance'];
                                    echo $balance > 0 ? 'positive' : ($balance < 0 ? 'negative' : 'zero');
                                ?>">
                                    <?php echo number_format($balance, 2); ?>
                                    <?php echo htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']); ?>
                                </span>
                            </p>
                            <p><strong>تاريخ الإنشاء:</strong> 
                                <?php echo date('Y-m-d', strtotime($bankAccount['created_at'])); ?>
                            </p>
                            <p><strong>الحالة:</strong> 
                                <span class="status-badge <?php echo $bankAccount['is_active'] ? 'active' : 'inactive'; ?>">
                                    <i class="fas fa-<?php echo $bankAccount['is_active'] ? 'check-circle' : 'times-circle'; ?> me-1"></i>
                                    <?php echo $bankAccount['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Summary Statistics -->
        <div class="col-md-4">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        ملخص الحركات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">إجمالي الإيداعات</small>
                        <div class="h5 text-success mb-0">
                            <?php echo number_format($summary['total_deposits'], 2); ?>
                            <?php echo htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']); ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">إجمالي السحوبات</small>
                        <div class="h5 text-warning mb-0">
                            <?php echo number_format($summary['total_withdrawals'], 2); ?>
                            <?php echo htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']); ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">صافي التغيير</small>
                        <div class="h5 <?php echo $summary['net_change'] >= 0 ? 'text-success' : 'text-danger'; ?> mb-0">
                            <?php echo ($summary['net_change'] >= 0 ? '+' : '') . number_format($summary['net_change'], 2); ?>
                            <?php echo htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']); ?>
                        </div>
                    </div>
                    <div>
                        <small class="text-muted">عدد الحركات</small>
                        <div class="h5 text-info mb-0"><?php echo number_format($summary['movement_count']); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="dashboard-card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>
                تصفية الحركات
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="id" value="<?php echo $bankAccountId; ?>">
                
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" 
                           value="<?php echo htmlspecialchars($dateFrom); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" 
                           value="<?php echo htmlspecialchars($dateTo); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">نوع الحركة</label>
                    <select name="type" class="form-select">
                        <option value="">جميع الحركات</option>
                        <option value="deposit" <?php echo $movementType === 'deposit' ? 'selected' : ''; ?>>إيداع</option>
                        <option value="withdrawal" <?php echo $movementType === 'withdrawal' ? 'selected' : ''; ?>>سحب</option>
                    </select>
                </div>
                
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        تصفية
                    </button>
                    <a href="?id=<?php echo $bankAccountId; ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Movements Table -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    سجل الحركات البنكية
                    <?php if ($totalMovements > 0): ?>
                        <span class="badge bg-primary"><?php echo number_format($totalMovements); ?></span>
                    <?php endif; ?>
                </h5>

                <?php if (!empty($movements)): ?>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV()">
                        <i class="fas fa-file-csv me-1"></i>
                        تصدير CSV
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i>
                        تصدير Excel
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="generateBankStatement()">
                        <i class="fas fa-file-pdf me-1"></i>
                        كشف حساب
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php elseif (empty($movements)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-university fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد حركات بنكية</h5>
                    <p class="text-muted">لم يتم العثور على حركات مالية للحساب البنكي في الفترة المحددة</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="bankMovementsTable">
                        <thead>
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>نوع الحركة</th>
                                <th>المبلغ</th>
                                <th>الرصيد بعد الحركة</th>
                                <th>الوصف</th>
                                <th>رقم المرجع</th>
                                <th>رقم الإيصال</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($movements as $movement): ?>
                                <tr>
                                    <td>
                                        <div class="fw-medium">
                                            <?php echo date('Y-m-d', strtotime($movement['created_at'])); ?>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo date('H:i:s', strtotime($movement['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($movement['type'] === 'deposit'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-arrow-down me-1"></i>
                                                إيداع بنكي
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-arrow-up me-1"></i>
                                                سحب بنكي
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="fw-medium <?php echo $movement['type'] === 'deposit' ? 'text-success' : 'text-warning'; ?>">
                                            <?php echo $movement['type'] === 'deposit' ? '+' : '-'; ?>
                                            <?php echo number_format($movement['amount'], 2); ?>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']); ?>
                                            </small>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="cash-balance <?php
                                            $balance = (float)$movement['balance_after'];
                                            echo $balance > 0 ? 'positive' : ($balance < 0 ? 'negative' : 'zero');
                                        ?>">
                                            <?php echo number_format($balance, 2); ?>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']); ?>
                                            </small>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($movement['description'])): ?>
                                            <span class="text-truncate" style="max-width: 200px;"
                                                  title="<?php echo htmlspecialchars($movement['description']); ?>">
                                                <?php echo htmlspecialchars($movement['description']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($movement['reference_number'])): ?>
                                            <code class="small bg-light px-2 py-1 rounded">
                                                <?php echo htmlspecialchars($movement['reference_number']); ?>
                                            </code>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($movement['receipt_number'])): ?>
                                            <code class="small bg-info text-white px-2 py-1 rounded">
                                                <?php echo htmlspecialchars($movement['receipt_number']); ?>
                                            </code>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <i class="fas fa-user-circle text-muted"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium small">
                                                    <?php echo htmlspecialchars($movement['user_name'] ?: 'غير محدد'); ?>
                                                </div>
                                                <small class="text-muted">
                                                    ID: <?php echo $movement['created_by']; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-info"
                                                    onclick="viewBankMovementDetails(<?php echo $movement['id']; ?>)"
                                                    data-bs-toggle="tooltip"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if (!empty($movement['receipt_number'])): ?>
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="printReceipt(<?php echo $movement['id']; ?>)"
                                                        data-bs-toggle="tooltip"
                                                        title="طباعة الإيصال">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            <?php endif; ?>
                                            <?php if ($auth->hasPermission('cash.movements.edit')): ?>
                                                <button class="btn btn-sm btn-outline-warning"
                                                        onclick="editBankMovement(<?php echo $movement['id']; ?>)"
                                                        data-bs-toggle="tooltip"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            <?php endif; ?>
                                            <?php if ($auth->hasPermission('cash.movements.delete')): ?>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteBankMovement(<?php echo $movement['id']; ?>)"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="تصفح الحركات البنكية" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?id=<?php echo $bankAccountId; ?>&page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'id'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $start = max(1, $page - 2);
                            $end = min($totalPages, $page + 2);

                            for ($i = $start; $i <= $end; $i++):
                            ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?id=<?php echo $bankAccountId; ?>&page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'id'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?id=<?php echo $bankAccountId; ?>&page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'id'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>

                        <div class="text-center text-muted small">
                            عرض <?php echo (($page - 1) * $limit) + 1; ?> - <?php echo min($page * $limit, $totalMovements); ?>
                            من أصل <?php echo number_format($totalMovements); ?> حركة بنكية
                        </div>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div> <!-- /.container-fluid -->

<!-- Bank Movement Details Modal -->
<div class="modal fade" id="bankMovementDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-university me-2"></i>
                    تفاصيل الحركة البنكية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="bankMovementDetailsContent">
                <!-- Content will be loaded via AJAX -->
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/footer.php'; ?>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// View bank movement details
function viewBankMovementDetails(movementId) {
    var modal = new bootstrap.Modal(document.getElementById('bankMovementDetailsModal'));
    var content = document.getElementById('bankMovementDetailsContent');

    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    modal.show();

    // Load movement details via AJAX
    fetch('ajax/get_bank_movement_details.php?id=' + movementId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${data.error || 'خطأ في تحميل التفاصيل'}
                    </div>
                `;
            }
        })
        .catch(error => {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    خطأ في الاتصال بالخادم
                </div>
            `;
        });
}

// Edit bank movement
function editBankMovement(movementId) {
    // Redirect to edit page or open edit modal
    window.location.href = 'edit_bank_movement.php?id=' + movementId;
}

// Delete bank movement
function deleteBankMovement(movementId) {
    if (confirm('هل أنت متأكد من حذف هذه الحركة البنكية؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'delete_bank_movement.php';
        form.style.display = 'none';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'movement_id';
        input.value = movementId;
        
        const bankAccountInput = document.createElement('input');
        bankAccountInput.type = 'hidden';
        bankAccountInput.name = 'bank_account_id';
        bankAccountInput.value = <?php echo $bankAccountId; ?>;
        
        form.appendChild(input);
        form.appendChild(bankAccountInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Print receipt
function printReceipt(movementId) {
    window.open('print_receipt.php?id=' + movementId + '&type=bank', '_blank');
}

// Generate bank statement
function generateBankStatement() {
    var params = new URLSearchParams(window.location.search);
    var url = 'generate_bank_statement.php?' + params.toString();
    window.open(url, '_blank');
}

// Export to CSV
function exportToCSV() {
    var table = document.getElementById('bankMovementsTable');
    var csv = [];
    var rows = table.querySelectorAll('tr');

    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll('td, th');

        for (var j = 0; j < cols.length - 1; j++) { // Exclude actions column
            var cellText = cols[j].innerText.replace(/"/g, '""');
            row.push('"' + cellText + '"');
        }

        csv.push(row.join(','));
    }

    var csvFile = new Blob([csv.join('\n')], {type: 'text/csv'});
    var downloadLink = document.createElement('a');
    downloadLink.download = 'bank_account_movements_<?php echo $bankAccountId; ?>_' + new Date().toISOString().slice(0,10) + '.csv';
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// Export to Excel (simplified)
function exportToExcel() {
    var table = document.getElementById('bankMovementsTable');
    var html = table.outerHTML;
    var url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
    var downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = 'bank_account_movements_<?php echo $bankAccountId; ?>_' + new Date().toISOString().slice(0,10) + '.xls';
    downloadLink.click();
}

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn, .pagination, .card-header .btn-group {
        display: none !important;
    }

    .dashboard-card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    .table {
        font-size: 12px;
    }

    .page-header {
        border-bottom: 2px solid #000;
        margin-bottom: 20px;
    }

    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }
}

.printing .sidebar,
.printing .navbar {
    display: none !important;
}

.printing .main-content {
    margin-right: 0 !important;
}

/* Bank-specific styling */
.bank-movement-row {
    border-right: 3px solid var(--primary);
}

.bank-movement-row.deposit {
    border-right-color: var(--success);
}

.bank-movement-row.withdrawal {
    border-right-color: var(--warning);
}
</style>

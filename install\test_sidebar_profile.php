<?php
/**
 * Test Sidebar Profile Image Display
 * This script tests the sidebar profile image functionality
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/image_helper.php';

// Create a test user for display
$testUser = [
    'id' => 999,
    'username' => 'test_user',
    'full_name' => 'مستخدم تجريبي',
    'role_name' => 'مدير النظام',
    'profile_image' => null // Test without image first
];

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1'>";
echo "<title>اختبار صورة الشريط الجانبي</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css'>";
echo "<link rel='stylesheet' href='../assets/css/profile-images.css'>";
echo "<style>";
echo ".sidebar { background: #2c3e50; color: white; padding: 20px; width: 300px; }";
echo ".profile-avatar { margin-right: 15px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h2 class='p-4'>اختبار صورة الشريط الجانبي</h2>";

echo "<div class='d-flex'>";
echo "<div class='sidebar'>";
echo "<h4 class='text-center mb-4'>Trust Plus</h4>";
echo "<hr class='border-light'>";

echo "<div class='p-4 border-bottom border-light'>";
echo "<div class='d-flex align-items-center'>";

// Test 1: Without profile image
echo "<h5>اختبار بدون صورة:</h5>";
echo "<div class='profile-avatar me-3'>";
echo "<div class='default-avatar default-avatar-lg' 
     style='width: 50px; height: 50px; background-color: rgba(255,255,255,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; border: none; color: rgba(255,255,255,0.8);'>";
echo "<i class='fas fa-user' style='font-size: 1.2em;'></i>";
echo "</div>";
echo "</div>";
echo "<div class='flex-grow-1 text-white'>";
echo "<p class='mb-1 fw-medium'>مستخدم تجريبي</p>";
echo "<p class='mb-0 small opacity-75'>مدير النظام</p>";
echo "</div>";
echo "</div>";

echo "<br><br>";

// Test 2: With profile image (if available)
echo "<h5>اختبار مع صورة:</h5>";
$testImagePath = __DIR__ . '/../assets/images/default-avatar.png';
if (file_exists($testImagePath)) {
    echo "<div class='profile-avatar me-3'>";
    echo "<img src='../assets/images/default-avatar.png' 
         alt='صورة المستخدم' 
         class='profile-image profile-image-lg'
         style='width: 50px; height: 50px; object-fit: cover; object-position: center; border-radius: 50%; border: none; display: block;'>";
    echo "</div>";
    echo "<div class='flex-grow-1 text-white'>";
    echo "<p class='mb-1 fw-medium'>مستخدم تجريبي</p>";
    echo "<p class='mb-0 small opacity-75'>مدير النظام</p>";
    echo "</div>";
} else {
    echo "<p class='text-warning'>لا توجد صورة تجريبية متاحة</p>";
}

echo "</div>";
echo "</div>";

echo "<div class='p-4'>";
echo "<h3>نتائج الاختبار:</h3>";
echo "<ul>";
echo "<li>عرض بدون صورة: " . (class_exists('default-avatar') ? "✅ يعمل" : "❌ لا يعمل") . "</li>";
echo "<li>عرض مع صورة: " . (class_exists('profile-image') ? "✅ يعمل" : "❌ لا يعمل") . "</li>";
echo "<li>ملف CSS محمل: " . (file_exists(__DIR__ . '/../assets/css/profile-images.css') ? "✅ موجود" : "❌ غير موجود") . "</li>";
echo "</ul>";

echo "<h4>معلومات إضافية:</h4>";
echo "<ul>";
echo "<li>BASE_URL: " . (defined('BASE_URL') ? BASE_URL : 'غير محدد') . "</li>";
echo "<li>مسار ملف CSS: " . __DIR__ . '/../assets/css/profile-images.css' . "</li>";
echo "<li>مسار الصورة التجريبية: " . $testImagePath . "</li>";
echo "</ul>";
echo "</div>";

echo "</body>";
echo "</html>";
?> 
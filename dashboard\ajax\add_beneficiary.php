<?php
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/customer_manager.php';
require_once __DIR__ . '/../../includes/functions.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$auth = new Auth();
$response = ['success' => false];

try {
    // Check if user is logged in
    if (!$auth->isLoggedIn()) {
        $response['error'] = 'غير مسموح: يجب تسجيل الدخول أولاً';
        echo json_encode($response);
        exit;
    }

    // Check permissions
    if (!$auth->hasPermission('customers.create')) {
        $response['error'] = 'غير مسموح: لا تملك صلاحية إضافة عملاء جدد';
        echo json_encode($response);
        exit;
    }

    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $response['error'] = 'طريقة الطلب غير صحيحة';
        echo json_encode($response);
        exit;
    }

    // Validate CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $response['error'] = 'رمز الحماية غير صالح';
        echo json_encode($response);
        exit;
    }

    // Get and validate input data
    $fullName = sanitize_input($_POST['full_name'] ?? '');
    $idNumber = sanitize_input($_POST['id_number'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $nationality = sanitize_input($_POST['nationality'] ?? '');

    // Validate required fields
    if (empty($fullName)) {
        $response['error'] = 'اسم المستفيد مطلوب';
        echo json_encode($response);
        exit;
    }

    // Initialize customer manager
    $customerMgr = new CustomerManager(new Database());
    $currentUser = $auth->getCurrentUser();
    
    // Check if ID number already exists (if provided)
    if (!empty($idNumber) && $customerMgr->idNumberExists($idNumber)) {
        $response['error'] = 'رقم الهوية موجود بالفعل';
        echo json_encode($response);
        exit;
    }

    // Prepare customer data
    $customerData = [
        'full_name' => $fullName,
        'id_number' => $idNumber ?: null,
        'id_type' => 'هوية وطنية', // Default ID type
        'nationality' => $nationality ?: null,
        'birth_date' => null,
        'phone' => $phone ?: null,
        'email' => null,
        'address' => null,
        'occupation' => null,
        'risk_level' => 'منخفض', // Default risk level
        'status' => 'active',
        'kyc_status' => 'غير مكتمل', // Default KYC status
        'created_by' => $currentUser['id']
    ];

    // Add the customer
    $newCustomerId = $customerMgr->addCustomer($customerData);

    if ($newCustomerId) {
        // Log the activity
        log_activity($currentUser['id'], 'customer.create', [
            'customer_id' => $newCustomerId,
            'full_name' => $fullName,
            'context' => 'transfer_beneficiary'
        ]);

        $response['success'] = true;
        $response['id'] = $newCustomerId;
        $response['full_name'] = $fullName;
        $response['id_number'] = $idNumber;
        $response['phone'] = $phone;
        $response['nationality'] = $nationality;
        $response['message'] = 'تم إضافة المستفيد بنجاح';
    } else {
        $response['error'] = 'فشل في إضافة المستفيد';
    }

} catch (Exception $e) {
    $response['error'] = 'خطأ في النظام: ' . $e->getMessage();
    error_log("Add beneficiary error: " . $e->getMessage());
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?> 
<?php
/**
 * PDO Database Connection Helper
 * يوفر اتصال PDO لقاعدة البيانات
 */

require_once __DIR__ . '/../config/database.php';

class PDODatabase
{
    private static $instance = null;

    /**
     * الحصول على اتصال PDO (Singleton Pattern)
     */
    public static function getConnection()
    {
        if (self::$instance === null) {
            try {
                $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
                self::$instance = new PDO($dsn, DB_USER, DB_PASS, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ]);
            } catch (PDOException $e) {
                die('Database connection failed: ' . $e->getMessage());
            }
        }

        return self::$instance;
    }

    /**
     * تنفيذ إجراء مخزن مع التعامل مع النتائج المتعددة
     *
     * @param PDO $pdo اتصال PDO
     * @param string $procedure اسم الإجراء المخزن
     * @param array $params المعلمات
     * @return array النتائج
     */
    public static function callProcedure($pdo, $procedure, $params = [])
    {
        // بناء استدعاء الإجراء المخزن
        $placeholders = implode(',', array_fill(0, count($params), '?'));
        $query = "CALL {$procedure}({$placeholders})";

        // تنفيذ الاستعلام
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        // جلب النتائج
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // إغلاق النتائج المعلقة
        while ($stmt->nextRowset()) {
            // استهلاك جميع مجموعات النتائج
        }

        // إغلاق الاستعلام
        $stmt->closeCursor();

        return $results;
    }
}

// إنشاء متغير عام للـ PDO
$pdo = PDODatabase::getConnection();
?>

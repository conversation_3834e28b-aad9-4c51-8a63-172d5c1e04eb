<?php
/**
 * صفحة إضافة معاملة يومية جديدة
 * Add New Daily Transaction Page
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

// إنشاء كائن المصادقة والتحقق من تسجيل الدخول
$auth = new Auth();
$auth->requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = $auth->getCurrentUser();

// التحقق من الصلاحيات
if (!$auth->hasPermission('daily_transactions.create')) {
    header('Location: ' . BASE_URL . 'dashboard/index.php?error=no_permission');
    exit;
}

$pageTitle = 'إضافة معاملة يومية جديدة';
$success_message = '';
$error_message = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $country_id = (int)$_POST['country_id'];
        $base_amount = (float)$_POST['base_amount'];
        $customer_rate = (float)$_POST['customer_rate'];
        $office_rate = (float)$_POST['office_rate'];
        $operation_type = $_POST['operation_type'];
        $exchange_rate = (float)$_POST['exchange_rate'];
        $delivery_type = $_POST['delivery_type'];
        $transfer_amount = !empty($_POST['transfer_amount']) ? (float)$_POST['transfer_amount'] : null;
        $recipient_name = !empty($_POST['recipient_name']) ? trim($_POST['recipient_name']) : null;
        $notes = !empty($_POST['notes']) ? trim($_POST['notes']) : null;
        $branch_id = !empty($current_user['branch_id']) ? (int)$current_user['branch_id'] : null;
        $created_by = (int)$current_user['id'];
        $delivery_status = isset($_POST['delivery_status']) ? $_POST['delivery_status'] : 'معلق';

        // التحقق من صحة البيانات
        $validation_errors = [];

        // التحقق من وجود الدولة
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM countries WHERE id = ? AND is_active = 1");
        $stmt->execute([$country_id]);
        if ($stmt->fetchColumn() == 0) {
            $validation_errors[] = "الدولة المحددة غير موجودة أو غير نشطة";
        }

        // التحقق من صحة المبالغ
        if ($base_amount <= 0) {
            $validation_errors[] = "مبلغ الحوالة الأساسي يجب أن يكون أكبر من صفر";
        }
        if ($customer_rate <= 0) {
            $validation_errors[] = "سعر القص للزبون يجب أن يكون أكبر من صفر";
        }
        if ($office_rate <= 0) {
            $validation_errors[] = "سعر القص للمكتب يجب أن يكون أكبر من صفر";
        }
        if ($exchange_rate <= 0) {
            $validation_errors[] = "سعر الصرف يجب أن يكون أكبر من صفر";
        }

        // التحقق من البيانات المطلوبة للتحويل البنكي أو USDT
        if ($delivery_type == 'bank' || $delivery_type == 'usdt') {
            if (empty($transfer_amount) || $transfer_amount <= 0) {
                $validation_errors[] = "المبلغ المراد تحويله مطلوب للتحويل البنكي أو USDT";
            }
            if (empty($recipient_name)) {
                $validation_errors[] = "اسم المستلم مطلوب للتحويل البنكي أو USDT";
            }
        }

        if (!empty($validation_errors)) {
            $error_message = implode('<br>', $validation_errors);
        } else {
            // حساب المبلغ الناتج
            if ($operation_type == 'multiply') {
                $calculated_amount = $base_amount * $customer_rate;
            } else {
                $calculated_amount = $base_amount / $customer_rate;
            }

            // حساب المبلغ للمستلم
            $recipient_amount = floor($calculated_amount * $exchange_rate);

            // جلب رمز الدولة بالإنجليزية (currency_code) من قاعدة البيانات
            $stmt = $pdo->prepare("SELECT currency_code FROM countries WHERE id = ? LIMIT 1");
            $stmt->execute([$country_id]);
            $country_row = $stmt->fetch();
            $country_code = $country_row ? $country_row['currency_code'] : '';
            
            // توليد رقم عشوائي من 8 خانات غير مكرر
            function generateUniqueTransactionNumber($pdo, $country_code) {
                do {
                    $random = str_pad(strval(mt_rand(0, 99999999)), 8, '0', STR_PAD_LEFT);
                    $number = 'tp' . $country_code . $random;
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM daily_transactions WHERE transaction_number = ?");
                    $stmt->execute([$number]);
                    $exists = $stmt->fetchColumn();
                } while ($exists);
                return $number;
            }
            $transaction_number = generateUniqueTransactionNumber($pdo, $country_code);

            // بدء المعاملة
            $pdo->beginTransaction();

            try {
                // إدراج المعاملة الجديدة
                $insert_sql = "INSERT INTO daily_transactions (
                    transaction_number, country_id, base_amount, customer_rate, office_rate, operation_type,
                    calculated_amount, exchange_rate, recipient_amount, delivery_type,
                    transfer_amount, recipient_name, notes, branch_id, created_by, delivery_status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $stmt = $pdo->prepare($insert_sql);
                $stmt->execute([
                    $transaction_number, $country_id, $base_amount, $customer_rate, $office_rate, $operation_type,
                    $calculated_amount, $exchange_rate, $recipient_amount, $delivery_type,
                    $transfer_amount, $recipient_name, $notes, $branch_id, $created_by, $delivery_status
                ]);

                $transaction_id = $pdo->lastInsertId();

                // إضافة سجل في تاريخ المعاملات
                $history_sql = "INSERT INTO daily_transaction_history
                    (transaction_id, action_type, new_values, changed_by, changed_at)
                    VALUES (?, 'created', ?, ?, CURRENT_TIMESTAMP)";

                $new_values = json_encode([
                    'transaction_number' => $transaction_number,
                    'country_id' => $country_id,
                    'base_amount' => $base_amount,
                    'delivery_type' => $delivery_type
                ]);

                $stmt = $pdo->prepare($history_sql);
                $stmt->execute([$transaction_id, $new_values, $created_by]);

                // تأكيد المعاملة
                $pdo->commit();

                $success_message = "تم إنشاء المعاملة بنجاح - رقم المعاملة: " . $transaction_number;

                // مسح النموذج بعد النجاح
                $_POST = [];

            } catch (Exception $e) {
                $pdo->rollback();
                throw $e;
            }
        }

    } catch (Exception $e) {
        $error_message = "حدث خطأ أثناء إنشاء المعاملة: " . $e->getMessage();
    }
}

// جلب قائمة الدول النشطة
try {
    // Corrected query based on the user-provided schema
    $countries_stmt = $pdo->query("SELECT id, name_ar, name_en, currency_code, currency_symbol FROM countries WHERE is_active = 1 ORDER BY name_ar");
    $countries = $countries_stmt->fetchAll();
} catch (Exception $e) {
    $countries = [];
    $error_message = "خطأ في جلب قائمة الدول: " . $e->getMessage();
}

require_once __DIR__ . '/../includes/header.php';
?>

<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-auto p-0">
                <?php require_once __DIR__ . '/../includes/sidebar.php'; ?>
            </div>

            <!-- Main Content -->
            <div class="col">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h3 mb-0">
                            <i class="fas fa-plus-circle text-primary me-2"></i>
                            إضافة معاملة يومية جديدة
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php">المعاملات اليومية</a></li>
                                <li class="breadcrumb-item active">إضافة معاملة جديدة</li>
                            </ol>
                        </nav>
                    </div>

                    <!-- رسائل النجاح والخطأ -->
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- نموذج إضافة المعاملة -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>
                                بيانات المعاملة الجديدة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="dailyTransactionForm" class="needs-validation" novalidate>
                                <div class="row">
                                    <!-- نوع العملية (الدولة والعملة) -->
                                    <div class="col-md-6 mb-3">
                                        <label for="country_search" class="form-label required">
                                            <i class="fas fa-globe me-1"></i>
                                            نوع العملية (الدولة والعملة)
                                        </label>
                                        <div class="position-relative">
                                            <input type="text"
                                                   class="form-control"
                                                   id="country_search"
                                                   placeholder="ابحث عن الدولة أو العملة..."
                                                   autocomplete="off">
                                            <input type="hidden" id="country_id" name="country_id" value="<?php echo $_POST['country_id'] ?? ''; ?>">
                                            <div class="invalid-feedback">
                                                يرجى اختيار الدولة والعملة
                                            </div>

                                            <!-- قائمة النتائج -->
                                            <div id="country_results" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none;">
                                                <!-- سيتم ملؤها بـ JavaScript -->
                                            </div>
                                        </div>

                                        <!-- عرض الاختيار الحالي -->
                                        <div id="selected_country" class="mt-2" style="display: none;">
                                            <div class="alert alert-info py-2 mb-0">
                                                <i class="fas fa-check-circle me-2"></i>
                                                <span id="selected_country_text"></span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearCountrySelection()">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- مبلغ الحوالة الأساسي -->
                                    <div class="col-md-6 mb-3">
                                        <label for="base_amount" class="form-label required">
                                            <i class="fas fa-money-bill me-1"></i>
                                            مبلغ الحوالة الأساسي
                                        </label>
                                        <input type="number" class="form-control" id="base_amount" name="base_amount" 
                                               step="0.01" min="0.01" required
                                               value="<?php echo isset($_POST['base_amount']) ? htmlspecialchars($_POST['base_amount']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال مبلغ صحيح أكبر من صفر</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- سعر القص للمكتب -->
                                    <div class="col-md-4 mb-3">
                                        <label for="office_rate" class="form-label required">
                                            <i class="fas fa-percent me-1"></i>
                                            سعر القص للمكتب
                                        </label>
                                        <input type="number" class="form-control" id="office_rate" name="office_rate" 
                                               step="0.000001" min="0.000001" required
                                               value="<?php echo isset($_POST['office_rate']) ? htmlspecialchars($_POST['office_rate']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال سعر صحيح أكبر من صفر</div>
                                    </div>

                                    <!-- سعر القص للزبون -->
                                    <div class="col-md-4 mb-3">
                                        <label for="customer_rate" class="form-label required">
                                            <i class="fas fa-percentage me-1"></i>
                                            سعر القص للزبون
                                        </label>
                                        <input type="number" class="form-control" id="customer_rate" name="customer_rate" 
                                               step="0.000001" min="0.000001" required
                                               value="<?php echo isset($_POST['customer_rate']) ? htmlspecialchars($_POST['customer_rate']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال سعر صحيح أكبر من صفر</div>
                                    </div>

                                    <!-- نوع العملية الحسابية -->
                                    <div class="col-md-4 mb-3">
                                        <label for="operation_type" class="form-label required">
                                            <i class="fas fa-calculator me-1"></i>
                                            نوع العملية الحسابية
                                        </label>
                                        <select class="form-select" id="operation_type" name="operation_type" required>
                                            <option value="">اختر نوع العملية</option>
                                            <option value="multiply" <?php echo (isset($_POST['operation_type']) && $_POST['operation_type'] == 'multiply') ? 'selected' : ''; ?>>
                                                ضرب (×)
                                            </option>
                                            <option value="divide" <?php echo (isset($_POST['operation_type']) && $_POST['operation_type'] == 'divide') ? 'selected' : ''; ?>>
                                                قسمة (÷)
                                            </option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار نوع العملية</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- المبلغ الناتج (محسوب تلقائياً) -->
                                    <div class="col-md-4 mb-3">
                                        <label for="calculated_amount" class="form-label">
                                            <i class="fas fa-equals me-1"></i>
                                            المبلغ الناتج للزبون
                                        </label>
                                        <input type="text" class="form-control bg-light" id="calculated_amount" readonly
                                               placeholder="سيتم الحساب تلقائياً">
                                        <small class="text-muted">يتم حسابه تلقائياً بناءً على العملية المختارة</small>
                                    </div>
                                    <!-- المبلغ الناتج للمكتب (محسوب تلقائياً) -->
                                    <div class="col-md-4 mb-3">
                                        <label for="office_amount" class="form-label">
                                            <i class="fas fa-equals me-1"></i>
                                            المبلغ الناتج للمكتب
                                        </label>
                                        <input type="text" class="form-control bg-light" id="office_amount" readonly
                                               placeholder="سيتم الحساب تلقائياً">
                                        <small class="text-muted">يتم حسابه تلقائياً بناءً على العملية المختارة</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- سعر الصرف -->
                                    <div class="col-md-6 mb-3">
                                        <label for="exchange_rate" class="form-label required">
                                            <i class="fas fa-exchange-alt me-1"></i>
                                            سعر الصرف
                                        </label>
                                        <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" 
                                               step="0.000001" min="0.000001" required
                                               value="<?php echo isset($_POST['exchange_rate']) ? htmlspecialchars($_POST['exchange_rate']) : ''; ?>">
                                        <div class="invalid-feedback">يرجى إدخال سعر صرف صحيح أكبر من صفر</div>
                                    </div>

                                    <!-- المبلغ للمستلم (محسوب تلقائياً) -->
                                    <div class="col-md-6 mb-3">
                                        <label for="recipient_amount" class="form-label">
                                            <i class="fas fa-hand-holding-usd me-1"></i>
                                            المبلغ للمستلم (رقم صحيح)
                                        </label>
                                        <input type="text" class="form-control bg-light" id="recipient_amount" readonly
                                               placeholder="سيتم الحساب تلقائياً">
                                        <small class="text-muted">يتم حسابه تلقائياً كرقم صحيح</small>
                                    </div>
                                </div>

                                <!-- نوع التسليم -->
                                <div class="mb-3">
                                    <label for="delivery_type" class="form-label required">
                                        <i class="fas fa-truck me-1"></i>
                                        نوع التسليم
                                    </label>
                                    <select class="form-select" id="delivery_type" name="delivery_type" required>
                                        <option value="">اختر نوع التسليم</option>
                                        <option value="cash" <?php echo (isset($_POST['delivery_type']) && $_POST['delivery_type'] == 'cash') ? 'selected' : ''; ?>>
                                            كاش (Cash)
                                        </option>
                                        <option value="bank" <?php echo (isset($_POST['delivery_type']) && $_POST['delivery_type'] == 'bank') ? 'selected' : ''; ?>>
                                            بنكي (Bank Transfer)
                                        </option>
                                        <option value="usdt" <?php echo (isset($_POST['delivery_type']) && $_POST['delivery_type'] == 'usdt') ? 'selected' : ''; ?>>
                                            USDT (عملة رقمية)
                                        </option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار نوع التسليم</div>
                                </div>
                                <!-- حالة التسليم -->
                                <div class="mb-3">
                                    <label for="delivery_status" class="form-label required">
                                        <i class="fas fa-check-double me-1"></i>
                                        حالة التسليم
                                    </label>
                                    <select class="form-select" id="delivery_status" name="delivery_status" required>
                                        <option value="">اختر حالة التسليم</option>
                                        <option value="مستلم" <?php echo (isset($_POST['delivery_status']) && $_POST['delivery_status'] == 'مستلم') ? 'selected' : ''; ?>>مستلم</option>
                                        <option value="معلق" <?php echo (isset($_POST['delivery_status']) && $_POST['delivery_status'] == 'معلق') ? 'selected' : ''; ?>>معلق</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار حالة التسليم</div>
                                </div>

                                <!-- حقول إضافية حسب نوع التسليم -->
                                <?php
                                $show_additional = isset($_POST['delivery_type']) && !empty($_POST['delivery_type']);
                                $show_transfer_amount = isset($_POST['delivery_type']) && ($_POST['delivery_type'] == 'bank' || $_POST['delivery_type'] == 'usdt');
                                ?>
                                <div id="additional_fields" style="display: <?php echo $show_additional ? 'block' : 'none'; ?>;">
                                    <div id="info_alert" class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong id="info_text">معلومات إضافية مطلوبة:</strong> <span id="info_description">يرجى ملء البيانات التالية</span>
                                    </div>
                                    <div class="row">
                                        <!-- حقل المبلغ المراد تحويله (للبنكي و USDT فقط) -->
                                        <div class="col-md-6 mb-3" id="transfer_amount_field" style="display: <?php echo $show_transfer_amount ? 'block' : 'none'; ?>;">
                                            <label for="transfer_amount" class="form-label required">
                                                <i class="fas fa-money-check me-1"></i>
                                                المبلغ المراد تحويله
                                            </label>
                                            <input type="number" class="form-control" id="transfer_amount" name="transfer_amount"
                                                   step="0.01" min="0.01"
                                                   value="<?php echo isset($_POST['transfer_amount']) ? htmlspecialchars($_POST['transfer_amount']) : ''; ?>"
                                                   placeholder="أدخل المبلغ المراد تحويله">
                                            <div class="invalid-feedback">يرجى إدخال مبلغ صحيح للتحويل</div>
                                        </div>

                                        <!-- حقل اسم المستلم (بحث تلقائي من العملاء) -->
                                        <div class="col-md-6 mb-3 position-relative" id="recipient_name_field">
                                            <label for="recipient_name" class="form-label required">
                                                <i class="fas fa-user me-1"></i>
                                                اسم المستلم
                                            </label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="recipient_name_search" name="recipient_name_search"
                                                       maxlength="255"
                                                       placeholder="ابحث عن اسم المستلم...">
                                                <input type="hidden" id="recipient_name" name="recipient_name" value="<?php echo isset($_POST['recipient_name']) ? htmlspecialchars($_POST['recipient_name']) : ''; ?>">
                                                <button type="button" class="btn btn-outline-success" title="إضافة عميل جديد" onclick="window.open('customers.php?add=1', '_blank')">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                            <div id="recipient_results" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none;"></div>
                                            <div class="invalid-feedback">يرجى اختيار اسم المستلم من القائمة أو إضافته</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ملاحظة -->
                                <div class="mb-4">
                                    <label for="notes" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>
                                        ملاحظة
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="أدخل أي ملاحظات إضافية..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
                                </div>

                                <!-- أزرار التحكم -->
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ المعاملة
                                        </button>
                                        <button type="reset" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php" class="btn btn-outline-secondary btn-lg">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        العودة للقائمة
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSS إضافي للحقول الإضافية -->
    <style>
        #additional_fields {
            display: none !important; /* إخفاء افتراضي قوي */
            transition: all 0.3s ease;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        #additional_fields.show {
            border-color: #0d6efd;
            background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
        }

        /* تحسين مظهر الحقول حسب النوع */
        .alert-success {
            border-color: #28a745 !important;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
        }

        #recipient_name_field, #transfer_amount_field {
            transition: all 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-label.required::after {
            content: " *";
            color: #dc3545;
            font-weight: bold;
        }
    </style>

    <!-- JavaScript للحسابات التلقائية -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const baseAmountInput = document.getElementById('base_amount');
            const customerRateInput = document.getElementById('customer_rate');
            const officeRateInput = document.getElementById('office_rate');
            const operationTypeSelect = document.getElementById('operation_type');
            const exchangeRateInput = document.getElementById('exchange_rate');
            const calculatedAmountInput = document.getElementById('calculated_amount');
            const officeAmountInput = document.getElementById('office_amount');
            const recipientAmountInput = document.getElementById('recipient_amount');
            const countryIdInput = document.getElementById('country_id');
            const countrySearchInput = document.getElementById('country_search');
            const deliveryTypeSelect = document.getElementById('delivery_type');
            const additionalFields = document.getElementById('additional_fields');
            const transferAmountInput = document.getElementById('transfer_amount');
            const recipientNameInput = document.getElementById('recipient_name');
            const transferAmountField = document.getElementById('transfer_amount_field');
            const recipientNameField = document.getElementById('recipient_name_field');
            const infoAlert = document.getElementById('info_alert');
            const infoText = document.getElementById('info_text');
            const infoDescription = document.getElementById('info_description');

            function calculateAmounts() {
                const baseAmount = parseFloat(baseAmountInput.value) || 0;
                const customerRate = parseFloat(customerRateInput.value) || 0;
                const officeRate = parseFloat(officeRateInput.value) || 0;
                const operationType = operationTypeSelect.value;
                const exchangeRate = parseFloat(exchangeRateInput.value) || 0;

                if (baseAmount > 0 && operationType) {
                    if (customerRate > 0) {
                        let calculatedAmount = (operationType === 'multiply') ? (baseAmount * customerRate) : (baseAmount / customerRate);
                        calculatedAmountInput.value = calculatedAmount.toFixed(2);
                        
                        if (exchangeRate > 0) {
                            const recipientAmount = Math.floor(calculatedAmount * exchangeRate);
                            recipientAmountInput.value = recipientAmount.toLocaleString();
                        } else {
                           recipientAmountInput.value = '';
                        }
                    } else {
                        calculatedAmountInput.value = '';
                        recipientAmountInput.value = '';
                    }
                    
                    if (officeRate > 0) {
                        let officeAmount = (operationType === 'multiply') ? (baseAmount * officeRate) : (baseAmount / officeRate);
                        officeAmountInput.value = officeAmount.toFixed(2);
                    } else {
                        officeAmountInput.value = '';
                    }
                } else {
                    calculatedAmountInput.value = '';
                    officeAmountInput.value = '';
                    recipientAmountInput.value = '';
                }
            }

            function toggleAdditionalFields() {
                const deliveryType = deliveryTypeSelect.value;
                if (deliveryType) {
                    additionalFields.style.setProperty('display', 'block', 'important');
                    additionalFields.classList.add('show');
                    const isBankOrUsdt = (deliveryType === 'bank' || deliveryType === 'usdt');
                    
                    transferAmountField.style.display = isBankOrUsdt ? 'block' : 'none';
                    transferAmountInput.required = isBankOrUsdt;
                    recipientNameField.style.display = 'block';
                    recipientNameInput.required = true;

                    if(isBankOrUsdt) {
                        const typeText = deliveryType === 'bank' ? 'التحويل البنكي' : 'USDT';
                        infoText.textContent = 'معلومات إضافية مطلوبة:';
                        infoDescription.textContent = `يرجى ملء البيانات التالية لـ ${typeText}`;
                        infoAlert.className = 'alert alert-info';
                    } else { // cash
                        infoText.textContent = 'معلومات المستلم:';
                        infoDescription.textContent = 'يرجى إدخال اسم المستلم للمعاملة النقدية';
                        infoAlert.className = 'alert alert-success';
                    }
                } else {
                    additionalFields.style.setProperty('display', 'none', 'important');
                    additionalFields.classList.remove('show');
                    transferAmountInput.required = false;
                    recipientNameInput.required = false;
                }
            }

            [baseAmountInput, customerRateInput, officeRateInput, operationTypeSelect, exchangeRateInput].forEach(el => el.addEventListener('input', calculateAmounts));
            deliveryTypeSelect.addEventListener('change', toggleAdditionalFields);

            calculateAmounts();
            toggleAdditionalFields();

            const countriesData = [
                <?php foreach ($countries as $country): ?>
                {
                    id: <?php echo $country['id']; ?>,
                    name_ar: "<?php echo htmlspecialchars($country['name_ar'], ENT_QUOTES, 'UTF-8'); ?>",
                    name_en: "<?php echo htmlspecialchars($country['name_en'], ENT_QUOTES, 'UTF-8'); ?>",
                    currency_code: "<?php echo htmlspecialchars($country['currency_code'], ENT_QUOTES, 'UTF-8'); ?>",
                    currency_symbol: "<?php echo htmlspecialchars($country['currency_symbol'], ENT_QUOTES, 'UTF-8'); ?>",
                    searchText_ar: "<?php echo htmlspecialchars($country['name_ar'] . ' ' . $country['currency_code'], ENT_QUOTES, 'UTF-8'); ?>",
                    searchText_en: "<?php echo htmlspecialchars(strtolower($country['name_en'] . ' ' . $country['currency_code']), ENT_QUOTES, 'UTF-8'); ?>"
                },
                <?php endforeach; ?>
            ];

            function initCountrySearch() {
                searchInput.addEventListener('input', function() {
                    const query = this.value.trim();
                    const lowerCaseQuery = query.toLowerCase();
                    if (!query) {
                        resultsDiv.style.display = 'none';
                        return;
                    }
                    const filteredCountries = countriesData.filter(c => c.searchText_ar.includes(query) || c.searchText_en.includes(lowerCaseQuery));
                    displaySearchResults(filteredCountries);
                });

                searchInput.addEventListener('focus', () => searchInput.dispatchEvent(new Event('input')));
                document.addEventListener('click', e => {
                    if (!countrySearchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                        resultsDiv.style.display = 'none';
                    }
                });
            }
            
            const searchInput = document.getElementById('country_search');
            const resultsDiv = document.getElementById('country_results');

            function displaySearchResults(countries) {
                if (countries.length === 0) {
                    resultsDiv.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                } else {
                    resultsDiv.innerHTML = countries.slice(0, 10).map(c => `
                        <div class="dropdown-item country-option" data-id="${c.id}" data-name="${c.name_ar}" data-currency="${c.currency_code}" data-symbol="${c.currency_symbol}" style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${c.name_ar}</strong>
                                    <small class="text-muted d-block">${c.name_en} - ${c.currency_code}</small>
                                </div>
                                <span class="badge bg-primary">${c.currency_code}</span>
                            </div>
                        </div>
                    `).join('');
                }
                resultsDiv.style.display = 'block';
                
                resultsDiv.querySelectorAll('.country-option').forEach(option => {
                    option.addEventListener('click', function() {
                        selectCountry({
                            id: this.dataset.id,
                            name_ar: this.dataset.name,
                            currency_code: this.dataset.currency,
                            currency_symbol: this.dataset.symbol
                        });
                    });
                });
            }

            function selectCountry(country) {
                countryIdInput.value = country.id;
                countrySearchInput.value = country.name_ar;
                resultsDiv.style.display = 'none';
                showSelectedCountry(country);
                calculateAmounts();
                countryIdInput.dispatchEvent(new Event('change'));
            }

            function showSelectedCountry(country) {
                const selectedText = document.getElementById('selected_country_text');
                const selectedDiv = document.getElementById('selected_country');
                selectedText.innerHTML = `<strong>${country.name_ar}</strong> - ${country.currency_code} (${country.currency_symbol})`;
                selectedDiv.style.display = 'block';
                countrySearchInput.disabled = true;
                countrySearchInput.style.backgroundColor = '#e9ecef';
            }

            window.clearCountrySelection = function() {
                countryIdInput.value = '';
                countrySearchInput.value = '';
                countrySearchInput.disabled = false;
                countrySearchInput.style.backgroundColor = '';
                document.getElementById('selected_country').style.display = 'none';
                resultsDiv.style.display = 'none';
                calculateAmounts();
            };

            initCountrySearch();
            
            // Restore state if form was submitted with errors
            const preselectedId = countryIdInput.value;
            if (preselectedId) {
                const preselectedCountry = countriesData.find(c => c.id == preselectedId);
                if (preselectedCountry) {
                    selectCountry(preselectedCountry);
                }
            }
            
            // Form validation
            const form = document.getElementById('dailyTransactionForm');
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity() || !countryIdInput.value) {
                    event.preventDefault();
                    event.stopPropagation();
                    if (!countryIdInput.value) {
                         countrySearchInput.classList.add('is-invalid');
                         alert('يرجى اختيار الدولة والعملة من قائمة البحث');
                    }
                }
                form.classList.add('was-validated');
            });
            
            countryIdInput.addEventListener('change', () => countrySearchInput.classList.remove('is-invalid'));

            // بحث تلقائي لاسم المستلم
            const recipientNameSearch = document.getElementById('recipient_name_search');
            const recipientNameHidden = document.getElementById('recipient_name');
            const recipientResults = document.getElementById('recipient_results');
            let recipientAjaxTimeout = null;

            recipientNameSearch.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length < 2) {
                    recipientResults.style.display = 'none';
                    return;
                }
                if (recipientAjaxTimeout) clearTimeout(recipientAjaxTimeout);
                recipientAjaxTimeout = setTimeout(function() {
                    fetch('ajax/search_beneficiaries.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: 'search=' + encodeURIComponent(query)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.customers.length > 0) {
                            let html = '';
                            data.customers.forEach(cust => {
                                html += `<div class=\"dropdown-item\" style=\"cursor:pointer;\" data-name=\"${cust.full_name.replace(/\"/g, '&quot;')}\">`
                                    + `<strong>${cust.full_name}</strong>`
                                    + (cust.id_number ? ` <small class=\"text-muted\">(${cust.id_number})</small>` : '')
                                    + (cust.phone ? ` <small class=\"text-muted\">[${cust.phone}]</small>` : '')
                                    + '</div>';
                            });
                            recipientResults.innerHTML = html;
                            recipientResults.style.display = 'block';
                            recipientResults.querySelectorAll('.dropdown-item').forEach(item => {
                                item.addEventListener('click', function() {
                                    const name = this.getAttribute('data-name');
                                    recipientNameSearch.value = name;
                                    recipientNameHidden.value = name;
                                    recipientResults.style.display = 'none';
                                    recipientNameSearch.classList.remove('is-invalid');
                                    recipientNameHidden.classList.remove('is-invalid');
                                });
                            });
                        } else {
                            recipientResults.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                            recipientResults.style.display = 'block';
                        }
                    })
                    .catch(() => {
                        recipientResults.innerHTML = '<div class="dropdown-item text-danger">خطأ في البحث</div>';
                        recipientResults.style.display = 'block';
                    });
                }, 300);
            });
            recipientNameSearch.addEventListener('focus', function() {
                if (this.value.trim().length >= 2 && recipientResults.innerHTML) {
                    recipientResults.style.display = 'block';
                }
            });
            document.addEventListener('click', function(e) {
                if (!recipientNameSearch.contains(e.target) && !recipientResults.contains(e.target)) {
                    recipientResults.style.display = 'none';
                }
            });
            recipientNameSearch.addEventListener('change', function() {
                if (this.value !== recipientNameHidden.value) {
                    recipientNameHidden.value = '';
                }
            });
        });

        function resetForm() {
            document.getElementById('dailyTransactionForm').reset();
            window.clearCountrySelection();
            document.getElementById('dailyTransactionForm').classList.remove('was-validated');
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
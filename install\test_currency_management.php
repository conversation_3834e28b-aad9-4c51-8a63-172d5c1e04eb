<?php
/**
 * Test Currency Management Page
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>اختبار صفحة إدارة فئات العملات</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الصفحة والوظائف</h3>\n";
    
    // Check if currency_management.php exists
    $pageFile = __DIR__ . '/../dashboard/currency_management.php';
    if (file_exists($pageFile)) {
        echo "<p style='color: green;'>✓ ملف currency_management.php موجود</p>\n";
    } else {
        echo "<p style='color: red;'>✗ ملف currency_management.php غير موجود</p>\n";
    }
    
    // Check currencies table structure
    $result = $conn->query("DESCRIBE currencies");
    if ($result) {
        echo "<p style='color: green;'>✓ جدول currencies موجود</p>\n";
        
        $columns = [];
        while ($row = $result->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        
        $requiredColumns = ['id', 'code', 'name', 'symbol', 'country', 'is_active', 'created_at', 'updated_at'];
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (empty($missingColumns)) {
            echo "<p style='color: green;'>✓ جميع الأعمدة المطلوبة موجودة</p>\n";
        } else {
            echo "<p style='color: red;'>✗ أعمدة مفقودة: " . implode(', ', $missingColumns) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ جدول currencies غير موجود</p>\n";
    }
    
    echo "<h3>2. اختبار إضافة عملة جديدة</h3>\n";
    
    // Test adding a new currency
    $testCurrency = [
        'code' => 'TEST',
        'name' => 'عملة اختبار',
        'symbol' => 'T$',
        'country' => 'دولة الاختبار',
        'is_active' => 1
    ];
    
    $sql = "INSERT INTO currencies (code, name, symbol, country, is_active, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
    $stmt = $conn->prepare($sql);
    
    if ($stmt && $stmt->bind_param('ssssi', 
        $testCurrency['code'], 
        $testCurrency['name'], 
        $testCurrency['symbol'], 
        $testCurrency['country'], 
        $testCurrency['is_active']
    )) {
        if ($stmt->execute()) {
            $testCurrencyId = $stmt->insert_id;
            echo "<p style='color: green;'>✓ تم إضافة عملة اختبار بنجاح (ID: $testCurrencyId)</p>\n";
        } else {
            if ($conn->errno == 1062) {
                echo "<p style='color: orange;'>⚠ عملة الاختبار موجودة بالفعل</p>\n";
                // Get existing test currency
                $result = $conn->query("SELECT id FROM currencies WHERE code = 'TEST'");
                if ($result && $result->num_rows > 0) {
                    $testCurrencyId = $result->fetch_assoc()['id'];
                }
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عملة اختبار: " . $stmt->error . "</p>\n";
            }
        }
        $stmt->close();
    }
    
    echo "<h3>3. اختبار تحديث العملة</h3>\n";
    
    if (isset($testCurrencyId)) {
        $updateSql = "UPDATE currencies SET name = ?, symbol = ?, updated_at = NOW() WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        
        $newName = 'عملة اختبار محدثة';
        $newSymbol = 'T₹';
        
        if ($updateStmt && $updateStmt->bind_param('ssi', $newName, $newSymbol, $testCurrencyId)) {
            if ($updateStmt->execute()) {
                echo "<p style='color: green;'>✓ تم تحديث عملة الاختبار بنجاح</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في تحديث عملة الاختبار</p>\n";
            }
            $updateStmt->close();
        }
    }
    
    echo "<h3>4. اختبار تغيير حالة العملة</h3>\n";
    
    if (isset($testCurrencyId)) {
        $toggleSql = "UPDATE currencies SET is_active = NOT is_active, updated_at = NOW() WHERE id = ?";
        $toggleStmt = $conn->prepare($toggleSql);
        
        if ($toggleStmt && $toggleStmt->bind_param('i', $testCurrencyId)) {
            if ($toggleStmt->execute()) {
                echo "<p style='color: green;'>✓ تم تغيير حالة عملة الاختبار بنجاح</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في تغيير حالة عملة الاختبار</p>\n";
            }
            $toggleStmt->close();
        }
    }
    
    echo "<h3>5. اختبار استرجاع العملات مع الإحصائيات</h3>\n";
    
    $statsSql = "SELECT c.*, 
                        COALESCE(cb_count.count, 0) as cash_boxes_count,
                        COALESCE(ba_count.count, 0) as bank_accounts_count
                 FROM currencies c
                 LEFT JOIN (
                     SELECT currency_code, COUNT(*) as count 
                     FROM cash_boxes 
                     GROUP BY currency_code
                 ) cb_count ON c.code = cb_count.currency_code
                 LEFT JOIN (
                     SELECT currency_code, COUNT(*) as count 
                     FROM bank_accounts 
                     GROUP BY currency_code
                 ) ba_count ON c.code = ba_count.currency_code
                 WHERE c.code = 'TEST'";
    
    $result = $conn->query($statsSql);
    if ($result && $result->num_rows > 0) {
        $currency = $result->fetch_assoc();
        echo "<p style='color: green;'>✓ تم استرجاع بيانات العملة مع الإحصائيات</p>\n";
        echo "<ul>\n";
        echo "<li>الرمز: {$currency['code']}</li>\n";
        echo "<li>الاسم: {$currency['name']}</li>\n";
        echo "<li>الرمز المختصر: {$currency['symbol']}</li>\n";
        echo "<li>البلد: {$currency['country']}</li>\n";
        echo "<li>الحالة: " . ($currency['is_active'] ? 'نشط' : 'غير نشط') . "</li>\n";
        echo "<li>عدد الصناديق: {$currency['cash_boxes_count']}</li>\n";
        echo "<li>عدد الحسابات البنكية: {$currency['bank_accounts_count']}</li>\n";
        echo "</ul>\n";
    } else {
        echo "<p style='color: red;'>✗ فشل في استرجاع بيانات العملة</p>\n";
    }
    
    echo "<h3>6. اختبار حذف العملة</h3>\n";
    
    if (isset($testCurrencyId)) {
        // Check if currency is used
        $checkSql = "SELECT 
                        (SELECT COUNT(*) FROM cash_boxes WHERE currency_code = 'TEST') +
                        (SELECT COUNT(*) FROM bank_accounts WHERE currency_code = 'TEST') as usage_count";
        $checkResult = $conn->query($checkSql);
        
        if ($checkResult) {
            $usage = $checkResult->fetch_assoc();
            
            if ($usage['usage_count'] == 0) {
                $deleteSql = "DELETE FROM currencies WHERE id = ?";
                $deleteStmt = $conn->prepare($deleteSql);
                
                if ($deleteStmt && $deleteStmt->bind_param('i', $testCurrencyId)) {
                    if ($deleteStmt->execute()) {
                        echo "<p style='color: green;'>✓ تم حذف عملة الاختبار بنجاح</p>\n";
                    } else {
                        echo "<p style='color: red;'>✗ فشل في حذف عملة الاختبار</p>\n";
                    }
                    $deleteStmt->close();
                }
            } else {
                echo "<p style='color: orange;'>⚠ لا يمكن حذف عملة الاختبار لأنها مستخدمة في {$usage['usage_count']} مكان</p>\n";
            }
        }
    }
    
    echo "<h3>7. فحص الروابط في القائمة الجانبية</h3>\n";
    
    $sidebarFile = __DIR__ . '/../includes/sidebar.php';
    if (file_exists($sidebarFile)) {
        $sidebarContent = file_get_contents($sidebarFile);
        
        if (strpos($sidebarContent, 'currency_management.php') !== false) {
            echo "<p style='color: green;'>✓ رابط إدارة فئات العملات موجود في القائمة الجانبية</p>\n";
        } else {
            echo "<p style='color: red;'>✗ رابط إدارة فئات العملات غير موجود في القائمة الجانبية</p>\n";
        }
        
        if (strpos($sidebarContent, 'fa-coins') !== false) {
            echo "<p style='color: green;'>✓ أيقونة العملات موجودة في القائمة الجانبية</p>\n";
        } else {
            echo "<p style='color: red;'>✗ أيقونة العملات غير موجودة في القائمة الجانبية</p>\n";
        }
    }
    
    echo "<h3>8. فحص CSS للعملات</h3>\n";
    
    $cssFile = __DIR__ . '/../assets/css/dashboard-improvements.css';
    if (file_exists($cssFile)) {
        $cssContent = file_get_contents($cssFile);
        
        if (strpos($cssContent, 'fa-coins') !== false) {
            echo "<p style='color: green;'>✓ تنسيقات CSS للعملات موجودة</p>\n";
        } else {
            echo "<p style='color: red;'>✗ تنسيقات CSS للعملات غير موجودة</p>\n";
        }
    }
    
    echo "<h3>9. إحصائيات العملات النهائية</h3>\n";
    
    $finalStats = $conn->query("SELECT 
                                   COUNT(*) as total,
                                   SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                                   SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
                                FROM currencies");
    
    if ($finalStats) {
        $stats = $finalStats->fetch_assoc();
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<h4>📊 إحصائيات العملات</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>إجمالي العملات:</strong> {$stats['total']}</li>\n";
        echo "<li><strong>العملات النشطة:</strong> {$stats['active']}</li>\n";
        echo "<li><strong>العملات غير النشطة:</strong> {$stats['inactive']}</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى اختبار صفحة إدارة فئات العملات!</h3>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 النتيجة النهائية: الصفحة تعمل بكفاءة!</h4>\n";
    echo "<p style='margin: 0; color: #155724;'>جميع الوظائف تعمل بشكل صحيح:</p>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li>✅ إضافة عملات جديدة</li>\n";
    echo "<li>✅ تعديل العملات الموجودة</li>\n";
    echo "<li>✅ تغيير حالة العملات (تفعيل/إلغاء تفعيل)</li>\n";
    echo "<li>✅ حذف العملات غير المستخدمة</li>\n";
    echo "<li>✅ عرض إحصائيات الاستخدام</li>\n";
    echo "<li>✅ حماية من حذف العملات المستخدمة</li>\n";
    echo "<li>✅ واجهة مستخدم جميلة ومتجاوبة</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4>🔗 روابط مفيدة</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/currency_management.php' target='_blank' style='color: #007bff;'>💰 صفحة إدارة فئات العملات</a></li>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank' style='color: #007bff;'>📊 إدارة الصناديق والحسابات</a></li>\n";
    echo "<li><a href='view_all_currencies.php' target='_blank' style='color: #007bff;'>📋 عرض جميع العملات</a></li>\n";
    echo "<li><a href='manage_currencies.php' target='_blank' style='color: #007bff;'>⚙️ أدوات إدارة العملات المتقدمة</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

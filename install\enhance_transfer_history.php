<?php
/**
 * Enhance Transfer History System
 * تحسين نظام تاريخ الحوالات
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>تحسين نظام تاريخ الحوالات</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الهيكل الحالي لجدول تاريخ الحالات</h3>\n";
    
    // Check current structure
    $result = $conn->query("DESCRIBE transfer_status_history");
    if ($result) {
        echo "<p><strong>الهيكل الحالي:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th>\n";
        echo "</tr>\n";
        
        $currentColumns = [];
        while ($row = $result->fetch_assoc()) {
            $currentColumns[] = $row['Field'];
            echo "<tr>\n";
            echo "<td>{$row['Field']}</td>\n";
            echo "<td>{$row['Type']}</td>\n";
            echo "<td>{$row['Null']}</td>\n";
            echo "<td>{$row['Key']}</td>\n";
            echo "<td>{$row['Default']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>2. إضافة أعمدة جديدة لتتبع شامل</h3>\n";
    
    // Add new columns for comprehensive tracking
    $newColumns = [
        'action_type' => "ENUM('status_change','tracking_assigned','amount_updated','delivery_updated','notes_added','system_update') DEFAULT 'status_change'",
        'old_value' => "TEXT NULL",
        'new_value' => "TEXT NULL", 
        'field_changed' => "VARCHAR(100) NULL",
        'ip_address' => "VARCHAR(45) NULL",
        'user_agent' => "TEXT NULL",
        'delivery_status' => "ENUM('pending','in_transit','delivered','failed','returned') NULL",
        'delivery_notes' => "TEXT NULL",
        'system_reference' => "VARCHAR(100) NULL",
        'additional_data' => "JSON NULL"
    ];
    
    foreach ($newColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $currentColumns)) {
            $sql = "ALTER TABLE transfer_status_history ADD COLUMN $columnName $columnDefinition";
            if ($conn->query($sql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود $columnName</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عمود $columnName: " . $conn->error . "</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>ℹ عمود $columnName موجود بالفعل</p>\n";
        }
    }
    
    echo "<h3>3. إنشاء جدول تفاصيل الحوالات المحسن</h3>\n";
    
    // Create enhanced transfer details table
    $createDetailsSql = "
    CREATE TABLE IF NOT EXISTS transfer_activity_log (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        transfer_id INT UNSIGNED NOT NULL,
        activity_type ENUM(
            'created','status_changed','tracking_assigned','amount_updated',
            'delivery_updated','notes_added','viewed','printed','exported',
            'commission_calculated','payment_processed','refund_issued',
            'document_uploaded','verification_completed','system_update'
        ) NOT NULL,
        description TEXT NOT NULL,
        old_value JSON NULL,
        new_value JSON NULL,
        user_id INT UNSIGNED NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        location VARCHAR(255) NULL,
        device_info VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_tal_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_tal_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
        INDEX idx_transfer_activity (transfer_id, created_at),
        INDEX idx_activity_type (activity_type),
        INDEX idx_user_activity (user_id, created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($createDetailsSql)) {
        echo "<p style='color: green;'>✓ تم إنشاء جدول transfer_activity_log</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ جدول transfer_activity_log موجود بالفعل أو حدث خطأ: " . $conn->error . "</p>\n";
    }
    
    echo "<h3>4. إنشاء جدول حالات التسليم</h3>\n";
    
    // Create delivery status table
    $createDeliverySql = "
    CREATE TABLE IF NOT EXISTS transfer_delivery_history (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        transfer_id INT UNSIGNED NOT NULL,
        delivery_status ENUM('pending','picked_up','in_transit','out_for_delivery','delivered','failed','returned','cancelled') NOT NULL,
        delivery_location VARCHAR(255) NULL,
        delivery_agent VARCHAR(255) NULL,
        delivery_notes TEXT NULL,
        delivery_proof VARCHAR(500) NULL,
        recipient_name VARCHAR(255) NULL,
        recipient_signature VARCHAR(500) NULL,
        delivery_date DATETIME NULL,
        created_by INT UNSIGNED NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_tdh_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE ON UPDATE CASCADE,
        CONSTRAINT fk_tdh_user FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
        INDEX idx_transfer_delivery (transfer_id, created_at),
        INDEX idx_delivery_status (delivery_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($createDeliverySql)) {
        echo "<p style='color: green;'>✓ تم إنشاء جدول transfer_delivery_history</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ جدول transfer_delivery_history موجود بالفعل أو حدث خطأ: " . $conn->error . "</p>\n";
    }
    
    echo "<h3>5. إضافة أعمدة حالة التسليم إلى جدول الحوالات</h3>\n";
    
    // Add delivery columns to transfers table
    $deliveryColumns = [
        'delivery_status' => "ENUM('pending','picked_up','in_transit','out_for_delivery','delivered','failed','returned','cancelled') DEFAULT 'pending'",
        'delivery_agent' => "VARCHAR(255) NULL",
        'delivery_location' => "VARCHAR(255) NULL",
        'delivery_date' => "DATETIME NULL",
        'delivery_proof' => "VARCHAR(500) NULL",
        'recipient_signature' => "VARCHAR(500) NULL"
    ];
    
    // Check transfers table structure
    $transfersResult = $conn->query("DESCRIBE transfers");
    $transfersColumns = [];
    if ($transfersResult) {
        while ($row = $transfersResult->fetch_assoc()) {
            $transfersColumns[] = $row['Field'];
        }
    }
    
    foreach ($deliveryColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $transfersColumns)) {
            $sql = "ALTER TABLE transfers ADD COLUMN $columnName $columnDefinition";
            if ($conn->query($sql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود $columnName إلى جدول transfers</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عمود $columnName: " . $conn->error . "</p>\n";
            }
        } else {
            echo "<p style='color: blue;'>ℹ عمود $columnName موجود بالفعل في جدول transfers</p>\n";
        }
    }
    
    echo "<h3>6. إنشاء دوال مساعدة لتسجيل الأنشطة</h3>\n";
    
    // Create stored procedures for logging
    $proceduresSql = [
        "DROP PROCEDURE IF EXISTS LogTransferActivity",
        "
        CREATE PROCEDURE LogTransferActivity(
            IN p_transfer_id INT UNSIGNED,
            IN p_activity_type VARCHAR(50),
            IN p_description TEXT,
            IN p_old_value JSON,
            IN p_new_value JSON,
            IN p_user_id INT UNSIGNED,
            IN p_ip_address VARCHAR(45),
            IN p_location VARCHAR(255)
        )
        BEGIN
            INSERT INTO transfer_activity_log (
                transfer_id, activity_type, description, old_value, new_value,
                user_id, ip_address, location, created_at
            ) VALUES (
                p_transfer_id, p_activity_type, p_description, p_old_value, p_new_value,
                p_user_id, p_ip_address, p_location, NOW()
            );
        END
        "
    ];
    
    foreach ($proceduresSql as $sql) {
        if ($conn->query($sql)) {
            echo "<p style='color: green;'>✓ تم إنشاء stored procedure</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ خطأ في إنشاء stored procedure: " . $conn->error . "</p>\n";
        }
    }
    
    echo "<h3>7. إنشاء بيانات تجريبية للاختبار</h3>\n";
    
    // Get a sample transfer for testing
    $transferResult = $conn->query("SELECT id FROM transfers LIMIT 1");
    if ($transferResult && $transferResult->num_rows > 0) {
        $transfer = $transferResult->fetch_assoc();
        $transferId = $transfer['id'];
        
        // Insert sample activity log entries
        $sampleActivities = [
            [
                'activity_type' => 'created',
                'description' => 'تم إنشاء الحوالة',
                'old_value' => null,
                'new_value' => '{"status": "معلقة", "amount": 1000}',
                'user_id' => 1
            ],
            [
                'activity_type' => 'status_changed',
                'description' => 'تم تغيير حالة الحوالة من معلقة إلى مقبولة',
                'old_value' => '{"status": "معلقة"}',
                'new_value' => '{"status": "مقبولة"}',
                'user_id' => 1
            ],
            [
                'activity_type' => 'tracking_assigned',
                'description' => 'تم تعيين رقم التتبع',
                'old_value' => '{"tracking_number": null}',
                'new_value' => '{"tracking_number": "TK123456789"}',
                'user_id' => 1
            ],
            [
                'activity_type' => 'delivery_updated',
                'description' => 'تم تحديث حالة التسليم إلى في الطريق',
                'old_value' => '{"delivery_status": "pending"}',
                'new_value' => '{"delivery_status": "in_transit", "delivery_agent": "أحمد محمد"}',
                'user_id' => 1
            ]
        ];
        
        foreach ($sampleActivities as $activity) {
            $stmt = $conn->prepare("
                INSERT INTO transfer_activity_log (
                    transfer_id, activity_type, description, old_value, new_value, user_id, ip_address, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $ip = '***********';
            $stmt->bind_param('issssis', 
                $transferId, 
                $activity['activity_type'], 
                $activity['description'],
                $activity['old_value'],
                $activity['new_value'],
                $activity['user_id'],
                $ip
            );
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ تم إضافة نشاط: {$activity['description']}</p>\n";
            }
            $stmt->close();
        }
        
        // Insert sample delivery history
        $deliveryStatuses = [
            ['status' => 'pending', 'notes' => 'في انتظار الاستلام'],
            ['status' => 'picked_up', 'notes' => 'تم استلام الحوالة من المرسل'],
            ['status' => 'in_transit', 'notes' => 'الحوالة في الطريق إلى المستفيد'],
            ['status' => 'out_for_delivery', 'notes' => 'خرجت للتسليم']
        ];
        
        foreach ($deliveryStatuses as $delivery) {
            $stmt = $conn->prepare("
                INSERT INTO transfer_delivery_history (
                    transfer_id, delivery_status, delivery_notes, created_by, created_at
                ) VALUES (?, ?, ?, ?, NOW())
            ");
            
            $userId = 1;
            $stmt->bind_param('issi', $transferId, $delivery['status'], $delivery['notes'], $userId);
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ تم إضافة حالة تسليم: {$delivery['notes']}</p>\n";
            }
            $stmt->close();
        }
    }
    
    echo "<h3>8. التحقق من النتائج</h3>\n";
    
    // Verify the results
    $tables = ['transfer_status_history', 'transfer_activity_log', 'transfer_delivery_history'];
    
    foreach ($tables as $table) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            echo "<p><strong>جدول $table:</strong> $count سجل</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم تحسين نظام تاريخ الحوالات بنجاح!</h3>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 الميزات الجديدة:</h4>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li>✅ تسجيل شامل لجميع الأنشطة</li>\n";
    echo "<li>✅ تتبع تفصيلي لحالات التسليم</li>\n";
    echo "<li>✅ حفظ القيم القديمة والجديدة</li>\n";
    echo "<li>✅ تسجيل معلومات المستخدم والجهاز</li>\n";
    echo "<li>✅ دعم JSON للبيانات المعقدة</li>\n";
    echo "<li>✅ تتبع عمليات النظام التلقائية</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4>🔗 الخطوات التالية:</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='create_enhanced_transfer_manager.php' target='_blank'>إنشاء TransferManager محسن</a></li>\n";
    echo "<li><a href='update_transfer_details_page.php' target='_blank'>تحديث صفحة تفاصيل الحوالة</a></li>\n";
    echo "<li><a href='../dashboard/all_transfers.php' target='_blank'>اختبار الصفحة المحدثة</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

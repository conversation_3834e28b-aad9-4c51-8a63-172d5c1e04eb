<?php
/**
 * صفحة عرض تفاصيل المعاملة اليومية
 * View Daily Transaction Details
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

// إنشاء كائن المصادقة والتحقق من تسجيل الدخول
$auth = new Auth();
$auth->requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = $auth->getCurrentUser();

// التحقق من الصلاحيات
if (!$auth->hasPermission('daily_transactions.view')) {
    header('Location: ' . BASE_URL . 'dashboard/index.php?error=no_permission');
    exit;
}

// التحقق من وجود معرف المعاملة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . BASE_URL . 'dashboard/daily_transactions.php?error=invalid_id');
    exit;
}

$transaction_id = (int)$_GET['id'];
$error_message = '';

// جلب تفاصيل المعاملة
try {
    $stmt = $pdo->prepare("
        SELECT dt.*, 
               c.name_ar as country_name_ar, 
               c.name_en as country_name_en, 
               c.currency_code, 
               c.currency_symbol,
               b.name as branch_name,
               u_created.full_name as created_by_name,
               u_created.username as created_by_username,
               u_updated.full_name as updated_by_name,
               u_updated.username as updated_by_username
        FROM daily_transactions dt
        LEFT JOIN countries c ON dt.country_id = c.id
        LEFT JOIN branches b ON dt.branch_id = b.id
        LEFT JOIN users u_created ON dt.created_by = u_created.id
        LEFT JOIN users u_updated ON dt.updated_by = u_updated.id
        WHERE dt.id = ?
    ");
    $stmt->execute([$transaction_id]);
    $transaction = $stmt->fetch();
    
    if (!$transaction) {
        header('Location: ' . BASE_URL . 'dashboard/daily_transactions.php?error=transaction_not_found');
        exit;
    }
    
    // جلب تاريخ التغييرات
    $history_stmt = $pdo->prepare("
        SELECT dth.*, 
               u.full_name as user_name,
               u.username
        FROM daily_transaction_history dth
        LEFT JOIN users u ON dth.changed_by = u.id
        WHERE dth.transaction_id = ?
        ORDER BY dth.changed_at DESC
    ");
    $history_stmt->execute([$transaction_id]);
    $history = $history_stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "خطأ في جلب تفاصيل المعاملة: " . $e->getMessage();
}

$pageTitle = 'تفاصيل المعاملة: ' . $transaction['transaction_number'];
require_once __DIR__ . '/../includes/header.php';
?>

<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-auto p-0">
                <?php require_once __DIR__ . '/../includes/sidebar.php'; ?>
            </div>

            <!-- Main Content -->
            <div class="col">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h3 mb-0">
                            <i class="fas fa-file-alt text-primary me-2"></i>
                            تفاصيل المعاملة
                            <span class="text-muted">#<?php echo htmlspecialchars($transaction['transaction_number']); ?></span>
                        </h2>
                        <div>
                            <a href="export_daily_transaction_pdf.php?id=<?php echo $transaction_id; ?>" target="_blank" class="btn btn-outline-danger">
                                <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                            </a>
                        </div>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php">المعاملات اليومية</a></li>
                                <li class="breadcrumb-item active">تفاصيل المعاملة</li>
                            </ol>
                        </nav>
                    </div>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- أزرار الإجراءات -->
                    <div class="mb-4">
                        <a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة للقائمة
                        </a>
                        <?php if ($auth->hasPermission('daily_transactions.edit')): ?>
                            <a href="<?php echo BASE_URL; ?>dashboard/edit_daily_transaction.php?id=<?php echo $transaction_id; ?>" class="btn btn-warning ms-2">
                                <i class="fas fa-edit me-2"></i>
                                تعديل المعاملة
                            </a>
                        <?php endif; ?>
                        <?php if ($auth->hasPermission('daily_transactions.delete')): ?>
                            <button type="button" class="btn btn-danger ms-2" 
                                    onclick="confirmDelete(<?php echo $transaction_id; ?>, '<?php echo htmlspecialchars($transaction['transaction_number']); ?>')">
                                <i class="fas fa-trash me-2"></i>
                                حذف المعاملة
                            </button>
                        <?php endif; ?>
                        <button type="button" class="btn btn-info ms-2" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>
                            طباعة
                        </button>
                    </div>

                    <!-- بطاقة تفاصيل المعاملة -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        تفاصيل المعاملة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-3">معلومات أساسية</h6>
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <th class="text-nowrap">رقم المعاملة:</th>
                                                    <td><strong class="text-primary"><?php echo htmlspecialchars($transaction['transaction_number']); ?></strong></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">تاريخ الإنشاء:</th>
                                                    <td><?php echo date('Y-m-d H:i:s', strtotime($transaction['created_at'])); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">الدولة/العملة:</th>
                                                    <td>
                                                        <?php echo htmlspecialchars($transaction['country_name_ar']); ?>
                                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($transaction['currency_code']); ?></span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">الفرع:</th>
                                                    <td><?php echo htmlspecialchars($transaction['branch_name'] ?? 'غير محدد'); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">سعر القص للزبون:</th>
                                                    <td><?php echo number_format($transaction['customer_rate'], 6); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">سعر القص للمكتب:</th>
                                                    <td><?php echo number_format($transaction['office_rate'], 6); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">نوع العملية:</th>
                                                    <td><?php echo $transaction['operation_type'] == 'multiply' ? 'ضرب' : 'قسمة'; ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">المبلغ الناتج للزبون:</th>
                                                    <td><?php echo number_format($transaction['calculated_amount'], 2); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">المبلغ الناتج للمكتب:</th>
                                                    <td><?php echo number_format($transaction['office_amount'], 2); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">سعر الصرف:</th>
                                                    <td><?php echo number_format($transaction['exchange_rate'], 6); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">المبلغ للمستلم:</th>
                                                    <td><?php echo number_format($transaction['recipient_amount']); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">نوع التسليم:</th>
                                                    <td>
                                                        <?php
                                                        $delivery_badges = [
                                                            'cash' => 'bg-success',
                                                            'bank' => 'bg-primary',
                                                            'usdt' => 'bg-warning'
                                                        ];
                                                        $delivery_names = [
                                                            'cash' => 'كاش',
                                                            'bank' => 'بنكي',
                                                            'usdt' => 'USDT'
                                                        ];
                                                        ?>
                                                        <span class="badge <?php echo $delivery_badges[$transaction['delivery_type']]; ?>">
                                                            <?php echo $delivery_names[$transaction['delivery_type']]; ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">حالة التسليم:</th>
                                                    <td><span class="badge <?php echo $transaction['delivery_status'] == 'مستلم' ? 'bg-success' : 'bg-warning'; ?>"><?php echo htmlspecialchars($transaction['delivery_status']); ?></span></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">المبلغ المراد تحويله:</th>
                                                    <td><?php echo $transaction['transfer_amount'] ? number_format($transaction['transfer_amount'], 2) : '-'; ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">اسم المستلم:</th>
                                                    <td><?php echo htmlspecialchars($transaction['recipient_name'] ?? 'غير محدد'); ?></td>
                                                </tr>
                                                <tr>
                                                    <th class="text-nowrap">ملاحظات:</th>
                                                    <td><?php echo $transaction['notes'] ? nl2br(htmlspecialchars($transaction['notes'])) : '<span class="text-muted">لا توجد ملاحظات</span>'; ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-3">معلومات المستخدم</h6>
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <th class="text-nowrap">تم الإنشاء بواسطة:</th>
                                                    <td>
                                                        <?php echo htmlspecialchars($transaction['created_by_name'] ?? 'غير محدد'); ?>
                                                        <?php if ($transaction['created_by_username']): ?>
                                                            <small class="text-muted">(<?php echo htmlspecialchars($transaction['created_by_username']); ?>)</small>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php if ($transaction['updated_at'] && $transaction['updated_at'] != $transaction['created_at']): ?>
                                                    <tr>
                                                        <th class="text-nowrap">تم التعديل بواسطة:</th>
                                                        <td>
                                                            <?php echo htmlspecialchars($transaction['updated_by_name'] ?? 'غير محدد'); ?>
                                                            <?php if ($transaction['updated_by_username']): ?>
                                                                <small class="text-muted">(<?php echo htmlspecialchars($transaction['updated_by_username']); ?>)</small>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <th class="text-nowrap">تاريخ التعديل:</th>
                                                        <td><?php echo date('Y-m-d H:i:s', strtotime($transaction['updated_at'])); ?></td>
                                                    </tr>
                                                <?php endif; ?>
                                            </table>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="row mb-4">
                                        <div class="col-md-12">
                                            <h6 class="text-muted mb-3">تفاصيل المبالغ والعمليات الحسابية</h6>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="card bg-light mb-3">
                                                        <div class="card-body text-center">
                                                            <h6 class="card-title">المبلغ الأساسي</h6>
                                                            <h4 class="mb-0"><?php echo number_format($transaction['base_amount'], 2); ?></h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card bg-light mb-3">
                                                        <div class="card-body text-center">
                                                            <h6 class="card-title">
                                                                <?php echo $transaction['operation_type'] == 'multiply' ? 'ضرب' : 'قسمة'; ?>
                                                                <small class="text-muted">(<?php echo number_format($transaction['customer_rate'], 6); ?>)</small>
                                                            </h6>
                                                            <h4 class="mb-0"><?php echo number_format($transaction['calculated_amount'], 2); ?></h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card bg-light mb-3">
                                                        <div class="card-body text-center">
                                                            <h6 class="card-title">
                                                                المبلغ الناتج للمكتب
                                                                <small class="text-muted">(<?php echo number_format($transaction['office_rate'], 6); ?>)</small>
                                                            </h6>
                                                            <h4 class="mb-0 text-info"><?php echo number_format($transaction['office_amount'], 2); ?></h4>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card bg-light mb-3">
                                                        <div class="card-body text-center">
                                                            <h6 class="card-title">
                                                                المبلغ للمستلم
                                                                <small class="text-muted">(<?php echo number_format($transaction['exchange_rate'], 6); ?>)</small>
                                                            </h6>
                                                            <h4 class="mb-0 text-success"><?php echo number_format($transaction['recipient_amount']); ?></h4>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <hr>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-3">معلومات التسليم</h6>
                                            <table class="table table-sm table-borderless">
                                                <tr>
                                                    <th class="text-nowrap">نوع التسليم:</th>
                                                    <td>
                                                        <?php
                                                        $delivery_badges = [
                                                            'cash' => 'bg-success',
                                                            'bank' => 'bg-primary',
                                                            'usdt' => 'bg-warning'
                                                        ];
                                                        $delivery_names = [
                                                            'cash' => 'كاش',
                                                            'bank' => 'بنكي',
                                                            'usdt' => 'USDT'
                                                        ];
                                                        ?>
                                                        <span class="badge <?php echo $delivery_badges[$transaction['delivery_type']]; ?>">
                                                            <?php echo $delivery_names[$transaction['delivery_type']]; ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                                <?php if ($transaction['delivery_type'] != 'cash'): ?>
                                                    <tr>
                                                        <th class="text-nowrap">المبلغ المراد تحويله:</th>
                                                        <td><?php echo number_format($transaction['transfer_amount'], 2); ?></td>
                                                    </tr>
                                                    <tr>
                                                        <th class="text-nowrap">اسم المستلم:</th>
                                                        <td><?php echo htmlspecialchars($transaction['recipient_name'] ?? 'غير محدد'); ?></td>
                                                    </tr>
                                                <?php endif; ?>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-muted mb-3">ملاحظات</h6>
                                            <div class="p-3 bg-light rounded">
                                                <?php if ($transaction['notes']): ?>
                                                    <?php echo nl2br(htmlspecialchars($transaction['notes'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">لا توجد ملاحظات</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-history me-2"></i>
                                        سجل التغييرات
                                    </h5>
                                </div>
                                <div class="card-body p-0">
                                    <?php if (empty($history)): ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                                            <p class="text-muted mb-0">لا توجد تغييرات مسجلة</p>
                                        </div>
                                    <?php else: ?>
                                        <div class="timeline p-3">
                                            <?php foreach ($history as $index => $entry): ?>
                                                <div class="timeline-item">
                                                    <div class="timeline-marker 
                                                        <?php 
                                                        if ($entry['action_type'] == 'created') echo 'bg-success';
                                                        elseif ($entry['action_type'] == 'updated') echo 'bg-warning';
                                                        elseif ($entry['action_type'] == 'deleted') echo 'bg-danger';
                                                        ?>">
                                                        <i class="fas 
                                                            <?php 
                                                            if ($entry['action_type'] == 'created') echo 'fa-plus';
                                                            elseif ($entry['action_type'] == 'updated') echo 'fa-edit';
                                                            elseif ($entry['action_type'] == 'deleted') echo 'fa-trash';
                                                            ?>">
                                                        </i>
                                                    </div>
                                                    <div class="timeline-content">
                                                        <div class="d-flex justify-content-between">
                                                            <h6 class="mb-1">
                                                                <?php 
                                                                if ($entry['action_type'] == 'created') echo 'تم إنشاء المعاملة';
                                                                elseif ($entry['action_type'] == 'updated') echo 'تم تعديل المعاملة';
                                                                elseif ($entry['action_type'] == 'deleted') echo 'تم حذف المعاملة';
                                                                ?>
                                                            </h6>
                                                            <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($entry['changed_at'])); ?></small>
                                                        </div>
                                                        <p class="mb-1">
                                                            <small>
                                                                بواسطة: <?php echo htmlspecialchars($entry['user_name'] ?? 'غير محدد'); ?>
                                                                <?php if ($entry['username']): ?>
                                                                    <span class="text-muted">(<?php echo htmlspecialchars($entry['username']); ?>)</span>
                                                                <?php endif; ?>
                                                            </small>
                                                        </p>
                                                        <?php if ($entry['action_type'] == 'updated' && $entry['old_values'] && $entry['new_values']): ?>
                                                            <button class="btn btn-sm btn-outline-secondary mt-2" type="button" 
                                                                    data-bs-toggle="collapse" data-bs-target="#changes-<?php echo $index; ?>">
                                                                عرض التغييرات
                                                            </button>
                                                            <div class="collapse mt-2" id="changes-<?php echo $index; ?>">
                                                                <div class="card card-body bg-light">
                                                                    <?php 
                                                                    $old_values = json_decode($entry['old_values'], true);
                                                                    $new_values = json_decode($entry['new_values'], true);
                                                                    
                                                                    foreach ($new_values as $key => $value) {
                                                                        if (isset($old_values[$key]) && $old_values[$key] != $value) {
                                                                            echo '<div class="mb-1">';
                                                                            echo '<small class="text-muted">' . htmlspecialchars(getFieldName($key)) . ':</small><br>';
                                                                            echo '<small class="text-danger">- ' . htmlspecialchars($old_values[$key]) . '</small><br>';
                                                                            echo '<small class="text-success">+ ' . htmlspecialchars($value) . '</small>';
                                                                            echo '</div>';
                                                                        }
                                                                    }
                                                                    ?>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المعاملة رقم <strong id="transactionNumber"></strong>؟</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم نقل المعاملة إلى قائمة المعاملات المحذوفة ولن يمكن استرجاعها من هذه الصفحة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <a href="#" id="confirmDeleteBtn" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف المعاملة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // تأكيد الحذف
        function confirmDelete(transactionId, transactionNumber) {
            document.getElementById('transactionNumber').textContent = transactionNumber;
            document.getElementById('confirmDeleteBtn').href = 'daily_transactions.php?delete=' + transactionId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
// دالة مساعدة لعرض أسماء الحقول بشكل مناسب
function getFieldName($key) {
    $field_names = [
        'country_id' => 'الدولة',
        'base_amount' => 'المبلغ الأساسي',
        'customer_rate' => 'سعر القص للزبون',
        'operation_type' => 'نوع العملية الحسابية',
        'exchange_rate' => 'سعر الصرف',
        'delivery_type' => 'نوع التسليم',
        'transfer_amount' => 'المبلغ المراد تحويله',
        'recipient_name' => 'اسم المستلم',
        'notes' => 'ملاحظات'
    ];
    
    return $field_names[$key] ?? $key;
}
?>

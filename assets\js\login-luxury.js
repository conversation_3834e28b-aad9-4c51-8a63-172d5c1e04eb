/**
 * صفحة تسجيل الدخول الفخمة - JavaScript
 * Luxury Login Page - JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // تهيئة الجسيمات
    initParticles();
    
    // تهيئة النموذج
    initLoginForm();
    
    // تهيئة التأثيرات
    initEffects();
    
    // تهيئة الرسوم المتحركة
    initAnimations();
    
    console.log('🎨 Luxury Login Page loaded successfully!');
});

// تهيئة خلفية الجسيمات
function initParticles() {
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            particles: {
                number: {
                    value: 80,
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#ffd700']
                },
                shape: {
                    type: 'circle',
                    stroke: {
                        width: 0,
                        color: '#000000'
                    }
                },
                opacity: {
                    value: 0.5,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 1,
                        opacity_min: 0.1,
                        sync: false
                    }
                },
                size: {
                    value: 3,
                    random: true,
                    anim: {
                        enable: true,
                        speed: 2,
                        size_min: 0.1,
                        sync: false
                    }
                },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#ffffff',
                    opacity: 0.2,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 2,
                    direction: 'none',
                    random: true,
                    straight: false,
                    out_mode: 'out',
                    bounce: false,
                    attract: {
                        enable: false,
                        rotateX: 600,
                        rotateY: 1200
                    }
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: {
                        enable: true,
                        mode: 'repulse'
                    },
                    onclick: {
                        enable: true,
                        mode: 'push'
                    },
                    resize: true
                },
                modes: {
                    grab: {
                        distance: 140,
                        line_linked: {
                            opacity: 1
                        }
                    },
                    bubble: {
                        distance: 400,
                        size: 40,
                        duration: 2,
                        opacity: 8,
                        speed: 3
                    },
                    repulse: {
                        distance: 100,
                        duration: 0.4
                    },
                    push: {
                        particles_nb: 4
                    },
                    remove: {
                        particles_nb: 2
                    }
                }
            },
            retina_detect: true
        });
    }
}

// تهيئة نموذج تسجيل الدخول
function initLoginForm() {
    const form = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    
    // تبديل إظهار كلمة المرور
    if (passwordToggle) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
    
    // تأثيرات المدخلات
    const inputs = document.querySelectorAll('.luxury-input');
    inputs.forEach(input => {
        // تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            addInputRipple(this);
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
        
        // تأثير الكتابة
        input.addEventListener('input', function() {
            if (this.value.length > 0) {
                this.parentElement.classList.add('has-value');
            } else {
                this.parentElement.classList.remove('has-value');
            }
            
            // تحقق من صحة البيانات
            validateInput(this);
        });
    });
    
    // معالجة إرسال النموذج
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // التحقق من البيانات
            if (validateForm()) {
                showLoadingState();
                
                // إرسال النموذج بعد تأخير قصير للتأثير البصري
                setTimeout(() => {
                    form.submit();
                }, 1000);
            }
        });
    }
}

// إضافة تأثير الموجة للمدخلات
function addInputRipple(input) {
    const container = input.parentElement;
    const ripple = document.createElement('div');
    ripple.className = 'input-ripple';
    ripple.style.cssText = `
        position: absolute;
        top: 50%;
        left: 20px;
        width: 0;
        height: 0;
        background: rgba(102, 126, 234, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        animation: inputRipple 0.6s ease-out;
        pointer-events: none;
        z-index: 1;
    `;
    
    container.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// التحقق من صحة المدخل
function validateInput(input) {
    const value = input.value.trim();
    const container = input.parentElement;
    
    // إزالة رسائل الخطأ السابقة
    const existingError = container.querySelector('.input-error');
    if (existingError) {
        existingError.remove();
    }
    
    let isValid = true;
    let errorMessage = '';
    
    if (input.hasAttribute('required') && value === '') {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    } else if (input.type === 'text' && value.length < 3) {
        isValid = false;
        errorMessage = 'يجب أن يكون 3 أحرف على الأقل';
    } else if (input.type === 'password' && value.length < 6) {
        isValid = false;
        errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    if (!isValid) {
        container.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'input-error';
        errorDiv.textContent = errorMessage;
        errorDiv.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            animation: fadeInUp 0.3s ease;
        `;
        container.appendChild(errorDiv);
    } else {
        container.classList.remove('error');
        container.classList.add('valid');
    }
    
    return isValid;
}

// التحقق من صحة النموذج
function validateForm() {
    const inputs = document.querySelectorAll('.luxury-input[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateInput(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// إظهار حالة التحميل
function showLoadingState() {
    const loginBtn = document.getElementById('loginBtn');
    if (loginBtn) {
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;
        
        // تأثير النبضة
        loginBtn.style.animation = 'pulse 1.5s infinite';
    }
}

// تهيئة التأثيرات
function initEffects() {
    // تأثير التمرير على البطاقة
    const loginCard = document.querySelector('.login-card');
    if (loginCard) {
        loginCard.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        loginCard.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    }
    
    // تأثير النقر على الزر
    const luxuryBtn = document.querySelector('.luxury-btn');
    if (luxuryBtn) {
        luxuryBtn.addEventListener('click', function(e) {
            const ripple = this.querySelector('.btn-ripple');
            if (ripple) {
                ripple.style.width = '300px';
                ripple.style.height = '300px';
                
                setTimeout(() => {
                    ripple.style.width = '0';
                    ripple.style.height = '0';
                }, 600);
            }
        });
    }
    
    // تأثير الكتابة المتحركة
    typeWriterEffect();
}

// تأثير الكتابة المتحركة للعنوان
function typeWriterEffect() {
    const title = document.querySelector('.login-title');
    if (title) {
        const text = title.textContent;
        title.textContent = '';
        title.style.borderRight = '2px solid #667eea';
        
        let i = 0;
        const timer = setInterval(() => {
            title.textContent += text.charAt(i);
            i++;
            
            if (i >= text.length) {
                clearInterval(timer);
                setTimeout(() => {
                    title.style.borderRight = 'none';
                }, 500);
            }
        }, 100);
    }
}

// تهيئة الرسوم المتحركة
function initAnimations() {
    // رسوم متحركة للعناصر عند التحميل
    const animatedElements = document.querySelectorAll('.login-card, .copyright');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });
    
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.6s ease';
        observer.observe(el);
    });
    
    // تأثير الأشكال العائمة
    animateFloatingShapes();
}

// تحريك الأشكال العائمة
function animateFloatingShapes() {
    const shapes = document.querySelectorAll('.shape');
    
    shapes.forEach((shape, index) => {
        // حركة عشوائية
        setInterval(() => {
            const x = Math.random() * 20 - 10;
            const y = Math.random() * 20 - 10;
            const rotation = Math.random() * 360;
            
            shape.style.transform = `translate(${x}px, ${y}px) rotate(${rotation}deg)`;
        }, 3000 + index * 500);
    });
}

// تأثيرات إضافية عند تحميل الصفحة
window.addEventListener('load', function() {
    // إخفاء شاشة التحميل إذا كانت موجودة
    const loader = document.querySelector('.page-loader');
    if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => {
            loader.style.display = 'none';
        }, 500);
    }
    
    // تأثير الظهور التدريجي
    document.body.style.opacity = '1';
    
    // تشغيل الموسيقى الخلفية (اختياري)
    // playBackgroundMusic();
});

// تأثيرات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // Enter للإرسال
    if (e.key === 'Enter' && !e.shiftKey) {
        const form = document.getElementById('loginForm');
        if (form && document.activeElement.tagName !== 'BUTTON') {
            e.preventDefault();
            form.dispatchEvent(new Event('submit'));
        }
    }
    
    // Escape لإلغاء التحميل
    if (e.key === 'Escape') {
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn && loginBtn.classList.contains('loading')) {
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
            loginBtn.style.animation = '';
        }
    }
});

// إضافة الأنماط المطلوبة للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes inputRipple {
        to {
            width: 100px;
            height: 100px;
            opacity: 0;
        }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }
    
    .input-container.error .luxury-input {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
    }
    
    .input-container.valid .luxury-input {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
    }
    
    .input-container.error .input-icon {
        color: #dc3545 !important;
    }
    
    .input-container.valid .input-icon {
        color: #28a745 !important;
    }
    
    body {
        opacity: 0;
        transition: opacity 0.5s ease;
    }
`;

document.head.appendChild(style);

<?php
/**
 * SystemActivityManager - مدير تسجيل العمليات الشامل للنظام
 * 
 * يسجل جميع العمليات في النظام بتفاصيل شاملة
 * يدعم التسجيل التلقائي والمتقدم مع معلومات الجلسة والشبكة
 */

require_once __DIR__ . '/database.php';
require_once __DIR__ . '/auth.php';

class SystemActivityManager
{
    /** @var mysqli */
    private $db;
    
    /** @var array */
    private $currentUser;
    
    /** @var float */
    private $startTime;
    
    public function __construct(Database $database = null)
    {
        $this->db = $database ? $database::getConnection() : Database::getConnection();
        $this->startTime = microtime(true);
        
        // جلب معلومات المستخدم الحالي
        try {
            $auth = new Auth();
            $this->currentUser = $auth->getCurrentUser();
        } catch (Exception $e) {
            $this->currentUser = null;
        }
    }
    
    /**
     * تسجيل عملية في النظام
     * 
     * @param string $actionType نوع العملية (CREATE, UPDATE, DELETE, etc.)
     * @param string $module الوحدة (users, customers, transfers, etc.)
     * @param string $operation العملية المحددة (add_user, edit_customer, etc.)
     * @param string $description وصف مفصل للعملية
     * @param array $options خيارات إضافية
     * @return bool
     */
    public function logActivity(
        string $actionType,
        string $module,
        string $operation,
        string $description,
        array $options = []
    ): bool {
        try {
            // إعداد البيانات الأساسية
            $data = [
                'user_id' => $this->currentUser['id'] ?? null,
                'username' => $this->currentUser['username'] ?? 'system',
                'user_full_name' => $this->currentUser['full_name'] ?? null,
                'user_role' => $this->currentUser['role_name'] ?? null,
                'action_type' => strtoupper($actionType),
                'module' => $module,
                'operation' => $operation,
                'description' => $description,
                'target_table' => $options['target_table'] ?? null,
                'target_id' => $options['target_id'] ?? null,
                'target_identifier' => $options['target_identifier'] ?? null,
                'old_values' => isset($options['old_values']) ? json_encode($options['old_values'], JSON_UNESCAPED_UNICODE) : null,
                'new_values' => isset($options['new_values']) ? json_encode($options['new_values'], JSON_UNESCAPED_UNICODE) : null,
                'changed_fields' => isset($options['changed_fields']) ? json_encode($options['changed_fields'], JSON_UNESCAPED_UNICODE) : null,
                'additional_data' => isset($options['additional_data']) ? json_encode($options['additional_data'], JSON_UNESCAPED_UNICODE) : null,
                'session_id' => session_id() ?: null,
                'ip_address' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? null,
                'request_url' => $this->getCurrentURL(),
                'status' => $options['status'] ?? 'SUCCESS',
                'error_message' => $options['error_message'] ?? null,
                'execution_time' => round(microtime(true) - $this->startTime, 3)
            ];
            
            // إعداد الاستعلام
            $sql = "INSERT INTO system_activity_logs (
                user_id, username, user_full_name, user_role,
                action_type, module, operation, description,
                target_table, target_id, target_identifier,
                old_values, new_values, changed_fields, additional_data,
                session_id, ip_address, user_agent, request_method, request_url,
                status, error_message, execution_time, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->db->prepare($sql);
            if (!$stmt) {
                error_log("SystemActivityManager: فشل في إعداد الاستعلام: " . $this->db->error);
                return false;
            }
            
            $stmt->bind_param(
                'issssssssissssssssssssd',
                $data['user_id'],
                $data['username'],
                $data['user_full_name'],
                $data['user_role'],
                $data['action_type'],
                $data['module'],
                $data['operation'],
                $data['description'],
                $data['target_table'],
                $data['target_id'],
                $data['target_identifier'],
                $data['old_values'],
                $data['new_values'],
                $data['changed_fields'],
                $data['additional_data'],
                $data['session_id'],
                $data['ip_address'],
                $data['user_agent'],
                $data['request_method'],
                $data['request_url'],
                $data['status'],
                $data['error_message'],
                $data['execution_time']
            );
            
            $result = $stmt->execute();
            $stmt->close();
            
            return $result;
            
        } catch (Exception $e) {
            error_log("SystemActivityManager: خطأ في تسجيل العملية: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تسجيل عملية إنشاء سجل جديد
     */
    public function logCreate(string $module, string $operation, string $description, array $newData, array $options = []): bool
    {
        $options['new_values'] = $newData;
        return $this->logActivity('CREATE', $module, $operation, $description, $options);
    }
    
    /**
     * تسجيل عملية تحديث سجل
     */
    public function logUpdate(string $module, string $operation, string $description, array $oldData, array $newData, array $options = []): bool
    {
        $options['old_values'] = $oldData;
        $options['new_values'] = $newData;
        $options['changed_fields'] = $this->getChangedFields($oldData, $newData);
        return $this->logActivity('UPDATE', $module, $operation, $description, $options);
    }
    
    /**
     * تسجيل عملية حذف سجل
     */
    public function logDelete(string $module, string $operation, string $description, array $deletedData, array $options = []): bool
    {
        $options['old_values'] = $deletedData;
        return $this->logActivity('DELETE', $module, $operation, $description, $options);
    }
    
    /**
     * تسجيل عملية عرض/استعلام
     */
    public function logView(string $module, string $operation, string $description, array $options = []): bool
    {
        return $this->logActivity('VIEW', $module, $operation, $description, $options);
    }
    
    /**
     * تسجيل عملية تسجيل دخول
     */
    public function logLogin(string $username, bool $success = true, string $errorMessage = null): bool
    {
        $options = [
            'status' => $success ? 'SUCCESS' : 'FAILED',
            'error_message' => $errorMessage,
            'additional_data' => ['username' => $username]
        ];
        
        $description = $success ? 
            "تسجيل دخول ناجح للمستخدم: $username" : 
            "فشل تسجيل دخول للمستخدم: $username";
            
        return $this->logActivity('LOGIN', 'auth', 'user_login', $description, $options);
    }
    
    /**
     * تسجيل عملية تسجيل خروج
     */
    public function logLogout(string $username): bool
    {
        $options = ['additional_data' => ['username' => $username]];
        return $this->logActivity('LOGOUT', 'auth', 'user_logout', "تسجيل خروج للمستخدم: $username", $options);
    }
    
    /**
     * تسجيل خطأ في النظام
     */
    public function logError(string $module, string $operation, string $errorMessage, array $options = []): bool
    {
        $options['status'] = 'FAILED';
        $options['error_message'] = $errorMessage;
        return $this->logActivity('ERROR', $module, $operation, "خطأ في النظام: $errorMessage", $options);
    }
    
    /**
     * الحصول على عنوان IP الحقيقي للعميل
     */
    private function getClientIP(): ?string
    {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? null;
    }
    
    /**
     * الحصول على URL الحالي
     */
    private function getCurrentURL(): ?string
    {
        if (!isset($_SERVER['HTTP_HOST'])) {
            return null;
        }
        
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        
        return $protocol . '://' . $host . $uri;
    }
    
    /**
     * مقارنة البيانات القديمة والجديدة لاستخراج الحقول المتغيرة
     */
    private function getChangedFields(array $oldData, array $newData): array
    {
        $changed = [];
        
        foreach ($newData as $key => $newValue) {
            $oldValue = $oldData[$key] ?? null;
            if ($oldValue !== $newValue) {
                $changed[$key] = [
                    'old' => $oldValue,
                    'new' => $newValue
                ];
            }
        }
        
        return $changed;
    }
    
    /**
     * دالة مساعدة سريعة للتسجيل
     */
    public static function quickLog(string $actionType, string $module, string $operation, string $description, array $options = []): bool
    {
        $manager = new self();
        return $manager->logActivity($actionType, $module, $operation, $description, $options);
    }
}

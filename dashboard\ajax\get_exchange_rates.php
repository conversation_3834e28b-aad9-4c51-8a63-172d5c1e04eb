<?php
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/exchange_manager.php';
require_once __DIR__ . '/../../includes/currency_manager.php';

header('Content-Type: application/json');

try {
    $auth = new Auth();
    $auth->requireLogin();
    $auth->requirePermission('exchange.view');
    
    $exManager = new ExchangeManager(new Database());
    $curManager = new CurrencyManager(new Database());
    
    // Get all exchange rates
    $rates = $exManager->getAllExchangeRates();
    $currencies = $curManager->getAllCurrencies(true);
    
    // Format rates for JavaScript
    $ratesMap = [];
    foreach ($rates as $rate) {
        $key = $rate['from_currency_id'] . '-' . $rate['to_currency_id'];
        $ratesMap[$key] = $rate;
    }
    
    // Format currencies for JavaScript
    $currenciesMap = [];
    foreach ($currencies as $currency) {
        $currenciesMap[$currency['id']] = $currency;
    }
    
    echo json_encode([
        'success' => true,
        'rates' => $ratesMap,
        'currencies' => $currenciesMap
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

<?php
// Ensure $auth object and $current_user are available from the including page
require_once __DIR__ . '/image_helper.php';
?>
<aside id="sidebar" class="sidebar">
    <!-- Sidebar Header -->
    <div class="p-4 border-bottom border-light">
        <div class="d-flex align-items-center justify-content-center">
            <h4 class="text-white fw-bold mb-0"><?php echo SYSTEM_NAME; ?></h4>
        </div>
    </div>

    <!-- User Profile Section -->
    <div class="p-4 border-bottom border-light">
        <div class="d-flex align-items-center">
            <div class="profile-avatar me-3">
                <?php if (!empty($current_user['profile_image'])): ?>
                    <img src="<?php echo ImageHelper::getProfileImageUrl($current_user['profile_image'], $current_user['id']); ?>" 
                         alt="صورة المستخدم"
                         class="profile-avatar-img">
                <?php else: ?>
                    <div class="default-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                <?php endif; ?>
            </div>
            <div class="flex-grow-1 text-white">
                <p class="mb-1 fw-medium">
                    <?php echo htmlspecialchars($current_user['full_name'] ?? $current_user['username'], ENT_QUOTES, 'UTF-8'); ?>
                </p>
                <p class="mb-0 small opacity-75">
                    <?php echo htmlspecialchars($current_user['role_name'] ?? '', ENT_QUOTES, 'UTF-8'); ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Navigation Links -->
    <nav class="mt-3 px-3">
        <!-- لوحة التحكم -->
        <a href="<?php echo BASE_URL; ?>dashboard/index.php"
           class="sidebar-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
            <i class="fas fa-tachometer-alt"></i>
            <span>لوحة التحكم</span>
        </a>

        <!-- قائمة المستخدمين -->
        <div class="sidebar-section">
            <div class="sidebar-section-header collapsible" data-target="users-section">
                <i class="fas fa-users"></i>
                <span>قائمة المستخدمين</span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>
            
            <div class="sidebar-sub-links" id="users-section">
                <?php if ($auth->hasPermission('users.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/users.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>">
                    <i class="fas fa-user-friends"></i>
                    <span>المستخدمون</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('roles.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/roles.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'roles.php' ? 'active' : ''; ?>">
                    <i class="fas fa-user-shield"></i>
                    <span>الأدوار</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('permissions.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/permissions.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'permissions.php' ? 'active' : ''; ?>">
                    <i class="fas fa-key"></i>
                    <span>الصلاحيات</span>
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- قائمة الحوالات المحسنة -->
        <div class="sidebar-section transfers-menu">
            <a href="#" class="transfers-main-link" data-bs-toggle="collapse" data-bs-target="#transfersMenu"
               aria-expanded="false" aria-controls="transfersMenu">
                <div class="transfers-icon-wrapper">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <span class="transfers-text">قائمة الحوالات</span>
                <i class="fas fa-chevron-down dropdown-icon transfers-chevron"></i>
            </a>
            <div class="collapse sidebar-submenu transfers-submenu" id="transfersMenu">
                <?php if ($auth->hasPermission('customers.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/customers.php"
                   class="sidebar-sublink transfers-sublink customers-link <?php echo basename($_SERVER['PHP_SELF']) == 'customers.php' ? 'active' : ''; ?>">
                    <div class="transfers-subicon-wrapper customers-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <span>العملاء</span>
                    <div class="transfers-indicator"></div>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('transfers.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/transfers.php"
                   class="sidebar-sublink transfers-sublink transfers-link <?php echo basename($_SERVER['PHP_SELF']) == 'transfers.php' ? 'active' : ''; ?>">
                    <div class="transfers-subicon-wrapper transfers-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <span>الحوالات</span>
                    <div class="transfers-indicator"></div>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('transfers.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/all_transfers.php"
                   class="sidebar-sublink transfers-sublink all-transfers-link <?php echo basename($_SERVER['PHP_SELF']) == 'all_transfers.php' ? 'active' : ''; ?>">
                    <div class="transfers-subicon-wrapper all-transfers-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <span>جميع الحوالات</span>
                    <div class="transfers-indicator"></div>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('transfers.create')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/transfers.php"
                   class="sidebar-sublink transfers-sublink add-transfer-link <?php echo basename($_SERVER['PHP_SELF']) == 'transfers.php' ? 'active' : ''; ?>">
                    <div class="transfers-subicon-wrapper add-transfer-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <span>إضافة حوالة جديدة</span>
                    <div class="transfers-indicator"></div>
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- المكاتب -->
        <?php if ($auth->hasPermission('offices.view')): ?>
        <a href="<?php echo BASE_URL; ?>dashboard/offices.php"
           class="sidebar-link <?php echo basename($_SERVER['PHP_SELF']) == 'offices.php' ? 'active' : ''; ?>">
            <i class="fas fa-building"></i>
            <span>المكاتب</span>
        </a>
        <?php endif; ?>

        <!-- حوالات PayPal -->
        <?php if ($auth->hasPermission('paypal.view') || $auth->hasPermission('paypal.dashboard')): ?>
        <div class="sidebar-dropdown paypal-menu">
            <a href="#" class="sidebar-link dropdown-toggle paypal-main-link" data-bs-toggle="collapse" data-bs-target="#paypalMenu">
                <div class="paypal-icon-wrapper">
                    <i class="fab fa-paypal paypal-icon"></i>
                </div>
                <span class="paypal-text">حوالات PayPal</span>
                <i class="fas fa-chevron-down dropdown-icon paypal-chevron"></i>
            </a>
            <div class="collapse sidebar-submenu paypal-submenu" id="paypalMenu">
                <?php if ($auth->hasPermission('paypal.dashboard')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/paypal_transfers.php"
                   class="sidebar-sublink paypal-sublink dashboard-link <?php echo basename($_SERVER['PHP_SELF']) == 'paypal_transfers.php' ? 'active' : ''; ?>">
                    <div class="paypal-subicon-wrapper dashboard-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <span>لوحة تحكم PayPal</span>
                    <div class="paypal-indicator"></div>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('paypal.incoming.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/paypal_incoming_transfers.php"
                   class="sidebar-sublink paypal-sublink incoming-link <?php echo basename($_SERVER['PHP_SELF']) == 'paypal_incoming_transfers.php' ? 'active' : ''; ?>">
                    <div class="paypal-subicon-wrapper incoming-icon">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <span>الحوالات الواردة</span>
                    <div class="paypal-indicator"></div>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('paypal.create')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/add_paypal_transfer.php"
                   class="sidebar-sublink paypal-sublink create-link <?php echo basename($_SERVER['PHP_SELF']) == 'add_paypal_transfer.php' ? 'active' : ''; ?>">
                    <div class="paypal-subicon-wrapper create-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <span>إضافة حوالة جديدة</span>
                    <div class="paypal-indicator"></div>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('paypal.withdrawals.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/withdrawals.php"
                   class="sidebar-sublink paypal-sublink withdrawals-link <?php echo basename($_SERVER['PHP_SELF']) == 'withdrawals.php' ? 'active' : ''; ?>">
                    <div class="paypal-subicon-wrapper withdrawals-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <span>إدارة السحوبات</span>
                    <div class="paypal-indicator"></div>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('paypal.reports')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/paypal_reports.php"
                   class="sidebar-sublink paypal-sublink reports-link <?php echo basename($_SERVER['PHP_SELF']) == 'paypal_reports.php' ? 'active' : ''; ?>">
                    <div class="paypal-subicon-wrapper reports-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <span>تقارير PayPal</span>
                    <div class="paypal-indicator"></div>
                </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- التقارير والبنوك -->
        <div class="sidebar-section">
            <div class="sidebar-section-header collapsible" data-target="reports-section">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير والبنوك</span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>
            
            <div class="sidebar-sub-links" id="reports-section">
                <?php if ($auth->hasPermission('reports.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/reports.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'reports.php' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('cash.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/cash.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'cash.php' ? 'active' : ''; ?>">
                    <i class="fas fa-university"></i>
                    <span>الصناديق والبنوك</span>
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- المعاملات اليومية -->
        <?php if ($auth->hasPermission('daily_transactions.view') || $auth->hasPermission('daily_transactions.create')): ?>
        <div class="sidebar-section">
            <div class="sidebar-section-header collapsible" data-target="daily-transactions-section">
                <i class="fas fa-list-alt"></i>
                <span>المعاملات اليومية</span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>

            <div class="sidebar-sub-links" id="daily-transactions-section">
                <?php if ($auth->hasPermission('daily_transactions.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'daily_transactions.php' ? 'active' : ''; ?>">
                    <i class="fas fa-list"></i>
                    <span>عرض المعاملات</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('daily_transactions.create')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/add_daily_transaction.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'add_daily_transaction.php' ? 'active' : ''; ?>">
                    <i class="fas fa-plus-circle"></i>
                    <span>إضافة معاملة جديدة</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('daily_transactions.view_deleted') && isset($current_user['role_id']) && (int)$current_user['role_id'] === 1): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/deleted_daily_transactions.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'deleted_daily_transactions.php' ? 'active' : ''; ?>">
                    <i class="fas fa-trash-alt"></i>
                    <span>المعاملات المحذوفة</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('daily_transactions.reports')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/daily_transactions_reports.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'daily_transactions_reports.php' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-bar"></i>
                    <span>تقارير المعاملات</span>
                </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- قائمة الصرافة -->
        <div class="sidebar-section">
            <div class="sidebar-section-header collapsible" data-target="exchange-section">
                <i class="fas fa-exchange-alt"></i>
                <span>قائمة الصرافة</span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>
            
            <div class="sidebar-sub-links" id="exchange-section">
                <?php if ($auth->hasPermission('admin.manage')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/currency_management.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'currency_management.php' ? 'active' : ''; ?>">
                    <i class="fas fa-coins"></i>
                    <span>إدارة فئات العملات</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('exchange.view')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/exchange.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'exchange.php' ? 'active' : ''; ?>">
                    <i class="fas fa-exchange-alt"></i>
                    <span>عمليات الصرافة</span>
                </a>
                <?php endif; ?>

                <?php if ($auth->hasPermission('exchange.rates')): ?>
                <a href="<?php echo BASE_URL; ?>dashboard/exchange_rates.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'exchange_rates.php' ? 'active' : ''; ?>">
                    <i class="fas fa-chart-line"></i>
                    <span>أسعار العملات</span>
                </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- إدارة النظام (للأدمن فقط) -->
        <?php if (isset($current_user['role_id']) && (int)$current_user['role_id'] === 1): ?>
        <div class="sidebar-section">
            <div class="sidebar-section-header collapsible" data-target="system-admin-section">
                <i class="fas fa-cogs"></i>
                <span>إدارة النظام</span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>

            <div class="sidebar-sub-links" id="system-admin-section">
                <a href="<?php echo BASE_URL; ?>dashboard/system_activity_logs.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'system_activity_logs.php' ? 'active' : ''; ?>">
                    <i class="fas fa-history"></i>
                    <span>سجل العمليات الشامل</span>
                </a>

                <a href="<?php echo BASE_URL; ?>dashboard/permissions.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'permissions.php' ? 'active' : ''; ?>">
                    <i class="fas fa-shield-alt"></i>
                    <span>الصلاحيات</span>
                </a>

                <a href="<?php echo BASE_URL; ?>dashboard/roles.php"
                   class="sidebar-link sidebar-sub-link <?php echo basename($_SERVER['PHP_SELF']) == 'roles.php' ? 'active' : ''; ?>">
                    <i class="fas fa-user-tag"></i>
                    <span>الأدوار</span>
                </a>

                <a href="<?php echo BASE_URL; ?>install/setup_system_activity_logs.php"
                   class="sidebar-link sidebar-sub-link" target="_blank">
                    <i class="fas fa-database"></i>
                    <span>إعداد قاعدة البيانات</span>
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Logout Link -->
        <div class="mt-4 pt-3 border-top border-light">
            <a href="<?php echo BASE_URL; ?>auth/logout.php" class="sidebar-link text-danger">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </nav>
</aside>

<!-- Include sidebar JavaScript -->
<script>
// Enhanced collapsible functionality for sidebar
document.addEventListener('DOMContentLoaded', function() {
    console.log('Sidebar JavaScript loaded'); // للتشخيص

    const headers = document.querySelectorAll('.sidebar-section-header.collapsible');
    console.log('Found headers:', headers.length); // للتشخيص

    headers.forEach(header => {
        header.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const targetId = this.getAttribute('data-target');
            console.log('Clicked header with target:', targetId); // للتشخيص

            if (!targetId) return;

            const target = document.getElementById(targetId);
            if (!target) {
                console.log('Target not found:', targetId); // للتشخيص
                return;
            }

            // Toggle collapsed class on both header and target
            this.classList.toggle('collapsed');
            target.classList.toggle('collapsed');

            console.log('Toggled classes for:', targetId); // للتشخيص
        });

        // Initialize state - collapse all sections by default except active ones
        const targetId = header.getAttribute('data-target');
        if (targetId) {
            const target = document.getElementById(targetId);
            if (target) {
                // Check if any link in this section is active
                const hasActiveLink = target.querySelector('.sidebar-link.active');
                if (!hasActiveLink) {
                    header.classList.add('collapsed');
                    target.classList.add('collapsed');
                }
            }
        }
    });

    // Add hover effects
    headers.forEach(header => {
        header.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });

        header.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});

// Enhanced functionality for transfers menu
document.addEventListener('DOMContentLoaded', function() {
    const transfersDropdown = document.querySelector('.transfers-main-link[data-bs-toggle="collapse"]');
    const transfersMenu = document.getElementById('transfersMenu');

    if (transfersDropdown && transfersMenu) {
        transfersDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            if (isExpanded) {
                transfersMenu.classList.remove('show');
                this.setAttribute('aria-expanded', 'false');
            } else {
                transfersMenu.classList.add('show');
                this.setAttribute('aria-expanded', 'true');
            }

            console.log('Transfers menu toggled:', !isExpanded);
        });
    }

    // Add hover effects for transfers sublinks
    const transfersSublinks = document.querySelectorAll('.transfers-sublink');
    transfersSublinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(-3px)';
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateX(0)';
            }
        });
    });
});
</script>


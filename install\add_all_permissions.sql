-- إضافة جميع الصلاحيات الشاملة لجميع صفحات النظام
-- Comprehensive Permissions for All System Pages
-- تاريخ الإنشاء: 2024-12-19

-- ===================================
-- 1. صلاحيات إدارة المستخدمين
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
-- المستخدمين
('users.view', 'عرض قائمة المستخدمين', 'users', NOW()),
('users.create', 'إضافة مستخدم جديد', 'users', NOW()),
('users.edit', 'تعديل بيانات المستخدم', 'users', NOW()),
('users.delete', 'حذف المستخدم', 'users', NOW()),
('users.activate', 'تفعيل المستخدم', 'users', NOW()),
('users.deactivate', 'إلغاء تفعيل المستخدم', 'users', NOW()),
('users.reset_password', 'إعادة تعيين كلمة المرور', 'users', NOW()),
('users.profile', 'عرض الملف الشخصي', 'users', NOW()),

-- الأدوار
('roles.view', 'عرض قائمة الأدوار', 'roles', NOW()),
('roles.create', 'إضافة دور جديد', 'roles', NOW()),
('roles.edit', 'تعديل الدور', 'roles', NOW()),
('roles.delete', 'حذف الدور', 'roles', NOW()),
('roles.permissions', 'إدارة صلاحيات الدور', 'roles', NOW()),

-- الصلاحيات
('permissions.view', 'عرض قائمة الصلاحيات', 'permissions', NOW()),
('permissions.create', 'إضافة صلاحية جديدة', 'permissions', NOW()),
('permissions.edit', 'تعديل الصلاحية', 'permissions', NOW()),
('permissions.delete', 'حذف الصلاحية', 'permissions', NOW());

-- ===================================
-- 2. صلاحيات إدارة العملاء
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('customers.view', 'عرض قائمة العملاء', 'customers', NOW()),
('customers.create', 'إضافة عميل جديد', 'customers', NOW()),
('customers.edit', 'تعديل بيانات العميل', 'customers', NOW()),
('customers.delete', 'حذف العميل', 'customers', NOW()),
('customers.details', 'عرض تفاصيل العميل', 'customers', NOW()),
('customers.search', 'البحث في العملاء', 'customers', NOW()),
('customers.export', 'تصدير بيانات العملاء', 'customers', NOW()),
('customers.import', 'استيراد بيانات العملاء', 'customers', NOW()),
('customers.stats', 'عرض إحصائيات العملاء', 'customers', NOW());

-- ===================================
-- 3. صلاحيات إدارة الحوالات
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('transfers.view', 'عرض قائمة الحوالات', 'transfers', NOW()),
('transfers.create', 'إنشاء حوالة جديدة', 'transfers', NOW()),
('transfers.edit', 'تعديل الحوالة', 'transfers', NOW()),
('transfers.delete', 'حذف الحوالة', 'transfers', NOW()),
('transfers.details', 'عرض تفاصيل الحوالة', 'transfers', NOW()),
('transfers.status_change', 'تغيير حالة الحوالة', 'transfers', NOW()),
('transfers.delivery_update', 'تحديث حالة التسليم', 'transfers', NOW()),
('transfers.search', 'البحث في الحوالات', 'transfers', NOW()),
('transfers.export', 'تصدير بيانات الحوالات', 'transfers', NOW()),
('transfers.print', 'طباعة الحوالات', 'transfers', NOW()),
('transfers.cancel', 'إلغاء الحوالة', 'transfers', NOW()),
('transfers.approve', 'الموافقة على الحوالة', 'transfers', NOW()),
('transfers.reject', 'رفض الحوالة', 'transfers', NOW());

-- ===================================
-- 4. صلاحيات إدارة المكاتب
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('offices.view', 'عرض قائمة المكاتب', 'offices', NOW()),
('offices.create', 'إضافة مكتب جديد', 'offices', NOW()),
('offices.edit', 'تعديل بيانات المكتب', 'offices', NOW()),
('offices.delete', 'حذف المكتب', 'offices', NOW()),
('offices.details', 'عرض تفاصيل المكتب', 'offices', NOW()),
('offices.rates.view', 'عرض أسعار صرف المكتب', 'offices', NOW()),
('offices.rates.create', 'إضافة سعر صرف للمكتب', 'offices', NOW()),
('offices.rates.edit', 'تعديل سعر صرف المكتب', 'offices', NOW()),
('offices.rates.delete', 'حذف سعر صرف المكتب', 'offices', NOW()),
('offices.operations.view', 'عرض عمليات المكتب', 'offices', NOW()),
('offices.operations.create', 'إضافة عملية للمكتب', 'offices', NOW()),
('offices.operations.edit', 'تعديل عملية المكتب', 'offices', NOW()),
('offices.operations.delete', 'حذف عملية المكتب', 'offices', NOW()),
('offices.stats', 'عرض إحصائيات المكاتب', 'offices', NOW());

-- ===================================
-- 5. صلاحيات حوالات PayPal
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('paypal.view', 'عرض حوالات PayPal', 'paypal', NOW()),
('paypal.create', 'إضافة حوالة PayPal جديدة', 'paypal', NOW()),
('paypal.edit', 'تعديل حوالة PayPal', 'paypal', NOW()),
('paypal.delete', 'حذف حوالة PayPal', 'paypal', NOW()),
('paypal.details', 'عرض تفاصيل حوالة PayPal', 'paypal', NOW()),
('paypal.mark_received', 'تأكيد استلام حوالة PayPal', 'paypal', NOW()),
('paypal.incoming.view', 'عرض الحوالات الواردة', 'paypal', NOW()),
('paypal.outgoing.view', 'عرض الحوالات الصادرة', 'paypal', NOW()),
('paypal.export', 'تصدير بيانات PayPal', 'paypal', NOW()),
('paypal.search', 'البحث في حوالات PayPal', 'paypal', NOW()),
('paypal.stats', 'عرض إحصائيات PayPal', 'paypal', NOW());

-- ===================================
-- 6. صلاحيات الصرافة والعملات
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('exchange.view', 'عرض عمليات الصرافة', 'exchange', NOW()),
('exchange.create', 'إنشاء عملية صرافة جديدة', 'exchange', NOW()),
('exchange.edit', 'تعديل عملية الصرافة', 'exchange', NOW()),
('exchange.delete', 'حذف عملية الصرافة', 'exchange', NOW()),
('exchange.rates.view', 'عرض أسعار الصرف', 'exchange', NOW()),
('exchange.rates.create', 'إضافة سعر صرف جديد', 'exchange', NOW()),
('exchange.rates.edit', 'تعديل سعر الصرف', 'exchange', NOW()),
('exchange.rates.delete', 'حذف سعر الصرف', 'exchange', NOW()),
('exchange.profit.view', 'عرض أرباح الصرافة', 'exchange', NOW()),

-- العملات
('currencies.view', 'عرض قائمة العملات', 'currencies', NOW()),
('currencies.create', 'إضافة عملة جديدة', 'currencies', NOW()),
('currencies.edit', 'تعديل العملة', 'currencies', NOW()),
('currencies.delete', 'حذف العملة', 'currencies', NOW()),
('currencies.activate', 'تفعيل العملة', 'currencies', NOW()),
('currencies.deactivate', 'إلغاء تفعيل العملة', 'currencies', NOW());

-- ===================================
-- 7. صلاحيات الصناديق والبنوك
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('cash.view', 'عرض الصناديق النقدية', 'cash', NOW()),
('cash.create', 'إضافة صندوق نقدي جديد', 'cash', NOW()),
('cash.edit', 'تعديل الصندوق النقدي', 'cash', NOW()),
('cash.delete', 'حذف الصندوق النقدي', 'cash', NOW()),
('cash.movements', 'تسجيل حركات الصندوق', 'cash', NOW()),
('cash.deposit', 'إيداع في الصندوق', 'cash', NOW()),
('cash.withdraw', 'سحب من الصندوق', 'cash', NOW()),
('cash.balance', 'عرض رصيد الصندوق', 'cash', NOW()),

-- البنوك
('banks.view', 'عرض الحسابات البنكية', 'banks', NOW()),
('banks.create', 'إضافة حساب بنكي جديد', 'banks', NOW()),
('banks.edit', 'تعديل الحساب البنكي', 'banks', NOW()),
('banks.delete', 'حذف الحساب البنكي', 'banks', NOW()),
('banks.movements', 'تسجيل حركات بنكية', 'banks', NOW()),
('banks.deposit', 'إيداع بنكي', 'banks', NOW()),
('banks.withdraw', 'سحب بنكي', 'banks', NOW()),
('banks.balance', 'عرض رصيد الحساب البنكي', 'banks', NOW()),
('banks.history', 'عرض تاريخ الحركات البنكية', 'banks', NOW());

-- ===================================
-- 8. صلاحيات التقارير
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('reports.view', 'عرض التقارير', 'reports', NOW()),
('reports.export', 'تصدير التقارير', 'reports', NOW()),
('reports.print', 'طباعة التقارير', 'reports', NOW()),
('reports.financial', 'التقارير المالية', 'reports', NOW()),
('reports.transfers', 'تقارير الحوالات', 'reports', NOW()),
('reports.exchange', 'تقارير الصرافة', 'reports', NOW()),
('reports.customers', 'تقارير العملاء', 'reports', NOW()),
('reports.offices', 'تقارير المكاتب', 'reports', NOW()),
('reports.daily', 'التقرير اليومي', 'reports', NOW()),
('reports.monthly', 'التقرير الشهري', 'reports', NOW()),
('reports.yearly', 'التقرير السنوي', 'reports', NOW()),
('reports.profit', 'تقارير الأرباح', 'reports', NOW()),
('reports.summary', 'التقارير الملخصة', 'reports', NOW()),
('reports.detailed', 'التقارير المفصلة', 'reports', NOW()),
('reports.analytics', 'التحليلات والإحصائيات', 'reports', NOW());

-- ===================================
-- 9. صلاحيات النظام والإدارة
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('system.dashboard', 'الوصول للوحة التحكم', 'system', NOW()),
('system.settings', 'إعدادات النظام', 'system', NOW()),
('system.maintenance', 'صيانة النظام', 'system', NOW()),
('system.backup', 'النسخ الاحتياطي', 'system', NOW()),
('system.restore', 'استعادة النسخ الاحتياطية', 'system', NOW()),
('system.logs', 'عرض سجلات النظام', 'system', NOW()),
('system.activity_logs.view', 'عرض سجل العمليات', 'system', NOW()),
('system.activity_logs.export', 'تصدير سجل العمليات', 'system', NOW()),
('system.activity_logs.details', 'تفاصيل سجل العمليات', 'system', NOW()),
('system.activity_logs.admin_only', 'وصول كامل لسجل العمليات', 'system', NOW()),
('system.database', 'إدارة قاعدة البيانات', 'system', NOW()),
('system.updates', 'تحديثات النظام', 'system', NOW()),
('system.security', 'إعدادات الأمان', 'system', NOW());

-- ===================================
-- 10. صلاحيات الفروع
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('branches.view', 'عرض قائمة الفروع', 'branches', NOW()),
('branches.create', 'إضافة فرع جديد', 'branches', NOW()),
('branches.edit', 'تعديل بيانات الفرع', 'branches', NOW()),
('branches.delete', 'حذف الفرع', 'branches', NOW()),
('branches.details', 'عرض تفاصيل الفرع', 'branches', NOW()),
('branches.stats', 'إحصائيات الفروع', 'branches', NOW()),
('branches.users', 'إدارة مستخدمي الفرع', 'branches', NOW());

-- ===================================
-- 11. صلاحيات المراجعة والتدقيق
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('audit.view', 'عرض سجلات المراجعة', 'audit', NOW()),
('audit.export', 'تصدير سجلات المراجعة', 'audit', NOW()),
('audit.details', 'تفاصيل المراجعة', 'audit', NOW()),
('audit.compliance', 'تقارير الامتثال', 'audit', NOW());

-- ===================================
-- 12. صلاحيات الإشعارات
-- ===================================
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('notifications.view', 'عرض الإشعارات', 'notifications', NOW()),
('notifications.create', 'إنشاء إشعار', 'notifications', NOW()),
('notifications.send', 'إرسال الإشعارات', 'notifications', NOW()),
('notifications.delete', 'حذف الإشعارات', 'notifications', NOW()),
('notifications.settings', 'إعدادات الإشعارات', 'notifications', NOW());

-- ===================================
-- ربط الصلاحيات بالأدوار
-- ===================================

-- ===================================
-- دور مدير النظام (System Admin) - role_id = 1
-- جميع الصلاحيات
-- ===================================
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions WHERE module IN (
    'users', 'roles', 'permissions', 'customers', 'transfers', 'offices',
    'paypal', 'exchange', 'currencies', 'cash', 'banks', 'reports',
    'system', 'branches', 'audit', 'notifications'
);

-- ===================================
-- دور مدير الفرع (Branch Manager) - role_id = 2
-- صلاحيات محدودة
-- ===================================
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions WHERE name IN (
    -- العملاء
    'customers.view', 'customers.create', 'customers.edit', 'customers.details', 'customers.search', 'customers.stats',
    -- الحوالات
    'transfers.view', 'transfers.create', 'transfers.edit', 'transfers.details', 'transfers.status_change',
    'transfers.delivery_update', 'transfers.search', 'transfers.print', 'transfers.approve',
    -- المكاتب
    'offices.view', 'offices.details', 'offices.rates.view', 'offices.operations.view', 'offices.stats',
    -- PayPal
    'paypal.view', 'paypal.create', 'paypal.edit', 'paypal.details', 'paypal.mark_received',
    'paypal.incoming.view', 'paypal.outgoing.view', 'paypal.search', 'paypal.stats',
    -- الصرافة
    'exchange.view', 'exchange.create', 'exchange.edit', 'exchange.rates.view', 'exchange.profit.view',
    'currencies.view',
    -- الصناديق والبنوك
    'cash.view', 'cash.movements', 'cash.deposit', 'cash.withdraw', 'cash.balance',
    'banks.view', 'banks.movements', 'banks.deposit', 'banks.withdraw', 'banks.balance', 'banks.history',
    -- التقارير
    'reports.view', 'reports.export', 'reports.print', 'reports.financial', 'reports.transfers',
    'reports.exchange', 'reports.customers', 'reports.offices', 'reports.daily', 'reports.monthly',
    'reports.profit', 'reports.summary', 'reports.analytics',
    -- النظام
    'system.dashboard', 'system.logs',
    -- الفروع
    'branches.view', 'branches.details', 'branches.stats', 'branches.users',
    -- الإشعارات
    'notifications.view', 'notifications.create', 'notifications.send'
);

-- ===================================
-- دور الصراف (Cashier) - role_id = 3
-- صلاحيات العمليات اليومية
-- ===================================
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions WHERE name IN (
    -- العملاء
    'customers.view', 'customers.create', 'customers.details', 'customers.search',
    -- الحوالات
    'transfers.view', 'transfers.create', 'transfers.details', 'transfers.delivery_update', 'transfers.search', 'transfers.print',
    -- PayPal
    'paypal.view', 'paypal.create', 'paypal.details', 'paypal.mark_received', 'paypal.incoming.view', 'paypal.search',
    -- الصرافة
    'exchange.view', 'exchange.create', 'exchange.rates.view',
    'currencies.view',
    -- الصناديق والبنوك
    'cash.view', 'cash.movements', 'cash.deposit', 'cash.withdraw', 'cash.balance',
    'banks.view', 'banks.movements', 'banks.balance', 'banks.history',
    -- التقارير الأساسية
    'reports.view', 'reports.daily', 'reports.summary',
    -- النظام
    'system.dashboard',
    -- الإشعارات
    'notifications.view'
);

-- ===================================
-- دور موظف العمليات (Operations Staff) - role_id = 4
-- صلاحيات العرض والمتابعة
-- ===================================
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions WHERE name IN (
    -- العملاء
    'customers.view', 'customers.details', 'customers.search', 'customers.stats',
    -- الحوالات
    'transfers.view', 'transfers.details', 'transfers.status_change', 'transfers.delivery_update', 'transfers.search',
    -- المكاتب
    'offices.view', 'offices.details', 'offices.operations.view', 'offices.stats',
    -- PayPal
    'paypal.view', 'paypal.details', 'paypal.incoming.view', 'paypal.outgoing.view', 'paypal.search', 'paypal.stats',
    -- الصرافة
    'exchange.view', 'exchange.rates.view', 'exchange.profit.view',
    'currencies.view',
    -- الصناديق والبنوك
    'cash.view', 'cash.balance', 'banks.view', 'banks.balance', 'banks.history',
    -- التقارير
    'reports.view', 'reports.transfers', 'reports.customers', 'reports.offices', 'reports.daily', 'reports.summary',
    -- النظام
    'system.dashboard',
    -- الإشعارات
    'notifications.view'
);

-- ===================================
-- دور المحاسب (Accountant) - role_id = 5
-- صلاحيات مالية ومحاسبية
-- ===================================
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT 5, id FROM permissions WHERE name IN (
    -- العملاء
    'customers.view', 'customers.details', 'customers.search', 'customers.stats', 'customers.export',
    -- الحوالات
    'transfers.view', 'transfers.details', 'transfers.search', 'transfers.export',
    -- المكاتب
    'offices.view', 'offices.details', 'offices.stats',
    -- PayPal
    'paypal.view', 'paypal.details', 'paypal.incoming.view', 'paypal.outgoing.view', 'paypal.export', 'paypal.stats',
    -- الصرافة
    'exchange.view', 'exchange.rates.view', 'exchange.profit.view',
    'currencies.view',
    -- الصناديق والبنوك
    'cash.view', 'cash.balance', 'banks.view', 'banks.balance', 'banks.history',
    -- التقارير (جميع التقارير المالية)
    'reports.view', 'reports.export', 'reports.print', 'reports.financial', 'reports.transfers',
    'reports.exchange', 'reports.customers', 'reports.offices', 'reports.daily', 'reports.monthly',
    'reports.yearly', 'reports.profit', 'reports.summary', 'reports.detailed', 'reports.analytics',
    -- النظام
    'system.dashboard',
    -- المراجعة
    'audit.view', 'audit.export', 'audit.details', 'audit.compliance',
    -- الإشعارات
    'notifications.view'
);

-- ===================================
-- التحقق من إضافة الصلاحيات
-- ===================================
SELECT 'تم إضافة جميع الصلاحيات بنجاح!' as status;
SELECT module, COUNT(*) as permission_count FROM permissions GROUP BY module ORDER BY module;
SELECT COUNT(*) as total_permissions FROM permissions;

<?php
/**
 * اختبار إصلاح أخطاء JavaScript
 * Test JavaScript Syntax Fix
 */
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح أخطاء JavaScript</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug text-danger"></i>
            <i class="fas fa-arrow-right text-muted mx-2"></i>
            <i class="fas fa-check text-success"></i>
            اختبار إصلاح أخطاء JavaScript
        </h1>
        
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle me-2"></i>تم إصلاح الأخطاء بنجاح!</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>الأخطاء التي تم إصلاحها:</h6>
                    <ul class="mb-0">
                        <li><code>Uncaught SyntaxError: Unexpected token '}'</code></li>
                        <li><code>updateCalculations is not defined</code></li>
                        <li>مشاكل النطاق (Scope) في JavaScript</li>
                        <li>الكود المكرر خارج DOMContentLoaded</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>التحسينات المطبقة:</h6>
                    <ul class="mb-0">
                        <li>تنظيف بناء الجملة JavaScript</li>
                        <li>توحيد استدعاء الدوال</li>
                        <li>تحسين هيكل الكود</li>
                        <li>إزالة التكرار غير المرغوب</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- عرض الإصلاحات -->
        <div class="row">
            <div class="col-md-6">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">❌ قبل الإصلاح</h6>
                    </div>
                    <div class="card-body">
                        <div class="code-block text-danger">
                            <strong>خطأ في بناء الجملة:</strong><br>
                            <code>
                            // تهيئة البحث التفاعلي للدول<br>
                            initCountrySearch();<br>
                            }); // ← خطأ: قوس إضافي<br>
                            </code>
                        </div>
                        <hr>
                        <div class="code-block text-danger">
                            <strong>دالة غير معرفة:</strong><br>
                            <code>
                            // تحديث الحسابات<br>
                            updateCalculations(); // ← خطأ: غير معرفة<br>
                            </code>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">✅ بعد الإصلاح</h6>
                    </div>
                    <div class="card-body">
                        <div class="code-block text-success">
                            <strong>بناء جملة صحيح:</strong><br>
                            <code>
                            // تهيئة البحث التفاعلي للدول<br>
                            initCountrySearch();<br>
                            // ← تم إزالة القوس الإضافي<br>
                            </code>
                        </div>
                        <hr>
                        <div class="code-block text-success">
                            <strong>دالة صحيحة:</strong><br>
                            <code>
                            // تحديث الحسابات<br>
                            calculateAmounts(); // ← صحيح: معرفة ومتاحة<br>
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار الملفات -->
        <div class="mt-4">
            <h3><i class="fas fa-file-code me-2"></i>حالة الملفات</h3>
            
            <?php
            $files_status = [
                'dashboard/add_daily_transaction.php' => [
                    'name' => 'صفحة إضافة معاملة',
                    'expected_functions' => ['initCountrySearch', 'calculateAmounts', 'selectCountry'],
                    'forbidden_patterns' => ['updateCalculations', 'Unexpected token']
                ],
                'dashboard/edit_daily_transaction.php' => [
                    'name' => 'صفحة تعديل معاملة',
                    'expected_functions' => ['initCountrySearch', 'calculateAmounts', 'selectCountry'],
                    'forbidden_patterns' => ['updateCalculations', 'Unexpected token']
                ],
                'dashboard/daily_transactions.php' => [
                    'name' => 'صفحة قائمة المعاملات',
                    'expected_functions' => ['initCountryFilterSearch', 'selectCountryFilter'],
                    'forbidden_patterns' => ['updateCalculations', 'Unexpected token']
                ]
            ];

            foreach ($files_status as $file => $config) {
                $file_path = __DIR__ . '/../' . $file;
                $exists = file_exists($file_path);
                
                echo "<div class='card mb-3'>";
                echo "<div class='card-header'>";
                echo "<h6 class='mb-0'><i class='fas fa-file me-2'></i>{$config['name']}</h6>";
                echo "</div>";
                echo "<div class='card-body'>";
                
                if (!$exists) {
                    echo "<div class='alert alert-danger'>❌ الملف غير موجود</div>";
                } else {
                    $content = file_get_contents($file_path);
                    $all_good = true;
                    $issues = [];
                    
                    // فحص الدوال المطلوبة
                    foreach ($config['expected_functions'] as $function) {
                        if (strpos($content, $function) === false) {
                            $all_good = false;
                            $issues[] = "دالة مفقودة: {$function}";
                        }
                    }
                    
                    // فحص الأنماط المحظورة
                    foreach ($config['forbidden_patterns'] as $pattern) {
                        if (strpos($content, $pattern) !== false) {
                            $all_good = false;
                            $issues[] = "مشكلة موجودة: {$pattern}";
                        }
                    }
                    
                    if ($all_good) {
                        echo "<div class='alert alert-success mb-0'>";
                        echo "<i class='fas fa-check-circle me-2'></i>";
                        echo "<strong>✅ الملف سليم</strong><br>";
                        echo "<small>جميع الدوال المطلوبة موجودة ولا توجد أخطاء</small>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning mb-0'>";
                        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                        echo "<strong>⚠ يحتاج مراجعة</strong><br>";
                        echo "<small>" . implode('<br>', $issues) . "</small>";
                        echo "</div>";
                    }
                }
                
                echo "</div>";
                echo "</div>";
            }
            ?>
        </div>

        <!-- إرشادات الاختبار -->
        <div class="alert alert-info">
            <h6><i class="fas fa-lightbulb me-2"></i>كيفية التحقق من الإصلاح:</h6>
            <ol class="mb-0">
                <li><strong>افتح أدوات المطور</strong> (F12) في المتصفح</li>
                <li><strong>انتقل إلى تبويب Console</strong></li>
                <li><strong>قم بتحديث الصفحة</strong> (Ctrl+F5)</li>
                <li><strong>تأكد من عدم ظهور أخطاء JavaScript</strong></li>
                <li><strong>جرب البحث التفاعلي</strong> للتأكد من عمله</li>
            </ol>
        </div>

        <!-- أزرار الاختبار -->
        <div class="text-center">
            <h5 class="mb-3">🧪 اختبر الإصلاحات</h5>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-primary btn-lg" target="_blank">
                <i class="fas fa-plus me-2"></i>
                اختبار صفحة الإضافة
            </a>
            <a href="../dashboard/edit_daily_transaction.php?id=1" class="btn btn-warning btn-lg ms-2" target="_blank">
                <i class="fas fa-edit me-2"></i>
                اختبار صفحة التعديل
            </a>
            <a href="../dashboard/daily_transactions.php" class="btn btn-success btn-lg ms-2" target="_blank">
                <i class="fas fa-list me-2"></i>
                اختبار صفحة القائمة
            </a>
        </div>

        <!-- نصائح إضافية -->
        <div class="mt-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-tips me-2"></i>نصائح للمطورين</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>لتجنب أخطاء JavaScript:</h6>
                            <ul>
                                <li>تحقق من إغلاق الأقواس بشكل صحيح</li>
                                <li>تأكد من تعريف الدوال قبل استدعائها</li>
                                <li>استخدم أدوات المطور للتشخيص</li>
                                <li>اختبر الكود في بيئات مختلفة</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>أفضل الممارسات:</h6>
                            <ul>
                                <li>ضع الكود داخل DOMContentLoaded</li>
                                <li>استخدم أسماء دوال واضحة</li>
                                <li>تجنب التكرار في الكود</li>
                                <li>اكتب تعليقات توضيحية</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسالة النجاح النهائية -->
        <div class="alert alert-success mt-4 text-center">
            <h4><i class="fas fa-trophy text-warning me-2"></i>تم الإصلاح بنجاح!</h4>
            <p class="mb-0">
                جميع أخطاء JavaScript تم إصلاحها والبحث التفاعلي للدول يعمل بشكل مثالي.
                <br>
                <strong>النظام جاهز للاستخدام!</strong> 🎉
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

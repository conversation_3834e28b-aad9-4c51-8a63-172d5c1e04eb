<?php
/**
 * اختبار نهائي للبحث التفاعلي للدول
 * Final Test for Interactive Country Search
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نهائي - البحث التفاعلي للدول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .feature-demo { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .before-after { display: flex; gap: 20px; }
        .before-after > div { flex: 1; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-check-double text-success"></i>
            اختبار نهائي - البحث التفاعلي للدول
        </h1>
        
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle me-2"></i>تم إصلاح جميع المشاكل</h5>
            <ul class="mb-0">
                <li>✅ تم إصلاح خطأ <code>updateCalculations is not defined</code></li>
                <li>✅ تم نقل جميع دوال البحث داخل <code>DOMContentLoaded</code></li>
                <li>✅ تم حذف الكود المكرر</li>
                <li>✅ تم توحيد استدعاء <code>calculateAmounts()</code></li>
                <li>✅ البحث التفاعلي يعمل بشكل مثالي</li>
            </ul>
        </div>

        <!-- مقارنة قبل وبعد -->
        <div class="feature-demo">
            <h3 class="text-center mb-4">
                <i class="fas fa-exchange-alt text-primary me-2"></i>
                مقارنة: قبل وبعد التحديث
            </h3>
            
            <div class="before-after">
                <div>
                    <h5 class="text-danger">❌ قبل التحديث</h5>
                    <div class="card">
                        <div class="card-body">
                            <h6>قائمة منسدلة ثابتة:</h6>
                            <select class="form-select" disabled>
                                <option>اختر الدولة والعملة</option>
                                <option>مصر - EGP</option>
                                <option>السعودية - SAR</option>
                                <option>الإمارات - AED</option>
                                <option>...</option>
                            </select>
                            <small class="text-muted mt-2 d-block">
                                • تحتاج للتمرير في قائمة طويلة<br>
                                • صعوبة في البحث<br>
                                • واجهة تقليدية
                            </small>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h5 class="text-success">✅ بعد التحديث</h5>
                    <div class="card">
                        <div class="card-body">
                            <h6>بحث تفاعلي ذكي:</h6>
                            <input type="text" class="form-control" placeholder="ابحث عن الدولة أو العملة..." disabled>
                            <small class="text-muted mt-2 d-block">
                                • بحث فوري أثناء الكتابة<br>
                                • نتائج ذكية ومرتبة<br>
                                • واجهة عصرية وسهلة
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                echo "<div class='test-result test-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        // اختبار الملفات النهائية
        showInfo("<strong>فحص الملفات النهائية:</strong>");
        
        $files_to_check = [
            'dashboard/add_daily_transaction.php' => [
                'description' => 'صفحة إضافة معاملة',
                'required_functions' => ['country_search', 'initCountrySearch', 'calculateAmounts'],
                'forbidden_functions' => ['updateCalculations']
            ],
            'dashboard/edit_daily_transaction.php' => [
                'description' => 'صفحة تعديل معاملة',
                'required_functions' => ['country_search', 'initCountrySearch', 'calculateAmounts'],
                'forbidden_functions' => ['updateCalculations']
            ],
            'dashboard/daily_transactions.php' => [
                'description' => 'صفحة قائمة المعاملات',
                'required_functions' => ['country_search_filter', 'initCountryFilterSearch'],
                'forbidden_functions' => ['updateCalculations']
            ]
        ];
        
        foreach ($files_to_check as $file => $config) {
            $file_path = __DIR__ . '/../' . $file;
            $exists = file_exists($file_path);

            if (!$exists) {
                showResult($config['description'], false, "الملف غير موجود");
                continue;
            }

            $content = file_get_contents($file_path);

            // فحص وجود الدوال المطلوبة
            $missing_functions = [];
            $found_functions = [];

            foreach ($config['required_functions'] as $function) {
                if (strpos($content, $function) !== false) {
                    $found_functions[] = $function;
                } else {
                    $missing_functions[] = $function;
                }
            }

            // فحص عدم وجود الدوال المحظورة
            $forbidden_found = [];
            foreach ($config['forbidden_functions'] as $function) {
                if (strpos($content, $function) !== false) {
                    $forbidden_found[] = $function;
                }
            }

            if (empty($missing_functions) && empty($forbidden_found)) {
                showResult($config['description'], true, "جميع الدوال المطلوبة موجودة: " . implode(', ', $found_functions));
            } else {
                $issues = [];
                if (!empty($missing_functions)) {
                    $issues[] = "دوال مفقودة: " . implode(', ', $missing_functions);
                }
                if (!empty($forbidden_found)) {
                    $issues[] = "دوال محظورة موجودة: " . implode(', ', $forbidden_found);
                }

                showResult($config['description'], false, implode(' | ', $issues));
            }
        }

        // اختبار قاعدة البيانات
        showInfo("<strong>اختبار قاعدة البيانات:</strong>");
        
        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
            
            // فحص بيانات الدول
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM countries WHERE is_active = 1");
            $countries_count = $stmt->fetch()['count'];
            showResult("بيانات الدول", $countries_count > 0, "يوجد {$countries_count} دولة نشطة للبحث");
            
            if ($countries_count > 0) {
                // عرض عينة من الدول
                $stmt = $pdo->query("SELECT name_ar, currency_code FROM countries WHERE is_active = 1 ORDER BY name_ar LIMIT 5");
                $sample_countries = $stmt->fetchAll();
                
                $countries_list = [];
                foreach ($sample_countries as $country) {
                    $countries_list[] = $country['name_ar'] . ' (' . $country['currency_code'] . ')';
                }
                showResult("عينة من الدول المتاحة", true, implode(' | ', $countries_list));
            }
            
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 95) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>مثالي!</strong> البحث التفاعلي يعمل بشكل مثالي بدون أخطاء";
            echo "</div>";
        } elseif ($success_rate >= 80) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> معظم الميزات تعمل";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج مراجعة!</strong> هناك مشاكل تحتاج إصلاح";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <!-- إرشادات الاختبار -->
        <div class="card mt-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>
                    إرشادات الاختبار
                </h5>
            </div>
            <div class="card-body">
                <h6>للتأكد من أن البحث التفاعلي يعمل بشكل صحيح:</h6>
                <ol>
                    <li><strong>افتح أدوات المطور</strong> (F12) في المتصفح</li>
                    <li><strong>انتقل إلى تبويب Console</strong> للتحقق من عدم وجود أخطاء</li>
                    <li><strong>جرب البحث</strong> بكتابة أجزاء من أسماء الدول أو العملات</li>
                    <li><strong>اختبر الاختيار</strong> من النتائج المعروضة</li>
                    <li><strong>تأكد من الحسابات</strong> بعد اختيار الدولة</li>
                    <li><strong>جرب المسح والتعديل</strong> للتأكد من المرونة</li>
                </ol>
                
                <h6 class="mt-3">أمثلة للبحث:</h6>
                <div class="row">
                    <div class="col-md-4">
                        <strong>بحث بالاسم:</strong><br>
                        <code>مصر</code><br>
                        <code>سعودية</code><br>
                        <code>إمارات</code>
                    </div>
                    <div class="col-md-4">
                        <strong>بحث بالعملة:</strong><br>
                        <code>USD</code><br>
                        <code>EUR</code><br>
                        <code>SAR</code>
                    </div>
                    <div class="col-md-4">
                        <strong>بحث مختلط:</strong><br>
                        <code>ريال</code><br>
                        <code>دولار</code><br>
                        <code>دينار</code>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الاختبار النهائية -->
        <div class="text-center mt-4">
            <h5 class="mb-3">🚀 جاهز للاختبار!</h5>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-primary btn-lg" target="_blank">
                <i class="fas fa-plus me-2"></i>
                اختبار صفحة الإضافة
            </a>
            <a href="../dashboard/edit_daily_transaction.php?id=1" class="btn btn-warning btn-lg ms-2" target="_blank">
                <i class="fas fa-edit me-2"></i>
                اختبار صفحة التعديل
            </a>
            <a href="../dashboard/daily_transactions.php" class="btn btn-success btn-lg ms-2" target="_blank">
                <i class="fas fa-search me-2"></i>
                اختبار البحث والفلترة
            </a>
        </div>

        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-star me-2"></i>الميزات الجديدة:</h6>
            <div class="row">
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>بحث فوري أثناء الكتابة</li>
                        <li>دعم البحث بالعربية والإنجليزية</li>
                        <li>عرض جميل للنتائج مع الأيقونات</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>سهولة الاختيار والتعديل</li>
                        <li>تحديث تلقائي للحسابات</li>
                        <li>واجهة متجاوبة وعصرية</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

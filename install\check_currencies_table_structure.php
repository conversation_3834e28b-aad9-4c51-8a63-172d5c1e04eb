<?php
/**
 * Check and fix currencies table structure
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>فحص وإصلاح هيكل جدول العملات</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الهيكل الحالي لجدول currencies</h3>\n";
    
    // Check if table exists
    $result = $conn->query("SHOW TABLES LIKE 'currencies'");
    if ($result->num_rows == 0) {
        echo "<p style='color: red;'>✗ جدول currencies غير موجود</p>\n";
        
        echo "<h3>2. إنشاء جدول currencies</h3>\n";
        $createTableSql = "
        CREATE TABLE currencies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(3) NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            country VARCHAR(100) DEFAULT NULL,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($createTableSql)) {
            echo "<p style='color: green;'>✓ تم إنشاء جدول currencies بنجاح</p>\n";
        } else {
            throw new Exception("فشل في إنشاء جدول currencies: " . $conn->error);
        }
    } else {
        echo "<p style='color: green;'>✓ جدول currencies موجود</p>\n";
        
        // Check current structure
        $result = $conn->query("DESCRIBE currencies");
        if ($result) {
            echo "<p><strong>الهيكل الحالي:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>\n";
            echo "</tr>\n";
            
            $columns = [];
            while ($row = $result->fetch_assoc()) {
                $columns[] = $row['Field'];
                echo "<tr>\n";
                echo "<td>{$row['Field']}</td>\n";
                echo "<td>{$row['Type']}</td>\n";
                echo "<td>{$row['Null']}</td>\n";
                echo "<td>{$row['Key']}</td>\n";
                echo "<td>{$row['Default']}</td>\n";
                echo "<td>{$row['Extra']}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
            
            echo "<h3>2. فحص الأعمدة المطلوبة</h3>\n";
            
            $requiredColumns = [
                'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
                'code' => 'VARCHAR(3) NOT NULL UNIQUE',
                'name' => 'VARCHAR(100) NOT NULL',
                'symbol' => 'VARCHAR(10) NOT NULL',
                'country' => 'VARCHAR(100) DEFAULT NULL',
                'is_active' => 'TINYINT(1) DEFAULT 1',
                'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            ];
            
            $missingColumns = [];
            foreach ($requiredColumns as $columnName => $columnDef) {
                if (!in_array($columnName, $columns)) {
                    $missingColumns[] = $columnName;
                    echo "<p style='color: red;'>✗ العمود '$columnName' مفقود</p>\n";
                } else {
                    echo "<p style='color: green;'>✓ العمود '$columnName' موجود</p>\n";
                }
            }
            
            if (!empty($missingColumns)) {
                echo "<h3>3. إضافة الأعمدة المفقودة</h3>\n";
                
                foreach ($missingColumns as $columnName) {
                    $columnDef = $requiredColumns[$columnName];
                    
                    // Special handling for different column types
                    switch ($columnName) {
                        case 'country':
                            $alterSql = "ALTER TABLE currencies ADD COLUMN country VARCHAR(100) DEFAULT NULL";
                            break;
                        case 'is_active':
                            $alterSql = "ALTER TABLE currencies ADD COLUMN is_active TINYINT(1) DEFAULT 1";
                            break;
                        case 'created_at':
                            $alterSql = "ALTER TABLE currencies ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
                            break;
                        case 'updated_at':
                            $alterSql = "ALTER TABLE currencies ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
                            break;
                        default:
                            continue 2; // Skip unknown columns
                    }
                    
                    if ($conn->query($alterSql)) {
                        echo "<p style='color: green;'>✓ تم إضافة العمود '$columnName'</p>\n";
                    } else {
                        echo "<p style='color: red;'>✗ فشل في إضافة العمود '$columnName': " . $conn->error . "</p>\n";
                    }
                }
            }
        }
    }
    
    echo "<h3>4. فحص جدول cash_boxes للتأكد من عمود currency_code</h3>\n";
    
    $result = $conn->query("DESCRIBE cash_boxes");
    if ($result) {
        $cashBoxColumns = [];
        while ($row = $result->fetch_assoc()) {
            $cashBoxColumns[] = $row['Field'];
        }
        
        if (!in_array('currency_code', $cashBoxColumns)) {
            echo "<p style='color: red;'>✗ العمود 'currency_code' مفقود في جدول cash_boxes</p>\n";
            
            // Add currency_code column to cash_boxes
            $alterSql = "ALTER TABLE cash_boxes ADD COLUMN currency_code VARCHAR(3) DEFAULT 'USD'";
            if ($conn->query($alterSql)) {
                echo "<p style='color: green;'>✓ تم إضافة العمود 'currency_code' إلى جدول cash_boxes</p>\n";
                
                // Update existing cash boxes with default currency
                $updateSql = "UPDATE cash_boxes SET currency_code = 'USD' WHERE currency_code IS NULL OR currency_code = ''";
                if ($conn->query($updateSql)) {
                    echo "<p style='color: green;'>✓ تم تحديث الصناديق الموجودة بالعملة الافتراضية</p>\n";
                }
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة العمود 'currency_code': " . $conn->error . "</p>\n";
            }
        } else {
            echo "<p style='color: green;'>✓ العمود 'currency_code' موجود في جدول cash_boxes</p>\n";
        }
    }
    
    echo "<h3>5. إدخال العملات الأساسية</h3>\n";
    
    // Insert basic currencies
    $basicCurrencies = [
        ['USD', 'دولار أمريكي', '$', 'الولايات المتحدة الأمريكية'],
        ['EUR', 'يورو', '€', 'الاتحاد الأوروبي'],
        ['SAR', 'سعودي', 'ر.س', 'المملكة العربية السعودية'],
        ['AED', 'درهم إماراتي', 'د.إ', 'الإمارات العربية المتحدة']
    ];
    
    $insertSql = "INSERT INTO currencies (code, name, symbol, country, is_active, created_at, updated_at) 
                  VALUES (?, ?, ?, ?, 1, NOW(), NOW()) 
                  ON DUPLICATE KEY UPDATE 
                  name = VALUES(name), 
                  symbol = VALUES(symbol), 
                  country = VALUES(country),
                  updated_at = NOW()";
    
    $stmt = $conn->prepare($insertSql);
    if ($stmt) {
        foreach ($basicCurrencies as $currency) {
            list($code, $name, $symbol, $country) = $currency;
            $stmt->bind_param('ssss', $code, $name, $symbol, $country);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ تم إدخال/تحديث العملة: $code - $name</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إدخال العملة: $code</p>\n";
            }
        }
        $stmt->close();
    }
    
    echo "<h3>6. التحقق النهائي</h3>\n";
    
    // Final verification
    $result = $conn->query("SELECT COUNT(*) as count FROM currencies");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<p><strong>عدد العملات في الجدول:</strong> $count</p>\n";
    }
    
    // Check table structure again
    $result = $conn->query("DESCRIBE currencies");
    if ($result) {
        echo "<p><strong>الهيكل النهائي لجدول currencies:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th>\n";
        echo "</tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>\n";
            echo "<td>{$row['Field']}</td>\n";
            echo "<td>{$row['Type']}</td>\n";
            echo "<td>{$row['Null']}</td>\n";
            echo "<td>{$row['Key']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح هيكل جدول العملات!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='insert_all_currencies.php' target='_blank'>إدخال جميع العملات</a></li>\n";
    echo "<li><a href='view_all_currencies.php' target='_blank'>عرض جميع العملات</a></li>\n";
    echo "<li><a href='manage_currencies.php' target='_blank'>إدارة العملات</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

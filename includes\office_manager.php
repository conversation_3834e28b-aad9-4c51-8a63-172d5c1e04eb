<?php
/**
 * OfficeManager – handles offices and their exchange rates
 */
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/functions.php';

class OfficeManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /* ---------------- Offices ---------------- */
    
    /**
     * Get all offices with optional filters
     */
    public function getAllOffices(array $filters = []): array
    {
        $sql = "SELECT * FROM offices WHERE 1=1";
        $params = [];
        $types = "";

        if (isset($filters['is_active'])) {
            $sql .= " AND is_active = ?";
            $params[] = $filters['is_active'];
            $types .= "i";
        }

        if (isset($filters['search'])) {
            $sql .= " AND (office_name LIKE ? OR manager_name LIKE ? OR phone_number LIKE ?)";
            $search = "%" . $filters['search'] . "%";
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
            $types .= "sss";
        }

        $sql .= " ORDER BY office_name";

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get office by ID
     */
    public function getOfficeById(int $officeId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM offices WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $officeId);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    /**
     * Check if office name exists
     */
    public function officeNameExists(string $officeName, ?int $excludeId = null): bool
    {
        $sql = 'SELECT COUNT(*) FROM offices WHERE office_name = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        
        if ($excludeId !== null) {
            $stmt->bind_param('si', $officeName, $excludeId);
        } else {
            $stmt->bind_param('s', $officeName);
        }
        
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    /**
     * Add new office
     */
    public function addOffice(array $data): int|false
    {
        $sql = 'INSERT INTO offices (office_name, phone_number, address, manager_name, is_active, created_at, updated_at) VALUES (?,?,?,?,?, NOW(), NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        
        $active = (int) ($data['is_active'] ?? 1);
        $stmt->bind_param('ssssi',
            $data['office_name'],
            $data['phone_number'],
            $data['address'],
            $data['manager_name'],
            $active
        );
        
        if ($stmt->execute()) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        $stmt->close();
        return false;
    }

    /**
     * Update office
     */
    public function updateOffice(int $officeId, array $data): bool
    {
        $sql = 'UPDATE offices SET office_name=?, phone_number=?, address=?, manager_name=?, is_active=?, updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        
        $active = (int) ($data['is_active'] ?? 1);
        $stmt->bind_param('ssssii',
            $data['office_name'],
            $data['phone_number'],
            $data['address'],
            $data['manager_name'],
            $active,
            $officeId
        );
        
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Delete office
     */
    public function deleteOffice(int $officeId): bool
    {
        $stmt = $this->db->prepare('DELETE FROM offices WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $officeId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /* ---------------- Office Exchange Rates ---------------- */
    
    /**
     * Get all exchange rates for an office
     */
    public function getOfficeExchangeRates(int $officeId): array
    {
        $sql = "SELECT * FROM office_exchange_rates WHERE office_id = ? ORDER BY operation_name";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->bind_param('i', $officeId);
        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get exchange rate by ID
     */
    public function getExchangeRateById(int $rateId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM office_exchange_rates WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $rateId);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    /**
     * Check if operation name exists for office
     */
    public function operationExistsForOffice(int $officeId, string $operationName, ?int $excludeId = null): bool
    {
        $sql = 'SELECT COUNT(*) FROM office_exchange_rates WHERE office_id = ? AND operation_name = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        
        if ($excludeId !== null) {
            $stmt->bind_param('isi', $officeId, $operationName, $excludeId);
        } else {
            $stmt->bind_param('is', $officeId, $operationName);
        }
        
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    /**
     * Add new exchange rate
     */
    public function addExchangeRate(array $data): int|false
    {
        $sql = 'INSERT INTO office_exchange_rates (office_id, operation_name, exchange_rate_percentage, is_active, created_at, updated_at) VALUES (?,?,?,?, NOW(), NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        
        $active = (int) ($data['is_active'] ?? 1);
        $stmt->bind_param('isdi',
            $data['office_id'],
            $data['operation_name'],
            $data['exchange_rate_percentage'],
            $active
        );
        
        if ($stmt->execute()) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        $stmt->close();
        return false;
    }

    /**
     * Update exchange rate
     */
    public function updateExchangeRate(int $rateId, array $data): bool
    {
        $sql = 'UPDATE office_exchange_rates SET office_id=?, operation_name=?, exchange_rate_percentage=?, is_active=?, updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        
        $active = (int) ($data['is_active'] ?? 1);
        $stmt->bind_param('isdii',
            $data['office_id'],
            $data['operation_name'],
            $data['exchange_rate_percentage'],
            $active,
            $rateId
        );
        
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Delete exchange rate
     */
    public function deleteExchangeRate(int $rateId): bool
    {
        $stmt = $this->db->prepare('DELETE FROM office_exchange_rates WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $rateId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Calculate exchange rate amount
     */
    public function calculateExchangeRateAmount(int $officeId, string $operationName, float $amount): float
    {
        $sql = 'SELECT exchange_rate_percentage FROM office_exchange_rates WHERE office_id = ? AND operation_name = ? AND is_active = 1 LIMIT 1';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 0.0;
        
        $stmt->bind_param('is', $officeId, $operationName);
        $stmt->execute();
        $stmt->bind_result($percentage);
        $stmt->fetch();
        $stmt->close();
        
        if ($percentage) {
            return $amount * $percentage;
        }
        
        return 0.0;
    }

    /**
     * Get all active offices for dropdown
     */
    public function getActiveOfficesForDropdown(): array
    {
        $sql = "SELECT id, office_name FROM offices WHERE is_active = 1 ORDER BY office_name";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /* ---------------- Office Operations ---------------- */
    
    /**
     * Get all operations for an office
     */
    public function getOfficeOperations(int $officeId): array
    {
        $sql = "SELECT * FROM office_operations WHERE office_id = ? ORDER BY created_at DESC";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->bind_param('i', $officeId);
        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get operation by ID
     */
    public function getOperationById(int $operationId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM office_operations WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $operationId);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    /**
     * Add new operation
     */
    public function addOperation(array $data): int|false
    {
        $sql = 'INSERT INTO office_operations (office_id, operation_name, tracking_number, base_amount, amount, operation_type, is_credit, created_at, updated_at) VALUES (?,?,?,?,?,?,?, NOW(), NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $isCredit = (int) ($data['is_credit'] ?? 0);
        $operationType = $data['operation_type'] ?? 'multiply';
        $stmt->bind_param('issddsi',
            $data['office_id'],
            $data['operation_name'],
            $data['tracking_number'],
            $data['base_amount'],
            $data['amount'],
            $operationType,
            $isCredit
        );

        if ($stmt->execute()) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        $stmt->close();
        return false;
    }

    /**
     * Update operation
     */
    public function updateOperation(int $operationId, array $data): bool
    {
        $sql = 'UPDATE office_operations SET operation_name=?, tracking_number=?, base_amount=?, amount=?, operation_type=?, is_credit=?, updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $isCredit = (int) ($data['is_credit'] ?? 0);
        $operationType = $data['operation_type'] ?? 'multiply';
        $stmt->bind_param('ssddsii',
            $data['operation_name'],
            $data['tracking_number'],
            $data['base_amount'],
            $data['amount'],
            $operationType,
            $isCredit,
            $operationId
        );

        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Delete operation
     */
    public function deleteOperation(int $operationId): bool
    {
        $stmt = $this->db->prepare('DELETE FROM office_operations WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $operationId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Calculate office balance
     * Returns an array with credit_total, debit_total, and balance
     */
    public function calculateOfficeBalance(int $officeId): array
    {
        // First get all operations for this office
        $operations = $this->getOfficeOperations($officeId);
        
        $creditTotal = 0;
        $debitTotal = 0;
        
        foreach ($operations as $operation) {
            // Use the final amount directly
            $finalAmount = $operation['amount'];
            
            if ((string)$operation['is_credit'] === '1') {
                $creditTotal += $finalAmount;
            } else {
                $debitTotal += $finalAmount;
            }
        }
        
        $balance = $creditTotal - $debitTotal;
        
        return [
            'credit_total' => $creditTotal,
            'debit_total' => $debitTotal,
            'balance' => $balance,
            'status' => $balance >= 0 ? 'profit' : 'loss'
        ];
    }
} 
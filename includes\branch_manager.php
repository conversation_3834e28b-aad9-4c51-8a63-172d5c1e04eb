<?php
require_once __DIR__ . '/database.php';

class BranchManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Retrieve all branches (id and name).
     *
     * @return array<int, array<string, mixed>>
     */
    public function getAllBranches(): array
    {
        $branches = [];
        $stmt = $this->db->prepare('SELECT id, name FROM branches ORDER BY name');
        if ($stmt) {
            $stmt->execute();
            $result = $stmt->get_result();
            $branches = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
            $stmt->close();
        }
        return $branches;
    }

    /**
     * Get the default branch (first active branch)
     *
     * @return int|null Default branch ID or null if no branches exist
     */
    public function getDefaultBranchId(): ?int
    {
        $stmt = $this->db->prepare('SELECT id FROM branches WHERE status = "active" ORDER BY id ASC LIMIT 1');
        if (!$stmt) return null;

        $stmt->execute();
        $result = $stmt->get_result();
        $branch = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return $branch ? (int)$branch['id'] : null;
    }

    /**
     * Get branch by ID
     *
     * @param int $branchId Branch ID
     * @return array|null Branch data or null if not found
     */
    public function getBranchById(int $branchId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM branches WHERE id = ? LIMIT 1');
        if (!$stmt) return null;

        $stmt->bind_param('i', $branchId);
        $stmt->execute();
        $result = $stmt->get_result();
        $branch = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return $branch;
    }
}
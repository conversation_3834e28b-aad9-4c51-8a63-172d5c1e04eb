-- تحديث سريع لقاعدة البيانات - نظام ActivityHelper
-- Quick Database Update for ActivityHelper System
-- يمكن تشغيل هذا الملف مباشرة في phpMyAdmin أو MySQL CLI

-- ===================================
-- إنشاء جدول تسجيل العمليات
-- ===================================

CREATE TABLE IF NOT EXISTS system_activity_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NULL,
    username VARCHAR(100) NULL,
    user_full_name VARCHAR(255) NULL,
    user_role VARCHAR(100) NULL,
    action_type ENUM('CREATE', 'UPDATE', 'DELETE', 'VIEW', 'LOGIN', 'LOGOUT', 'EXPORT', 'IMPORT', 'APPROVE', 'REJECT', 'CANCEL', 'RESTORE', 'TRANSFER', 'EXCHANGE', 'PAYMENT', 'WITHDRAWAL', 'DEPOSIT', 'SYSTEM', 'ERROR', 'WARNING', 'INFO') NOT NULL,
    module VARCHAR(50) NOT NULL,
    operation VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    target_table VARCHAR(50) NULL,
    target_id INT UNSIGNED NULL,
    target_identifier VARCHAR(255) NULL,
    old_values JSON NULL,
    new_values JSON NULL,
    changed_fields JSON NULL,
    additional_data JSON NULL,
    session_id VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    request_method VARCHAR(10) NULL,
    request_url TEXT NULL,
    status ENUM('SUCCESS', 'FAILED', 'PARTIAL', 'PENDING') DEFAULT 'SUCCESS',
    error_message TEXT NULL,
    execution_time DECIMAL(8,3) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_module (module),
    INDEX idx_operation (operation),
    INDEX idx_target_table_id (target_table, target_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    INDEX idx_ip_address (ip_address),
    INDEX idx_user_action_date (user_id, action_type, created_at),
    INDEX idx_module_operation_date (module, operation, created_at),
    CONSTRAINT fk_system_activity_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- إضافة الفهارس المتقدمة
-- ===================================

CREATE INDEX IF NOT EXISTS idx_comprehensive_search ON system_activity_logs (module, action_type, created_at, user_id);
CREATE FULLTEXT INDEX IF NOT EXISTS idx_description_search ON system_activity_logs (description);

-- ===================================
-- إضافة الصلاحيات
-- ===================================

INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
('system_activity_logs.view', 'عرض سجل العمليات الشامل للنظام', 'system', NOW()),
('system_activity_logs.export', 'تصدير سجل العمليات', 'system', NOW()),
('system_activity_logs.details', 'عرض تفاصيل سجل العمليات', 'system', NOW()),
('system_activity_logs.admin_only', 'الوصول الكامل لسجل العمليات (أدمن فقط)', 'system', NOW()),
('system.maintenance', 'صيانة النظام', 'system', NOW()),
('system.backup', 'نسخ احتياطي للنظام', 'system', NOW()),
('system.settings', 'إعدادات النظام', 'system', NOW()),
('system.logs', 'عرض سجلات النظام', 'system', NOW());

-- ===================================
-- ربط الصلاحيات بدور الأدمن
-- ===================================

INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 1, id FROM permissions WHERE name IN (
    'system_activity_logs.view',
    'system_activity_logs.export', 
    'system_activity_logs.details',
    'system_activity_logs.admin_only',
    'system.maintenance',
    'system.backup',
    'system.settings', 
    'system.logs'
);

-- ===================================
-- إدراج سجل تجريبي
-- ===================================

INSERT INTO system_activity_logs (
    user_id, username, user_full_name, user_role,
    action_type, module, operation, description,
    target_table, additional_data, ip_address, status
) VALUES (
    1, 'admin', 'مدير النظام', 'مدير',
    'SYSTEM', 'system', 'database_update', 'تحديث قاعدة البيانات لدعم نظام ActivityHelper',
    'system_activity_logs', JSON_OBJECT('update_version', '1.0', 'update_date', NOW()),
    '127.0.0.1', 'SUCCESS'
);

-- ===================================
-- التحقق من التحديث
-- ===================================

SELECT 'Database update completed successfully!' as status;
SELECT COUNT(*) as total_activity_logs FROM system_activity_logs;
SELECT COUNT(*) as total_system_permissions FROM permissions WHERE module = 'system';

<?php
/**
 * ReportsManager – aggregates data for reporting purposes.
 *
 * Each method returns an array of associative rows already aggregated. The
 * caller (controller / UI page) is responsible for rendering.
 */
require_once __DIR__ . '/database.php';

class ReportsManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /* ------------------------------------------------------------------ */
    /* EXCHANGE – Profit report                                            */
    /* ------------------------------------------------------------------ */

    /**
     * Get aggregated exchange profit data.
     *
     * Supported $filters keys:
     *  - from_date (Y-m-d), to_date (Y-m-d)
     *  - branch_id (int)
     *  - from_currency_id (int)
     *  - to_currency_id   (int)
     *
     * Result fields:
     *  date, pair, branch_id, branch_name, total_tx, volume_from, volume_to, total_commission, total_profit
     */
    public function getExchangeProfitReportData(array $filters = []): array
    {
        $sql = "SELECT DATE(e.created_at) AS report_date,
                       CONCAT(fc.code,'/',tc.code) AS pair,
                       b.id   AS branch_id,
                       b.name AS branch_name,
                       COUNT(e.id)                        AS total_tx,
                       SUM(e.amount_from)                 AS volume_from,
                       SUM(e.amount_to)                   AS volume_to,
                       SUM(e.commission)                  AS total_commission,
                       SUM(e.profit)                      AS total_profit
                FROM exchanges e
                JOIN currencies fc ON fc.id = e.from_currency_id
                JOIN currencies tc ON tc.id = e.to_currency_id
                LEFT JOIN branches b ON b.id = e.branch_id
                WHERE 1 = 1";
        $types=''; $params=[];
        if(!empty($filters['from_date'])){ $sql.=' AND e.created_at >= ?'; $types.='s'; $params[]=$filters['from_date'].' 00:00:00'; }
        if(!empty($filters['to_date']))  { $sql.=' AND e.created_at <= ?'; $types.='s'; $params[]=$filters['to_date'].' 23:59:59'; }
        if(!empty($filters['branch_id'])){ $sql.=' AND e.branch_id = ?'; $types.='i'; $params[]=$filters['branch_id']; }
        if(!empty($filters['from_currency_id'])){ $sql.=' AND e.from_currency_id = ?'; $types.='i'; $params[]=$filters['from_currency_id']; }
        if(!empty($filters['to_currency_id']))  { $sql.=' AND e.to_currency_id = ?'; $types.='i'; $params[]=$filters['to_currency_id']; }

        $sql .= " GROUP BY report_date, pair, branch_id ORDER BY report_date DESC, pair";

        $stmt = $this->db->prepare($sql);
        if(!$stmt) return [];
        if($params){ $stmt->bind_param($types,...$params); }
        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC):[];
        $stmt->close();
        return $rows;
    }

    /* ------------------------------------------------------------------ */
    /* TRANSFERS – Summary report                                          */
    /* ------------------------------------------------------------------ */

    /**
     * Get aggregated transfer summary.
     *
     * Supported $filters keys:
     *  - from_date, to_date (Y-m-d)
     *  - branch_id (int)
     *  - transfer_type ('صادرة'|'واردة')
     *  - status (enum)
     *  - currency_id (sending currency)
     */
    public function getTransferSummaryReportData(array $filters = []): array
    {
        $sql = "SELECT t.status,
                       t.transfer_type,
                       b.id   AS branch_id,
                       b.name AS branch_name,
                       COUNT(t.id)          AS total_tx,
                       SUM(t.sending_amount)   AS total_sent,
                       SUM(t.receiving_amount) AS total_received,
                       SUM(t.transfer_fee)     AS total_fees,
                       SUM(t.profit)           AS total_profit
                FROM transfers t
                LEFT JOIN branches b ON b.id = t.branch_id
                WHERE 1=1";
        $types=''; $params=[];
        if(!empty($filters['from_date'])){ $sql.=' AND t.created_at >= ?'; $types.='s'; $params[]=$filters['from_date'].' 00:00:00'; }
        if(!empty($filters['to_date']))  { $sql.=' AND t.created_at <= ?'; $types.='s'; $params[]=$filters['to_date'].' 23:59:59'; }
        if(!empty($filters['branch_id'])){ $sql.=' AND t.branch_id = ?'; $types.='i'; $params[]=$filters['branch_id']; }
        if(!empty($filters['transfer_type'])){ $sql.=' AND t.transfer_type = ?'; $types.='s'; $params[]=$filters['transfer_type']; }
        if(!empty($filters['status']))   { $sql.=' AND t.status = ?'; $types.='s'; $params[]=$filters['status']; }
        if(!empty($filters['currency_id'])){ $sql.=' AND t.sending_currency_id = ?'; $types.='i'; $params[]=$filters['currency_id']; }

        $sql .= " GROUP BY t.status, t.transfer_type, branch_id ORDER BY branch_name, t.status";

        $stmt=$this->db->prepare($sql);
        if(!$stmt) return [];
        if($params){ $stmt->bind_param($types,...$params); }
        $stmt->execute();
        $res=$stmt->get_result();
        $rows=$res? $res->fetch_all(MYSQLI_ASSOC):[];
        $stmt->close();
        return $rows;
    }
} 
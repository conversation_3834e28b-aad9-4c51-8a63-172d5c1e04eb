-- تحديث قاعدة البيانات لدعم نظام ActivityHelper
-- Database Update for ActivityHelper Integration
-- تاريخ الإنشاء: 2024-12-19

-- ===================================
-- 1. إنشاء جدول تسجيل العمليات الشامل
-- ===================================

CREATE TABLE IF NOT EXISTS system_activity_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- معلومات المستخدم
    user_id INT UNSIGNED NULL,
    username VARCHAR(100) NULL,
    user_full_name VARCHAR(255) NULL,
    user_role VARCHAR(100) NULL,
    
    -- معلومات العملية
    action_type ENUM(
        'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'LOGIN', 'LOGOUT', 
        'EXPORT', 'IMPORT', 'APPROVE', 'REJECT', 'CANCEL', 'RESTORE',
        'TRANSFER', 'EXCHANGE', 'PAYMENT', 'WITHDRAWAL', 'DEPOSIT',
        'SYSTEM', 'ERROR', 'WARNING', 'INFO'
    ) NOT NULL,
    
    module VARCHAR(50) NOT NULL COMMENT 'الوحدة: users, customers, transfers, exchange, etc.',
    operation VARCHAR(100) NOT NULL COMMENT 'العملية المحددة: add_user, edit_customer, etc.',
    description TEXT NOT NULL COMMENT 'وصف مفصل للعملية',
    
    -- معلومات السجل المتأثر
    target_table VARCHAR(50) NULL COMMENT 'الجدول المتأثر',
    target_id INT UNSIGNED NULL COMMENT 'معرف السجل المتأثر',
    target_identifier VARCHAR(255) NULL COMMENT 'معرف إضافي للسجل (اسم، رقم، إلخ)',
    
    -- البيانات القديمة والجديدة
    old_values JSON NULL COMMENT 'القيم القديمة قبل التغيير',
    new_values JSON NULL COMMENT 'القيم الجديدة بعد التغيير',
    changed_fields JSON NULL COMMENT 'الحقول التي تم تغييرها فقط',
    
    -- معلومات إضافية
    additional_data JSON NULL COMMENT 'بيانات إضافية حسب نوع العملية',
    
    -- معلومات الجلسة والشبكة
    session_id VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    request_method VARCHAR(10) NULL COMMENT 'GET, POST, PUT, DELETE',
    request_url TEXT NULL,
    
    -- معلومات النتيجة
    status ENUM('SUCCESS', 'FAILED', 'PARTIAL', 'PENDING') DEFAULT 'SUCCESS',
    error_message TEXT NULL,
    execution_time DECIMAL(8,3) NULL COMMENT 'وقت التنفيذ بالثواني',
    
    -- معلومات التوقيت
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- فهارس للبحث السريع
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_module (module),
    INDEX idx_operation (operation),
    INDEX idx_target_table_id (target_table, target_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    INDEX idx_ip_address (ip_address),
    INDEX idx_user_action_date (user_id, action_type, created_at),
    INDEX idx_module_operation_date (module, operation, created_at),
    
    -- قيد خارجي للمستخدم
    CONSTRAINT fk_system_activity_user 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE SET NULL 
        ON UPDATE CASCADE
        
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='سجل شامل لجميع العمليات في النظام';

-- إنشاء فهرس مركب للبحث المتقدم
CREATE INDEX IF NOT EXISTS idx_comprehensive_search 
ON system_activity_logs (module, action_type, created_at, user_id);

-- إنشاء فهرس للبحث النصي
CREATE FULLTEXT INDEX IF NOT EXISTS idx_description_search 
ON system_activity_logs (description);

-- ===================================
-- 2. إضافة الصلاحيات الجديدة
-- ===================================

-- إضافة صلاحيات سجل العمليات
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
    ('system_activity_logs.view', 'عرض سجل العمليات الشامل للنظام', 'system', NOW()),
    ('system_activity_logs.export', 'تصدير سجل العمليات', 'system', NOW()),
    ('system_activity_logs.details', 'عرض تفاصيل سجل العمليات', 'system', NOW()),
    ('system_activity_logs.admin_only', 'الوصول الكامل لسجل العمليات (أدمن فقط)', 'system', NOW());

-- إضافة صلاحيات عامة للنظام
INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES
    ('system.maintenance', 'صيانة النظام', 'system', NOW()),
    ('system.backup', 'نسخ احتياطي للنظام', 'system', NOW()),
    ('system.settings', 'إعدادات النظام', 'system', NOW()),
    ('system.logs', 'عرض سجلات النظام', 'system', NOW());

-- ===================================
-- 3. ربط الصلاحيات بدور الأدمن
-- ===================================

-- ربط صلاحيات سجل العمليات بدور الأدمن (role_id = 1)
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 1, id FROM permissions 
WHERE name IN (
    'system_activity_logs.view',
    'system_activity_logs.export', 
    'system_activity_logs.details',
    'system_activity_logs.admin_only'
);

-- ربط الصلاحيات العامة بدور الأدمن
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 1, id FROM permissions 
WHERE name IN (
    'system.maintenance',
    'system.backup',
    'system.settings', 
    'system.logs'
);

-- ===================================
-- 4. تحسينات الأداء
-- ===================================

-- تحسين جدول activity_logs القديم إذا كان موجوداً
ALTER TABLE activity_logs 
ADD INDEX IF NOT EXISTS idx_user_id_created (user_id, created_at),
ADD INDEX IF NOT EXISTS idx_action_created (action, created_at);

-- ===================================
-- 5. إعدادات قاعدة البيانات
-- ===================================

-- تحسين إعدادات MySQL لتحسين الأداء
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL query_cache_size = 67108864; -- 64MB
SET GLOBAL query_cache_type = 1;

-- ===================================
-- 6. التحقق من التحديث
-- ===================================

-- عرض معلومات الجدول الجديد
SELECT 
    'system_activity_logs table created successfully' as status,
    COUNT(*) as current_records
FROM system_activity_logs;

-- عرض الصلاحيات المضافة
SELECT 
    'Permissions added successfully' as status,
    COUNT(*) as total_permissions
FROM permissions 
WHERE module = 'system';

-- عرض الفهارس المنشأة
SHOW INDEX FROM system_activity_logs;

-- ===================================
-- 7. بيانات تجريبية (اختيارية)
-- ===================================

-- إدراج سجل تجريبي للتأكد من عمل النظام
INSERT INTO system_activity_logs (
    user_id, username, user_full_name, user_role,
    action_type, module, operation, description,
    target_table, target_id, target_identifier,
    additional_data, ip_address, status
) VALUES (
    1, 'admin', 'مدير النظام', 'مدير',
    'SYSTEM', 'system', 'database_update', 'تحديث قاعدة البيانات لدعم نظام ActivityHelper',
    'system_activity_logs', NULL, 'database_update',
    JSON_OBJECT('update_version', '1.0', 'update_date', NOW()),
    '127.0.0.1', 'SUCCESS'
);

-- ===================================
-- 8. تنظيف البيانات القديمة (اختياري)
-- ===================================

-- حذف السجلات الأقدم من 6 أشهر من الجدول القديم
-- DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- ===================================
-- انتهاء التحديث
-- ===================================

SELECT 'Database update completed successfully for ActivityHelper integration!' as final_status;

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/office_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('offices.view');

$officeMgr = new OfficeManager(new Database());

// Get office ID
$officeId = (int)($_GET['id'] ?? 0);
if (!$officeId) {
    set_flash('danger', 'معرف المكتب غير صحيح');
    redirect('offices.php');
}

// Get office details
$office = $officeMgr->getOfficeById($officeId);
if (!$office) {
    set_flash('danger', 'المكتب غير موجود');
    redirect('offices.php');
}

// Handle exchange rate delete
if (isset($_GET['action']) && $_GET['action'] === 'delete_rate' && isset($_GET['rate_id'])) {
    $auth->requirePermission('offices.rates.delete');
    if (verify_csrf_token($_GET['token'] ?? '')) {
        $rateId = (int)$_GET['rate_id'];
        $rate = $officeMgr->getExchangeRateById($rateId);
        if ($rate && $rate['office_id'] == $officeId && $officeMgr->deleteExchangeRate($rateId)) {
            log_activity($auth->getCurrentUser()['id'], 'office.rate.delete', ['rate_id' => $rateId]);
            set_flash('success', 'تم حذف سعر القص بنجاح');
        } else {
            set_flash('danger', 'فشل الحذف');
        }
    } else {
        set_flash('danger', 'CSRF غير صالح');
    }
    redirect("office_details.php?id=$officeId");
}

// Get exchange rates for this office
$exchangeRates = $officeMgr->getOfficeExchangeRates($officeId);

// Get operations for this office
$operations = $officeMgr->getOfficeOperations($officeId);

// Calculate office balance
$balance = $officeMgr->calculateOfficeBalance($officeId);

// Handle operation delete
if (isset($_GET['action']) && $_GET['action'] === 'delete_operation' && isset($_GET['operation_id'])) {
    $auth->requirePermission('offices.operations.delete');
    if (verify_csrf_token($_GET['token'] ?? '')) {
        $operationId = (int)$_GET['operation_id'];
        $operation = $officeMgr->getOperationById($operationId);
        if ($operation && $operation['office_id'] == $officeId && $officeMgr->deleteOperation($operationId)) {
            log_activity($auth->getCurrentUser()['id'], 'office.operation.delete', ['operation_id' => $operationId]);
            set_flash('success', 'تم حذف العملية بنجاح');
        } else {
            set_flash('danger', 'فشل الحذف');
        }
    } else {
        set_flash('danger', 'CSRF غير صالح');
    }
    redirect("office_details.php?id=$officeId");
}

$csrf = get_csrf_token();

$pageTitle = 'تفاصيل المكتب - ' . $office['office_name'];
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container p-4">
    <!-- Office Details -->
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-building"></i> تفاصيل المكتب
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary"><?php echo htmlspecialchars($office['office_name']); ?></h5>
                            <p class="text-muted mb-3">
                                <i class="fas fa-user me-2"></i>
                                <strong>مسؤول المكتب:</strong> <?php echo htmlspecialchars($office['manager_name']); ?>
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-phone me-2"></i>
                                <strong>رقم الهاتف:</strong> 
                                <a href="tel:<?php echo htmlspecialchars($office['phone_number']); ?>">
                                    <?php echo htmlspecialchars($office['phone_number']); ?>
                                </a>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="text-muted mb-3">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <strong>العنوان:</strong><br>
                                <?php echo nl2br(htmlspecialchars($office['address'])); ?>
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-calendar me-2"></i>
                                <strong>تاريخ الإنشاء:</strong> 
                                <?php echo date('Y/m/d', strtotime($office['created_at'])); ?>
                            </p>
                            <p class="text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>الحالة:</strong>
                                <?php if ($office['is_active']): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير نشط</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i> إجراءات سريعة
                    </h5>
                    <a href="exchange_rates.php?office_id=<?php echo $office['id']; ?>" 
   class="btn btn-warning btn-circle shadow-sm d-flex align-items-center justify-content-center" 
   style="width: 42px; height: 42px; border-radius: 50%; background: linear-gradient(135deg, #ffce00 60%, #ff9100 100%); color: #fff; font-size: 1.4rem;" 
   title="إدارة أسعار القص" data-bs-toggle="tooltip">
    <i class="fas fa-percentage"></i>
</a>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($auth->hasPermission('offices.edit')): ?>
                        <a href="edit_office.php?id=<?php echo $office['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit"></i> تعديل المكتب
                        </a>
                        <?php endif; ?>
                        
                        <?php if ($auth->hasPermission('offices.rates.create')): ?>
                        <a href="add_exchange_rate.php?office_id=<?php echo $office['id']; ?>" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة سعر قص جديد
                        </a>
                        <?php endif; ?>
                        
                        <a href="offices.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> رجوع للمكاتب
                        </a>
                    </div>
                </div>
            </div>
        </div>
</div>

<?php foreach(get_flash() as $t=>$m): ?>
    <div class="alert alert-<?php echo $t; ?> alert-dismissible fade show" role="alert">
        <?php echo $m; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endforeach; ?>

<!-- فلاتر البحث والتصدير للعمليات -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h6 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            فلاتر البحث والتصدير
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <input type="hidden" name="id" value="<?php echo $officeId; ?>">
            <div class="col-md-3">
                <label for="operation_name" class="form-label">اسم المعاملة</label>
                <input type="text" class="form-control" id="operation_name" name="operation_name" value="<?php echo htmlspecialchars($_GET['operation_name'] ?? ''); ?>">
            </div>
            <div class="col-md-2">
                <label for="amount" class="form-label">مبلغ المعاملة</label>
                <input type="number" step="0.01" class="form-control" id="amount" name="amount" value="<?php echo htmlspecialchars($_GET['amount'] ?? ''); ?>">
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($_GET['date_from'] ?? ''); ?>">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($_GET['date_to'] ?? ''); ?>">
            </div>
            <div class="col-md-3 d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i> بحث
                </button>
                <!-- زر التصدير تم حذفه هنا -->
            </div>
        </form>
    </div>
</div>
<?php
// تطبيق الفلاتر على العمليات
$filtered_operations = $operations;
if (!empty($_GET['operation_name'])) {
    $filtered_operations = array_filter($filtered_operations, function($op) {
        return stripos($op['operation_name'], $_GET['operation_name']) !== false;
    });
}
if (!empty($_GET['amount'])) {
    $filtered_operations = array_filter($filtered_operations, function($op) {
        return (float)$op['amount'] == (float)$_GET['amount'];
    });
}
if (!empty($_GET['date_from'])) {
    $filtered_operations = array_filter($filtered_operations, function($op) {
        return isset($op['created_at']) && $op['created_at'] >= $_GET['date_from'];
    });
}
if (!empty($_GET['date_to'])) {
    $filtered_operations = array_filter($filtered_operations, function($op) {
        return isset($op['created_at']) && $op['created_at'] <= $_GET['date_to'];
    });
}
// عند التصدير، استخدم $filtered_operations بدلاً من $operations
if (isset($_GET['export']) && $_GET['export'] == '1') {
    // هنا يمكنك إعادة استخدام منطق التصدير الحالي لكن مع $filtered_operations
    // ... كود التصدير الحالي ...
}
?>

<!-- Office Operations Section -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-exchange-alt"></i> عمليات المكتب
            <span class="badge bg-info ms-2"><?php echo count($operations); ?></span>
        </h5>
        <div>
            <?php if (!empty($operations)): ?>
                <div class="btn-group me-2">
                    <a href="export_operations.php?office_id=<?php echo $office['id']; ?>&format=pdf" class="btn btn-outline-danger btn-sm" target="_blank">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </a>
                    <a href="export_operations.php?office_id=<?php echo $office['id']; ?>&format=excel" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                </div>
            <?php endif; ?>
            <?php if ($auth->hasPermission('offices.operations.create')): ?>
                <a href="add_office_operation.php?office_id=<?php echo $office['id']; ?>" class="btn btn-success btn-sm">
                    <i class="fas fa-plus"></i> إضافة عملية جديدة
                </a>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($operations)): ?>
        <div class="text-center py-4">
            <i class="fas fa-exchange-alt fa-2x text-muted mb-2"></i>
            <div class="text-muted">لا توجد عمليات لهذا المكتب</div>
            <?php if ($auth->hasPermission('offices.operations.create')): ?>
            <a href="add_office_operation.php?office_id=<?php echo $office['id']; ?>" class="btn btn-success btn-sm mt-2">
                <i class="fas fa-plus me-1"></i>إضافة عملية جديدة
            </a>
            <?php endif; ?>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th style="width: 60px;">#</th>
                        <th style="width: 180px;">اسم العملية</th>
                        <th style="width: 180px;">رقم التتبع</th>
                        <th style="width: 120px;">نوع العملية</th>
                        <th style="width: 130px;">المبلغ الأساسي</th>
                        <th style="width: 100px;">لنا</th>
                        <th style="width: 100px;">لكم</th>
                        <th style="width: 150px;">تاريخ العملية</th>
                        <th style="width: 120px;">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($filtered_operations as $operation): 
                        // Use base_amount if available, otherwise calculate it
                        $baseAmount = isset($operation['base_amount']) ? $operation['base_amount'] : $operation['amount'];
                        $finalAmount = $operation['amount'];
                        
                        // If base_amount is not set (for old records), try to calculate it
                        if (!isset($operation['base_amount']) || $operation['base_amount'] == 0) {
                            foreach ($exchangeRates as $rate) {
                                if ($rate['operation_name'] === $operation['operation_name'] && $rate['exchange_rate_percentage'] > 0) {
                                    $baseAmount = ($finalAmount * 100) / (100 - $rate['exchange_rate_percentage']);
                                    break;
                                }
                            }
                        }
                    ?>
                    <tr>
                        <td><?php echo $operation['id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($operation['operation_name']); ?></strong>
                        </td>
                        <td>
                            <?php echo $operation['tracking_number'] ? htmlspecialchars($operation['tracking_number']) : '-'; ?>
                        </td>
                        <td>
                            <?php
                            $operationType = $operation['operation_type'] ?? 'multiply';
                            if ($operationType === 'multiply') {
                                echo '<span class="badge bg-info">ضرب</span>';
                            } else {
                                echo '<span class="badge bg-warning">قسمة</span>';
                            }
                            ?>
                        </td>
                        <td class="text-nowrap">
                            <?php echo number_format($baseAmount, 2); ?>
                        </td>
                        <td>
                            <?php if ((string)$operation['is_credit'] === '1'): ?>
                                <span class="badge bg-success">
                                    <?php echo number_format($finalAmount, 2); ?>
                                </span>
                            <?php else: ?>
                                <span>-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ((string)$operation['is_credit'] === '0'): ?>
                                <span class="badge bg-danger">
                                    <?php echo number_format($finalAmount, 2); ?>
                                </span>
                            <?php else: ?>
                                <span>-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('Y/m/d H:i', strtotime($operation['created_at'])); ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <?php if ($auth->hasPermission('offices.operations.edit')): ?>
                                <a href="edit_office_operation.php?id=<?php echo $operation['id']; ?>" 
                                   class="btn btn-outline-primary" title="تعديل العملية">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php endif; ?>
                                <?php if ($auth->hasPermission('offices.operations.delete')): ?>
                                <a href="office_details.php?id=<?php echo $office['id']; ?>&action=delete_operation&operation_id=<?php echo $operation['id']; ?>&token=<?php echo $csrf; ?>" 
                                   class="btn btn-outline-danger" 
                                   onclick="return confirm('هل أنت متأكد من حذف هذه العملية؟')"
                                   title="حذف العملية">
                                    <i class="fas fa-trash"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <!-- حذف التذييل الخاص بالمجموع هنا بالكامل -->
            </table>
        </div>
        <?php endif; ?>
    </div>
    <?php if (!empty($operations)): ?>
        <div class="card-footer bg-light border-top">
            <div class="row text-center">
                <div class="col-md-4 mb-2 mb-md-0">
                    <span class="fw-bold">مجموع الرصيد لنا:</span>
                    <span class="badge bg-success fs-6"><?php echo number_format($balance['credit_total'], 2); ?></span>
                </div>
                <div class="col-md-4 mb-2 mb-md-0">
                    <span class="fw-bold">مجموع الرصيد لكم:</span>
                    <span class="badge bg-danger fs-6"><?php echo number_format($balance['debit_total'], 2); ?></span>
                </div>
                <div class="col-md-4">
                    <span class="fw-bold">رصيد المكتب:</span>
                    <?php if ($balance['balance'] >= 0): ?>
                        <span class="badge bg-success fs-6"><?php echo number_format($balance['balance'], 2); ?> لنا</span>
                    <?php else: ?>
                        <span class="badge bg-danger fs-6"><?php echo number_format(abs($balance['balance']), 2); ?> لكم</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Exchange Rates Section -->
<!-- تم حذف قسم أسعار القص من هنا بناءً على طلب المستخدم -->

</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
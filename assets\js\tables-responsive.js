/*
 * تحسينات الجداول المتجاوبة JavaScript
 * Responsive Tables JavaScript Enhancements
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Responsive Tables JavaScript loaded');

    // متغيرات النظام
    let currentBreakpoint = getCurrentBreakpoint();
    let mobileCardsCreated = false;

    // الحصول على نقطة التوقف الحالية
    function getCurrentBreakpoint() {
        const width = window.innerWidth;
        if (width >= 1400) return 'xxl';
        if (width >= 1200) return 'xl';
        if (width >= 992) return 'lg';
        if (width >= 768) return 'md';
        if (width >= 576) return 'sm';
        return 'xs';
    }

    // تحويل الجداول للعرض المتجاوب
    function makeTablesResponsive() {
        const tables = document.querySelectorAll('table:not(.table-responsive table)');
        
        tables.forEach(table => {
            if (!table.closest('.table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
            
            // إضافة فئات الاستجابة
            if (!table.classList.contains('table-mobile-stack')) {
                table.classList.add('table-mobile-stack');
            }
        });
    }

    // إنشاء عرض البطاقات للجداول على الأجهزة المحمولة
    function createMobileTableCards() {
        if (currentBreakpoint !== 'xs') {
            removeMobileTableCards();
            return;
        }

        const tables = document.querySelectorAll('.table-mobile-stack');
        
        tables.forEach(table => {
            createMobileCardsForTable(table);
        });
        
        mobileCardsCreated = true;
    }

    // إنشاء بطاقات للجدول
    function createMobileCardsForTable(table) {
        const existingCards = table.parentNode.querySelector('.table-mobile-cards');
        if (existingCards) return;

        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');
        
        if (rows.length === 0) return;

        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'table-mobile-cards d-mobile-block';
        
        rows.forEach((row, rowIndex) => {
            const cells = row.querySelectorAll('td');
            if (cells.length === 0) return;

            const card = document.createElement('div');
            card.className = 'mobile-card';
            card.setAttribute('data-row-index', rowIndex);
            
            // عنوان البطاقة (أول خلية عادة)
            if (cells.length > 0) {
                const header = document.createElement('div');
                header.className = 'mobile-card-header';
                header.innerHTML = cells[0].innerHTML;
                card.appendChild(header);
            }
            
            // باقي البيانات
            cells.forEach((cell, index) => {
                if (index === 0) return; // تخطي العنوان
                
                // تخطي الخلايا المخفية
                if (cell.classList.contains('d-mobile-none') || 
                    cell.classList.contains('d-tablet-none')) {
                    return;
                }
                
                const cardRow = document.createElement('div');
                cardRow.className = 'mobile-card-row';
                
                const label = document.createElement('div');
                label.className = 'mobile-card-label';
                label.textContent = headers[index] || `العمود ${index + 1}`;
                
                const value = document.createElement('div');
                value.className = 'mobile-card-value';
                value.innerHTML = cell.innerHTML;
                
                cardRow.appendChild(label);
                cardRow.appendChild(value);
                card.appendChild(cardRow);
            });
            
            cardsContainer.appendChild(card);
        });
        
        table.parentNode.appendChild(cardsContainer);
        table.classList.add('d-mobile-none');
    }

    // إزالة بطاقات الجدول
    function removeMobileTableCards() {
        const cardsContainers = document.querySelectorAll('.table-mobile-cards');
        cardsContainers.forEach(container => {
            container.remove();
        });
        
        const tables = document.querySelectorAll('.table-mobile-stack');
        tables.forEach(table => {
            table.classList.remove('d-mobile-none');
        });
        
        mobileCardsCreated = false;
    }

    // تحسين أزرار الجداول للأجهزة المحمولة
    function optimizeTableButtons() {
        const buttonGroups = document.querySelectorAll('.table-actions .btn-group');
        
        buttonGroups.forEach(group => {
            if (currentBreakpoint === 'xs' || currentBreakpoint === 'sm') {
                // تحويل إلى عمودي للأجهزة المحمولة
                if (!group.classList.contains('btn-group-vertical')) {
                    group.classList.add('btn-group-vertical');
                    group.style.width = '100%';
                }
            } else {
                // إعادة تعيين للشاشات الكبيرة
                group.classList.remove('btn-group-vertical');
                group.style.width = '';
            }
        });
    }

    // إضافة تأثيرات التحميل للجداول
    function addTableLoadingEffects() {
        const tables = document.querySelectorAll('.table');
        
        tables.forEach(table => {
            // إضافة مؤشر التحميل عند النقر على الأزرار
            const buttons = table.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.href || this.type === 'submit') {
                        this.classList.add('table-loading');
                        
                        // إزالة التأثير بعد 3 ثوان
                        setTimeout(() => {
                            this.classList.remove('table-loading');
                        }, 3000);
                    }
                });
            });
        });
    }

    // تحسين البحث في الجداول
    function enhanceTableSearch() {
        const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="بحث"]');
        
        searchInputs.forEach(input => {
            let searchTimeout;
            
            input.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = this.value.toLowerCase().trim();
                
                searchTimeout = setTimeout(() => {
                    filterTableRows(searchTerm);
                }, 300);
            });
        });
    }

    // تصفية صفوف الجدول
    function filterTableRows(searchTerm) {
        const tables = document.querySelectorAll('.table tbody');
        
        tables.forEach(tbody => {
            const rows = tbody.querySelectorAll('tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const shouldShow = searchTerm === '' || text.includes(searchTerm);
                
                row.style.display = shouldShow ? '' : 'none';
            });
        });
        
        // تحديث البطاقات المحمولة إذا كانت موجودة
        if (mobileCardsCreated) {
            filterMobileCards(searchTerm);
        }
    }

    // تصفية البطاقات المحمولة
    function filterMobileCards(searchTerm) {
        const cards = document.querySelectorAll('.mobile-card');
        
        cards.forEach(card => {
            const text = card.textContent.toLowerCase();
            const shouldShow = searchTerm === '' || text.includes(searchTerm);
            
            card.style.display = shouldShow ? '' : 'none';
        });
    }

    // إضافة ترقيم للصفحات
    function addTablePagination() {
        const tables = document.querySelectorAll('.table');
        
        tables.forEach(table => {
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 20) { // إضافة ترقيم إذا كان هناك أكثر من 20 صف
                createPaginationForTable(table, rows);
            }
        });
    }

    // إنشاء ترقيم للجدول
    function createPaginationForTable(table, rows) {
        const rowsPerPage = currentBreakpoint === 'xs' ? 10 : 20;
        const totalPages = Math.ceil(rows.length / rowsPerPage);
        
        if (totalPages <= 1) return;
        
        // إخفاء جميع الصفوف
        rows.forEach(row => row.style.display = 'none');
        
        // إظهار الصفحة الأولى
        showTablePage(rows, 1, rowsPerPage);
        
        // إنشاء عناصر التحكم في الترقيم
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'table-pagination';
        
        const paginationInfo = document.createElement('div');
        paginationInfo.className = 'table-pagination-info';
        paginationInfo.textContent = `عرض 1-${Math.min(rowsPerPage, rows.length)} من ${rows.length} عنصر`;
        
        const paginationNav = document.createElement('nav');
        paginationNav.innerHTML = createPaginationHTML(totalPages, 1);
        
        paginationContainer.appendChild(paginationInfo);
        paginationContainer.appendChild(paginationNav);
        
        table.parentNode.appendChild(paginationContainer);
        
        // إضافة أحداث النقر
        setupPaginationEvents(paginationNav, rows, rowsPerPage, paginationInfo);
    }

    // إظهار صفحة معينة من الجدول
    function showTablePage(rows, page, rowsPerPage) {
        const startIndex = (page - 1) * rowsPerPage;
        const endIndex = startIndex + rowsPerPage;
        
        rows.forEach((row, index) => {
            row.style.display = (index >= startIndex && index < endIndex) ? '' : 'none';
        });
    }

    // إنشاء HTML للترقيم
    function createPaginationHTML(totalPages, currentPage) {
        let html = '<ul class="pagination pagination-sm">';
        
        // زر السابق
        html += `<li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage - 1}">السابق</a>
                 </li>`;
        
        // أرقام الصفحات
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage || i === 1 || i === totalPages || 
                (i >= currentPage - 1 && i <= currentPage + 1)) {
                html += `<li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" data-page="${i}">${i}</a>
                         </li>`;
            } else if (i === currentPage - 2 || i === currentPage + 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // زر التالي
        html += `<li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" data-page="${currentPage + 1}">التالي</a>
                 </li>`;
        
        html += '</ul>';
        return html;
    }

    // إعداد أحداث الترقيم
    function setupPaginationEvents(nav, rows, rowsPerPage, infoElement) {
        nav.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (e.target.classList.contains('page-link') && e.target.dataset.page) {
                const page = parseInt(e.target.dataset.page);
                const totalPages = Math.ceil(rows.length / rowsPerPage);
                
                if (page >= 1 && page <= totalPages) {
                    showTablePage(rows, page, rowsPerPage);
                    
                    // تحديث الترقيم
                    nav.innerHTML = createPaginationHTML(totalPages, page);
                    
                    // تحديث معلومات الترقيم
                    const startIndex = (page - 1) * rowsPerPage + 1;
                    const endIndex = Math.min(page * rowsPerPage, rows.length);
                    infoElement.textContent = `عرض ${startIndex}-${endIndex} من ${rows.length} عنصر`;
                }
            }
        });
    }

    // معالج تغيير حجم النافذة
    function handleResize() {
        const newBreakpoint = getCurrentBreakpoint();
        
        if (newBreakpoint !== currentBreakpoint) {
            currentBreakpoint = newBreakpoint;
            console.log('Tables breakpoint changed to:', currentBreakpoint);
            
            createMobileTableCards();
            optimizeTableButtons();
        }
    }

    // تهيئة النظام
    function init() {
        makeTablesResponsive();
        createMobileTableCards();
        optimizeTableButtons();
        addTableLoadingEffects();
        enhanceTableSearch();
        addTablePagination();
        
        // إعداد معالج تغيير حجم النافذة
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleResize, 150);
        });
        
        console.log('Responsive Tables system initialized');
    }

    // تشغيل النظام
    init();

    // إتاحة الوظائف للاستخدام الخارجي
    window.ResponsiveTables = {
        makeTablesResponsive,
        createMobileTableCards,
        removeMobileTableCards,
        getCurrentBreakpoint
    };
});

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paypal_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('paypal.edit');

$id = (int)($_GET['id'] ?? 0);
$paypalMgr = new PayPalManager(new Database());
$transfer = $paypalMgr->getTransferById($id);
if (!$transfer) {
    set_flash('danger', 'الحوالة غير موجودة');
    redirect('paypal_transfers.php');
}

$errors = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf = $_POST['csrf_token'] ?? '';
    if (!verify_csrf_token($csrf)) {
        $errors[] = 'CSRF غير صالح';
    } else {
        // collect fields
        $data = [
            'recipient_name'   => sanitize_input($_POST['recipient_name'] ?? ''),
            'recipient_phone'  => sanitize_input($_POST['recipient_phone'] ?? ''),
            'transaction_code' => sanitize_input($_POST['transaction_code'] ?? ''),
            'sender_name'      => sanitize_input($_POST['sender_name'] ?? ''),
            'amount'           => (float)($_POST['amount'] ?? 0),
            'status'           => ($_POST['status'] ?? 'لم يستلم') === 'مستلم' ? 'مستلم' : 'لم يستلم',
        ];
        foreach ($data as $k => $v) {
            if (($k === 'amount' && $v <= 0) || ($k !== 'amount' && $v === '')) {
                $errors[] = 'جميع الحقول مطلوبة والمبلغ > 0';
                break;
            }
        }
        if (empty($errors)) {
            if ($paypalMgr->updateTransfer($id, $data)) {
                // تسجيل عملية تعديل حوالة PayPal
                ActivityHelper::logUpdate(
                    'paypal_transfers',
                    "حوالة PayPal #$id",
                    $transfer,
                    $data,
                    $id
                );
                set_flash('success', 'تم التحديث بنجاح');
                redirect('paypal_transfers.php');
            } else {
                // تسجيل فشل التعديل
                ActivityHelper::logError(
                    'paypal_transfers',
                    'edit_paypal_transfer',
                    'فشل في تعديل حوالة PayPal #' . $id,
                    [
                        'transfer_id' => $id,
                        'attempted_data' => $data
                    ]
                );
                $errors[] = 'فشل التحديث';
            }
        }
    }
}

$csrf = get_csrf_token();
$pageTitle = 'تعديل حوالة #' . $id;
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>
<div class="container p-4">
    <h3 class="mb-4"><i class="fas fa-edit"></i> تعديل حوالة PayPal</h3>
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger"><ul class="mb-0"><?php foreach ($errors as $e) echo '<li>'.htmlspecialchars($e).'</li>'; ?></ul></div>
    <?php endif; ?>
    <form method="post" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
        <div class="mb-3"><label class="form-label">اسم المستلم *</label>
            <input type="text" name="recipient_name" class="form-control" required value="<?php echo htmlspecialchars($transfer['recipient_name']); ?>"></div>
        <div class="mb-3"><label class="form-label">رقم الجوال *</label>
            <input type="text" name="recipient_phone" class="form-control" required value="<?php echo htmlspecialchars($transfer['recipient_phone']); ?>"></div>
        <div class="mb-3"><label class="form-label">رمز المعاملة *</label>
            <input type="text" name="transaction_code" class="form-control" required value="<?php echo htmlspecialchars($transfer['transaction_code']); ?>"></div>
        <div class="mb-3"><label class="form-label">اسم المرسل *</label>
            <input type="text" name="sender_name" class="form-control" required value="<?php echo htmlspecialchars($transfer['sender_name']); ?>"></div>
        <div class="mb-3"><label class="form-label">المبلغ *</label>
            <input type="number" step="0.01" name="amount" class="form-control" required value="<?php echo htmlspecialchars($transfer['amount']); ?>"></div>
        <div class="mb-3"><label class="form-label d-block">الحالة</label>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="status" value="مستلم" <?php echo $transfer['status']==='مستلم'?'checked':''; ?>>
                <label class="form-check-label">مستلم</label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="status" value="لم يستلم" <?php echo $transfer['status']==='لم يستلم'?'checked':''; ?>>
                <label class="form-check-label">لم يستلم</label>
            </div>
        </div>
        <button class="btn btn-primary" type="submit"><i class="fas fa-save"></i> حفظ</button>
        <a href="paypal_transfers.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> رجوع</a>
    </form>
</div>
<?php require_once __DIR__ . '/../includes/footer.php'; ?>

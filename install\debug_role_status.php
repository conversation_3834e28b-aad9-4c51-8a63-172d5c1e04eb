<?php
/**
 * فحص وإصلاح مشكلة تحديث حالة الأدوار
 * Debug Role Status Update Issue
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/role_manager.php';

// إعداد الترميز
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>فحص مشكلة تحديث حالة الأدوار</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔍 فحص مشكلة تحديث حالة الأدوار</h1>";

try {
    $db = new Database();
    $conn = Database::getConnection();
    
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // 1. فحص بنية جدول roles
    echo "<div class='test-section'>";
    echo "<h3>1. فحص بنية جدول roles</h3>";
    
    $result = $conn->query("DESCRIBE roles");
    if ($result) {
        echo "<table>";
        echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='error'>❌ فشل في جلب بنية الجدول</div>";
    }
    echo "</div>";
    
    // 2. عرض الأدوار الحالية
    echo "<div class='test-section'>";
    echo "<h3>2. الأدوار الحالية</h3>";
    
    $result = $conn->query("SELECT id, name, description, status FROM roles ORDER BY id");
    if ($result) {
        echo "<table>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الوصف</th><th>الحالة</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['description']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['status']) . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='error'>❌ فشل في جلب الأدوار</div>";
    }
    echo "</div>";
    
    // 3. اختبار تحديث حالة دور
    echo "<div class='test-section'>";
    echo "<h3>3. اختبار تحديث حالة الدور</h3>";
    
    $roleMgr = new RoleManager($db);
    
    // البحث عن دور للاختبار (غير دور الأدمن)
    $testRole = $conn->query("SELECT * FROM roles WHERE id != 1 LIMIT 1")->fetch_assoc();
    
    if ($testRole) {
        echo "<div class='info'>📋 سيتم اختبار الدور: " . htmlspecialchars($testRole['name']) . " (ID: " . $testRole['id'] . ")</div>";
        echo "<div class='info'>📋 الحالة الحالية: " . htmlspecialchars($testRole['status']) . "</div>";
        
        // تحديد الحالة الجديدة
        $currentStatus = $testRole['status'];
        $newStatus = ($currentStatus === 'نشط' || $currentStatus === 'active') ? 'غير نشط' : 'نشط';
        
        echo "<div class='info'>📋 الحالة الجديدة المطلوبة: " . htmlspecialchars($newStatus) . "</div>";
        
        // محاولة التحديث
        $data = ['status' => $newStatus];
        $success = $roleMgr->updateRole($testRole['id'], $data);
        
        if ($success) {
            echo "<div class='success'>✅ تم تحديث الحالة بنجاح</div>";
            
            // التحقق من التحديث
            $updatedRole = $conn->query("SELECT status FROM roles WHERE id = " . $testRole['id'])->fetch_assoc();
            if ($updatedRole) {
                echo "<div class='info'>📋 الحالة بعد التحديث: " . htmlspecialchars($updatedRole['status']) . "</div>";
                
                if ($updatedRole['status'] === $newStatus) {
                    echo "<div class='success'>✅ التحديث تم بنجاح في قاعدة البيانات</div>";
                } else {
                    echo "<div class='error'>❌ التحديث لم يتم حفظه في قاعدة البيانات</div>";
                    echo "<div class='error'>المتوقع: " . htmlspecialchars($newStatus) . " | الفعلي: " . htmlspecialchars($updatedRole['status']) . "</div>";
                }
            }
            
            // إعادة الحالة الأصلية
            $roleMgr->updateRole($testRole['id'], ['status' => $currentStatus]);
            echo "<div class='info'>🔄 تم إعادة الحالة الأصلية</div>";
            
        } else {
            echo "<div class='error'>❌ فشل في تحديث الحالة</div>";
        }
    } else {
        echo "<div class='warning'>⚠️ لا يوجد دور متاح للاختبار</div>";
    }
    echo "</div>";
    
    // 4. اختبار الاستعلام المباشر
    echo "<div class='test-section'>";
    echo "<h3>4. اختبار الاستعلام المباشر</h3>";
    
    if ($testRole) {
        $testId = $testRole['id'];
        $testStatus = 'اختبار';
        
        echo "<div class='info'>📋 تجربة تحديث مباشر للدور ID: $testId</div>";
        
        // استعلام مباشر
        $stmt = $conn->prepare("UPDATE roles SET status = ? WHERE id = ?");
        if ($stmt) {
            $stmt->bind_param('si', $testStatus, $testId);
            $result = $stmt->execute();
            
            if ($result) {
                echo "<div class='success'>✅ الاستعلام المباشر نجح</div>";
                
                // التحقق
                $check = $conn->query("SELECT status FROM roles WHERE id = $testId")->fetch_assoc();
                echo "<div class='info'>📋 الحالة بعد الاستعلام المباشر: " . htmlspecialchars($check['status']) . "</div>";
                
                // إعادة الحالة الأصلية
                $stmt2 = $conn->prepare("UPDATE roles SET status = ? WHERE id = ?");
                $stmt2->bind_param('si', $testRole['status'], $testId);
                $stmt2->execute();
                $stmt2->close();
                
            } else {
                echo "<div class='error'>❌ الاستعلام المباشر فشل: " . $conn->error . "</div>";
            }
            $stmt->close();
        } else {
            echo "<div class='error'>❌ فشل في تحضير الاستعلام: " . $conn->error . "</div>";
        }
    }
    echo "</div>";
    
    // 5. فحص الأخطاء المحتملة
    echo "<div class='test-section'>";
    echo "<h3>5. فحص الأخطاء المحتملة</h3>";
    
    // فحص الترميز
    $charset = $conn->character_set_name();
    echo "<div class='info'>📋 ترميز قاعدة البيانات: " . htmlspecialchars($charset) . "</div>";
    
    // فحص إعدادات الجدول
    $tableInfo = $conn->query("SHOW TABLE STATUS LIKE 'roles'")->fetch_assoc();
    if ($tableInfo) {
        echo "<div class='info'>📋 ترميز الجدول: " . htmlspecialchars($tableInfo['Collation']) . "</div>";
        echo "<div class='info'>📋 محرك الجدول: " . htmlspecialchars($tableInfo['Engine']) . "</div>";
    }
    
    // فحص الصلاحيات
    $privileges = $conn->query("SHOW GRANTS")->fetch_all(MYSQLI_ASSOC);
    echo "<div class='info'>📋 عدد صلاحيات المستخدم: " . count($privileges) . "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في الفحص</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h4>📋 التوصيات:</h4>";
echo "<ol>";
echo "<li>تأكد من أن عمود 'status' في جدول 'roles' من نوع VARCHAR أو TEXT</li>";
echo "<li>تحقق من أن قاعدة البيانات تدعم الترميز UTF-8</li>";
echo "<li>تأكد من أن المستخدم لديه صلاحيات UPDATE على جدول roles</li>";
echo "<li>فحص سجلات الأخطاء في MySQL</li>";
echo "<li>تأكد من عدم وجود triggers أو constraints تمنع التحديث</li>";
echo "</ol>";
echo "</div>";

echo "</div></body></html>";
?>

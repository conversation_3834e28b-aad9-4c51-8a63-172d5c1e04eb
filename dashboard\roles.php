<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/role_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$current_user = $auth->getCurrentUser();

$pageTitle = 'الأدوار';

// Permission check
if (!$auth->hasPermission('roles.view')) {
    include __DIR__ . '/../includes/header.php';
    echo '<main class="content p-4"><div class="alert alert-danger">تم رفض الوصول</div></main>';
    include __DIR__ . '/../includes/footer.php';
    exit;
}

$db         = new Database();
$roleMgr    = new RoleManager($db);
$roles      = $roleMgr->getAllRoles();

// تسجيل عملية عرض قائمة الأدوار
ActivityHelper::logView('roles', 'عرض قائمة الأدوار');

include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-users-cog text-primary me-2"></i>
            إدارة الأدوار والصلاحيات
        </h2>
        <div class="d-flex gap-2">
            <?php if ($auth->hasPermission('roles.create')): ?>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                <i class="fas fa-plus me-1"></i>
                إضافة دور جديد
            </button>
            <?php endif; ?>
            <button class="btn btn-info" onclick="exportRoles()">
                <i class="fas fa-download me-1"></i>
                تصدير الأدوار
            </button>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <?php
        $totalRoles = count($roles);
        $activeRoles = 0;
        $inactiveRoles = 0;
        foreach ($roles as $role) {
            if ($role['status'] === 'نشط' || $role['status'] === 'active') {
                $activeRoles++;
            } else {
                $inactiveRoles++;
            }
        }
        ?>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $totalRoles; ?></h4>
                            <p class="mb-0">إجمالي الأدوار</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users-cog fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $activeRoles; ?></h4>
                            <p class="mb-0">أدوار نشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $inactiveRoles; ?></h4>
                            <p class="mb-0">أدوار غير نشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <?php
                            // حساب متوسط الصلاحيات لكل دور
                            $totalPermissions = 0;
                            foreach ($roles as $role) {
                                $permissions = $roleMgr->getRolePermissions($role['id']);
                                $totalPermissions += count($permissions);
                            }
                            $avgPermissions = $totalRoles > 0 ? round($totalPermissions / $totalRoles) : 0;
                            ?>
                            <h4 class="mb-0"><?php echo $avgPermissions; ?></h4>
                            <p class="mb-0">متوسط الصلاحيات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر البحث والتصفية
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <label for="statusFilter" class="form-label">تصفية حسب الحالة:</label>
                    <select id="statusFilter" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="نشط">نشط</option>
                        <option value="غير نشط">غير نشط</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="searchInput" class="form-label">البحث في الأدوار:</label>
                    <input type="text" id="searchInput" class="form-control" placeholder="ابحث في اسم أو وصف الدور...">
                </div>
                <div class="col-md-4">
                    <label for="permissionCountFilter" class="form-label">تصفية حسب عدد الصلاحيات:</label>
                    <select id="permissionCountFilter" class="form-select">
                        <option value="">جميع الأدوار</option>
                        <option value="high">أدوار بصلاحيات كثيرة (20+)</option>
                        <option value="medium">أدوار بصلاحيات متوسطة (10-19)</option>
                        <option value="low">أدوار بصلاحيات قليلة (أقل من 10)</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الأدوار -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الأدوار
                <span id="roleCount" class="badge bg-primary ms-2"><?php echo $totalRoles; ?></span>
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="rolesTable">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 60px;">#</th>
                            <th style="width: 200px;">اسم الدور</th>
                            <th>الوصف</th>
                            <th style="width: 100px;">الحالة</th>
                            <th style="width: 120px;">عدد الصلاحيات</th>
                            <th style="width: 120px;">عدد المستخدمين</th>
                            <?php if ($auth->hasPermission('roles.edit') || $auth->hasPermission('roles.delete') || $auth->hasPermission('roles.permissions')): ?>
                            <th style="width: 200px;">الإجراءات</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($roles as $role):
                            $rolePermissions = $roleMgr->getRolePermissions($role['id']);
                            $permissionCount = count($rolePermissions);
                            $userCount = $roleMgr->getRoleUserCount($role['id']);
                            $statusClass = ($role['status'] === 'نشط' || $role['status'] === 'active') ? 'success' : 'warning';
                            $statusText = ($role['status'] === 'نشط' || $role['status'] === 'active') ? 'نشط' : 'غير نشط';
                        ?>
                        <tr data-status="<?php echo $statusText; ?>" data-permission-count="<?php echo $permissionCount; ?>">
                            <td>
                                <span class="badge bg-secondary"><?php echo $role['id']; ?></span>
                            </td>
                            <td>
                                <strong class="text-primary"><?php echo htmlspecialchars($role['name']); ?></strong>
                            </td>
                            <td>
                                <span class="text-dark"><?php echo htmlspecialchars($role['description']); ?></span>
                            </td>
                            <td>
                                <?php if ($auth->hasPermission('roles.edit') && $role['id'] != 1): ?>
                                <span class="badge bg-<?php echo $statusClass; ?> status-badge"
                                      style="cursor: pointer;"
                                      onclick="toggleRoleStatus(<?php echo $role['id']; ?>)"
                                      title="اضغط لتغيير الحالة من <?php echo $statusText; ?> إلى <?php echo ($statusText === 'نشط') ? 'غير نشط' : 'نشط'; ?>"
                                      data-role-id="<?php echo $role['id']; ?>"
                                      data-bs-toggle="tooltip"
                                      data-bs-placement="top">
                                    <i class="fas fa-<?php echo ($statusText === 'نشط') ? 'check-circle' : 'pause-circle'; ?> me-1"></i>
                                    <?php echo $statusText; ?>
                                    <i class="fas fa-exchange-alt ms-1" style="font-size: 0.7em; opacity: 0.7;"></i>
                                </span>
                                <?php else: ?>
                                <span class="badge bg-<?php echo $statusClass; ?>"
                                      title="<?php echo ($role['id'] == 1) ? 'لا يمكن تغيير حالة دور مدير النظام' : 'ليس لديك إذن لتغيير الحالة'; ?>"
                                      data-bs-toggle="tooltip"
                                      data-bs-placement="top">
                                    <i class="fas fa-<?php echo ($statusText === 'نشط') ? 'check-circle' : 'pause-circle'; ?> me-1"></i>
                                    <?php echo $statusText; ?>
                                    <?php if ($role['id'] == 1): ?>
                                    <i class="fas fa-lock ms-1" style="font-size: 0.7em; opacity: 0.7;"></i>
                                    <?php endif; ?>
                                </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    <?php echo $permissionCount; ?> صلاحية
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-primary">
                                    <?php echo $userCount; ?> مستخدم
                                </span>
                            </td>
                            <?php if ($auth->hasPermission('roles.edit') || $auth->hasPermission('roles.delete') || $auth->hasPermission('roles.permissions')): ?>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <?php if ($auth->hasPermission('roles.permissions')): ?>
                                    <button class="btn btn-outline-info btn-sm" onclick="managePermissions(<?php echo $role['id']; ?>)" title="إدارة الصلاحيات">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    <?php endif; ?>
                                    <?php if ($auth->hasPermission('roles.edit')): ?>
                                    <button class="btn btn-outline-primary btn-sm" onclick="editRole(<?php echo $role['id']; ?>)" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php endif; ?>
                                    <?php if ($auth->hasPermission('roles.delete') && $role['id'] != 1): // منع حذف دور الأدمن ?>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteRole(<?php echo $role['id']; ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <?php endif; ?>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</main>

<!-- Modal إضافة دور جديد -->
<?php if ($auth->hasPermission('roles.create')): ?>
<div class="modal fade" id="addRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دور جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addRoleForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roleName" class="form-label">اسم الدور</label>
                        <input type="text" class="form-control" id="roleName" required
                               placeholder="مثال: مدير المبيعات">
                    </div>
                    <div class="mb-3">
                        <label for="roleDescription" class="form-label">وصف الدور</label>
                        <textarea class="form-control" id="roleDescription" rows="3" required
                                  placeholder="وصف مفصل لمهام ومسؤوليات هذا الدور"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="roleStatus" class="form-label">حالة الدور</label>
                        <select class="form-select" id="roleStatus" required>
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة الدور</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Modal تعديل دور -->
<?php if ($auth->hasPermission('roles.edit')): ?>
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الدور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editRoleForm">
                <input type="hidden" id="editRoleId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editRoleName" class="form-label">اسم الدور</label>
                        <input type="text" class="form-control" id="editRoleName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRoleDescription" class="form-label">وصف الدور</label>
                        <textarea class="form-control" id="editRoleDescription" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editRoleStatus" class="form-label">حالة الدور</label>
                        <select class="form-select" id="editRoleStatus" required>
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Modal إدارة الصلاحيات -->
<?php if ($auth->hasPermission('roles.permissions')): ?>
<div class="modal fade" id="permissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة صلاحيات الدور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="permissionsRoleId">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الصلاحيات المتاحة</h6>
                        <div id="availablePermissions" class="border p-3" style="height: 400px; overflow-y: auto;">
                            <!-- سيتم تحميل الصلاحيات هنا -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>صلاحيات الدور</h6>
                        <div id="rolePermissions" class="border p-3" style="height: 400px; overflow-y: auto;">
                            <!-- سيتم تحميل صلاحيات الدور هنا -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="savePermissions()">حفظ الصلاحيات</button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // فلترة حسب الحالة
    document.getElementById('statusFilter').addEventListener('change', function() {
        filterTable();
    });

    // البحث في النص
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTable();
    });

    // فلترة حسب عدد الصلاحيات
    document.getElementById('permissionCountFilter').addEventListener('change', function() {
        filterTable();
    });

    function filterTable() {
        const statusFilter = document.getElementById('statusFilter').value;
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        const permissionCountFilter = document.getElementById('permissionCountFilter').value;
        const rows = document.querySelectorAll('#rolesTable tbody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const status = row.dataset.status;
            const permissionCount = parseInt(row.dataset.permissionCount);
            const text = row.textContent.toLowerCase();

            let showRow = true;

            // فلترة الحالة
            if (statusFilter && status !== statusFilter) {
                showRow = false;
            }

            // فلترة البحث
            if (searchInput && !text.includes(searchInput)) {
                showRow = false;
            }

            // فلترة عدد الصلاحيات
            if (permissionCountFilter) {
                if (permissionCountFilter === 'high' && permissionCount < 20) {
                    showRow = false;
                } else if (permissionCountFilter === 'medium' && (permissionCount < 10 || permissionCount >= 20)) {
                    showRow = false;
                } else if (permissionCountFilter === 'low' && permissionCount >= 10) {
                    showRow = false;
                }
            }

            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleCount++;
        });

        // تحديث عداد الأدوار
        document.getElementById('roleCount').textContent = visibleCount;
    }
});

// دالة تصدير الأدوار
function exportRoles() {
    const table = document.getElementById('rolesTable');
    const rows = Array.from(table.querySelectorAll('tbody tr')).filter(row => row.style.display !== 'none');

    let csv = 'الرقم,اسم الدور,الوصف,الحالة,عدد الصلاحيات,عدد المستخدمين\n';

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const data = [
            cells[0].textContent.trim(),
            cells[1].textContent.trim(),
            cells[2].textContent.trim(),
            cells[3].textContent.trim(),
            cells[4].textContent.trim(),
            cells[5].textContent.trim()
        ];
        csv += data.join(',') + '\n';
    });

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'roles_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}

// دالة تعديل الدور
function editRole(id) {
    // جلب بيانات الدور
    fetch(`ajax/get_role.php?id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('editRoleId').value = data.role.id;
                document.getElementById('editRoleName').value = data.role.name;
                document.getElementById('editRoleDescription').value = data.role.description;
                document.getElementById('editRoleStatus').value = data.role.status;

                const modal = new bootstrap.Modal(document.getElementById('editRoleModal'));
                modal.show();
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في جلب بيانات الدور');
        });
}

// دالة حذف الدور
function deleteRole(id) {
    if (confirm('هل أنت متأكد من حذف هذا الدور؟\nسيتم حذف جميع الصلاحيات المرتبطة به.')) {
        fetch('ajax/delete_role.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `id=${id}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في حذف الدور');
        });
    }
}

// دالة تغيير حالة الدور
function toggleRoleStatus(id) {
    const badge = document.querySelector(`[data-role-id="${id}"]`);
    const originalContent = badge.innerHTML;

    // إظهار مؤشر التحميل
    badge.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
    badge.style.pointerEvents = 'none';

    fetch('ajax/toggle_role_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث الشارة
            badge.className = `badge bg-${data.status_class} status-badge`;
            const icon = data.new_status === 'نشط' ? 'check-circle' : 'pause-circle';
            badge.innerHTML = `<i class="fas fa-${icon} me-1"></i>${data.new_status}`;

            // تحديث البيانات في الجدول
            const row = badge.closest('tr');
            row.dataset.status = data.new_status;

            // تحديث tooltip
            const newTooltip = data.new_status === 'نشط' ?
                'اضغط لتغيير الحالة من نشط إلى غير نشط' :
                'اضغط لتغيير الحالة من غير نشط إلى نشط';
            badge.setAttribute('title', newTooltip);
            badge.setAttribute('data-bs-original-title', newTooltip);

            // تحديث الإحصائيات
            updateStatistics();

            showAlert('success', data.message);
        } else {
            // استعادة المحتوى الأصلي
            badge.innerHTML = originalContent;
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        badge.innerHTML = originalContent;
        showAlert('danger', 'حدث خطأ في تغيير حالة الدور');
    })
    .finally(() => {
        badge.style.pointerEvents = 'auto';
    });
}

// دالة تحديث الإحصائيات
function updateStatistics() {
    const rows = document.querySelectorAll('#rolesTable tbody tr');
    let activeCount = 0;
    let inactiveCount = 0;

    rows.forEach(row => {
        const status = row.dataset.status;
        if (status === 'نشط') {
            activeCount++;
        } else {
            inactiveCount++;
        }
    });

    // تحديث بطاقات الإحصائيات
    const activeCard = document.querySelector('.card.bg-success h4');
    const inactiveCard = document.querySelector('.card.bg-warning h4');

    if (activeCard) activeCard.textContent = activeCount;
    if (inactiveCard) inactiveCard.textContent = inactiveCount;
}

// دالة إدارة الصلاحيات
function managePermissions(roleId) {
    document.getElementById('permissionsRoleId').value = roleId;

    // جلب الصلاحيات المتاحة وصلاحيات الدور
    Promise.all([
        fetch('ajax/get_all_permissions.php').then(r => r.json()),
        fetch(`ajax/get_role_permissions.php?role_id=${roleId}`).then(r => r.json())
    ])
    .then(([allPermissions, rolePermissions]) => {
        if (allPermissions.success && rolePermissions.success) {
            displayPermissions(allPermissions.permissions, rolePermissions.permissions);
            const modal = new bootstrap.Modal(document.getElementById('permissionsModal'));
            modal.show();
        } else {
            showAlert('danger', 'فشل في جلب الصلاحيات');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في جلب الصلاحيات');
    });
}

// دالة عرض الصلاحيات
function displayPermissions(allPermissions, rolePermissions) {
    const availableDiv = document.getElementById('availablePermissions');
    const roleDiv = document.getElementById('rolePermissions');

    availableDiv.innerHTML = '';
    roleDiv.innerHTML = '';

    const rolePermissionIds = rolePermissions.map(p => p.id);

    allPermissions.forEach(permission => {
        const div = document.createElement('div');
        div.className = 'form-check mb-2';
        div.innerHTML = `
            <input class="form-check-input" type="checkbox" value="${permission.id}"
                   id="perm_${permission.id}" ${rolePermissionIds.includes(permission.id) ? 'checked' : ''}>
            <label class="form-check-label" for="perm_${permission.id}">
                <strong>${permission.name}</strong><br>
                <small class="text-muted">${permission.description}</small>
            </label>
        `;

        if (rolePermissionIds.includes(permission.id)) {
            roleDiv.appendChild(div);
        } else {
            availableDiv.appendChild(div);
        }
    });
}

// دالة حفظ الصلاحيات
function savePermissions() {
    const roleId = document.getElementById('permissionsRoleId').value;
    const checkboxes = document.querySelectorAll('#permissionsModal input[type="checkbox"]:checked');
    const permissionIds = Array.from(checkboxes).map(cb => cb.value);

    fetch('ajax/update_role_permissions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            role_id: roleId,
            permission_ids: permissionIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            const modal = bootstrap.Modal.getInstance(document.getElementById('permissionsModal'));
            modal.hide();
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في حفظ الصلاحيات');
    });
}

// معالج إضافة دور جديد
<?php if ($auth->hasPermission('roles.create')): ?>
document.getElementById('addRoleForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإضافة...';

    const formData = new FormData();
    formData.append('name', document.getElementById('roleName').value);
    formData.append('description', document.getElementById('roleDescription').value);
    formData.append('status', document.getElementById('roleStatus').value);

    fetch('ajax/add_role.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('addRoleModal'));
            modal.hide();
            document.getElementById('addRoleForm').reset();
            showAlert('success', data.message);
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في إضافة الدور');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});
<?php endif; ?>

// معالج تعديل دور
<?php if ($auth->hasPermission('roles.edit')): ?>
document.getElementById('editRoleForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';

    const formData = new FormData();
    formData.append('id', document.getElementById('editRoleId').value);
    formData.append('name', document.getElementById('editRoleName').value);
    formData.append('description', document.getElementById('editRoleDescription').value);
    formData.append('status', document.getElementById('editRoleStatus').value);

    fetch('ajax/edit_role.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('editRoleModal'));
            modal.hide();
            showAlert('success', data.message);
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في تعديل الدور');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});
<?php endif; ?>

// دالة إظهار التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
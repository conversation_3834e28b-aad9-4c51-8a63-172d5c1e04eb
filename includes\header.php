<?php
require_once __DIR__ . '/../config/config.php';
// Page title can be overridden by the including page via $pageTitle variable
$pageTitle = isset($pageTitle) ? $pageTitle : SYSTEM_NAME;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="TrustPlus Money Exchange System">
    <title><?php echo htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8'); ?></title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome 6 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom CSS Files - ترتيب محسن لتجنب التضارب -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/main.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/tables-responsive.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/forms-responsive.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/profile-images.css">

    <!-- Unified Sidebar CSS - الحل الموحد للشريط الجانبي (يجب أن يكون أولاً) -->
    <?php if (strpos($_SERVER['REQUEST_URI'], '/dashboard/') !== false): ?>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/sidebar-unified.css">
    <?php endif; ?>

    <!-- Dashboard CSS بعد الشريط الجانبي -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/dashboard.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/dashboard-improvements.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/dashboard-enhanced.css">

    <!-- Sidebar Force Fix CSS - إصلاح قسري نهائي (يجب أن يكون الأخير) -->
    <?php if (strpos($_SERVER['REQUEST_URI'], '/dashboard/') !== false): ?>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/sidebar-force-fix.css">
    <?php endif; ?>

    <?php if (basename($_SERVER['PHP_SELF']) == 'transfers.php'): ?>
    <!-- Transfers-specific CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/transfers-enhanced.css">
    <?php endif; ?>

    <?php if (basename($_SERVER['PHP_SELF']) == 'permissions.php'): ?>
    <!-- Permissions-specific CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/permissions.css">
    <?php endif; ?>

    <?php if (basename($_SERVER['PHP_SELF']) == 'roles.php'): ?>
    <!-- Roles-specific CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/roles.css">
    <?php endif; ?>

    <?php if (in_array(basename($_SERVER['PHP_SELF']), ['daily_transactions.php', 'add_daily_transaction.php', 'edit_daily_transaction.php', 'view_daily_transaction.php', 'deleted_daily_transactions.php'])): ?>
    <!-- Daily Transactions-specific CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/daily-transactions.css">
    <?php endif; ?>

    <!-- PayPal Sidebar CSS - تحميل في جميع صفحات لوحة التحكم (بدون تضارب) -->
    <?php if (strpos($_SERVER['REQUEST_URI'], '/dashboard/') !== false): ?>
    <style>
    /* تعطيل أي أنماط متضاربة من ملفات PayPal */
    .paypal-sidebar-container,
    .old-paypal-sidebar {
        display: none !important;
    }
    </style>
    <?php endif; ?>

    <!-- PayPal Reports CSS - تحميل في صفحة التقارير فقط -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'paypal_reports.php'): ?>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/paypal-reports.css">
    <?php endif; ?>
</head>
<body>
    <!-- Session Messages (Hidden elements for JavaScript to read) -->
    <?php if (isset($_SESSION['success_message'])): ?>
    <div id="session-success-message" style="display: none;"><?php echo htmlspecialchars($_SESSION['success_message'], ENT_QUOTES, 'UTF-8'); unset($_SESSION['success_message']); ?></div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
    <div id="session-error-message" style="display: none;"><?php echo htmlspecialchars($_SESSION['error_message'], ENT_QUOTES, 'UTF-8'); unset($_SESSION['error_message']); ?></div>
    <?php endif; ?>

    <?php if (isset($_SESSION['warning_message'])): ?>
    <div id="session-warning-message" style="display: none;"><?php echo htmlspecialchars($_SESSION['warning_message'], ENT_QUOTES, 'UTF-8'); unset($_SESSION['warning_message']); ?></div>
    <?php endif; ?>

    <?php if (isset($_SESSION['info_message'])): ?>
    <div id="session-info-message" style="display: none;"><?php echo htmlspecialchars($_SESSION['info_message'], ENT_QUOTES, 'UTF-8'); unset($_SESSION['info_message']); ?></div>
    <?php endif; ?>

    <!-- Sidebar Toggle Button - سيتم إنشاؤه تلقائياً بواسطة JavaScript -->
    <!-- <button class="btn btn-primary sidebar-toggle-btn d-lg-none" id="sidebar-toggle-btn" type="button"
            data-bs-toggle="tooltip" data-bs-placement="bottom" title="فتح/إغلاق القائمة الجانبية">
        <i class="fas fa-bars"></i>
    </button> -->

    <!-- Main Page Wrapper -->
    <div class="page-wrapper d-flex">

        <?php
        // Ensure $current_user is defined for includes like sidebar.php
        // Assumes $auth = new Auth() has been called BEFORE including header.php
        if (isset($auth) && is_object($auth)) {
            $current_user = $auth->getCurrentUser();
            // If $current_user is null (e.g., not logged in, though requireLogin should handle this),
            // initialize it as an empty array to prevent warnings when accessing keys.
            if ($current_user === null) {
                $current_user = [
                    'id' => null,
                    'username' => 'غير محدد',
                    'full_name' => 'مستخدم غير محدد',
                    'role_name' => 'غير محدد'
                ];
            }
        } else {
            // Fallback if $auth is not available
            $current_user = [
                'id' => null,
                'username' => 'غير محدد',
                'full_name' => 'مستخدم غير محدد',
                'role_name' => 'غير محدد'
            ];
        }
        ?>

        <!-- Include Sidebar -->
        <?php include __DIR__ . '/sidebar.php'; ?>

        <!-- Main Content Area -->
        <div class="main-content"><?php
            // Main content starts here - dashboard pages will add their content
            // The closing div will be in footer.php
        ?>

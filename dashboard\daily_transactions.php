<?php
/**
 * صفحة عرض المعاملات اليومية مع البحث والفلترة
 * Daily Transactions List with Search and Filtering
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

// إنشاء كائن المصادقة والتحقق من تسجيل الدخول
$auth = new Auth();
$auth->requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = $auth->getCurrentUser();

// التحقق من الصلاحيات
if (!$auth->hasPermission('daily_transactions.view')) {
    header('Location: ' . BASE_URL . 'dashboard/index.php?error=no_permission');
    exit;
}

$pageTitle = 'سجل المعاملات اليومية';
$error_message = '';
$success_message = '';

// معالجة الحذف
if (isset($_GET['delete']) && $auth->hasPermission('daily_transactions.delete')) {
    $transaction_id = (int)$_GET['delete'];
    try {
        // التحقق من وجود المعاملة
        $stmt = $pdo->prepare("SELECT * FROM daily_transactions WHERE id = ?");
        $stmt->execute([$transaction_id]);
        $transaction_to_delete = $stmt->fetch();

        if (!$transaction_to_delete) {
            $error_message = "المعاملة المحددة غير موجودة";
        } else {
            // بدء المعاملة
            $pdo->beginTransaction();

            try {
                // حفظ المعاملة في جدول المحذوفات
                $insert_deleted_sql = "INSERT INTO deleted_daily_transactions (
                    original_id, transaction_number, country_id, base_amount, customer_rate,
                    operation_type, calculated_amount, exchange_rate, recipient_amount,
                    delivery_type, transfer_amount, recipient_name, notes, branch_id,
                    created_by, created_at, updated_by, updated_at, deleted_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $stmt = $pdo->prepare($insert_deleted_sql);
                $stmt->execute([
                    $transaction_to_delete['id'],
                    $transaction_to_delete['transaction_number'],
                    $transaction_to_delete['country_id'],
                    $transaction_to_delete['base_amount'],
                    $transaction_to_delete['customer_rate'],
                    $transaction_to_delete['operation_type'],
                    $transaction_to_delete['calculated_amount'],
                    $transaction_to_delete['exchange_rate'],
                    $transaction_to_delete['recipient_amount'],
                    $transaction_to_delete['delivery_type'],
                    $transaction_to_delete['transfer_amount'],
                    $transaction_to_delete['recipient_name'],
                    $transaction_to_delete['notes'],
                    $transaction_to_delete['branch_id'],
                    $transaction_to_delete['created_by'],
                    $transaction_to_delete['created_at'],
                    $transaction_to_delete['updated_by'],
                    $transaction_to_delete['updated_at'],
                    $current_user['id']
                ]);

                // إضافة سجل في تاريخ المعاملات
                $history_sql = "INSERT INTO daily_transaction_history
                    (transaction_id, action_type, old_values, changed_by, changed_at)
                    VALUES (?, 'deleted', ?, ?, CURRENT_TIMESTAMP)";

                $old_values = json_encode([
                    'transaction_number' => $transaction_to_delete['transaction_number'],
                    'country_id' => $transaction_to_delete['country_id'],
                    'base_amount' => $transaction_to_delete['base_amount'],
                    'delivery_type' => $transaction_to_delete['delivery_type']
                ]);

                $stmt = $pdo->prepare($history_sql);
                $stmt->execute([$transaction_to_delete['id'], $old_values, $current_user['id']]);

                // حذف المعاملة من الجدول الأساسي
                $delete_sql = "DELETE FROM daily_transactions WHERE id = ?";
                $stmt = $pdo->prepare($delete_sql);
                $stmt->execute([$transaction_id]);

                // تأكيد المعاملة
                $pdo->commit();

                $success_message = "تم حذف المعاملة بنجاح";

            } catch (Exception $e) {
                $pdo->rollback();
                throw $e;
            }
        }
    } catch (Exception $e) {
        $error_message = "خطأ في حذف المعاملة: " . $e->getMessage();
    }
}

// معاملات البحث والفلترة
$search_params = [
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'country_id' => $_GET['country_id'] ?? '',
    'delivery_type' => $_GET['delivery_type'] ?? '',
    'min_amount' => $_GET['min_amount'] ?? '',
    'max_amount' => $_GET['max_amount'] ?? '',
    'recipient_name' => $_GET['recipient_name'] ?? '',
    'branch_id' => $_GET['branch_id'] ?? ''
];

// إعدادات الصفحات
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// جلب المعاملات باستخدام استعلام مباشر
try {
    // بناء استعلام البحث
    $sql = "SELECT * FROM v_daily_transactions_full WHERE 1=1";
    $params = [];

    if ($search_params['date_from']) {
        $sql .= " AND DATE(created_at) >= ?";
        $params[] = $search_params['date_from'];
    }
    if ($search_params['date_to']) {
        $sql .= " AND DATE(created_at) <= ?";
        $params[] = $search_params['date_to'];
    }
    if ($search_params['country_id']) {
        $sql .= " AND country_id = ?";
        $params[] = $search_params['country_id'];
    }
    if ($search_params['delivery_type']) {
        $sql .= " AND delivery_type = ?";
        $params[] = $search_params['delivery_type'];
    }
    if ($search_params['min_amount']) {
        $sql .= " AND base_amount >= ?";
        $params[] = $search_params['min_amount'];
    }
    if ($search_params['max_amount']) {
        $sql .= " AND base_amount <= ?";
        $params[] = $search_params['max_amount'];
    }
    if ($search_params['recipient_name']) {
        $sql .= " AND recipient_name LIKE ?";
        $params[] = '%' . $search_params['recipient_name'] . '%';
    }
    if ($search_params['branch_id']) {
        $sql .= " AND branch_id = ?";
        $params[] = $search_params['branch_id'];
    }

    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $transactions = $stmt->fetchAll();

    // حساب العدد الإجمالي للصفحات
    $count_sql = "SELECT COUNT(*) as total FROM v_daily_transactions_full WHERE 1=1";
    $count_params = [];

    if ($search_params['date_from']) {
        $count_sql .= " AND DATE(created_at) >= ?";
        $count_params[] = $search_params['date_from'];
    }
    if ($search_params['date_to']) {
        $count_sql .= " AND DATE(created_at) <= ?";
        $count_params[] = $search_params['date_to'];
    }
    if ($search_params['country_id']) {
        $count_sql .= " AND country_id = ?";
        $count_params[] = $search_params['country_id'];
    }
    if ($search_params['delivery_type']) {
        $count_sql .= " AND delivery_type = ?";
        $count_params[] = $search_params['delivery_type'];
    }
    if ($search_params['min_amount']) {
        $count_sql .= " AND base_amount >= ?";
        $count_params[] = $search_params['min_amount'];
    }
    if ($search_params['max_amount']) {
        $count_sql .= " AND base_amount <= ?";
        $count_params[] = $search_params['max_amount'];
    }
    if ($search_params['recipient_name']) {
        $count_sql .= " AND recipient_name LIKE ?";
        $count_params[] = '%' . $search_params['recipient_name'] . '%';
    }
    if ($search_params['branch_id']) {
        $count_sql .= " AND branch_id = ?";
        $count_params[] = $search_params['branch_id'];
    }

    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($count_params);
    $total_records = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_records / $limit);

} catch (Exception $e) {
    $transactions = [];
    $total_records = 0;
    $total_pages = 0;
    $error_message = "خطأ في جلب المعاملات: " . $e->getMessage();
}

// جلب قائمة الدول للفلترة
try {
    $countries_stmt = $pdo->query("SELECT id, name_ar, currency_code FROM countries WHERE is_active = 1 ORDER BY name_ar");
    $countries = $countries_stmt->fetchAll();
} catch (Exception $e) {
    $countries = [];
}

// جلب قائمة الفروع للفلترة
try {
    $branches_stmt = $pdo->query("SELECT id, name FROM branches WHERE status = 'active' ORDER BY name");
    $branches = $branches_stmt->fetchAll();
} catch (Exception $e) {
    $branches = [];
}

require_once __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>
<main class="content p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h3 mb-0">
                            <i class="fas fa-list-alt text-primary me-2"></i>
                            سجل المعاملات اليومية
                        </h2>
                        <div>
                            <?php if ($auth->hasPermission('daily_transactions.create')): ?>
                                <a href="<?php echo BASE_URL; ?>dashboard/add_daily_transaction.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>
                                    إضافة معاملة جديدة
                                </a>
                            <?php endif; ?>
                            <?php if ($auth->hasPermission('daily_transactions.export')): ?>
                                <!-- قائمة منسدلة للتصدير -->
                                <div class="btn-group ms-2" role="group">
                                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-download me-2"></i>
                                        تصدير
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>dashboard/export_daily_transactions.php<?php echo !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] . '&format=excel' : '?format=excel'; ?>">
                                                <i class="fas fa-file-excel text-success me-2"></i>
                                                تصدير إلى Excel
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>dashboard/export_daily_transactions.php<?php echo !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] . '&format=pdf' : '?format=pdf'; ?>">
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                تصدير إلى PDF
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>dashboard/export_daily_transactions.php<?php echo !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] . '&format=csv' : '?format=csv'; ?>">
                                                <i class="fas fa-file-csv text-info me-2"></i>
                                                تصدير إلى CSV
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>dashboard/daily_transactions_reports.php<?php echo !empty($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : ''; ?>">
                                                <i class="fas fa-chart-bar text-primary me-2"></i>
                                                عرض التقارير
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- رسائل النجاح والخطأ -->
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- فلاتر البحث -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-search me-2"></i>
                                أدوات البحث والفلترة
                                <button class="btn btn-sm btn-outline-secondary float-end" type="button"
                                        data-bs-toggle="collapse" data-bs-target="#searchFilters">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </h6>
                        </div>
                        <div class="collapse show" id="searchFilters">
                            <div class="card-body">
                                <form method="GET" class="row g-3">
                                    <!-- الفترة الزمنية -->
                                    <div class="col-md-3">
                                        <label for="date_from" class="form-label">من تاريخ</label>
                                        <input type="date" class="form-control" id="date_from" name="date_from"
                                               value="<?php echo htmlspecialchars($search_params['date_from']); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="date_to" class="form-label">إلى تاريخ</label>
                                        <input type="date" class="form-control" id="date_to" name="date_to"
                                               value="<?php echo htmlspecialchars($search_params['date_to']); ?>">
                                    </div>

                                    <!-- الدولة -->
                                    <div class="col-md-3">
                                        <label for="country_search_filter" class="form-label">الدولة</label>
                                        <div class="position-relative">
                                            <input type="text"
                                                   class="form-control"
                                                   id="country_search_filter"
                                                   placeholder="ابحث عن الدولة..."
                                                   autocomplete="off">
                                            <input type="hidden" id="country_id" name="country_id" value="<?php echo $search_params['country_id']; ?>">

                                            <!-- قائمة النتائج -->
                                            <div id="country_filter_results" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto; display: none;">
                                                <!-- سيتم ملؤها بـ JavaScript -->
                                            </div>
                                        </div>

                                        <!-- عرض الاختيار الحالي -->
                                        <div id="selected_country_filter" class="mt-1" style="display: none;">
                                            <small class="text-info">
                                                <i class="fas fa-check-circle me-1"></i>
                                                <span id="selected_country_filter_text"></span>
                                                <button type="button" class="btn btn-sm btn-link text-danger p-0 ms-1" onclick="clearCountryFilter()">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </small>
                                        </div>
                                    </div>

                                    <!-- نوع التسليم -->
                                    <div class="col-md-3">
                                        <label for="delivery_type" class="form-label">نوع التسليم</label>
                                        <select class="form-select" id="delivery_type" name="delivery_type">
                                            <option value="">جميع الأنواع</option>
                                            <option value="cash" <?php echo ($search_params['delivery_type'] == 'cash') ? 'selected' : ''; ?>>كاش</option>
                                            <option value="bank" <?php echo ($search_params['delivery_type'] == 'bank') ? 'selected' : ''; ?>>بنكي</option>
                                            <option value="usdt" <?php echo ($search_params['delivery_type'] == 'usdt') ? 'selected' : ''; ?>>USDT</option>
                                        </select>
                                    </div>

                                    <!-- نطاق المبلغ -->
                                    <div class="col-md-3">
                                        <label for="min_amount" class="form-label">أقل مبلغ</label>
                                        <input type="number" class="form-control" id="min_amount" name="min_amount"
                                               step="0.01" value="<?php echo htmlspecialchars($search_params['min_amount']); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="max_amount" class="form-label">أكبر مبلغ</label>
                                        <input type="number" class="form-control" id="max_amount" name="max_amount"
                                               step="0.01" value="<?php echo htmlspecialchars($search_params['max_amount']); ?>">
                                    </div>

                                    <!-- اسم المستلم -->
                                    <div class="col-md-3">
                                        <label for="recipient_name" class="form-label">اسم المستلم</label>
                                        <input type="text" class="form-control" id="recipient_name" name="recipient_name"
                                               value="<?php echo htmlspecialchars($search_params['recipient_name']); ?>">
                                    </div>

                                    <!-- الفرع -->
                                    <div class="col-md-3">
                                        <label for="branch_id" class="form-label">الفرع</label>
                                        <select class="form-select" id="branch_id" name="branch_id">
                                            <option value="">جميع الفروع</option>
                                            <?php foreach ($branches as $branch): ?>
                                                <option value="<?php echo $branch['id']; ?>"
                                                        <?php echo ($search_params['branch_id'] == $branch['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($branch['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <!-- أزرار التحكم -->
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>
                                            بحث
                                        </button>
                                        <a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php" class="btn btn-secondary ms-2">
                                            <i class="fas fa-times me-2"></i>
                                            مسح الفلاتر
                                        </a>
                                        <button type="button" class="btn btn-info ms-2" onclick="setTodayFilter()">
                                            <i class="fas fa-calendar-day me-2"></i>
                                            اليوم
                                        </button>
                                        <button type="button" class="btn btn-info ms-2" onclick="setWeekFilter()">
                                            <i class="fas fa-calendar-week me-2"></i>
                                            هذا الأسبوع
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي المعاملات</h6>
                                            <h4><?php echo number_format($total_records); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-list-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">معاملات نقدية</h6>
                                            <h4><?php echo count(array_filter($transactions, function($t) { return $t['delivery_type'] == 'cash'; })); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-money-bill fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">معاملات بنكية</h6>
                                            <h4><?php echo count(array_filter($transactions, function($t) { return $t['delivery_type'] == 'bank'; })); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-university fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">معاملات USDT</h6>
                                            <h4><?php echo count(array_filter($transactions, function($t) { return $t['delivery_type'] == 'usdt'; })); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fab fa-bitcoin fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المعاملات -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    قائمة المعاملات
                                    <?php if ($total_records > 0): ?>
                                        <span class="badge bg-primary"><?php echo number_format($total_records); ?></span>
                                    <?php endif; ?>
                                </h6>
                                <div class="d-flex align-items-center">
                                    <span class="text-muted me-3">
                                        عرض <?php echo count($transactions); ?> من أصل <?php echo $total_records; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($transactions)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد معاملات</h5>
                                    <p class="text-muted">لم يتم العثور على معاملات تطابق معايير البحث المحددة</p>
                                    <?php if ($auth->hasPermission('daily_transactions.create')): ?>
                                        <a href="<?php echo BASE_URL; ?>dashboard/add_daily_transaction.php" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة أول معاملة
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>رقم المعاملة</th>
                                                <th>التاريخ</th>
                                                <th>الدولة/العملة</th>
                                                <th>المبلغ الأساسي</th>
                                                <th>سعر القص للمكتب</th>
                                                <th>سعر القص للزبون</th>
                                                <th>نوع العملية</th>
                                                <th>المبلغ الناتج للمكتب</th>
                                                <th>المبلغ الناتج للزبون</th>
                                                <th>سعر الصرف</th>
                                                <th>المبلغ للمستلم</th>
                                                <th>نوع التسليم</th>
                                                <th>حالة التسليم</th>
                                                <th>اسم المستلم</th>
                                                <th>المبلغ المراد تحويله</th>
                                                <th>الفرع</th>
                                                <th>المنشئ</th>
                                                <th>المحدث</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $total_office_amount = 0;
                                            $total_customer_amount = 0;
                                            foreach ($transactions as $transaction):
                                                $total_office_amount += (float)$transaction['office_amount'];
                                                $total_customer_amount += (float)$transaction['calculated_amount'];
                                            ?>
                                            <tr>
                                                <td><strong class="text-primary"><?php echo htmlspecialchars($transaction['transaction_number']); ?></strong></td>
                                                <td><small><?php echo date('Y-m-d', strtotime($transaction['created_at'])); ?><br><span class="text-muted"><?php echo date('H:i', strtotime($transaction['created_at'])); ?></span></small></td>
                                                <td><span class="badge bg-secondary"><?php echo htmlspecialchars($transaction['currency_code']); ?></span><br><small class="text-muted"><?php echo htmlspecialchars($transaction['country_name_ar']); ?></small></td>
                                                <td><strong><?php echo number_format($transaction['base_amount'], 2); ?></strong></td>
                                                <td><span class="text-info"><?php echo number_format($transaction['office_rate'], 6); ?></span></td>
                                                <td><span class="text-info"><?php echo number_format($transaction['customer_rate'], 6); ?></span></td>
                                                <td><span class="badge <?php echo $transaction['operation_type'] == 'multiply' ? 'bg-success' : 'bg-info'; ?>"><?php echo $transaction['operation_type'] == 'multiply' ? 'ضرب' : 'قسمة'; ?></span></td>
                                                <td><strong><?php echo number_format($transaction['office_amount'], 2); ?></strong></td>
                                                <td><strong><?php echo number_format($transaction['calculated_amount'], 2); ?></strong></td>
                                                <td><?php echo number_format($transaction['exchange_rate'], 6); ?></td>
                                                <td><strong class="text-success"><?php echo number_format($transaction['recipient_amount']); ?></strong></td>
                                                <td>
                                                    <?php
                                                    $delivery_badges = [
                                                        'cash' => 'bg-success',
                                                        'bank' => 'bg-primary',
                                                        'usdt' => 'bg-warning'
                                                    ];
                                                    $delivery_names = [
                                                        'cash' => 'كاش',
                                                        'bank' => 'بنكي',
                                                        'usdt' => 'USDT'
                                                    ];
                                                    ?>
                                                    <span class="badge <?php echo $delivery_badges[$transaction['delivery_type']]; ?>">
                                                        <?php echo $delivery_names[$transaction['delivery_type']]; ?>
                                                    </span>
                                                </td>
                                                <td><span class="badge <?php echo $transaction['delivery_status'] == 'مستلم' ? 'bg-success' : 'bg-warning'; ?>"><?php echo htmlspecialchars($transaction['delivery_status']); ?></span></td>
                                                <td><?php echo htmlspecialchars($transaction['recipient_name']); ?></td>
                                                <td><?php echo $transaction['transfer_amount'] ? number_format($transaction['transfer_amount'], 2) : '-'; ?></td>
                                                <td><?php echo htmlspecialchars($transaction['branch_name'] ?? '-'); ?></td>
                                                <td><small><?php echo htmlspecialchars($transaction['created_by_name'] ?? 'غير محدد'); ?><br><span class="text-muted"><?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?></span></small></td>
                                                <td>
                                                    <?php if (isset($transaction['updated_at']) && $transaction['updated_at'] && $transaction['updated_at'] != $transaction['created_at']): ?>
                                                        <small><?php echo htmlspecialchars($transaction['updated_by_name'] ?? 'غير محدد'); ?><br><span class="text-muted"><?php echo date('Y-m-d H:i', strtotime($transaction['updated_at'])); ?></span></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="<?php echo BASE_URL; ?>dashboard/view_daily_transaction.php?id=<?php echo $transaction['id']; ?>" class="btn btn-outline-info" title="عرض التفاصيل"><i class="fas fa-eye"></i></a>
                                                        <?php if ($auth->hasPermission('daily_transactions.edit')): ?>
                                                            <a href="<?php echo BASE_URL; ?>dashboard/edit_daily_transaction.php?id=<?php echo $transaction['id']; ?>" class="btn btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></a>
                                                        <?php endif; ?>
                                                        <?php if ($auth->hasPermission('daily_transactions.delete')): ?>
                                                            <button type="button" class="btn btn-outline-danger" onclick="confirmDelete(<?php echo $transaction['id']; ?>, '<?php echo htmlspecialchars($transaction['transaction_number']); ?>')" title="حذف"><i class="fas fa-trash"></i></button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="7" class="text-end fw-bold">المجموع:</td>
                                                <td class="fw-bold text-info"><?php echo number_format($total_office_amount, 2); ?></td>
                                                <td class="fw-bold text-info"><?php echo number_format($total_customer_amount, 2); ?></td>
                                                <td class="fw-bold text-success" colspan="1">صافي الأرباح:<br><?php echo number_format($total_office_amount - $total_customer_amount, 2); ?></td>
                                                <td colspan="9"></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- التصفح -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تصفح المعاملات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <!-- الصفحة السابقة -->
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <!-- أرقام الصفحات -->
                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);

                                if ($start_page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                                    </li>
                                    <?php if ($start_page > 2): ?>
                                        <li class="page-item disabled"><span class="page-link">...</span></li>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                    <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($end_page < $total_pages): ?>
                                    <?php if ($end_page < $total_pages - 1): ?>
                                        <li class="page-item disabled"><span class="page-link">...</span></li>
                                    <?php endif; ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>"><?php echo $total_pages; ?></a>
                                    </li>
                                <?php endif; ?>

                                <!-- الصفحة التالية -->
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
</main>
<!-- /.main-content -->

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المعاملة رقم <strong id="transactionNumber"></strong>؟</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم نقل المعاملة إلى قائمة المعاملات المحذوفة ولن يمكن استرجاعها من هذه الصفحة.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        إلغاء
                    </button>
                    <a href="#" id="confirmDeleteBtn" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف المعاملة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // تأكيد الحذف
        function confirmDelete(transactionId, transactionNumber) {
            document.getElementById('transactionNumber').textContent = transactionNumber;
            document.getElementById('confirmDeleteBtn').href = '?delete=' + transactionId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // فلاتر سريعة للتاريخ
        function setTodayFilter() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('date_from').value = today;
            document.getElementById('date_to').value = today;
        }

        function setWeekFilter() {
            const today = new Date();
            const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
            const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));

            document.getElementById('date_from').value = weekStart.toISOString().split('T')[0];
            document.getElementById('date_to').value = weekEnd.toISOString().split('T')[0];
        }

        // تحديث تلقائي للصفحة كل 5 دقائق (اختياري)
        // setInterval(function() {
        //     if (!document.hidden) {
        //         location.reload();
        //     }
        // }, 300000);

        // إضافة تأثيرات بصرية للجدول
        document.addEventListener('DOMContentLoaded', function() {
            // تمييز الصفوف عند التمرير
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f8f9fa';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });

            // إضافة tooltips للأزرار
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
            const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // تهيئة البحث التفاعلي للدول
            initCountryFilterSearch();
        });

        // بيانات الدول والعملات للفلترة
        const countriesFilterData = [
            { id: '', name_ar: 'جميع الدول', currency_code: '', searchText_ar: 'جميع الدول', searchText_en: 'all' },
            <?php foreach ($countries as $country): ?>
            {
                id: <?php echo $country['id']; ?>,
                name_ar: "<?php echo htmlspecialchars($country['name_ar']); ?>",
                currency_code: "<?php echo htmlspecialchars($country['currency_code']); ?>",
                searchText_ar: "<?php echo htmlspecialchars($country['name_ar']); ?>",
                searchText_en: "<?php echo htmlspecialchars(strtolower($country['currency_code'])); ?>"
            },
            <?php endforeach; ?>
        ];

        // البحث التفاعلي في الدول للفلترة
        function initCountryFilterSearch() {
            const searchInput = document.getElementById('country_search_filter');
            const resultsDiv = document.getElementById('country_filter_results');
            const hiddenInput = document.getElementById('country_id');
            const selectedDiv = document.getElementById('selected_country_filter');
            const selectedText = document.getElementById('selected_country_filter_text');

            // إذا كان هناك قيمة محددة مسبقاً
            const preselectedId = hiddenInput.value;
            if (preselectedId) {
                const preselectedCountry = countriesFilterData.find(c => c.id == preselectedId);
                if (preselectedCountry) {
                    showSelectedCountryFilter(preselectedCountry);
                }
            }

            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                const lowerCaseQuery = query.toLowerCase();

                if (query.length < 1) {
                    resultsDiv.style.display = 'none';
                    return;
                }

                const filteredCountries = countriesFilterData.filter(country =>
                    country.searchText_ar.includes(query) ||
                    country.searchText_en.includes(lowerCaseQuery)
                );

                displayFilterSearchResults(filteredCountries);
            });

            searchInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    const query = this.value.trim();
                    const lowerCaseQuery = query.toLowerCase();
                    const filteredCountries = countriesFilterData.filter(country =>
                        country.searchText_ar.includes(query) ||
                        country.searchText_en.includes(lowerCaseQuery)
                    );
                    displayFilterSearchResults(filteredCountries);
                } else {
                    // عرض جميع الخيارات عند التركيز بدون نص
                    displayFilterSearchResults(countriesFilterData.slice(0, 8));
                }
            });

            // إخفاء النتائج عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                    resultsDiv.style.display = 'none';
                }
            });
        }

        function displayFilterSearchResults(countries) {
            const resultsDiv = document.getElementById('country_filter_results');

            if (countries.length === 0) {
                resultsDiv.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                resultsDiv.style.display = 'block';
                return;
            }

            let html = '';
            countries.slice(0, 8).forEach(country => { // عرض أول 8 نتائج فقط
                html += `
                    <div class="dropdown-item country-filter-option"
                         data-id="${country.id}"
                         data-name="${country.name_ar}"
                         data-currency="${country.currency_code}"
                         style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span>${country.name_ar}</span>
                                ${country.currency_code ? `<small class="text-muted"> - ${country.currency_code}</small>` : ''}
                            </div>
                            ${country.currency_code ? `<span class="badge bg-secondary">${country.currency_code}</span>` : ''}
                        </div>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';

            // إضافة مستمعي الأحداث للخيارات
            resultsDiv.querySelectorAll('.country-filter-option').forEach(option => {
                option.addEventListener('click', function() {
                    const countryData = {
                        id: this.dataset.id,
                        name_ar: this.dataset.name,
                        currency_code: this.dataset.currency
                    };
                    selectCountryFilter(countryData);
                });
            });
        }

        function selectCountryFilter(country) {
            const searchInput = document.getElementById('country_search_filter');
            const hiddenInput = document.getElementById('country_id');
            const resultsDiv = document.getElementById('country_filter_results');

            // تعيين القيم
            hiddenInput.value = country.id;
            searchInput.value = '';
            resultsDiv.style.display = 'none';

            // عرض الاختيار
            showSelectedCountryFilter(country);
        }

        function showSelectedCountryFilter(country) {
            const selectedDiv = document.getElementById('selected_country_filter');
            const selectedText = document.getElementById('selected_country_filter_text');
            const searchInput = document.getElementById('country_search_filter');

            if (country.id === '') {
                selectedText.innerHTML = 'جميع الدول';
            } else {
                selectedText.innerHTML = `${country.name_ar} - ${country.currency_code}`;
            }
            selectedDiv.style.display = 'block';
            searchInput.style.display = 'none';
        }

        function clearCountryFilter() {
            const searchInput = document.getElementById('country_search_filter');
            const hiddenInput = document.getElementById('country_id');
            const selectedDiv = document.getElementById('selected_country_filter');
            const resultsDiv = document.getElementById('country_filter_results');

            hiddenInput.value = '';
            searchInput.value = '';
            searchInput.style.display = 'block';
            selectedDiv.style.display = 'none';
            resultsDiv.style.display = 'none';
        }
    </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
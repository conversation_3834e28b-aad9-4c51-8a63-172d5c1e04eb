<?php
/**
 * Fix movement details display issues
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';

echo "<h2>إصلاح عرض تفاصيل الحركات المالية</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص البيانات الخام في قاعدة البيانات</h3>\n";
    
    // Check raw data in cash_movements table
    $result = $conn->query("
        SELECT id, movement_type, amount, description, reference_number, user_id, created_at
        FROM cash_movements 
        ORDER BY id DESC 
        LIMIT 5
    ");
    
    if ($result) {
        echo "<p><strong>البيانات الخام من جدول cash_movements:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID</th><th>النوع</th><th>المبلغ</th><th>الوصف</th><th>رقم المرجع</th><th>المستخدم</th><th>التاريخ</th>\n";
        echo "</tr>\n";
        
        $hasEmptyData = false;
        while ($row = $result->fetch_assoc()) {
            $description = $row['description'] ?? '';
            $reference = $row['reference_number'] ?? '';
            
            if (empty($description) || empty($reference)) {
                $hasEmptyData = true;
            }
            
            echo "<tr>\n";
            echo "<td>{$row['id']}</td>\n";
            echo "<td>{$row['movement_type']}</td>\n";
            echo "<td>{$row['amount']}</td>\n";
            echo "<td>" . (empty($description) ? '<span style="color: red;">فارغ</span>' : htmlspecialchars($description)) . "</td>\n";
            echo "<td>" . (empty($reference) ? '<span style="color: red;">فارغ</span>' : htmlspecialchars($reference)) . "</td>\n";
            echo "<td>{$row['user_id']}</td>\n";
            echo "<td>{$row['created_at']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        if ($hasEmptyData) {
            echo "<p style='color: orange;'>⚠ تم العثور على حركات بدون وصف أو رقم مرجع</p>\n";
            
            echo "<h3>2. تحديث الحركات الفارغة</h3>\n";
            
            // Update empty descriptions
            $updateDescSql = "UPDATE cash_movements 
                             SET description = CONCAT('حركة مالية - ', movement_type, ' - ', DATE(created_at))
                             WHERE description IS NULL OR description = ''";
            
            if ($conn->query($updateDescSql)) {
                $affectedRows = $conn->affected_rows;
                echo "<p style='color: green;'>✓ تم تحديث $affectedRows حركة بوصف افتراضي</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في تحديث الأوصاف الفارغة</p>\n";
            }
            
            // Update empty reference numbers
            $updateRefSql = "UPDATE cash_movements 
                            SET reference_number = CONCAT(UPPER(LEFT(movement_type, 3)), '_', id, '_', UNIX_TIMESTAMP(created_at))
                            WHERE reference_number IS NULL OR reference_number = ''";
            
            if ($conn->query($updateRefSql)) {
                $affectedRows = $conn->affected_rows;
                echo "<p style='color: green;'>✓ تم تحديث $affectedRows حركة برقم مرجع افتراضي</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في تحديث أرقام المرجع الفارغة</p>\n";
            }
        } else {
            echo "<p style='color: green;'>✓ جميع الحركات تحتوي على بيانات كاملة</p>\n";
        }
    }
    
    echo "<h3>3. اختبار دالة getCashBoxMovements</h3>\n";
    
    // Get a cash box for testing
    $cashBoxResult = $conn->query("SELECT id, name FROM cash_boxes ORDER BY id DESC LIMIT 1");
    if ($cashBoxResult && $cashBoxResult->num_rows > 0) {
        $cashBox = $cashBoxResult->fetch_assoc();
        $cashBoxId = $cashBox['id'];
        
        echo "<p><strong>اختبار مع الصندوق:</strong> {$cashBox['name']} (ID: $cashBoxId)</p>\n";
        
        $movements = $cashManager->getCashBoxMovements($cashBoxId, [], 5, 0);
        
        echo "<p><strong>عدد الحركات المسترجعة:</strong> " . count($movements) . "</p>\n";
        
        if (!empty($movements)) {
            echo "<p><strong>تفاصيل الحركات المسترجعة:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>ID</th><th>النوع</th><th>المبلغ</th><th>الوصف</th><th>رقم المرجع</th><th>المستخدم</th><th>الرصيد بعد</th>\n";
            echo "</tr>\n";
            
            foreach ($movements as $movement) {
                $movementType = $movement['type'] ?? $movement['movement_type'] ?? 'غير محدد';
                $amount = number_format((float)($movement['amount'] ?? 0), 2);
                $description = $movement['description'] ?? '';
                $reference = $movement['reference_number'] ?? '';
                $userName = $movement['user_name'] ?? '';
                $balanceAfter = number_format((float)($movement['balance_after'] ?? 0), 2);
                
                echo "<tr>\n";
                echo "<td>{$movement['id']}</td>\n";
                echo "<td>$movementType</td>\n";
                echo "<td>$amount</td>\n";
                echo "<td>" . (empty($description) ? '<span style="color: red;">فارغ</span>' : htmlspecialchars($description)) . "</td>\n";
                echo "<td>" . (empty($reference) ? '<span style="color: red;">فارغ</span>' : htmlspecialchars($reference)) . "</td>\n";
                echo "<td>" . (empty($userName) ? '<span style="color: red;">غير محدد</span>' : htmlspecialchars($userName)) . "</td>\n";
                echo "<td>$balanceAfter</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
            
            // Check for issues
            $issues = [];
            foreach ($movements as $movement) {
                if (empty($movement['description'])) {
                    $issues[] = "الحركة {$movement['id']}: وصف فارغ";
                }
                if (empty($movement['reference_number'])) {
                    $issues[] = "الحركة {$movement['id']}: رقم مرجع فارغ";
                }
                if (empty($movement['user_name'])) {
                    $issues[] = "الحركة {$movement['id']}: اسم مستخدم فارغ";
                }
            }
            
            if (!empty($issues)) {
                echo "<h3>4. المشاكل المكتشفة</h3>\n";
                echo "<ul>\n";
                foreach ($issues as $issue) {
                    echo "<li style='color: red;'>$issue</li>\n";
                }
                echo "</ul>\n";
            } else {
                echo "<p style='color: green;'>✓ لا توجد مشاكل في البيانات المسترجعة</p>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ لم يتم استرجاع أي حركات</p>\n";
        }
    }
    
    echo "<h3>5. فحص ربط المستخدمين</h3>\n";
    
    // Check user linkage
    $userLinkResult = $conn->query("
        SELECT cm.id, cm.user_id, u.full_name
        FROM cash_movements cm
        LEFT JOIN users u ON u.id = cm.user_id
        WHERE cm.user_id IS NOT NULL
        ORDER BY cm.id DESC
        LIMIT 5
    ");
    
    if ($userLinkResult) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID الحركة</th><th>ID المستخدم</th><th>اسم المستخدم</th><th>الحالة</th>\n";
        echo "</tr>\n";
        
        while ($row = $userLinkResult->fetch_assoc()) {
            $status = !empty($row['full_name']) ? '✓ مربوط' : '✗ غير مربوط';
            $statusColor = !empty($row['full_name']) ? 'green' : 'red';
            
            echo "<tr>\n";
            echo "<td>{$row['id']}</td>\n";
            echo "<td>{$row['user_id']}</td>\n";
            echo "<td>" . htmlspecialchars($row['full_name'] ?? 'غير موجود') . "</td>\n";
            echo "<td style='color: $statusColor;'>$status</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>6. إنشاء حركات تجريبية بتفاصيل كاملة</h3>\n";
    
    if (isset($cashBoxId)) {
        $testMovements = [
            [
                'type' => 'deposit',
                'amount' => 500.00,
                'description' => 'إيداع تجريبي مع تفاصيل كاملة - ' . date('Y-m-d H:i:s'),
                'reference' => 'TEST_DEPOSIT_' . time()
            ],
            [
                'type' => 'withdrawal',
                'amount' => 100.00,
                'description' => 'سحب تجريبي مع تفاصيل كاملة - ' . date('Y-m-d H:i:s'),
                'reference' => 'TEST_WITHDRAWAL_' . time()
            ]
        ];
        
        foreach ($testMovements as $i => $testMovement) {
            $result = $cashManager->addCashMovement(
                $cashBoxId,
                $testMovement['type'],
                $testMovement['amount'],
                $testMovement['description'],
                $testMovement['reference'],
                1 // User ID
            );
            
            if ($result['success']) {
                echo "<p style='color: green;'>✓ تم إنشاء حركة تجريبية " . ($i + 1) . " بنجاح (ID: {$result['movement_id']})</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إنشاء حركة تجريبية " . ($i + 1) . ": " . ($result['error'] ?? 'خطأ غير معروف') . "</p>\n";
            }
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح عرض تفاصيل الحركات!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    if (isset($cashBoxId)) {
        echo "<li><a href='../dashboard/cash_box_history.php?id=$cashBoxId' target='_blank'>اختبر صفحة تاريخ الصندوق</a></li>\n";
    }
    echo "<li><a href='../dashboard/cash.php' target='_blank'>العودة لصفحة الصناديق</a></li>\n";
    echo "<li><a href='test_movements_display_final.php' target='_blank'>اختبار شامل للعرض</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

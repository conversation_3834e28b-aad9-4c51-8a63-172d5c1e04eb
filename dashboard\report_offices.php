<?php
/**
 * Office Reports
 * تقارير المكاتب
 */
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/office_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

// التحقق من الصلاحيات
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// التحقق من صلاحيات عرض التقارير
if (!$auth->hasPermission('reports.view')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب لعرض التقارير');
    redirect('dashboard/index.php');
}

// إنشاء مديري البيانات
$db = new Database();
$officeManager = new OfficeManager($db);
$currencyManager = new CurrencyManager($db);

// الحصول على المكاتب
$filters = [
    'is_active' => isset($_GET['show_inactive']) ? null : 1,
    'search' => $_GET['search'] ?? ''
];

$offices = $officeManager->getAllOffices($filters);
$currencies = $currencyManager->getAllCurrencies(true);

// تسجيل عملية عرض تقرير المكاتب
ActivityHelper::logView('reports', 'عرض تقرير المكاتب', $filters);

// تجهيز بيانات الرسوم البيانية
$officeNames = [];
$officeBalances = [];
$officeOperationsCount = [];

// تحليل بيانات المكاتب
$activeOffices = 0;
$inactiveOffices = 0;
$totalOperations = 0;

foreach ($offices as $office) {
    // إحصاء المكاتب النشطة وغير النشطة
    if ($office['is_active']) {
        $activeOffices++;
    } else {
        $inactiveOffices++;
    }
    
    // الحصول على أرصدة المكتب
    $balance = $officeManager->calculateOfficeBalance($office['id']);
    
    // الحصول على عمليات المكتب
    $operations = $officeManager->getOfficeOperations($office['id']);
    $operationsCount = count($operations);
    $totalOperations += $operationsCount;
    
    // تجهيز بيانات الرسوم البيانية
    $officeNames[] = $office['office_name'];
    $officeOperationsCount[] = $operationsCount;
    
    // حساب إجمالي الرصيد بالعملة الافتراضية
    $totalBalance = 0;
    if (is_array($balance)) {
        foreach ($balance as $currencyBalance) {
            // تأكد من أن البيانات متوفرة في الشكل المتوقع
            if (isset($currencyBalance['currency_code']) && isset($currencyBalance['balance'])) {
                if ($currencyBalance['currency_code'] === 'USD') {
                    $totalBalance = (float)$currencyBalance['balance'];
                    break;
                }
            }
        }
    }
    
    $officeBalances[] = $totalBalance;
}

$pageTitle = 'تقرير المكاتب';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-building"></i> تقرير المكاتب</h3>
        <div>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>
    
    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">إجمالي المكاتب</h6>
                            <h4 class="mb-0"><?php echo count($offices); ?></h4>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-building text-primary fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">المكاتب النشطة</h6>
                            <h4 class="mb-0"><?php echo $activeOffices; ?></h4>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-danger h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">المكاتب غير النشطة</h6>
                            <h4 class="mb-0"><?php echo $inactiveOffices; ?></h4>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-times-circle text-danger fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">إجمالي العمليات</h6>
                            <h4 class="mb-0"><?php echo $totalOperations; ?></h4>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-exchange-alt text-info fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- البحث والفلترة -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0">البحث والفلترة</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" placeholder="اسم المكتب، اسم المدير، رقم الهاتف..." value="<?php echo htmlspecialchars($filters['search']); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">عرض المكاتب غير النشطة</label>
                    <div class="form-check form-switch mt-2">
                        <input class="form-check-input" type="checkbox" name="show_inactive" id="showInactive" <?php echo isset($_GET['show_inactive']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="showInactive">عرض المكاتب غير النشطة</label>
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <div class="d-grid gap-2 d-md-flex w-100">
                        <button type="submit" class="btn btn-primary flex-grow-1">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <a href="report_offices.php" class="btn btn-secondary flex-grow-1">
                            <i class="fas fa-redo me-1"></i> إعادة تعيين
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">توزيع عمليات المكاتب</h5>
                </div>
                <div class="card-body">
                    <canvas id="officeOperationsChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">أرصدة المكاتب</h5>
                </div>
                <div class="card-body">
                    <canvas id="officeBalanceChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول المكاتب -->
    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <h5 class="mb-0">قائمة المكاتب</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>اسم المكتب</th>
                            <th>المدير</th>
                            <th>رقم الهاتف</th>
                            <th>العنوان</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($offices)): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4 text-muted">
                                    <i class="fas fa-building fa-3x mb-3"></i>
                                    <p>لا توجد مكاتب مطابقة للبحث</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($offices as $index => $office): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td class="fw-bold"><?php echo htmlspecialchars($office['office_name']); ?></td>
                                    <td><?php echo htmlspecialchars($office['manager_name']); ?></td>
                                    <td><?php echo htmlspecialchars($office['phone_number']); ?></td>
                                    <td><?php echo htmlspecialchars($office['address']); ?></td>
                                    <td>
                                        <span class="badge <?php echo $office['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $office['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="office_details.php?id=<?php echo $office['id']; ?>" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- إضافة مكتبة Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الألوان
    const primaryColor = '#4e73df';
    const successColor = '#1cc88a';
    const infoColor = '#36b9cc';
    const warningColor = '#f6c23e';
    const dangerColor = '#e74a3b';
    const secondaryColor = '#858796';
    
    // توليد ألوان عشوائية للرسوم البيانية
    function generateColors(count) {
        const colors = [];
        const baseColors = [primaryColor, successColor, infoColor, warningColor, dangerColor, secondaryColor];
        
        for (let i = 0; i < count; i++) {
            if (i < baseColors.length) {
                colors.push(baseColors[i]);
            } else {
                // توليد لون عشوائي إذا كان عدد المكاتب أكبر من عدد الألوان المتاحة
                const r = Math.floor(Math.random() * 200) + 55;
                const g = Math.floor(Math.random() * 200) + 55;
                const b = Math.floor(Math.random() * 200) + 55;
                colors.push(`rgba(${r}, ${g}, ${b}, 0.8)`);
            }
        }
        
        return colors;
    }
    
    // رسم بياني لتوزيع عمليات المكاتب
    const officeOperationsCtx = document.getElementById('officeOperationsChart').getContext('2d');
    const officeNames = <?php echo json_encode(array_values(array_map('htmlspecialchars', $officeNames))); ?>;
    const officeOperations = <?php echo json_encode(array_values(array_map('intval', $officeOperationsCount))); ?>;
    const officeColors = generateColors(officeNames.length);
    
    new Chart(officeOperationsCtx, {
        type: 'bar',
        data: {
            labels: officeNames,
            datasets: [{
                label: 'عدد العمليات',
                data: officeOperations,
                backgroundColor: officeColors,
                borderWidth: 0,
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
    
    // رسم بياني لأرصدة المكاتب
    const officeBalanceCtx = document.getElementById('officeBalanceChart').getContext('2d');
    const officeBalances = <?php echo json_encode(array_map('floatval', $officeBalances)); ?>;
    
    new Chart(officeBalanceCtx, {
        type: 'bar',
        data: {
            labels: officeNames,
            datasets: [{
                label: 'الرصيد',
                data: officeBalances,
                backgroundColor: officeColors,
                borderWidth: 0,
                borderRadius: 4
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                y: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
    
    // تفعيل التبديل التلقائي للنموذج عند تغيير حالة المكاتب غير النشطة
    document.getElementById('showInactive').addEventListener('change', function() {
        this.closest('form').submit();
    });
});
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
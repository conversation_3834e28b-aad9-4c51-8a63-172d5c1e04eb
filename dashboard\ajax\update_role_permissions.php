<?php
/**
 * تحديث صلاحيات دور عبر AJAX
 * Update Role Permissions via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/role_manager.php';
require_once __DIR__ . '/../../includes/activity_helper.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.permissions')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لإدارة صلاحيات الأدوار'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

try {
    // قراءة البيانات JSON
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        echo json_encode([
            'success' => false,
            'message' => 'بيانات غير صحيحة'
        ]);
        exit;
    }
    
    $roleId = (int)($data['role_id'] ?? 0);
    $permissionIds = $data['permission_ids'] ?? [];
    
    if (!$roleId) {
        echo json_encode([
            'success' => false,
            'message' => 'معرف الدور مطلوب'
        ]);
        exit;
    }
    
    // منع تعديل صلاحيات دور الأدمن من قبل غير الأدمن
    if ($roleId == 1 && $auth->getCurrentUser()['role_id'] != 1) {
        echo json_encode([
            'success' => false,
            'message' => 'لا يمكن تعديل صلاحيات دور مدير النظام'
        ]);
        exit;
    }
    
    $db = new Database();
    $roleMgr = new RoleManager($db);
    
    // التحقق من وجود الدور
    $role = $roleMgr->getRoleById($roleId);
    if (!$role) {
        echo json_encode([
            'success' => false,
            'message' => 'الدور غير موجود'
        ]);
        exit;
    }
    
    // جلب الصلاحيات الحالية للمقارنة
    $currentPermissions = $roleMgr->getRolePermissions($roleId);
    $currentPermissionIds = array_column($currentPermissions, 'id');
    
    // تحديث صلاحيات الدور
    $success = $roleMgr->updateRolePermissions($roleId, $permissionIds);
    
    if ($success) {
        // تسجيل العملية
        ActivityHelper::logUpdate(
            'role_permissions',
            "صلاحيات الدور: " . $role['name'],
            ['permission_ids' => $currentPermissionIds],
            ['permission_ids' => $permissionIds],
            $roleId
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث صلاحيات الدور بنجاح'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'فشل في تحديث صلاحيات الدور'
        ]);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    ActivityHelper::logError(
        'role_permissions',
        'update_role_permissions',
        'فشل في تحديث صلاحيات الدور: ' . $e->getMessage(),
        [
            'attempted_data' => $data ?? null,
            'error' => $e->getMessage()
        ]
    );
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

<?php
/**
 * اختبار إصلاح البحث التفاعلي للدول
 * Test Country Search Fix
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح البحث التفاعلي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .test-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-tools text-success"></i>
            اختبار إصلاح البحث التفاعلي للدول
        </h1>
        
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle me-2"></i>تم إصلاح المشكلة</h5>
            <p class="mb-2">تم إصلاح خطأ JavaScript التالي:</p>
            <div class="code-block text-danger mb-2">
                <strong>الخطأ السابق:</strong><br>
                Uncaught ReferenceError: updateCalculations is not defined
            </div>
            <div class="code-block text-success">
                <strong>الإصلاح:</strong><br>
                تم تغيير <code>updateCalculations()</code> إلى <code>calculateAmounts()</code>
            </div>
        </div>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '', $type = 'normal') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                $class = $type == 'warning' ? 'test-warning' : 'test-error';
                $icon = $type == 'warning' ? 'exclamation-triangle' : 'times-circle';
                echo "<div class='test-result {$class}'>";
                echo "<i class='fas fa-{$icon} me-2'></i>";
                echo "<strong>" . ($type == 'warning' ? '⚠' : '✗') . " {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        // اختبار الملفات المحدثة
        showInfo("<strong>فحص الإصلاحات في الملفات:</strong>");
        
        $files_to_check = [
            'dashboard/add_daily_transaction.php' => [
                'description' => 'صفحة إضافة معاملة',
                'check_functions' => ['calculateAmounts', 'selectCountry', 'clearCountrySelection'],
                'avoid_functions' => ['updateCalculations']
            ],
            'dashboard/edit_daily_transaction.php' => [
                'description' => 'صفحة تعديل معاملة', 
                'check_functions' => ['calculateAmounts', 'selectCountry', 'clearCountrySelection'],
                'avoid_functions' => ['updateCalculations']
            ]
        ];
        
        foreach ($files_to_check as $file => $config) {
            $file_path = __DIR__ . '/../' . $file;
            $exists = file_exists($file_path);
            
            if (!$exists) {
                showResult($config['description'], false, "الملف غير موجود");
                continue;
            }
            
            $content = file_get_contents($file_path);
            
            // فحص وجود الدوال المطلوبة
            $functions_found = 0;
            foreach ($config['check_functions'] as $function) {
                if (strpos($content, $function) !== false) {
                    $functions_found++;
                }
            }
            
            // فحص عدم وجود الدوال المحظورة
            $bad_functions_found = 0;
            foreach ($config['avoid_functions'] as $function) {
                if (strpos($content, $function) !== false) {
                    $bad_functions_found++;
                }
            }
            
            if ($functions_found == count($config['check_functions']) && $bad_functions_found == 0) {
                showResult($config['description'], true, "جميع الدوال صحيحة ولا توجد أخطاء");
            } elseif ($bad_functions_found > 0) {
                showResult($config['description'], false, "يحتوي على دوال محظورة: " . implode(', ', $config['avoid_functions']));
            } else {
                showResult($config['description'], false, "بعض الدوال مفقودة");
            }
        }

        // اختبار JavaScript المحدد
        showInfo("<strong>فحص كود JavaScript المحدد:</strong>");
        
        $js_checks = [
            'dashboard/add_daily_transaction.php' => [
                'selectCountry.*calculateAmounts' => 'استدعاء calculateAmounts في selectCountry',
                'clearCountrySelection.*calculateAmounts' => 'استدعاء calculateAmounts في clearCountrySelection',
                'initCountrySearch' => 'وجود دالة initCountrySearch'
            ],
            'dashboard/edit_daily_transaction.php' => [
                'selectCountry.*calculateAmounts' => 'استدعاء calculateAmounts في selectCountry',
                'clearCountrySelection.*calculateAmounts' => 'استدعاء calculateAmounts في clearCountrySelection',
                'initCountrySearch' => 'وجود دالة initCountrySearch'
            ]
        ];
        
        foreach ($js_checks as $file => $checks) {
            $file_path = __DIR__ . '/../' . $file;
            if (file_exists($file_path)) {
                $content = file_get_contents($file_path);
                
                foreach ($checks as $pattern => $description) {
                    if (preg_match('/' . $pattern . '/s', $content)) {
                        showResult($description, true, "موجود في $file");
                    } else {
                        showResult($description, false, "غير موجود في $file");
                    }
                }
            }
        }

        // اختبار قاعدة البيانات
        showInfo("<strong>اختبار قاعدة البيانات:</strong>");
        
        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
            
            // فحص بيانات الدول
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM countries WHERE is_active = 1");
            $countries_count = $stmt->fetch()['count'];
            showResult("بيانات الدول", $countries_count > 0, "يوجد {$countries_count} دولة نشطة");
            
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 95) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> تم إصلاح جميع المشاكل بنجاح";
            echo "</div>";
        } elseif ($success_rate >= 80) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> معظم المشاكل تم إصلاحها";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج مراجعة!</strong> هناك مشاكل تحتاج إصلاح";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <!-- معلومات الإصلاح -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الإصلاح
                </h5>
            </div>
            <div class="card-body">
                <h6>المشكلة الأصلية:</h6>
                <p>كان هناك خطأ في JavaScript حيث يتم استدعاء دالة <code>updateCalculations()</code> غير الموجودة.</p>
                
                <h6>الحل المطبق:</h6>
                <ul>
                    <li>تم تغيير <code>updateCalculations()</code> إلى <code>calculateAmounts()</code> في دالة <code>selectCountry</code></li>
                    <li>تم تغيير <code>updateCalculations()</code> إلى <code>calculateAmounts()</code> في دالة <code>clearCountrySelection</code></li>
                    <li>تم التأكد من أن الدالة الصحيحة موجودة ومعرفة في النطاق الصحيح</li>
                </ul>
                
                <h6>النتيجة:</h6>
                <p class="text-success mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    الآن البحث التفاعلي يعمل بشكل صحيح بدون أخطاء JavaScript
                </p>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="text-center mt-4">
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-primary btn-lg" target="_blank">
                <i class="fas fa-plus me-2"></i>
                اختبار صفحة الإضافة
            </a>
            <a href="../dashboard/edit_daily_transaction.php?id=1" class="btn btn-warning btn-lg ms-2" target="_blank">
                <i class="fas fa-edit me-2"></i>
                اختبار صفحة التعديل
            </a>
            <a href="../dashboard/daily_transactions.php" class="btn btn-success btn-lg ms-2" target="_blank">
                <i class="fas fa-list me-2"></i>
                اختبار صفحة القائمة
            </a>
        </div>

        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb me-2"></i>نصائح للاختبار:</h6>
            <ol class="mb-0">
                <li>افتح أدوات المطور في المتصفح (F12)</li>
                <li>انتقل إلى تبويب Console</li>
                <li>جرب البحث في الدول والعملات</li>
                <li>تأكد من عدم ظهور أخطاء JavaScript</li>
                <li>تأكد من أن الحسابات تتم بشكل صحيح بعد اختيار الدولة</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

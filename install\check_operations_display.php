<?php
/**
 * Check Operations Display
 * This script simulates the display of operations in the office_details.php page
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/office_manager.php';

echo "<h2>فحص عرض العمليات في صفحة تفاصيل المكتب</h2>";

try {
    $db = Database::getConnection();
    $officeMgr = new OfficeManager(new Database());
    
    // Get all offices
    $offices = $officeMgr->getAllOffices();
    
    foreach ($offices as $office) {
        echo "<h3>المكتب: " . htmlspecialchars($office['office_name']) . "</h3>";
        
        // Get operations for this office
        $operations = $officeMgr->getOfficeOperations($office['id']);
        
        // Get exchange rates for this office
        $exchangeRates = $officeMgr->getOfficeExchangeRates($office['id']);
        
        if (empty($operations)) {
            echo "<p>لا توجد عمليات لهذا المكتب</p>";
            continue;
        }
        
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>العملية</th><th>المبلغ الأساسي</th><th>لنا</th><th>لكم</th><th>is_credit</th></tr>";
        
        foreach ($operations as $operation) {
            // Use base_amount if available, otherwise calculate it
            $baseAmount = isset($operation['base_amount']) ? $operation['base_amount'] : $operation['amount'];
            $finalAmount = $operation['amount'];
            
            // If base_amount is not set (for old records), try to calculate it
            if (!isset($operation['base_amount']) || $operation['base_amount'] == 0) {
                foreach ($exchangeRates as $rate) {
                    if ($rate['operation_name'] === $operation['operation_name'] && $rate['exchange_rate_percentage'] > 0) {
                        $baseAmount = $finalAmount / (1 - $rate['exchange_rate_percentage']);
                        break;
                    }
                }
            }
            
            echo "<tr>";
            echo "<td>" . $operation['id'] . "</td>";
            echo "<td>" . htmlspecialchars($operation['operation_name']) . "</td>";
            echo "<td>" . number_format($baseAmount, 2) . "</td>";
            
            // "لنا" column
            echo "<td>";
            if ($operation['is_credit'] == 1) {
                echo "<span style='color: green; font-weight: bold;'>" . number_format($finalAmount, 2) . "</span>";
            } else {
                echo "-";
            }
            echo "</td>";
            
            // "لكم" column
            echo "<td>";
            if ($operation['is_credit'] == 0) {
                echo "<span style='color: red; font-weight: bold;'>" . number_format($finalAmount, 2) . "</span>";
            } else {
                echo "-";
            }
            echo "</td>";
            
            // Debug is_credit value
            echo "<td>" . $operation['is_credit'] . " (" . gettype($operation['is_credit']) . ")</td>";
            
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في فحص عرض العمليات</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
}
?> 
<?php
/**
 * إنشاء بيانات تجريبية لجدول paypal_transfers لاختبار التقارير
 * Create Sample PayPal Data for Testing Reports
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// إعداد الترميز
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إنشاء بيانات تجريبية لـ PayPal</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🎯 إنشاء بيانات تجريبية لـ PayPal</h1>";
echo "<p>هذا الملف سيقوم بإنشاء بيانات تجريبية في جدول paypal_transfers لاختبار صفحة التقارير.</p>";

try {
    // الاتصال بقاعدة البيانات
    $db = new Database();
    $conn = Database::getConnection();
    
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

    // التحقق من وجود الجدول
    $tableCheck = $conn->query("SHOW TABLES LIKE 'paypal_transfers'");
    if ($tableCheck->num_rows == 0) {
        echo "<div class='error'>❌ جدول paypal_transfers غير موجود. يرجى إنشاؤه أولاً.</div>";
        exit;
    }
    
    echo "<div class='info'>📋 جدول paypal_transfers موجود</div>";

    // التحقق من وجود بيانات
    $countResult = $conn->query("SELECT COUNT(*) as count FROM paypal_transfers");
    $currentCount = $countResult->fetch_assoc()['count'];
    
    echo "<div class='info'>📊 عدد السجلات الحالية: $currentCount</div>";

    // بيانات تجريبية
    $sampleData = [
        [
            'recipient_name' => 'أحمد محمد علي',
            'recipient_phone' => '+966501234567',
            'transaction_code' => 'PP001' . date('Ymd') . '001',
            'sender_name' => 'John Smith',
            'amount' => 500.00,
            'status' => 'مستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
        ],
        [
            'recipient_name' => 'فاطمة أحمد',
            'recipient_phone' => '+966502345678',
            'transaction_code' => 'PP001' . date('Ymd') . '002',
            'sender_name' => 'Sarah Johnson',
            'amount' => 750.50,
            'status' => 'لم يستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-4 days'))
        ],
        [
            'recipient_name' => 'محمد عبدالله',
            'recipient_phone' => '+966503456789',
            'transaction_code' => 'PP001' . date('Ymd') . '003',
            'sender_name' => 'Michael Brown',
            'amount' => 1200.00,
            'status' => 'مستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
        ],
        [
            'recipient_name' => 'عائشة سالم',
            'recipient_phone' => '+966504567890',
            'transaction_code' => 'PP001' . date('Ymd') . '004',
            'sender_name' => 'David Wilson',
            'amount' => 300.25,
            'status' => 'مستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
        ],
        [
            'recipient_name' => 'خالد إبراهيم',
            'recipient_phone' => '+966505678901',
            'transaction_code' => 'PP001' . date('Ymd') . '005',
            'sender_name' => 'Robert Davis',
            'amount' => 850.75,
            'status' => 'لم يستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ],
        [
            'recipient_name' => 'نورا حسن',
            'recipient_phone' => '+966506789012',
            'transaction_code' => 'PP001' . date('Ymd') . '006',
            'sender_name' => 'James Miller',
            'amount' => 450.00,
            'status' => 'مستلم',
            'created_at' => date('Y-m-d H:i:s')
        ],
        [
            'recipient_name' => 'عبدالرحمن أحمد',
            'recipient_phone' => '+966507890123',
            'transaction_code' => 'PP001' . date('Ymd') . '007',
            'sender_name' => 'William Garcia',
            'amount' => 675.30,
            'status' => 'لم يستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-6 days'))
        ],
        [
            'recipient_name' => 'مريم عبدالله',
            'recipient_phone' => '+966508901234',
            'transaction_code' => 'PP001' . date('Ymd') . '008',
            'sender_name' => 'Thomas Anderson',
            'amount' => 920.80,
            'status' => 'مستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-7 days'))
        ],
        [
            'recipient_name' => 'سعد محمد',
            'recipient_phone' => '+966509012345',
            'transaction_code' => 'PP001' . date('Ymd') . '009',
            'sender_name' => 'Christopher Lee',
            'amount' => 380.45,
            'status' => 'مستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-8 days'))
        ],
        [
            'recipient_name' => 'هند سالم',
            'recipient_phone' => '+966500123456',
            'transaction_code' => 'PP001' . date('Ymd') . '010',
            'sender_name' => 'Daniel Martinez',
            'amount' => 1150.60,
            'status' => 'لم يستلم',
            'created_at' => date('Y-m-d H:i:s', strtotime('-9 days'))
        ]
    ];

    // إدراج البيانات
    $insertedCount = 0;
    $skippedCount = 0;
    
    $insertQuery = "INSERT INTO paypal_transfers (recipient_name, recipient_phone, transaction_code, sender_name, amount, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    
    if (!$stmt) {
        throw new Exception('فشل في تحضير الاستعلام: ' . $conn->error);
    }

    foreach ($sampleData as $data) {
        // التحقق من عدم وجود رمز المعاملة
        $checkQuery = "SELECT COUNT(*) as count FROM paypal_transfers WHERE transaction_code = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param('s', $data['transaction_code']);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $exists = $checkResult->fetch_assoc()['count'] > 0;
        $checkStmt->close();
        
        if ($exists) {
            $skippedCount++;
            echo "<div class='warning'>⚠️ تم تخطي: " . htmlspecialchars($data['transaction_code']) . " (موجود بالفعل)</div>";
            continue;
        }
        
        $stmt->bind_param('ssssdss',
            $data['recipient_name'],
            $data['recipient_phone'],
            $data['transaction_code'],
            $data['sender_name'],
            $data['amount'],
            $data['status'],
            $data['created_at']
        );
        
        if ($stmt->execute()) {
            $insertedCount++;
            echo "<div class='success'>✅ تم إدراج: " . htmlspecialchars($data['recipient_name']) . " - $" . number_format($data['amount'], 2) . "</div>";
        } else {
            echo "<div class='error'>❌ فشل في إدراج: " . htmlspecialchars($data['recipient_name']) . " - " . $stmt->error . "</div>";
        }
    }
    
    $stmt->close();

    // عرض النتائج النهائية
    echo "<h2>📊 ملخص العملية</h2>";
    echo "<div class='info'>";
    echo "<p><strong>تم إدراج:</strong> $insertedCount سجل جديد</p>";
    echo "<p><strong>تم تخطي:</strong> $skippedCount سجل (موجود بالفعل)</p>";
    echo "<p><strong>إجمالي السجلات الآن:</strong> " . ($currentCount + $insertedCount) . " سجل</p>";
    echo "</div>";

    // عرض إحصائيات سريعة
    $statsQuery = "
        SELECT 
            COUNT(*) as total,
            SUM(amount) as total_amount,
            COUNT(CASE WHEN status = 'مستلم' THEN 1 END) as received_count,
            COUNT(CASE WHEN status = 'لم يستلم' THEN 1 END) as pending_count,
            SUM(CASE WHEN status = 'مستلم' THEN amount ELSE 0 END) as received_amount,
            SUM(CASE WHEN status = 'لم يستلم' THEN amount ELSE 0 END) as pending_amount
        FROM paypal_transfers
    ";
    
    $statsResult = $conn->query($statsQuery);
    if ($statsResult) {
        $stats = $statsResult->fetch_assoc();
        
        echo "<h2>📈 إحصائيات سريعة</h2>";
        echo "<div class='info'>";
        echo "<p><strong>إجمالي الحوالات:</strong> " . $stats['total'] . "</p>";
        echo "<p><strong>إجمالي المبلغ:</strong> $" . number_format($stats['total_amount'], 2) . "</p>";
        echo "<p><strong>المستلمة:</strong> " . $stats['received_count'] . " حوالة ($" . number_format($stats['received_amount'], 2) . ")</p>";
        echo "<p><strong>لم تستلم:</strong> " . $stats['pending_count'] . " حوالة ($" . number_format($stats['pending_amount'], 2) . ")</p>";
        echo "</div>";
    }

    echo "<div class='success'>";
    echo "<h3>🎉 تم إنجاز إنشاء البيانات التجريبية بنجاح!</h3>";
    echo "<p>يمكنك الآن اختبار صفحة تقارير PayPal مع البيانات الجديدة.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في إنشاء البيانات</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>انتقل إلى صفحة تقارير PayPal: <a href='../dashboard/paypal_reports.php' target='_blank'>paypal_reports.php</a></li>";
echo "<li>اختبر الفلاتر المختلفة</li>";
echo "<li>جرب تصدير التقارير بصيغ مختلفة</li>";
echo "<li>تحقق من الرسوم البيانية والإحصائيات</li>";
echo "</ol>";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>📝 ملاحظات:</h4>";
echo "<ul>";
echo "<li>هذه بيانات تجريبية للاختبار فقط</li>";
echo "<li>يمكن حذف هذه البيانات لاحقاً إذا لزم الأمر</li>";
echo "<li>أرقام المعاملات فريدة ولن تتكرر</li>";
echo "<li>التواريخ موزعة على آخر 10 أيام</li>";
echo "</ul>";
echo "</div>";

echo "</div></body></html>";
?>

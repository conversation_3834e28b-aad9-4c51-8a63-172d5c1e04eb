<?php
/**
 * Add updated_at column to cash_movements table
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إضافة عمود updated_at إلى جدول cash_movements</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص العمود الحالي</h3>\n";
    
    // Check if updated_at column exists
    $result = $conn->query("SHOW COLUMNS FROM cash_movements LIKE 'updated_at'");
    
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✓ عمود updated_at موجود بالفعل</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ عمود updated_at غير موجود، سيتم إضافته</p>\n";
        
        echo "<h3>2. إضا<PERSON>ة عمود updated_at</h3>\n";
        
        // Add updated_at column
        $addColumnSql = "ALTER TABLE cash_movements 
                        ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL 
                        AFTER created_at";
        
        if ($conn->query($addColumnSql)) {
            echo "<p style='color: green;'>✓ تم إضافة عمود updated_at بنجاح</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في إضافة عمود updated_at: " . $conn->error . "</p>\n";
            exit;
        }
        
        echo "<h3>3. تحديث القيم الموجودة</h3>\n";
        
        // Update existing records to have updated_at = created_at
        $updateSql = "UPDATE cash_movements SET updated_at = created_at WHERE updated_at IS NULL";
        
        if ($conn->query($updateSql)) {
            $affectedRows = $conn->affected_rows;
            echo "<p style='color: green;'>✓ تم تحديث $affectedRows سجل</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في تحديث السجلات الموجودة: " . $conn->error . "</p>\n";
        }
    }
    
    echo "<h3>4. فحص النتيجة</h3>\n";
    
    // Verify the column was added
    $result = $conn->query("DESCRIBE cash_movements");
    if ($result) {
        echo "<p><strong>هيكل الجدول الحالي:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Default</th>\n";
        echo "</tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            $highlight = ($row['Field'] === 'updated_at') ? 'style="background: #d4edda;"' : '';
            echo "<tr $highlight>\n";
            echo "<td>{$row['Field']}</td>\n";
            echo "<td>{$row['Type']}</td>\n";
            echo "<td>{$row['Null']}</td>\n";
            echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>5. اختبار استعلام التحديث</h3>\n";
    
    // Test the update query
    $testSql = "UPDATE cash_movements SET 
                    movement_type = ?,
                    amount = ?,
                    description = ?,
                    reference_number = ?,
                    updated_at = NOW()
                WHERE id = ?";
    
    $stmt = $conn->prepare($testSql);
    if ($stmt) {
        echo "<p style='color: green;'>✓ استعلام التحديث يعمل الآن بشكل صحيح</p>\n";
        $stmt->close();
    } else {
        echo "<p style='color: red;'>✗ ما زال هناك خطأ في استعلام التحديث: " . $conn->error . "</p>\n";
    }
    
    echo "<h3>6. اختبار تحديث تجريبي</h3>\n";
    
    // Find a movement to test with
    $result = $conn->query("SELECT id FROM cash_movements ORDER BY id DESC LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $movementId = $result->fetch_assoc()['id'];
        
        // Test update
        $testUpdateSql = "UPDATE cash_movements SET updated_at = NOW() WHERE id = ?";
        $stmt = $conn->prepare($testUpdateSql);
        if ($stmt) {
            $stmt->bind_param('i', $movementId);
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✓ تم اختبار التحديث بنجاح على الحركة رقم $movementId</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في اختبار التحديث: " . $stmt->error . "</p>\n";
            }
            $stmt->close();
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إضافة عمود updated_at بنجاح!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='test_update_movement.php' target='_blank'>اختبر دالة updateCashMovement</a></li>\n";
    echo "<li><a href='../dashboard/edit_movement.php?id=$movementId' target='_blank'>اختبر صفحة تعديل الحركة</a></li>\n";
    echo "<li><a href='check_cash_movements_table.php' target='_blank'>تحقق من هيكل الجدول</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

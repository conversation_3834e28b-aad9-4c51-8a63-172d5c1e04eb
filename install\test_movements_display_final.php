<?php
/**
 * Final test for movements display
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';

echo "<h2>اختبار نهائي لعرض الحركات المالية</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. إنشاء بيانات تجريبية كاملة</h3>\n";
    
    // Get or create a cash box
    $result = $conn->query("SELECT id, name, initial_balance FROM cash_boxes ORDER BY id DESC LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $cashBox = $result->fetch_assoc();
        $cashBoxId = $cashBox['id'];
        echo "<p style='color: green;'>✓ استخدام الصندوق الموجود: {$cashBox['name']} (ID: $cashBoxId)</p>\n";
    } else {
        echo "<p style='color: red;'>✗ لا توجد صناديق للاختبار</p>\n";
        exit;
    }
    
    echo "<h3>2. إنشاء حركات تجريبية متنوعة</h3>\n";
    
    $testMovements = [
        [
            'type' => 'deposit',
            'amount' => 1000.00,
            'description' => 'إيداع ابتدائي للاختبار',
            'reference' => 'DEP_' . time() . '_001'
        ],
        [
            'type' => 'withdrawal',
            'amount' => 250.50,
            'description' => 'سحب تجريبي',
            'reference' => 'WTH_' . time() . '_002'
        ],
        [
            'type' => 'deposit',
            'amount' => 500.75,
            'description' => 'إيداع إضافي مع وصف طويل جداً لاختبار عرض النص المقطوع في الجدول',
            'reference' => 'DEP_' . time() . '_003'
        ]
    ];
    
    $createdMovements = [];
    foreach ($testMovements as $i => $movement) {
        $result = $cashManager->addCashMovement(
            $cashBoxId,
            $movement['type'],
            $movement['amount'],
            $movement['description'],
            $movement['reference'],
            1 // User ID
        );
        
        if ($result['success']) {
            $createdMovements[] = $result['movement_id'];
            echo "<p style='color: green;'>✓ تم إنشاء حركة " . ($i + 1) . ": {$movement['type']} - {$movement['amount']}</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في إنشاء حركة " . ($i + 1) . ": " . ($result['error'] ?? 'خطأ غير معروف') . "</p>\n";
        }
    }
    
    echo "<h3>3. اختبار دالة getCashBoxMovements</h3>\n";
    
    $movements = $cashManager->getCashBoxMovements($cashBoxId, [], 10, 0);
    
    echo "<p><strong>عدد الحركات المسترجعة:</strong> " . count($movements) . "</p>\n";
    
    if (!empty($movements)) {
        echo "<h3>4. عرض الحركات بالتفصيل</h3>\n";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID</th><th>النوع</th><th>المبلغ</th><th>الوصف</th><th>المرجع</th><th>المستخدم</th><th>الرصيد بعد</th><th>التاريخ</th>\n";
        echo "</tr>\n";
        
        foreach ($movements as $movement) {
            $movementType = $movement['type'] ?? $movement['movement_type'] ?? '';
            $amount = (float)($movement['amount'] ?? 0);
            $description = $movement['description'] ?? '';
            $reference = $movement['reference_number'] ?? '';
            $userName = $movement['user_name'] ?? '';
            $balanceAfter = (float)($movement['balance_after'] ?? 0);
            $createdAt = $movement['created_at'] ?? '';
            
            $typeColor = ($movementType === 'deposit') ? 'green' : 'orange';
            $amountSign = ($movementType === 'deposit') ? '+' : '-';
            
            echo "<tr>\n";
            echo "<td>{$movement['id']}</td>\n";
            echo "<td style='color: $typeColor;'><strong>$movementType</strong></td>\n";
            echo "<td style='color: $typeColor;'>$amountSign" . number_format($amount, 2) . "</td>\n";
            echo "<td>" . htmlspecialchars(substr($description, 0, 30) . (strlen($description) > 30 ? '...' : '')) . "</td>\n";
            echo "<td><code>" . htmlspecialchars($reference) . "</code></td>\n";
            echo "<td>" . htmlspecialchars($userName ?: 'غير محدد') . "</td>\n";
            echo "<td><strong>" . number_format($balanceAfter, 2) . "</strong></td>\n";
            echo "<td>" . date('Y-m-d H:i', strtotime($createdAt)) . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        echo "<h3>5. فحص صحة البيانات</h3>\n";
        
        $firstMovement = $movements[0];
        $checks = [
            'ID موجود' => !empty($firstMovement['id']),
            'نوع الحركة محدد' => !empty($firstMovement['type'] ?? $firstMovement['movement_type']),
            'المبلغ صحيح' => is_numeric($firstMovement['amount']) && $firstMovement['amount'] > 0,
            'الوصف موجود' => !empty($firstMovement['description']),
            'رقم المرجع موجود' => !empty($firstMovement['reference_number']),
            'الرصيد محسوب' => is_numeric($firstMovement['balance_after']),
            'التاريخ صحيح' => !empty($firstMovement['created_at'])
        ];
        
        foreach ($checks as $check => $result) {
            $status = $result ? '✓' : '✗';
            $color = $result ? 'green' : 'red';
            echo "<p style='color: $color;'>$status $check</p>\n";
        }
        
        echo "<h3>6. اختبار صفحة العرض</h3>\n";
        
        $historyUrl = "../dashboard/cash_box_history.php?id=$cashBoxId";
        echo "<p><a href='$historyUrl' target='_blank' style='color: #007bff; font-weight: bold; font-size: 18px;'>🔗 اختبر صفحة تاريخ الصندوق</a></p>\n";
        
        echo "<h3>7. اختبار التصفية</h3>\n";
        
        // Test filtering
        $depositMovements = $cashManager->getCashBoxMovements($cashBoxId, ['type' => 'deposit'], 5, 0);
        $withdrawalMovements = $cashManager->getCashBoxMovements($cashBoxId, ['type' => 'withdrawal'], 5, 0);
        
        echo "<p><strong>حركات الإيداع:</strong> " . count($depositMovements) . "</p>\n";
        echo "<p><strong>حركات السحب:</strong> " . count($withdrawalMovements) . "</p>\n";
        
        // Test date filtering
        $today = date('Y-m-d');
        $todayMovements = $cashManager->getCashBoxMovements($cashBoxId, ['date_from' => $today, 'date_to' => $today], 10, 0);
        echo "<p><strong>حركات اليوم:</strong> " . count($todayMovements) . "</p>\n";
        
    } else {
        echo "<p style='color: red;'>✗ لم يتم استرجاع أي حركات</p>\n";
    }
    
    echo "<h3>8. اختبار تعديل الحركة</h3>\n";
    
    if (!empty($createdMovements)) {
        $testMovementId = $createdMovements[0];
        $editUrl = "../dashboard/edit_movement.php?id=$testMovementId";
        echo "<p><a href='$editUrl' target='_blank' style='color: #007bff; font-weight: bold;'>🔗 اختبر تعديل الحركة رقم $testMovementId</a></p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى الاختبار النهائي لعرض الحركات!</h3>\n";
    echo "<p><strong>النتيجة:</strong> النظام جاهز لعرض الحركات المالية بشكل صحيح</p>\n";
    
    echo "<p><strong>الروابط المهمة:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='$historyUrl' target='_blank'>صفحة تاريخ الصندوق</a></li>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>صفحة الصناديق الرئيسية</a></li>\n";
    if (!empty($createdMovements)) {
        echo "<li><a href='../dashboard/edit_movement.php?id={$createdMovements[0]}' target='_blank'>تعديل حركة مالية</a></li>\n";
    }
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

<?php
/**
 * <PERSON><PERSON><PERSON> to fix missing exchange_id columns in database tables
 * Run this script once to add the missing columns
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = Database::getConnection();

    echo "<h2>إصلاح قاعدة البيانات - إضافة أعمدة exchange_id</h2>\n";

    $successCount = 0;
    $errorCount = 0;

    // Manual fixes for better error handling
    $fixes = [
        [
            'description' => 'إضافة عمود exchange_id إلى cash_movements',
            'sql' => "ALTER TABLE cash_movements ADD COLUMN exchange_id INT UNSIGNED NULL AFTER user_id",
            'check' => "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'cash_movements' AND COLUMN_NAME = 'exchange_id'"
        ],
        [
            'description' => 'إضافة عمود exchange_id إلى bank_movements',
            'sql' => "ALTER TABLE bank_movements ADD COLUMN exchange_id INT UNSIGNED NULL AFTER user_id",
            'check' => "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'bank_movements' AND COLUMN_NAME = 'exchange_id'"
        ],
        [
            'description' => 'إضافة عمود transfer_id إلى cash_movements',
            'sql' => "ALTER TABLE cash_movements ADD COLUMN transfer_id INT UNSIGNED NULL AFTER exchange_id",
            'check' => "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'cash_movements' AND COLUMN_NAME = 'transfer_id'"
        ],
        [
            'description' => 'إضافة عمود transfer_id إلى bank_movements',
            'sql' => "ALTER TABLE bank_movements ADD COLUMN transfer_id INT UNSIGNED NULL AFTER exchange_id",
            'check' => "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'bank_movements' AND COLUMN_NAME = 'transfer_id'"
        ],
        [
            'description' => 'إضافة قيد خارجي لـ cash_movements.exchange_id',
            'sql' => "ALTER TABLE cash_movements ADD CONSTRAINT fk_cmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE",
            'check' => "SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'cash_movements' AND CONSTRAINT_NAME = 'fk_cmov_exchange'"
        ],
        [
            'description' => 'إضافة قيد خارجي لـ bank_movements.exchange_id',
            'sql' => "ALTER TABLE bank_movements ADD CONSTRAINT fk_bmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE",
            'check' => "SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'bank_movements' AND CONSTRAINT_NAME = 'fk_bmov_exchange'"
        ]
    ];

    foreach ($fixes as $fix) {
        try {
            // Check if already exists
            $result = $db->query($fix['check']);
            $exists = $result->fetch_row()[0] > 0;

            if ($exists) {
                echo "<p style='color: blue;'>ℹ️ موجود بالفعل: " . $fix['description'] . "</p>\n";
                $successCount++;
            } else {
                if ($db->query($fix['sql'])) {
                    echo "<p style='color: green;'>✓ تم بنجاح: " . $fix['description'] . "</p>\n";
                    $successCount++;
                } else {
                    echo "<p style='color: red;'>✗ فشل: " . $fix['description'] . " - " . $db->error . "</p>\n";
                    $errorCount++;
                }
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ خطأ: " . $fix['description'] . " - " . $e->getMessage() . "</p>\n";
            $errorCount++;
        }
    }
    
    echo "<hr>\n";
    echo "<h3>النتائج:</h3>\n";
    echo "<p>العمليات الناجحة: $successCount</p>\n";
    echo "<p>العمليات الفاشلة: $errorCount</p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold;'>تم إصلاح قاعدة البيانات بنجاح!</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>تم الإصلاح مع بعض الأخطاء. يرجى مراجعة الأخطاء أعلاه.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>\n";
}
?>

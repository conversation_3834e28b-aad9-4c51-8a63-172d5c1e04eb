<?php
/**
 * Export Incoming PayPal Transfers to PDF or Excel
 * تصدير حوالات PayPal الواردة إلى PDF أو Excel
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paypal_manager.php';
require_once __DIR__ . '/../vendor/autoload.php'; // For TCPDF

// Check authentication
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// Check permissions
if (!$auth->hasPermission('transfers.view')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب');
    redirect('dashboard.php');
}

$db = new Database();
$paypalManager = new PayPalManager($db);

// Get export format
$format = sanitize_input($_GET['format'] ?? 'pdf');
if (!in_array($format, ['pdf', 'excel'])) {
    $format = 'pdf';
}

// Get filters
$filters = [];
$search = sanitize_input($_GET['search'] ?? '');
$status = sanitize_input($_GET['status'] ?? '');
$date_from = sanitize_input($_GET['date_from'] ?? '');
$date_to = sanitize_input($_GET['date_to'] ?? '');

if (!empty($search)) $filters['search'] = $search;
if (!empty($status)) $filters['status'] = $status;
if (!empty($date_from)) $filters['date_from'] = $date_from;
if (!empty($date_to)) $filters['date_to'] = $date_to;

// Get PayPal transfers
$transfers = $paypalManager->getTransfers($filters);

// Prepare filter description
$filterDescription = "جميع حوالات PayPal الواردة";
if (!empty($date_from) && !empty($date_to)) {
    $filterDescription .= " من $date_from إلى $date_to";
} elseif (!empty($date_from)) {
    $filterDescription .= " من $date_from";
} elseif (!empty($date_to)) {
    $filterDescription .= " حتى $date_to";
}

if (!empty($status)) {
    $filterDescription .= " - الحالة: $status";
}

if (!empty($search)) {
    $filterDescription .= " - البحث: $search";
}

// Calculate totals
$totalAmount = 0;
$receivedCount = 0;
$pendingCount = 0;
foreach ($transfers as $transfer) {
    $totalAmount += $transfer['amount'];
    if ($transfer['status'] === 'مستلم') {
        $receivedCount++;
    } else {
        $pendingCount++;
    }
}

// Export to PDF
if ($format === 'pdf') {
    // Create new PDF document
    require_once(__DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
    
    // تعريف فئة PDF مخصصة لدعم اللغة العربية
    class MYPDF extends TCPDF {
        public function Header() {
            // تعيين الخط
            $this->SetFont('dejavusans', 'B', 16);
            // العنوان
            $this->Cell(0, 15, 'تقرير حوالات PayPal الواردة', 0, false, 'C', 0, '', 0, false, 'M', 'M');
        }

        public function Footer() {
            // تعيين موضع الصفحة
            $this->SetY(-15);
            // تعيين الخط
            $this->SetFont('dejavusans', 'I', 8);
            // رقم الصفحة
            $this->Cell(0, 10, 'الصفحة '.$this->getAliasNumPage().'/'.$this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
        }
    }

    // إنشاء مستند PDF جديد بدعم RTL
    $pdf = new MYPDF('L', 'mm', 'A4', true, 'UTF-8', false);

    // تعيين معلومات المستند
    $pdf->SetCreator('TrustPlus');
    $pdf->SetAuthor('TrustPlus System');
    $pdf->SetTitle('تقرير حوالات PayPal الواردة');
    $pdf->SetSubject('تقرير حوالات PayPal الواردة');
    $pdf->SetKeywords('PayPal, حوالات واردة, تقرير, تصدير');

    // تعيين بيانات الرأس الافتراضية
    $pdf->SetHeaderData('', 0, 'تقرير حوالات PayPal الواردة', $filterDescription);

    // تعيين الهوامش
    $pdf->SetMargins(10, 20, 10);
    $pdf->SetHeaderMargin(5);
    $pdf->SetFooterMargin(10);

    // تعيين فواصل الصفحة التلقائية
    $pdf->SetAutoPageBreak(TRUE, 15);

    // تعيين عامل مقياس الصورة
    $pdf->setImageScale(1.25);

    // إضافة صفحة
    $pdf->AddPage();

    // تعيين اتجاه RTL
    $pdf->setRTL(true);
    
    // تعيين الخط
    $pdf->SetFont('dejavusans', 'B', 12);
    
    // إضافة وصف التصفية
    $pdf->Cell(0, 10, $filterDescription, 0, 1, 'C');
    $pdf->Ln(5);

    // إنشاء رأس الجدول
    $pdf->SetFont('dejavusans', 'B', 10);
    $pdf->SetFillColor(240, 240, 240);
    
    // تعيين عرض الأعمدة
    $colWidth1 = 20; // ID
    $colWidth2 = 40; // اسم المستلم
    $colWidth3 = 30; // رقم الجوال
    $colWidth4 = 35; // رمز المعاملة
    $colWidth5 = 40; // اسم المرسل
    $colWidth6 = 25; // المبلغ
    $colWidth7 = 20; // الحالة
    $colWidth8 = 30; // التاريخ
    
    // رأس الجدول
    $pdf->Cell($colWidth1, 10, 'ID', 1, 0, 'C', 1);
    $pdf->Cell($colWidth2, 10, 'اسم المستلم', 1, 0, 'C', 1);
    $pdf->Cell($colWidth3, 10, 'رقم الجوال', 1, 0, 'C', 1);
    $pdf->Cell($colWidth4, 10, 'رمز المعاملة', 1, 0, 'C', 1);
    $pdf->Cell($colWidth5, 10, 'اسم المرسل', 1, 0, 'C', 1);
    $pdf->Cell($colWidth6, 10, 'المبلغ', 1, 0, 'C', 1);
    $pdf->Cell($colWidth7, 10, 'الحالة', 1, 0, 'C', 1);
    $pdf->Cell($colWidth8, 10, 'التاريخ', 1, 1, 'C', 1);

    // إضافة صفوف البيانات
    $pdf->SetFont('dejavusans', '', 9);
    
    // تحديد لون الخلفية البديلة
    $altBg = false;
    
    foreach ($transfers as $transfer) {
        // تبديل لون الخلفية
        if ($altBg) {
            $pdf->SetFillColor(245, 245, 245);
        } else {
            $pdf->SetFillColor(255, 255, 255);
        }
        $altBg = !$altBg;
        
        // بيانات الصف
        $pdf->Cell($colWidth1, 8, $transfer['id'], 1, 0, 'C', 1);
        
        // التعامل مع النصوص الطويلة
        $recipientName = $transfer['recipient_name'];
        if (strlen($recipientName) > 25) {
            $recipientName = substr($recipientName, 0, 23) . '..';
        }
        $pdf->Cell($colWidth2, 8, $recipientName, 1, 0, 'R', 1);
        
        $pdf->Cell($colWidth3, 8, $transfer['recipient_phone'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth4, 8, $transfer['transaction_code'], 1, 0, 'C', 1);
        
        $senderName = $transfer['sender_name'];
        if (strlen($senderName) > 25) {
            $senderName = substr($senderName, 0, 23) . '..';
        }
        $pdf->Cell($colWidth5, 8, $senderName, 1, 0, 'R', 1);
        
        $pdf->Cell($colWidth6, 8, number_format($transfer['amount'], 2), 1, 0, 'C', 1);
        $pdf->Cell($colWidth7, 8, $transfer['status'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth8, 8, date('Y-m-d', strtotime($transfer['created_at'])), 1, 1, 'C', 1);
    }

    // إضافة صف الإجماليات
    $pdf->SetFont('dejavusans', 'B', 10);
    $pdf->SetFillColor(240, 240, 240);
    
    // حساب العرض الإجمالي للأعمدة قبل المبلغ
    $totalWidth = $colWidth1 + $colWidth2 + $colWidth3 + $colWidth4 + $colWidth5;
    
    // حساب العرض الإجمالي للأعمدة بعد المبلغ
    $remainingWidth = $colWidth7 + $colWidth8;
    
    $pdf->Cell($totalWidth, 10, 'إجمالي المبلغ:', 1, 0, 'R', 1);
    $pdf->Cell($colWidth6, 10, number_format($totalAmount, 2), 1, 0, 'C', 1);
    $pdf->Cell($remainingWidth, 10, '', 1, 1, 'C', 1);
    
    $pdf->Cell($totalWidth, 10, 'عدد المستلم:', 1, 0, 'R', 1);
    $pdf->Cell($colWidth6, 10, $receivedCount, 1, 0, 'C', 1);
    $pdf->Cell($remainingWidth, 10, '', 1, 1, 'C', 1);
    
    $pdf->Cell($totalWidth, 10, 'عدد لم يستلم:', 1, 0, 'R', 1);
    $pdf->Cell($colWidth6, 10, $pendingCount, 1, 0, 'C', 1);
    $pdf->Cell($remainingWidth, 10, '', 1, 1, 'C', 1);

    // إضافة معلومات التذييل
    $pdf->SetFont('dejavusans', 'I', 8);
    $pdf->Cell(0, 10, 'تم إنشاء هذا التقرير بواسطة نظام TrustPlus بتاريخ ' . date('Y-m-d H:i'), 0, 1, 'C');

    // إخراج ملف PDF
    $pdf->Output('تقرير_حوالات_PayPal_الواردة_' . date('Y-m-d') . '.pdf', 'D');
    exit;
}

// Export to Excel
if ($format === 'excel') {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="تقرير_حوالات_PayPal_الواردة_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create Excel content
    echo '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>تقرير حوالات PayPal الواردة</title>
        <style>
            table { direction: rtl; border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #000; padding: 5px; text-align: right; }
            th { background-color: #f0f0f0; }
            .total { font-weight: bold; background-color: #f0f0f0; }
        </style>
    </head>
    <body>
        <h1>تقرير حوالات PayPal الواردة</h1>
        <p>' . $filterDescription . '</p>
        
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>اسم المستلم</th>
                    <th>رقم الجوال</th>
                    <th>رمز المعاملة</th>
                    <th>اسم المرسل</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($transfers as $transfer) {
        echo '<tr>
                <td>' . $transfer['id'] . '</td>
                <td>' . htmlspecialchars($transfer['recipient_name']) . '</td>
                <td>' . htmlspecialchars($transfer['recipient_phone']) . '</td>
                <td>' . htmlspecialchars($transfer['transaction_code']) . '</td>
                <td>' . htmlspecialchars($transfer['sender_name']) . '</td>
                <td>' . number_format($transfer['amount'], 2) . '</td>
                <td>' . htmlspecialchars($transfer['status']) . '</td>
                <td>' . date('Y-m-d', strtotime($transfer['created_at'])) . '</td>
            </tr>';
    }
    
    echo '</tbody>
            <tfoot>
                <tr class="total">
                    <td colspan="5" style="text-align: left;">إجمالي المبلغ:</td>
                    <td>' . number_format($totalAmount, 2) . '</td>
                    <td colspan="2"></td>
                </tr>
                <tr class="total">
                    <td colspan="5" style="text-align: left;">عدد المستلم:</td>
                    <td>' . $receivedCount . '</td>
                    <td colspan="2"></td>
                </tr>
                <tr class="total">
                    <td colspan="5" style="text-align: left;">عدد لم يستلم:</td>
                    <td>' . $pendingCount . '</td>
                    <td colspan="2"></td>
                </tr>
            </tfoot>
        </table>
    </body>
    </html>';
    
    exit;
}

// Fallback if no valid format is specified
redirect('paypal_incoming_transfers.php');
?> 
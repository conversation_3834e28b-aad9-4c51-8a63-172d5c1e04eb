<?php
/**
 * Fix cash_boxes table to use currency_code instead of currency_id
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إصلاح هيكل جدول الصناديق لاستخدام رمز العملة</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الهيكل الحالي لجدول cash_boxes</h3>\n";
    
    // Check current structure
    $result = $conn->query("DESCRIBE cash_boxes");
    if ($result) {
        echo "<p><strong>الهيكل الحالي:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th>\n";
        echo "</tr>\n";
        
        $columns = [];
        while ($row = $result->fetch_assoc()) {
            $columns[] = $row['Field'];
            echo "<tr>\n";
            echo "<td>{$row['Field']}</td>\n";
            echo "<td>{$row['Type']}</td>\n";
            echo "<td>{$row['Null']}</td>\n";
            echo "<td>{$row['Key']}</td>\n";
            echo "<td>{$row['Default']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        echo "<h3>2. تحديث هيكل الجدول</h3>\n";
        
        $hasCurrencyId = in_array('currency_id', $columns);
        $hasCurrencyCode = in_array('currency_code', $columns);
        
        echo "<p><strong>حالة الأعمدة:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>currency_id: " . ($hasCurrencyId ? '✓ موجود' : '✗ غير موجود') . "</li>\n";
        echo "<li>currency_code: " . ($hasCurrencyCode ? '✓ موجود' : '✗ غير موجود') . "</li>\n";
        echo "</ul>\n";
        
        if ($hasCurrencyId && !$hasCurrencyCode) {
            echo "<h4>أ. إضافة عمود currency_code</h4>\n";
            
            $alterSql = "ALTER TABLE cash_boxes ADD COLUMN currency_code VARCHAR(3) DEFAULT 'USD' AFTER currency_id";
            if ($conn->query($alterSql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود currency_code</p>\n";
                
                echo "<h4>ب. تحديث البيانات الموجودة</h4>\n";
                
                // Update existing records with currency codes
                $updateSql = "UPDATE cash_boxes cb 
                             LEFT JOIN currencies c ON c.id = cb.currency_id 
                             SET cb.currency_code = COALESCE(c.code, 'USD')";
                
                if ($conn->query($updateSql)) {
                    $affectedRows = $conn->affected_rows;
                    echo "<p style='color: green;'>✓ تم تحديث $affectedRows صندوق برموز العملات</p>\n";
                } else {
                    echo "<p style='color: red;'>✗ فشل في تحديث البيانات: " . $conn->error . "</p>\n";
                }
                
                echo "<h4>ج. إزالة عمود currency_id القديم</h4>\n";
                
                // Remove the old currency_id column
                $dropSql = "ALTER TABLE cash_boxes DROP COLUMN currency_id";
                if ($conn->query($dropSql)) {
                    echo "<p style='color: green;'>✓ تم إزالة عمود currency_id القديم</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ لم يتم إزالة عمود currency_id: " . $conn->error . "</p>\n";
                }
                
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عمود currency_code: " . $conn->error . "</p>\n";
            }
            
        } elseif (!$hasCurrencyId && !$hasCurrencyCode) {
            echo "<h4>إضافة عمود currency_code</h4>\n";
            
            $alterSql = "ALTER TABLE cash_boxes ADD COLUMN currency_code VARCHAR(3) DEFAULT 'USD'";
            if ($conn->query($alterSql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود currency_code</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عمود currency_code: " . $conn->error . "</p>\n";
            }
            
        } elseif ($hasCurrencyCode) {
            echo "<p style='color: green;'>✓ عمود currency_code موجود بالفعل</p>\n";
        }
    }
    
    echo "<h3>3. فحص جدول bank_accounts</h3>\n";
    
    // Check bank_accounts table
    $result = $conn->query("DESCRIBE bank_accounts");
    if ($result) {
        $bankColumns = [];
        while ($row = $result->fetch_assoc()) {
            $bankColumns[] = $row['Field'];
        }
        
        $hasBankCurrencyId = in_array('currency_id', $bankColumns);
        $hasBankCurrencyCode = in_array('currency_code', $bankColumns);
        
        echo "<p><strong>حالة أعمدة bank_accounts:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>currency_id: " . ($hasBankCurrencyId ? '✓ موجود' : '✗ غير موجود') . "</li>\n";
        echo "<li>currency_code: " . ($hasBankCurrencyCode ? '✓ موجود' : '✗ غير موجود') . "</li>\n";
        echo "</ul>\n";
        
        if ($hasBankCurrencyId && !$hasBankCurrencyCode) {
            echo "<h4>تحديث جدول bank_accounts</h4>\n";
            
            $alterSql = "ALTER TABLE bank_accounts ADD COLUMN currency_code VARCHAR(3) DEFAULT 'USD' AFTER currency_id";
            if ($conn->query($alterSql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود currency_code إلى bank_accounts</p>\n";
                
                // Update existing records
                $updateSql = "UPDATE bank_accounts ba 
                             LEFT JOIN currencies c ON c.id = ba.currency_id 
                             SET ba.currency_code = COALESCE(c.code, 'USD')";
                
                if ($conn->query($updateSql)) {
                    $affectedRows = $conn->affected_rows;
                    echo "<p style='color: green;'>✓ تم تحديث $affectedRows حساب مصرفي برموز العملات</p>\n";
                }
                
                // Remove old column
                $dropSql = "ALTER TABLE bank_accounts DROP COLUMN currency_id";
                if ($conn->query($dropSql)) {
                    echo "<p style='color: green;'>✓ تم إزالة عمود currency_id من bank_accounts</p>\n";
                }
                
            } else {
                echo "<p style='color: red;'>✗ فشل في تحديث bank_accounts: " . $conn->error . "</p>\n";
            }
        }
    }
    
    echo "<h3>4. فحص جدول cash_movements</h3>\n";
    
    // Check cash_movements table
    $result = $conn->query("DESCRIBE cash_movements");
    if ($result) {
        $movementColumns = [];
        while ($row = $result->fetch_assoc()) {
            $movementColumns[] = $row['Field'];
        }
        
        $hasMovementCurrencyId = in_array('currency_id', $movementColumns);
        
        echo "<p><strong>حالة أعمدة cash_movements:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>currency_id: " . ($hasMovementCurrencyId ? '✓ موجود' : '✗ غير موجود') . "</li>\n";
        echo "</ul>\n";
        
        if ($hasMovementCurrencyId) {
            echo "<h4>إزالة عمود currency_id من cash_movements</h4>\n";
            echo "<p style='color: orange;'>ملاحظة: العملة يتم تحديدها من الصندوق المرتبط</p>\n";
            
            $dropSql = "ALTER TABLE cash_movements DROP COLUMN currency_id";
            if ($conn->query($dropSql)) {
                echo "<p style='color: green;'>✓ تم إزالة عمود currency_id من cash_movements</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠ لم يتم إزالة عمود currency_id من cash_movements: " . $conn->error . "</p>\n";
            }
        }
    }
    
    echo "<h3>5. التحقق النهائي</h3>\n";
    
    // Final verification
    $tables = ['cash_boxes', 'bank_accounts', 'cash_movements'];
    
    foreach ($tables as $table) {
        $result = $conn->query("DESCRIBE $table");
        if ($result) {
            echo "<p><strong>الهيكل النهائي لجدول $table:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 15px;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th>\n";
            echo "</tr>\n";
            
            while ($row = $result->fetch_assoc()) {
                $highlight = (strpos($row['Field'], 'currency') !== false) ? 'background: #fff3cd;' : '';
                echo "<tr style='$highlight'>\n";
                echo "<td>{$row['Field']}</td>\n";
                echo "<td>{$row['Type']}</td>\n";
                echo "<td>{$row['Null']}</td>\n";
                echo "<td>{$row['Key']}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    }
    
    echo "<h3>6. اختبار البيانات</h3>\n";
    
    // Test data
    $result = $conn->query("SELECT id, name, currency_code FROM cash_boxes LIMIT 5");
    if ($result) {
        echo "<p><strong>عينة من الصناديق:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID</th><th>الاسم</th><th>رمز العملة</th>\n";
        echo "</tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>\n";
            echo "<td>{$row['id']}</td>\n";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>\n";
            echo "<td><strong>{$row['currency_code']}</strong></td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح هيكل الجداول!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='update_cash_manager_for_currency_code.php' target='_blank'>تحديث CashManager لاستخدام currency_code</a></li>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>اختبار صفحة الصناديق</a></li>\n";
    echo "<li><a href='view_all_currencies.php' target='_blank'>عرض جميع العملات</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

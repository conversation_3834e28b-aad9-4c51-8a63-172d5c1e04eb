<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('customers.create');

$db              = new Database();
$customerManager = new CustomerManager($db);
$current_user    = $auth->getCurrentUser();

$errors = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز CSRF غير صالح.';
    }

    // Sanitize inputs
    $full_name   = sanitize_input($_POST['full_name'] ?? '');
    $id_number   = sanitize_input($_POST['id_number'] ?? '');
    $id_type     = sanitize_input($_POST['id_type'] ?? '');
    $nationality = sanitize_input($_POST['nationality'] ?? '');
    $birth_date  = sanitize_input($_POST['birth_date'] ?? '');
    $phone       = sanitize_input($_POST['phone'] ?? '');
    $email       = sanitize_input($_POST['email'] ?? '');
    $address     = sanitize_input($_POST['address'] ?? '');
    $occupation  = sanitize_input($_POST['occupation'] ?? '');
    $risk_level  = sanitize_input($_POST['risk_level'] ?? 'low');
    $status      = sanitize_input($_POST['status'] ?? 'active');
    $kyc_status  = sanitize_input($_POST['kyc_status'] ?? 'pending');
    
    // Validation
    if ($full_name === '') $errors[] = 'الاسم مطلوب';
    $validIdTypes = ['national_id','passport','residence'];
    if (!in_array($id_type, $validIdTypes, true)) $errors[] = 'نوع الهوية غير صالح';
    $validRisk = ['low','medium','high'];
    if (!in_array($risk_level, $validRisk, true)) $errors[] = 'مستوى المخاطر غير صالح';
    $validStatus = ['active','inactive','blocked'];
    if (!in_array($status, $validStatus, true)) $errors[] = 'الحالة غير صالحة';
    $validKyc = ['pending','under_review','approved','rejected'];
    if (!in_array($kyc_status, $validKyc, true)) $errors[] = 'حالة KYC غير صالحة';

    if ($id_number && $customerManager->idNumberExists($id_number)) {
        $errors[] = 'رقم الهوية مستخدم مسبقاً';
    }

    if (empty($errors)) {
        $data = [
            'full_name'   => $full_name,
            'id_number'   => $id_number ?: null,
            'id_type'     => $id_type,
            'nationality' => $nationality,
            'birth_date'  => $birth_date ?: null,
            'phone'       => $phone,
            'email'       => $email,
            'address'     => $address,
            'occupation'  => $occupation,
            'risk_level'  => $risk_level,
            'status'      => $status,
            'kyc_status'  => $kyc_status,
            'created_by'  => $current_user['id'],
        ];
        $newId = $customerManager->addCustomer($data);
        if ($newId) {
            // Handle document upload (generic)
            if (isset($_FILES['document_file']) && $_FILES['document_file']['error'] !== UPLOAD_ERR_NO_FILE) {
                $docType = sanitize_input($_POST['document_type'] ?? 'id_card');
                $allowedTypes = ['id_card','passport','driving_license','utility_bill','bank_statement','other'];
                if (!in_array($docType, $allowedTypes, true)) {
                    $docType = 'other';
                }
                $processed = handle_uploaded_file('document_file');
                if ($processed['success']) {
                    $customerManager->addDocument($newId, $processed, $docType, $current_user['id']);
                }
            }

            // تسجيل عملية إضافة العميل
            ActivityHelper::logCreate(
                'customers',
                $full_name,
                $data,
                $newId
            );

            header('Location: customer_details.php?id=' . $newId);
            exit;
        } else {
            // تسجيل فشل إضافة العميل
            ActivityHelper::logError(
                'customers',
                'add_customer',
                'فشل في إضافة عميل جديد: ' . $full_name,
                ['attempted_data' => $data]
            );
            $errors[] = 'فشل إنشاء العميل';
        }
    }
}

$pageTitle = 'إضافة عميل';
include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>
<main class="content p-4">
    <h2 class="mb-4">إضافة عميل جديد</h2>
    <?php if ($errors): ?>
        <div class="alert alert-danger"><ul class="mb-0"><?php foreach ($errors as $e): ?><li><?php echo $e; ?></li><?php endforeach; ?></ul></div>
    <?php endif; ?>
    <form method="post" enctype="multipart/form-data" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
        <div class="mb-3">
            <label class="form-label">الاسم الكامل</label>
            <input type="text" name="full_name" class="form-control" value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label">رقم الهوية</label>
                <input type="text" name="id_number" class="form-control" value="<?php echo htmlspecialchars($_POST['id_number'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">نوع الهوية</label>
                <select name="id_type" class="form-select" required>
                    <?php $sel = $_POST['id_type'] ?? ''; ?>
                    <option value="national_id" <?php echo ($sel==='national_id')?'selected':''; ?>>بطاقة هوية</option>
                    <option value="passport" <?php echo ($sel==='passport')?'selected':''; ?>>جواز سفر</option>
                    <option value="residence" <?php echo ($sel==='residence')?'selected':''; ?>>إقامة</option>
                </select>
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">الجنسية</label>
                <input type="text" name="nationality" class="form-control" value="<?php echo htmlspecialchars($_POST['nationality'] ?? ''); ?>">
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label">تاريخ الميلاد</label>
                <input type="date" name="birth_date" class="form-control" value="<?php echo htmlspecialchars($_POST['birth_date'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">الهاتف</label>
                <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">البريد الإلكتروني</label>
                <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">العنوان</label>
            <textarea name="address" class="form-control" rows="2"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">المهنة</label>
                <input type="text" name="occupation" class="form-control" value="<?php echo htmlspecialchars($_POST['occupation'] ?? ''); ?>">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">مستوى المخاطر</label>
                <?php $selRisk = $_POST['risk_level'] ?? 'low'; ?>
                <select name="risk_level" class="form-select">
                    <option value="low" <?php echo ($selRisk==='low')?'selected':''; ?>>منخفض</option>
                    <option value="medium" <?php echo ($selRisk==='medium')?'selected':''; ?>>متوسط</option>
                    <option value="high" <?php echo ($selRisk==='high')?'selected':''; ?>>مرتفع</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الحالة</label>
                <?php $selStatus = $_POST['status'] ?? 'active'; ?>
                <select name="status" class="form-select">
                    <option value="active" <?php echo ($selStatus==='active')?'selected':''; ?>>نشط</option>
                    <option value="inactive" <?php echo ($selStatus==='inactive')?'selected':''; ?>>غير نشط</option>
                    <option value="blocked" <?php echo ($selStatus==='blocked')?'selected':''; ?>>محظور</option>
                </select>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">حالة KYC</label>
            <?php $selKyc = $_POST['kyc_status'] ?? 'pending'; ?>
            <select name="kyc_status" class="form-select">
                <option value="pending" <?php echo ($selKyc==='pending')?'selected':''; ?>>قيد الانتظار</option>
                <option value="under_review" <?php echo ($selKyc==='under_review')?'selected':''; ?>>تحت المراجعة</option>
                <option value="approved" <?php echo ($selKyc==='approved')?'selected':''; ?>>مقبول</option>
                <option value="rejected" <?php echo ($selKyc==='rejected')?'selected':''; ?>>مرفوض</option>
            </select>
        </div>
        <h5 class="mt-4">المستندات</h5>
        <div class="row mb-3">
            <div class="col-md-4">
                <label class="form-label">نوع المستند</label>
                <select name="document_type" class="form-select">
                    <option value="id_card">بطاقة هوية</option>
                    <option value="passport">جواز سفر</option>
                    <option value="driving_license">رخصة قيادة</option>
                    <option value="utility_bill">فاتورة خدمات</option>
                    <option value="bank_statement">كشف حساب بنكي</option>
                    <option value="other">أخرى</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">ملف المستند (PDF/JPG/PNG)</label>
                <input type="file" name="document_file" class="form-control" accept=".pdf,.jpg,.jpeg,.png">
            </div>
        </div>

        <button type="submit" class="btn btn-primary">حفظ</button>
        <a href="customers.php" class="btn btn-secondary">إلغاء</a>
    </form>
</main>
<?php include __DIR__ . '/../includes/footer.php'; ?> 
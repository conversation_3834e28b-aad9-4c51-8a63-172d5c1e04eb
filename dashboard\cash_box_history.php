<?php
/**
 * Cash Box History Page
 * Display movement history for a specific cash box
 */

// Required includes
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/cash_manager.php';

// Initialize classes
$auth = new Auth();
$db = new Database();
$cashManager = new CashManager($db);

// Authentication and permission checks
$auth->requireLogin();
$auth->requirePermission('cash.view');

// Get current user
$current_user_id = $auth->getCurrentUser()['id'] ?? null;

// Get cash box ID from URL
$cashBoxId = (int)($_GET['id'] ?? 0);

if ($cashBoxId <= 0) {
    set_flash('danger', 'معرف الصندوق غير صالح');
    redirect('cash.php');
    exit;
}

// Get cash box details
try {
    $cashBox = $cashManager->getCashBoxById($cashBoxId);
    if (!$cashBox) {
        set_flash('danger', 'الصندوق غير موجود');
        redirect('cash.php');
        exit;
    }
} catch (Exception $e) {
    set_flash('danger', 'خطأ في تحميل بيانات الصندوق: ' . $e->getMessage());
    redirect('cash.php');
    exit;
}

// Pagination settings
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Date filters
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$movementType = $_GET['type'] ?? '';
$operationType = $_GET['operation_type'] ?? ''; // exchange, transfer, normal

// Build filter conditions
$filters = [];
if ($dateFrom) {
    $filters['date_from'] = $dateFrom;
}
if ($dateTo) {
    $filters['date_to'] = $dateTo;
}
if ($movementType && in_array($movementType, ['deposit', 'withdrawal'])) {
    $filters['type'] = $movementType;
}
if ($operationType && in_array($operationType, ['exchange', 'transfer', 'normal'])) {
    $filters['operation_type'] = $operationType;
}

// Get movements with pagination
try {
    $movements = $cashManager->getCashBoxMovements($cashBoxId, $filters, $limit, $offset);
    $totalMovements = $cashManager->getCashBoxMovementsCount($cashBoxId, $filters);
    $totalPages = ceil($totalMovements / $limit);
} catch (Exception $e) {
    $movements = [];
    $totalMovements = 0;
    $totalPages = 0;
    $error_message = 'خطأ في تحميل الحركات: ' . $e->getMessage();
}

// Calculate summary statistics
try {
    $summary = $cashManager->getCashBoxSummary($cashBoxId, $filters);
} catch (Exception $e) {
    $summary = [
        'total_deposits' => 0,
        'total_withdrawals' => 0,
        'net_change' => 0,
        'movement_count' => 0
    ];
}

// Set page title
$page_title = 'تاريخ حركات الصندوق - ' . htmlspecialchars($cashBox['name']);

// Include header
include __DIR__ . '/../includes/header.php';
?>

<!-- Dashboard content starts here -->
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-history me-2"></i>
                    تاريخ حركات الصندوق
                </h1>
                <p class="text-muted mb-0">
                    عرض جميع الحركات المالية للصندوق: 
                    <strong><?php echo htmlspecialchars($cashBox['name']); ?></strong>
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="cash.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للصناديق
                </a>
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>

    <!-- Cash Box Info Card -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الصندوق
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم الصندوق:</strong> <?php echo htmlspecialchars($cashBox['name']); ?></p>
                            <p><strong>العملة:</strong> <?php echo htmlspecialchars($cashBox['currency_name'] . ' (' . $cashBox['currency_code'] . ')'); ?></p>
                            <p><strong>الفرع:</strong> <?php echo htmlspecialchars($cashBox['branch_name']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>المسؤول:</strong> <?php echo htmlspecialchars($cashBox['responsible_name']); ?></p>
                            <p><strong>الرصيد الحالي:</strong> 
                                <span class="cash-balance <?php 
                                    $balance = (float)$cashBox['current_balance'];
                                    echo $balance > 0 ? 'positive' : ($balance < 0 ? 'negative' : 'zero');
                                ?>">
                                    <?php echo number_format($balance, 2); ?>
                                    <?php echo htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']); ?>
                                </span>
                            </p>
                            <p><strong>الحالة:</strong> 
                                <span class="status-badge <?php echo $cashBox['is_active'] ? 'active' : 'inactive'; ?>">
                                    <i class="fas fa-<?php echo $cashBox['is_active'] ? 'check-circle' : 'times-circle'; ?> me-1"></i>
                                    <?php echo $cashBox['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Summary Statistics -->
        <div class="col-md-4">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        ملخص الحركات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">إجمالي الإيداعات</small>
                        <div class="h5 text-success mb-0">
                            <?php echo number_format($summary['total_deposits'], 2); ?>
                            <?php echo htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']); ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">إجمالي السحوبات</small>
                        <div class="h5 text-warning mb-0">
                            <?php echo number_format($summary['total_withdrawals'], 2); ?>
                            <?php echo htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']); ?>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">صافي التغيير</small>
                        <div class="h5 <?php echo $summary['net_change'] >= 0 ? 'text-success' : 'text-danger'; ?> mb-0">
                            <?php echo ($summary['net_change'] >= 0 ? '+' : '') . number_format($summary['net_change'], 2); ?>
                            <?php echo htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']); ?>
                        </div>
                    </div>
                    <div>
                        <small class="text-muted">عدد الحركات</small>
                        <div class="h5 text-info mb-0"><?php echo number_format($summary['movement_count']); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="dashboard-card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>
                تصفية الحركات
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <input type="hidden" name="id" value="<?php echo $cashBoxId; ?>">
                
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" 
                           value="<?php echo htmlspecialchars($dateFrom); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" 
                           value="<?php echo htmlspecialchars($dateTo); ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">نوع الحركة</label>
                    <select name="type" class="form-select">
                        <option value="">جميع الحركات</option>
                        <option value="deposit" <?php echo $movementType === 'deposit' ? 'selected' : ''; ?>>إيداع</option>
                        <option value="withdrawal" <?php echo $movementType === 'withdrawal' ? 'selected' : ''; ?>>سحب</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">نوع العملية</label>
                    <select name="operation_type" class="form-select">
                        <option value="">جميع العمليات</option>
                        <option value="normal" <?php echo $operationType === 'normal' ? 'selected' : ''; ?>>عادية</option>
                        <option value="exchange" <?php echo $operationType === 'exchange' ? 'selected' : ''; ?>>صرافة</option>
                        <option value="transfer" <?php echo $operationType === 'transfer' ? 'selected' : ''; ?>>تحويل</option>
                    </select>
                </div>

                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        تصفية
                    </button>
                    <a href="?id=<?php echo $cashBoxId; ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Movements Table -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    سجل الحركات
                    <?php if ($totalMovements > 0): ?>
                        <span class="badge bg-primary"><?php echo number_format($totalMovements); ?></span>
                    <?php endif; ?>
                </h5>

                <?php if (!empty($movements)): ?>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-primary" onclick="exportToCSV()">
                        <i class="fas fa-file-csv me-1"></i>
                        تصدير CSV
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i>
                        تصدير Excel
                    </button>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body">
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php elseif (empty($movements)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد حركات</h5>
                    <p class="text-muted">لم يتم العثور على حركات مالية للصندوق في الفترة المحددة</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="movementsTable">
                        <thead>
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>نوع الحركة</th>
                                <th>نوع العملية</th>
                                <th>المبلغ</th>
                                <th>الرصيد بعد الحركة</th>
                                <th>الوصف</th>
                                <th>رقم المرجع</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($movements as $movement): ?>
                                <tr>
                                    <td>
                                        <div class="fw-medium">
                                            <?php echo date('Y-m-d', strtotime($movement['created_at'])); ?>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo date('H:i:s', strtotime($movement['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php
                                        $movementType = $movement['type'] ?? $movement['movement_type'] ?? '';
                                        if ($movementType === 'deposit'): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-plus me-1"></i>
                                                إيداع
                                            </span>
                                        <?php elseif ($movementType === 'withdrawal'): ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-minus me-1"></i>
                                                سحب
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-question me-1"></i>
                                                <?php echo htmlspecialchars($movementType ?: 'غير محدد'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($movement['exchange_id'])): ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-exchange-alt me-1"></i>
                                                صرافة
                                            </span>
                                            <br>
                                            <small class="text-muted">
                                                <a href="view_exchange.php?id=<?php echo $movement['exchange_id']; ?>"
                                                   class="text-decoration-none" target="_blank">
                                                    #<?php echo $movement['exchange_id']; ?>
                                                </a>
                                            </small>
                                        <?php elseif (!empty($movement['transfer_id'])): ?>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-paper-plane me-1"></i>
                                                تحويل
                                            </span>
                                            <br>
                                            <small class="text-muted">
                                                <a href="view_transfer.php?id=<?php echo $movement['transfer_id']; ?>"
                                                   class="text-decoration-none" target="_blank">
                                                    #<?php echo $movement['transfer_id']; ?>
                                                </a>
                                            </small>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-hand-holding-usd me-1"></i>
                                                عادية
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $movementType = $movement['type'] ?? $movement['movement_type'] ?? '';
                                        $amount = (float)($movement['amount'] ?? 0);
                                        ?>
                                        <span class="fw-medium <?php echo $movementType === 'deposit' ? 'text-success' : 'text-warning'; ?>">
                                            <?php echo $movementType === 'deposit' ? '+' : '-'; ?>
                                            <?php echo number_format($amount, 2); ?>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']); ?>
                                            </small>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="cash-balance <?php
                                            $balance = (float)$movement['balance_after'];
                                            echo $balance > 0 ? 'positive' : ($balance < 0 ? 'negative' : 'zero');
                                        ?>">
                                            <?php echo number_format($balance, 2); ?>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars($cashBox['currency_symbol'] ?: $cashBox['currency_code']); ?>
                                            </small>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $description = $movement['description'] ?? '';
                                        if (!empty($description)): ?>
                                            <span class="text-truncate" style="max-width: 200px; display: inline-block;"
                                                  title="<?php echo htmlspecialchars($description); ?>">
                                                <?php echo htmlspecialchars($description); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $referenceNumber = $movement['reference_number'] ?? '';
                                        if (!empty($referenceNumber)): ?>
                                            <code class="small"><?php echo htmlspecialchars($referenceNumber); ?></code>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm me-2">
                                                <i class="fas fa-user-circle text-muted"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium small">
                                                    <?php
                                                    $userName = $movement['user_name'] ?? '';
                                                    echo htmlspecialchars($userName ?: 'غير محدد');
                                                    ?>
                                                </div>
                                                <small class="text-muted">
                                                    ID: <?php echo $movement['user_id'] ?? 'غير محدد'; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-info"
                                                    onclick="viewMovementDetails(<?php echo $movement['id']; ?>)"
                                                    data-bs-toggle="tooltip"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($auth->hasPermission('cash.movements.edit')): ?>
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="editMovement(<?php echo $movement['id']; ?>)"
                                                        data-bs-toggle="tooltip"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            <?php endif; ?>
                                            <?php if ($auth->hasPermission('cash.movements.delete')): ?>
                                                <button class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteMovement(<?php echo $movement['id']; ?>)"
                                                        data-bs-toggle="tooltip"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="تصفح الحركات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?id=<?php echo $cashBoxId; ?>&page=<?php echo $page - 1; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'id'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $start = max(1, $page - 2);
                            $end = min($totalPages, $page + 2);

                            for ($i = $start; $i <= $end; $i++):
                            ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?id=<?php echo $cashBoxId; ?>&page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'id'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?id=<?php echo $cashBoxId; ?>&page=<?php echo $page + 1; ?>&<?php echo http_build_query(array_filter($_GET, function($k) { return $k !== 'page' && $k !== 'id'; }, ARRAY_FILTER_USE_KEY)); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>

                        <div class="text-center text-muted small">
                            عرض <?php echo (($page - 1) * $limit) + 1; ?> - <?php echo min($page * $limit, $totalMovements); ?>
                            من أصل <?php echo number_format($totalMovements); ?> حركة
                        </div>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div> <!-- /.container-fluid -->

<!-- Movement Details Modal -->
<div class="modal fade" id="movementDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الحركة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="movementDetailsContent">
                <!-- Content will be loaded via AJAX -->
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../includes/footer.php'; ?>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// View movement details
function viewMovementDetails(movementId) {
    var modal = new bootstrap.Modal(document.getElementById('movementDetailsModal'));
    var content = document.getElementById('movementDetailsContent');

    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;

    modal.show();

    // Load movement details via AJAX
    fetch('ajax/get_movement_details.php?id=' + movementId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        ${data.error || 'خطأ في تحميل التفاصيل'}
                    </div>
                `;
            }
        })
        .catch(error => {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    خطأ في الاتصال بالخادم
                </div>
            `;
        });
}

// Edit movement
function editMovement(movementId) {
    // Redirect to edit page or open edit modal
    window.location.href = 'edit_movement.php?id=' + movementId;
}

// Delete movement
function deleteMovement(movementId) {
    if (confirm('هل أنت متأكد من حذف هذه الحركة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'delete_movement.php';
        form.style.display = 'none';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'movement_id';
        input.value = movementId;
        
        const cashBoxInput = document.createElement('input');
        cashBoxInput.type = 'hidden';
        cashBoxInput.name = 'cash_box_id';
        cashBoxInput.value = <?php echo $cashBoxId; ?>;
        
        form.appendChild(input);
        form.appendChild(cashBoxInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Export to CSV
function exportToCSV() {
    var table = document.getElementById('movementsTable');
    var csv = [];
    var rows = table.querySelectorAll('tr');

    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll('td, th');

        for (var j = 0; j < cols.length - 1; j++) { // Exclude actions column
            var cellText = cols[j].innerText.replace(/"/g, '""');
            row.push('"' + cellText + '"');
        }

        csv.push(row.join(','));
    }

    var csvFile = new Blob([csv.join('\n')], {type: 'text/csv'});
    var downloadLink = document.createElement('a');
    downloadLink.download = 'cash_box_movements_<?php echo $cashBoxId; ?>_' + new Date().toISOString().slice(0,10) + '.csv';
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// Export to Excel (simplified)
function exportToExcel() {
    var table = document.getElementById('movementsTable');
    var html = table.outerHTML;
    var url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
    var downloadLink = document.createElement('a');
    downloadLink.href = url;
    downloadLink.download = 'cash_box_movements_<?php echo $cashBoxId; ?>_' + new Date().toISOString().slice(0,10) + '.xls';
    downloadLink.click();
}

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>

<style>
@media print {
    .btn, .pagination, .card-header .btn-group {
        display: none !important;
    }

    .dashboard-card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    .table {
        font-size: 12px;
    }

    .page-header {
        border-bottom: 2px solid #000;
        margin-bottom: 20px;
    }
}

.printing .sidebar,
.printing .navbar {
    display: none !important;
}

.printing .main-content {
    margin-right: 0 !important;
}
</style>

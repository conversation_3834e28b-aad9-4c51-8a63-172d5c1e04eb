<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار حقول نوع التسليم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        #additional_fields {
            display: none !important;
            transition: all 0.3s ease;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            margin-top: 20px;
        }
        
        #additional_fields.show {
            border-color: #0d6efd;
            background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>اختبار حقول نوع التسليم</h2>
        
        <div class="card">
            <div class="card-body">
                <div class="mb-3">
                    <label for="delivery_type" class="form-label">نوع التسليم</label>
                    <select class="form-select" id="delivery_type" name="delivery_type">
                        <option value="">اختر نوع التسليم</option>
                        <option value="cash">كاش (Cash)</option>
                        <option value="bank">بنكي (Bank Transfer)</option>
                        <option value="usdt">USDT (عملة رقمية)</option>
                    </select>
                </div>

                <!-- حقول إضافية حسب نوع التسليم -->
                <div id="additional_fields">
                    <div id="info_alert" class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong id="info_text">معلومات إضافية مطلوبة:</strong> <span id="info_description">يرجى ملء البيانات التالية</span>
                    </div>
                    <div class="row">
                        <!-- حقل المبلغ المراد تحويله (للبنكي و USDT فقط) -->
                        <div class="col-md-6 mb-3" id="transfer_amount_field" style="display: none;">
                            <label for="transfer_amount" class="form-label">المبلغ المراد تحويله</label>
                            <input type="number" class="form-control" id="transfer_amount" name="transfer_amount"
                                   step="0.01" min="0.01" placeholder="أدخل المبلغ المراد تحويله">
                        </div>

                        <!-- حقل اسم المستلم (لجميع الأنواع) -->
                        <div class="col-md-6 mb-3" id="recipient_name_field">
                            <label for="recipient_name" class="form-label">اسم المستلم</label>
                            <input type="text" class="form-control" id="recipient_name" name="recipient_name"
                                   maxlength="255" placeholder="أدخل اسم المستلم">
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h5>معلومات التشخيص:</h5>
                    <div id="debug_info" class="alert alert-secondary">
                        <p><strong>نوع التسليم المختار:</strong> <span id="selected_type">لا شيء</span></p>
                        <p><strong>حالة الحقول الإضافية:</strong> <span id="fields_status">مخفية</span></p>
                        <p><strong>عرض CSS:</strong> <span id="css_display">-</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const deliveryTypeSelect = document.getElementById('delivery_type');
            const additionalFields = document.getElementById('additional_fields');
            const transferAmountInput = document.getElementById('transfer_amount');
            const recipientNameInput = document.getElementById('recipient_name');
            
            // عناصر التشخيص
            const selectedTypeSpan = document.getElementById('selected_type');
            const fieldsStatusSpan = document.getElementById('fields_status');
            const cssDisplaySpan = document.getElementById('css_display');
            
            function updateDebugInfo() {
                selectedTypeSpan.textContent = deliveryTypeSelect.value || 'لا شيء';
                fieldsStatusSpan.textContent = additionalFields.style.display === 'none' ? 'مخفية' : 'ظاهرة';
                cssDisplaySpan.textContent = window.getComputedStyle(additionalFields).display;
            }
            
            function toggleAdditionalFields() {
                const deliveryType = deliveryTypeSelect.value;
                console.log('Delivery type selected:', deliveryType);
                
                if (deliveryType === 'bank' || deliveryType === 'usdt') {
                    console.log('Showing additional fields');
                    additionalFields.style.setProperty('display', 'block', 'important');
                    additionalFields.style.animation = 'fadeIn 0.3s ease';
                    additionalFields.classList.add('show');
                    
                    if (transferAmountInput) transferAmountInput.required = true;
                    if (recipientNameInput) recipientNameInput.required = true;
                } else {
                    console.log('Hiding additional fields');
                    additionalFields.style.setProperty('display', 'none', 'important');
                    additionalFields.classList.remove('show');
                    
                    if (transferAmountInput) {
                        transferAmountInput.required = false;
                        transferAmountInput.value = '';
                    }
                    if (recipientNameInput) {
                        recipientNameInput.required = false;
                        recipientNameInput.value = '';
                    }
                }
                
                updateDebugInfo();
            }
            
            // ربط الحدث
            deliveryTypeSelect.addEventListener('change', function() {
                console.log('Delivery type changed to:', this.value);
                toggleAdditionalFields();
            });
            
            // تشغيل أولي
            toggleAdditionalFields();
            updateDebugInfo();
            
            // تحديث معلومات التشخيص كل ثانية
            setInterval(updateDebugInfo, 1000);
        });
    </script>
</body>
</html>

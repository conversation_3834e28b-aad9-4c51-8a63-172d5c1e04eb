<?php
/**
 * Currency Management Page
 * إدارة فئات العملات
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

// Check authentication
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// Check permissions
if (!$auth->hasPermission('admin.manage')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب');
    redirect('dashboard.php');
}

$db = new Database();
$conn = Database::getConnection();

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_currency':
            if ($auth->hasPermission('admin.manage')) {
                $code = strtoupper(sanitize_input($_POST['code'] ?? ''));
                $name = sanitize_input($_POST['name'] ?? '');
                $symbol = sanitize_input($_POST['symbol'] ?? '');
                $country = sanitize_input($_POST['country'] ?? '');
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                if (!empty($code) && !empty($name) && !empty($symbol)) {
                    $sql = "INSERT INTO currencies (code, name, symbol, country, is_active, created_at, updated_at) 
                            VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
                    $stmt = $conn->prepare($sql);
                    
                    if ($stmt && $stmt->bind_param('ssssi', $code, $name, $symbol, $country, $isActive)) {
                        if ($stmt->execute()) {
                            set_flash('success', 'تم إضافة العملة بنجاح');
                        } else {
                            if ($conn->errno == 1062) {
                                set_flash('danger', 'رمز العملة موجود بالفعل');
                            } else {
                                set_flash('danger', 'فشل في إضافة العملة');
                            }
                        }
                        $stmt->close();
                    } else {
                        set_flash('danger', 'خطأ في إعداد الاستعلام');
                    }
                } else {
                    set_flash('danger', 'يرجى ملء جميع الحقول المطلوبة');
                }
            }
            break;
            
        case 'edit_currency':
            if ($auth->hasPermission('admin.manage')) {
                $id = (int)($_POST['currency_id'] ?? 0);
                $code = strtoupper(sanitize_input($_POST['code'] ?? ''));
                $name = sanitize_input($_POST['name'] ?? '');
                $symbol = sanitize_input($_POST['symbol'] ?? '');
                $country = sanitize_input($_POST['country'] ?? '');
                $isActive = isset($_POST['is_active']) ? 1 : 0;
                
                if ($id > 0 && !empty($code) && !empty($name) && !empty($symbol)) {
                    $sql = "UPDATE currencies SET code = ?, name = ?, symbol = ?, country = ?, is_active = ?, updated_at = NOW() WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    
                    if ($stmt && $stmt->bind_param('ssssii', $code, $name, $symbol, $country, $isActive, $id)) {
                        if ($stmt->execute()) {
                            set_flash('success', 'تم تحديث العملة بنجاح');
                        } else {
                            if ($conn->errno == 1062) {
                                set_flash('danger', 'رمز العملة موجود بالفعل');
                            } else {
                                set_flash('danger', 'فشل في تحديث العملة');
                            }
                        }
                        $stmt->close();
                    } else {
                        set_flash('danger', 'خطأ في إعداد الاستعلام');
                    }
                } else {
                    set_flash('danger', 'بيانات غير صحيحة');
                }
            }
            break;
            
        case 'delete_currency':
            if ($auth->hasPermission('admin.manage')) {
                $id = (int)($_POST['currency_id'] ?? 0);
                
                if ($id > 0) {
                    // Check if currency is used in cash boxes or bank accounts
                    $checkSql = "SELECT 
                                    (SELECT COUNT(*) FROM cash_boxes WHERE currency_code = (SELECT code FROM currencies WHERE id = ?)) +
                                    (SELECT COUNT(*) FROM bank_accounts WHERE currency_code = (SELECT code FROM currencies WHERE id = ?)) as usage_count";
                    $checkStmt = $conn->prepare($checkSql);
                    
                    if ($checkStmt && $checkStmt->bind_param('ii', $id, $id)) {
                        $checkStmt->execute();
                        $result = $checkStmt->get_result();
                        $usage = $result->fetch_assoc();
                        $checkStmt->close();
                        
                        if ($usage['usage_count'] > 0) {
                            set_flash('danger', 'لا يمكن حذف العملة لأنها مستخدمة في الصناديق أو الحسابات البنكية');
                        } else {
                            $sql = "DELETE FROM currencies WHERE id = ?";
                            $stmt = $conn->prepare($sql);
                            
                            if ($stmt && $stmt->bind_param('i', $id)) {
                                if ($stmt->execute()) {
                                    set_flash('success', 'تم حذف العملة بنجاح');
                                } else {
                                    set_flash('danger', 'فشل في حذف العملة');
                                }
                                $stmt->close();
                            } else {
                                set_flash('danger', 'خطأ في إعداد الاستعلام');
                            }
                        }
                    }
                } else {
                    set_flash('danger', 'معرف العملة غير صحيح');
                }
            }
            break;
            
        case 'toggle_status':
            if ($auth->hasPermission('admin.manage')) {
                $id = (int)($_POST['currency_id'] ?? 0);
                
                if ($id > 0) {
                    $sql = "UPDATE currencies SET is_active = NOT is_active, updated_at = NOW() WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    
                    if ($stmt && $stmt->bind_param('i', $id)) {
                        if ($stmt->execute()) {
                            set_flash('success', 'تم تغيير حالة العملة بنجاح');
                        } else {
                            set_flash('danger', 'فشل في تغيير حالة العملة');
                        }
                        $stmt->close();
                    } else {
                        set_flash('danger', 'خطأ في إعداد الاستعلام');
                    }
                } else {
                    set_flash('danger', 'معرف العملة غير صحيح');
                }
            }
            break;
    }
    
    redirect('currency_management.php');
}

// Get all currencies with usage statistics
$sql = "SELECT c.*, 
               COALESCE(cb_count.count, 0) as cash_boxes_count,
               COALESCE(ba_count.count, 0) as bank_accounts_count
        FROM currencies c
        LEFT JOIN (
            SELECT currency_code, COUNT(*) as count 
            FROM cash_boxes 
            GROUP BY currency_code
        ) cb_count ON c.code = cb_count.currency_code
        LEFT JOIN (
            SELECT currency_code, COUNT(*) as count 
            FROM bank_accounts 
            GROUP BY currency_code
        ) ba_count ON c.code = ba_count.currency_code
        ORDER BY c.is_active DESC, c.name ASC";

$result = $conn->query($sql);
$currencies = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $currencies[] = $row;
    }
}

// Get statistics
$stats = [
    'total' => count($currencies),
    'active' => count(array_filter($currencies, function($c) { return $c['is_active']; })),
    'inactive' => count(array_filter($currencies, function($c) { return !$c['is_active']; })),
    'used' => count(array_filter($currencies, function($c) { return ($c['cash_boxes_count'] + $c['bank_accounts_count']) > 0; }))
];

$pageTitle = 'إدارة فئات العملات';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-coins"></i> إدارة فئات العملات</h3>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCurrencyModal">
            <i class="fas fa-plus"></i> إضافة عملة جديدة
        </button>
    </div>

    <?php foreach (get_flash() as $type => $msg): ?>
        <div class="alert alert-<?php echo $type; ?> alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($msg); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endforeach; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['total']; ?></h4>
                            <p class="mb-0">إجمالي العملات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['active']; ?></h4>
                            <p class="mb-0">العملات النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['inactive']; ?></h4>
                            <p class="mb-0">العملات غير النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['used']; ?></h4>
                            <p class="mb-0">العملات المستخدمة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Currencies Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">قائمة العملات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-bordered align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>رمز العملة</th>
                            <th>اسم العملة</th>
                            <th>الرمز المختصر</th>
                            <th>البلد/المنطقة</th>
                            <th>الحالة</th>
                            <th>الاستخدام</th>
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($currencies as $idx => $currency): ?>
                            <tr>
                                <td><?php echo $idx + 1; ?></td>
                                <td>
                                    <span class="badge bg-secondary fs-6"><?php echo htmlspecialchars($currency['code']); ?></span>
                                </td>
                                <td><?php echo htmlspecialchars($currency['name']); ?></td>
                                <td class="text-center">
                                    <span class="fw-bold"><?php echo htmlspecialchars($currency['symbol']); ?></span>
                                </td>
                                <td><?php echo htmlspecialchars($currency['country'] ?: '-'); ?></td>
                                <td>
                                    <?php if ($currency['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php 
                                    $totalUsage = $currency['cash_boxes_count'] + $currency['bank_accounts_count'];
                                    if ($totalUsage > 0): 
                                    ?>
                                        <span class="badge bg-info">
                                            <?php echo $currency['cash_boxes_count']; ?> صندوق، 
                                            <?php echo $currency['bank_accounts_count']; ?> حساب
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">غير مستخدم</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editCurrencyModal"
                                                data-id="<?php echo $currency['id']; ?>"
                                                data-code="<?php echo htmlspecialchars($currency['code']); ?>"
                                                data-name="<?php echo htmlspecialchars($currency['name']); ?>"
                                                data-symbol="<?php echo htmlspecialchars($currency['symbol']); ?>"
                                                data-country="<?php echo htmlspecialchars($currency['country']); ?>"
                                                data-active="<?php echo $currency['is_active']; ?>"
                                                title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        
                                        <form method="post" class="d-inline" onsubmit="return confirm('هل تريد تغيير حالة هذه العملة؟')">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="currency_id" value="<?php echo $currency['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-warning" title="تغيير الحالة">
                                                <i class="fas fa-toggle-<?php echo $currency['is_active'] ? 'on' : 'off'; ?>"></i>
                                            </button>
                                        </form>
                                        
                                        <?php if ($totalUsage == 0): ?>
                                            <form method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه العملة؟ هذا الإجراء لا يمكن التراجع عنه!')">
                                                <input type="hidden" name="action" value="delete_currency">
                                                <input type="hidden" name="currency_id" value="<?php echo $currency['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <button class="btn btn-sm btn-outline-secondary" disabled title="لا يمكن الحذف - العملة مستخدمة">
                                                <i class="fas fa-lock"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        
                        <?php if (empty($currencies)): ?>
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-coins fa-3x mb-3"></i>
                                    <p>لا توجد عملات مسجلة</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Currency Modal -->
<div class="modal fade" id="addCurrencyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عملة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_currency">

                    <div class="mb-3">
                        <label class="form-label">رمز العملة <span class="text-danger">*</span></label>
                        <input type="text" name="code" class="form-control" maxlength="3" required
                               placeholder="مثال: USD" style="text-transform: uppercase;">
                        <div class="form-text">رمز العملة المكون من 3 أحرف (مثل USD, EUR, SAR)</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم العملة <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" required
                               placeholder="مثال: دولار أمريكي">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الرمز المختصر <span class="text-danger">*</span></label>
                        <input type="text" name="symbol" class="form-control" maxlength="10" required
                               placeholder="مثال: $">
                        <div class="form-text">الرمز المختصر للعملة (مثل $, €, ر.س)</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">البلد/المنطقة</label>
                        <input type="text" name="country" class="form-control"
                               placeholder="مثال: الولايات المتحدة الأمريكية">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" class="form-check-input" id="addIsActive" checked>
                            <label class="form-check-label" for="addIsActive">
                                العملة نشطة
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة العملة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Currency Modal -->
<div class="modal fade" id="editCurrencyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل العملة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_currency">
                    <input type="hidden" name="currency_id" id="editCurrencyId">

                    <div class="mb-3">
                        <label class="form-label">رمز العملة <span class="text-danger">*</span></label>
                        <input type="text" name="code" id="editCode" class="form-control" maxlength="3" required
                               style="text-transform: uppercase;">
                        <div class="form-text">رمز العملة المكون من 3 أحرف (مثل USD, EUR, SAR)</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">اسم العملة <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="editName" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الرمز المختصر <span class="text-danger">*</span></label>
                        <input type="text" name="symbol" id="editSymbol" class="form-control" maxlength="10" required>
                        <div class="form-text">الرمز المختصر للعملة (مثل $, €, ر.س)</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">البلد/المنطقة</label>
                        <input type="text" name="country" id="editCountry" class="form-control">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" class="form-check-input" id="editIsActive">
                            <label class="form-check-label" for="editIsActive">
                                العملة نشطة
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Handle edit modal
document.addEventListener('DOMContentLoaded', function() {
    const editModal = document.getElementById('editCurrencyModal');
    if (editModal) {
        editModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;

            document.getElementById('editCurrencyId').value = button.getAttribute('data-id');
            document.getElementById('editCode').value = button.getAttribute('data-code');
            document.getElementById('editName').value = button.getAttribute('data-name');
            document.getElementById('editSymbol').value = button.getAttribute('data-symbol');
            document.getElementById('editCountry').value = button.getAttribute('data-country');
            document.getElementById('editIsActive').checked = button.getAttribute('data-active') == '1';
        });
    }

    // Auto-uppercase currency codes
    const codeInputs = document.querySelectorAll('input[name="code"]');
    codeInputs.forEach(input => {
        input.addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    });
});
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

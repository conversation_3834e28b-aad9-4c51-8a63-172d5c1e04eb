<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/office_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('offices.edit');

$officeMgr = new OfficeManager(new Database());
$errors = [];

// Get office ID
$officeId = (int)($_GET['id'] ?? 0);
if (!$officeId) {
    set_flash('danger', 'معرف المكتب غير صحيح');
    redirect('offices.php');
}

// Get office details
$office = $officeMgr->getOfficeById($officeId);
if (!$office) {
    set_flash('danger', 'المكتب غير موجود');
    redirect('offices.php');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf = $_POST['csrf_token'] ?? '';
    if (!verify_csrf_token($csrf)) {
        $errors[] = 'CSRF غير صالح';
    } else {
        // Validate and sanitize input
        $officeName = trim($_POST['office_name'] ?? '');
        $phoneNumber = trim($_POST['phone_number'] ?? '');
        $address = trim($_POST['address'] ?? '');
        $managerName = trim($_POST['manager_name'] ?? '');
        $isActive = isset($_POST['is_active']) ? 1 : 0;

        // Validation
        if (empty($officeName)) {
            $errors[] = 'اسم المكتب مطلوب';
        } elseif (strlen($officeName) < 3) {
            $errors[] = 'اسم المكتب يجب أن يكون 3 أحرف على الأقل';
        }

        if (empty($managerName)) {
            $errors[] = 'اسم مسؤول المكتب مطلوب';
        }

        if (empty($phoneNumber)) {
            $errors[] = 'رقم الهاتف مطلوب';
        }

        if (empty($address)) {
            $errors[] = 'العنوان مطلوب';
        }

        // Check if office name already exists (excluding current office)
        if (empty($errors) && $officeMgr->officeNameExists($officeName, $officeId)) {
            $errors[] = 'اسم المكتب موجود مسبقاً';
        }

        if (empty($errors)) {
            $officeData = [
                'office_name' => $officeName,
                'phone_number' => $phoneNumber,
                'address' => $address,
                'manager_name' => $managerName,
                'is_active' => $isActive
            ];

            if ($officeMgr->updateOffice($officeId, $officeData)) {
                // تسجيل عملية تعديل المكتب
                ActivityHelper::logUpdate(
                    'offices',
                    $office['office_name'],
                    $office,
                    $officeData,
                    $officeId
                );
                set_flash('success', 'تم تحديث المكتب بنجاح');
                redirect('offices.php');
            } else {
                // تسجيل فشل التعديل
                ActivityHelper::logError(
                    'offices',
                    'edit_office',
                    'فشل في تعديل المكتب: ' . $office['office_name'],
                    [
                        'office_id' => $officeId,
                        'attempted_data' => $officeData
                    ]
                );
                $errors[] = 'حدث خطأ أثناء تحديث المكتب';
            }
        }
    }
}

$csrf = get_csrf_token();
$pageTitle = 'تعديل المكتب - ' . $office['office_name'];
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container p-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-edit"></i> تعديل المكتب
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="office_name" class="form-label">اسم المكتب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="office_name" name="office_name" 
                                       value="<?php echo htmlspecialchars($_POST['office_name'] ?? $office['office_name']); ?>" 
                                       required>
                                <div class="invalid-feedback">
                                    اسم المكتب مطلوب
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="manager_name" class="form-label">مسؤول المكتب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="manager_name" name="manager_name" 
                                       value="<?php echo htmlspecialchars($_POST['manager_name'] ?? $office['manager_name']); ?>" 
                                       required>
                                <div class="invalid-feedback">
                                    اسم مسؤول المكتب مطلوب
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone_number" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                                       value="<?php echo htmlspecialchars($_POST['phone_number'] ?? $office['phone_number']); ?>" 
                                       required>
                                <div class="invalid-feedback">
                                    رقم الهاتف مطلوب
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="is_active" class="form-label">الحالة</label>
                                <div class="form-check form-switch mt-2">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           <?php echo (isset($_POST['is_active']) || (!isset($_POST['is_active']) && $office['is_active'])) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        مكتب نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      required><?php echo htmlspecialchars($_POST['address'] ?? $office['address']); ?></textarea>
                            <div class="invalid-feedback">
                                العنوان مطلوب
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="office_details.php?id=<?php echo $officeId; ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> رجوع
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
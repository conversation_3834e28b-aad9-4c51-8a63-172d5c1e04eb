<?php
/**
 * Activity Helper - مساعد تسجيل العمليات
 * 
 * يوفر دوال مساعدة سريعة لتسجيل العمليات في جميع أنحاء النظام
 */

require_once __DIR__ . '/system_activity_manager.php';

class ActivityHelper
{
    /**
     * تسجيل سريع لعملية إنشاء
     */
    public static function logCreate(string $module, string $itemName, array $data, int $newId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $manager->logCreate(
                $module,
                "create_$module",
                "إنشاء $itemName جديد",
                $data,
                [
                    'target_table' => $module,
                    'target_id' => $newId,
                    'target_identifier' => $itemName
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logCreate Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل سريع لعملية تحديث
     */
    public static function logUpdate(string $module, string $itemName, array $oldData, array $newData, int $itemId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $manager->logUpdate(
                $module,
                "update_$module",
                "تحديث $itemName",
                $oldData,
                $newData,
                [
                    'target_table' => $module,
                    'target_id' => $itemId,
                    'target_identifier' => $itemName
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logUpdate Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل سريع لعملية حذف
     */
    public static function logDelete(string $module, string $itemName, array $deletedData, int $itemId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $manager->logDelete(
                $module,
                "delete_$module",
                "حذف $itemName",
                $deletedData,
                [
                    'target_table' => $module,
                    'target_id' => $itemId,
                    'target_identifier' => $itemName
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logDelete Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل سريع لعملية عرض
     */
    public static function logView(string $module, string $description = null, array $filters = []): void
    {
        try {
            $manager = new SystemActivityManager();
            $desc = $description ?: "عرض قائمة $module";
            $manager->logView(
                $module,
                "view_$module",
                $desc,
                [
                    'filters' => $filters,
                    'additional_data' => ['view_type' => 'list']
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logView Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل عملية تحويل مالي
     */
    public static function logTransfer(string $type, array $transferData, int $transferId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $amount = $transferData['amount'] ?? 'غير محدد';
            $currency = $transferData['currency'] ?? '';
            $from = $transferData['from'] ?? 'غير محدد';
            $to = $transferData['to'] ?? 'غير محدد';
            
            $description = "تحويل $type بمبلغ $amount $currency من $from إلى $to";
            
            $manager->logActivity(
                'TRANSFER',
                'transfers',
                "transfer_$type",
                $description,
                [
                    'target_table' => 'transfers',
                    'target_id' => $transferId,
                    'new_values' => $transferData,
                    'additional_data' => [
                        'transfer_type' => $type,
                        'amount' => $amount,
                        'currency' => $currency
                    ]
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logTransfer Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل عملية صرافة
     */
    public static function logExchange(array $exchangeData, int $exchangeId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $fromAmount = $exchangeData['from_amount'] ?? 'غير محدد';
            $fromCurrency = $exchangeData['from_currency'] ?? '';
            $toAmount = $exchangeData['to_amount'] ?? 'غير محدد';
            $toCurrency = $exchangeData['to_currency'] ?? '';
            $rate = $exchangeData['exchange_rate'] ?? 'غير محدد';
            
            $description = "عملية صرافة: $fromAmount $fromCurrency إلى $toAmount $toCurrency (سعر: $rate)";
            
            $manager->logActivity(
                'EXCHANGE',
                'exchange',
                'currency_exchange',
                $description,
                [
                    'target_table' => 'exchanges',
                    'target_id' => $exchangeId,
                    'new_values' => $exchangeData,
                    'additional_data' => [
                        'from_amount' => $fromAmount,
                        'from_currency' => $fromCurrency,
                        'to_amount' => $toAmount,
                        'to_currency' => $toCurrency,
                        'exchange_rate' => $rate
                    ]
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logExchange Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل عملية دفع
     */
    public static function logPayment(string $type, array $paymentData, int $paymentId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $amount = $paymentData['amount'] ?? 'غير محدد';
            $currency = $paymentData['currency'] ?? '';
            $method = $paymentData['payment_method'] ?? 'غير محدد';
            
            $description = "عملية دفع $type بمبلغ $amount $currency عبر $method";
            
            $manager->logActivity(
                'PAYMENT',
                'payments',
                "payment_$type",
                $description,
                [
                    'target_table' => 'payments',
                    'target_id' => $paymentId,
                    'new_values' => $paymentData,
                    'additional_data' => [
                        'payment_type' => $type,
                        'amount' => $amount,
                        'currency' => $currency,
                        'payment_method' => $method
                    ]
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logPayment Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل عملية تصدير
     */
    public static function logExport(string $module, string $format, array $filters = [], int $recordCount = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $description = "تصدير بيانات $module بصيغة $format";
            if ($recordCount) {
                $description .= " ($recordCount سجل)";
            }
            
            $manager->logActivity(
                'EXPORT',
                $module,
                "export_$module",
                $description,
                [
                    'additional_data' => [
                        'export_format' => $format,
                        'filters' => $filters,
                        'record_count' => $recordCount,
                        'export_time' => date('Y-m-d H:i:s')
                    ]
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logExport Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل خطأ في النظام
     */
    public static function logError(string $module, string $operation, string $errorMessage, array $context = []): void
    {
        try {
            $manager = new SystemActivityManager();
            $manager->logError($module, $operation, $errorMessage, [
                'additional_data' => $context
            ]);
        } catch (Exception $e) {
            error_log("ActivityHelper::logError Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل تحديث حالة
     */
    public static function logStatusChange(string $module, string $itemName, string $oldStatus, string $newStatus, int $itemId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $description = "تغيير حالة $itemName من '$oldStatus' إلى '$newStatus'";
            
            $manager->logUpdate(
                $module,
                "status_change_$module",
                $description,
                ['status' => $oldStatus],
                ['status' => $newStatus],
                [
                    'target_table' => $module,
                    'target_id' => $itemId,
                    'target_identifier' => $itemName,
                    'additional_data' => [
                        'status_change' => true,
                        'old_status' => $oldStatus,
                        'new_status' => $newStatus
                    ]
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logStatusChange Error: " . $e->getMessage());
        }
    }
    
    /**
     * تسجيل عملية موافقة/رفض
     */
    public static function logApproval(string $module, string $itemName, bool $approved, string $reason = null, int $itemId = null): void
    {
        try {
            $manager = new SystemActivityManager();
            $action = $approved ? 'موافقة على' : 'رفض';
            $description = "$action $itemName";
            if ($reason) {
                $description .= " - السبب: $reason";
            }
            
            $actionType = $approved ? 'APPROVE' : 'REJECT';
            
            $manager->logActivity(
                $actionType,
                $module,
                "approval_$module",
                $description,
                [
                    'target_table' => $module,
                    'target_id' => $itemId,
                    'target_identifier' => $itemName,
                    'additional_data' => [
                        'approved' => $approved,
                        'reason' => $reason,
                        'approval_time' => date('Y-m-d H:i:s')
                    ]
                ]
            );
        } catch (Exception $e) {
            error_log("ActivityHelper::logApproval Error: " . $e->getMessage());
        }
    }
}

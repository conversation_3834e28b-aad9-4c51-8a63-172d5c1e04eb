<?php
/**
 * اختبار البحث التفاعلي للدول والعملات
 * Test Interactive Country Search
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث التفاعلي للدول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .feature-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .demo-section { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-search text-primary"></i>
            اختبار البحث التفاعلي للدول والعملات
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>الميزة الجديدة</h5>
            <p class="mb-0">تم تحويل قوائم الدول والعملات من قوائم منسدلة ثابتة إلى <strong>مربعات بحث تفاعلية</strong> تدعم:</p>
            <ul class="mt-2 mb-0">
                <li>البحث بكتابة أي جزء من اسم الدولة أو رمز العملة</li>
                <li>عرض النتائج فورياً أثناء الكتابة</li>
                <li>واجهة سهلة الاستخدام مع إمكانية المسح والتعديل</li>
                <li>دعم كامل للغة العربية</li>
            </ul>
        </div>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                echo "<div class='test-result test-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
            
            // اختبار وجود بيانات الدول
            showInfo("<strong>اختبار بيانات الدول:</strong>");
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM countries WHERE is_active = 1");
                $countries_count = $stmt->fetch()['count'];
                showResult("وجود دول نشطة", $countries_count > 0, "يوجد {$countries_count} دولة نشطة");
                
                if ($countries_count > 0) {
                    // عرض عينة من الدول
                    $stmt = $pdo->query("SELECT name_ar, currency_code, currency_symbol FROM countries WHERE is_active = 1 LIMIT 5");
                    $sample_countries = $stmt->fetchAll();
                    
                    $countries_list = [];
                    foreach ($sample_countries as $country) {
                        $countries_list[] = $country['name_ar'] . ' (' . $country['currency_code'] . ')';
                    }
                    showResult("عينة من الدول", true, implode(' | ', $countries_list));
                }
            } catch (Exception $e) {
                showResult("فحص بيانات الدول", false, $e->getMessage());
            }
            
            // اختبار الملفات المحدثة
            showInfo("<strong>اختبار الملفات المحدثة:</strong>");
            
            $updated_files = [
                'dashboard/add_daily_transaction.php' => 'صفحة إضافة معاملة',
                'dashboard/edit_daily_transaction.php' => 'صفحة تعديل معاملة',
                'dashboard/daily_transactions.php' => 'صفحة قائمة المعاملات'
            ];
            
            foreach ($updated_files as $file => $description) {
                $file_path = __DIR__ . '/../' . $file;
                $exists = file_exists($file_path);
                
                if ($exists) {
                    $content = file_get_contents($file_path);
                    $has_search = strpos($content, 'country_search') !== false;
                    $has_js = strpos($content, 'initCountrySearch') !== false || strpos($content, 'initCountryFilterSearch') !== false;
                    
                    if ($has_search && $has_js) {
                        showResult($description, true, "يحتوي على البحث التفاعلي");
                    } else {
                        showResult($description, false, "لا يحتوي على البحث التفاعلي الكامل");
                    }
                } else {
                    showResult($description, false, "الملف غير موجود");
                }
            }
            
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 90) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> البحث التفاعلي يعمل بشكل مثالي";
            echo "</div>";
        } elseif ($success_rate >= 70) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> معظم الميزات تعمل";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج إصلاح!</strong> هناك مشاكل في النظام";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <!-- عرض توضيحي للبحث التفاعلي -->
        <div class="demo-section">
            <h3 class="text-center mb-4">
                <i class="fas fa-play-circle text-success me-2"></i>
                عرض توضيحي للبحث التفاعلي
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>البحث في الدول والعملات:</h5>
                    <div class="position-relative">
                        <input type="text" 
                               class="form-control" 
                               id="demo_search" 
                               placeholder="جرب البحث: مصر، USD، ريال..."
                               autocomplete="off">
                        
                        <div id="demo_results" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto; display: none;">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                    
                    <div id="demo_selected" class="mt-2" style="display: none;">
                        <div class="alert alert-success py-2 mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            <span id="demo_selected_text"></span>
                            <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearDemoSelection()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5>مميزات البحث:</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>بحث فوري أثناء الكتابة</li>
                        <li><i class="fas fa-check text-success me-2"></i>دعم البحث بالعربية والإنجليزية</li>
                        <li><i class="fas fa-check text-success me-2"></i>عرض اسم الدولة ورمز العملة</li>
                        <li><i class="fas fa-check text-success me-2"></i>واجهة سهلة ومرنة</li>
                        <li><i class="fas fa-check text-success me-2"></i>إمكانية المسح والتعديل</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- بطاقات الاختبار -->
        <div class="row mt-4">
            <div class="col-md-4 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            إضافة معاملة
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>اختبار البحث التفاعلي في صفحة الإضافة</p>
                        <a href="../dashboard/add_daily_transaction.php" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            اختبار الإضافة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تعديل معاملة
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>اختبار البحث التفاعلي في صفحة التعديل</p>
                        <a href="../dashboard/daily_transactions.php" class="btn btn-warning" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            اختبار التعديل
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            البحث والفلترة
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>اختبار البحث التفاعلي في الفلاتر</p>
                        <a href="../dashboard/daily_transactions.php" class="btn btn-success" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            اختبار الفلترة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="../dashboard/daily_transactions.php" class="btn btn-primary btn-lg">
                <i class="fas fa-list me-2"></i>
                قائمة المعاملات
            </a>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-success btn-lg ms-2">
                <i class="fas fa-plus me-2"></i>
                إضافة معاملة
            </a>
        </div>
    </div>

    <script>
        // بيانات تجريبية للعرض التوضيحي
        const demoCountries = [
            { id: 1, name_ar: 'مصر', currency_code: 'EGP', currency_symbol: 'ج.م' },
            { id: 2, name_ar: 'السعودية', currency_code: 'SAR', currency_symbol: 'ر.س' },
            { id: 3, name_ar: 'الإمارات', currency_code: 'AED', currency_symbol: 'د.إ' },
            { id: 4, name_ar: 'الكويت', currency_code: 'KWD', currency_symbol: 'د.ك' },
            { id: 5, name_ar: 'الأردن', currency_code: 'JOD', currency_symbol: 'د.أ' },
            { id: 6, name_ar: 'أمريكا', currency_code: 'USD', currency_symbol: '$' },
            { id: 7, name_ar: 'أوروبا', currency_code: 'EUR', currency_symbol: '€' }
        ];

        // البحث التوضيحي
        document.getElementById('demo_search').addEventListener('input', function() {
            const query = this.value.trim().toLowerCase();
            const resultsDiv = document.getElementById('demo_results');
            
            if (query.length < 1) {
                resultsDiv.style.display = 'none';
                return;
            }

            const filtered = demoCountries.filter(country => 
                country.name_ar.toLowerCase().includes(query) ||
                country.currency_code.toLowerCase().includes(query) ||
                country.currency_symbol.includes(query)
            );

            if (filtered.length === 0) {
                resultsDiv.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
            } else {
                let html = '';
                filtered.forEach(country => {
                    html += `
                        <div class="dropdown-item demo-option" 
                             data-id="${country.id}"
                             data-name="${country.name_ar}"
                             data-currency="${country.currency_code}"
                             data-symbol="${country.currency_symbol}"
                             style="cursor: pointer;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${country.name_ar}</strong>
                                    <small class="text-muted d-block">${country.currency_code} - ${country.currency_symbol}</small>
                                </div>
                                <span class="badge bg-primary">${country.currency_code}</span>
                            </div>
                        </div>
                    `;
                });
                resultsDiv.innerHTML = html;

                // إضافة مستمعي الأحداث
                resultsDiv.querySelectorAll('.demo-option').forEach(option => {
                    option.addEventListener('click', function() {
                        selectDemoCountry({
                            id: this.dataset.id,
                            name_ar: this.dataset.name,
                            currency_code: this.dataset.currency,
                            currency_symbol: this.dataset.symbol
                        });
                    });
                });
            }
            
            resultsDiv.style.display = 'block';
        });

        function selectDemoCountry(country) {
            const searchInput = document.getElementById('demo_search');
            const resultsDiv = document.getElementById('demo_results');
            const selectedDiv = document.getElementById('demo_selected');
            const selectedText = document.getElementById('demo_selected_text');

            searchInput.value = '';
            resultsDiv.style.display = 'none';
            
            selectedText.innerHTML = `<strong>${country.name_ar}</strong> - ${country.currency_code} (${country.currency_symbol})`;
            selectedDiv.style.display = 'block';
            searchInput.style.display = 'none';
        }

        function clearDemoSelection() {
            const searchInput = document.getElementById('demo_search');
            const selectedDiv = document.getElementById('demo_selected');
            const resultsDiv = document.getElementById('demo_results');

            searchInput.value = '';
            searchInput.style.display = 'block';
            selectedDiv.style.display = 'none';
            resultsDiv.style.display = 'none';
        }

        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', function(e) {
            const searchInput = document.getElementById('demo_search');
            const resultsDiv = document.getElementById('demo_results');
            
            if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                resultsDiv.style.display = 'none';
            }
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

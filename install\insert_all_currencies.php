<?php
/**
 * Insert all Arab and international currencies into the database
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إدخال جميع العملات العربية والأجنبية</h2>\n";

try {
    $conn = Database::getConnection();
    
    // All currencies data (Arab and International)
    $currencies = [
        // العملات العربية
        ['AED', 'درهم إماراتي', 'د.إ', 'الإمارات العربية المتحدة', 1],
        ['SAR', 'سعودي', 'ر.س', 'المملكة العربية السعودية', 1],
        ['KWD', 'دينار كويتي', 'د.ك', 'الكويت', 1],
        ['QAR', 'قطري', 'ر.ق', 'قطر', 1],
        ['BHD', 'دينار بحريني', 'د.ب', 'البحرين', 1],
        ['OMR', 'عماني', 'ر.ع', 'عمان', 1],
        ['JOD', 'دينار أردني', 'د.أ', 'الأردن', 1],
        ['LBP', 'ليرة لبنانية', 'ل.ل', 'لبنان', 1],
        ['SYP', 'ليرة سورية', 'ل.س', 'سوريا', 1],
        ['IQD', 'دينار عراقي', 'د.ع', 'العراق', 1],
        ['EGP', 'جنيه مصري', 'ج.م', 'مصر', 1],
        ['LYD', 'دينار ليبي', 'د.ل', 'ليبيا', 1],
        ['TND', 'دينار تونسي', 'د.ت', 'تونس', 1],
        ['DZD', 'دينار جزائري', 'د.ج', 'الجزائر', 1],
        ['MAD', 'درهم مغربي', 'د.م', 'المغرب', 1],
        ['MRU', 'أوقية موريتانية', 'أ.م', 'موريتانيا', 1],
        ['SDG', 'جنيه سوداني', 'ج.س', 'السودان', 1],
        ['SOS', 'شلن صومالي', 'ش.ص', 'الصومال', 1],
        ['DJF', 'فرنك جيبوتي', 'ف.ج', 'جيبوتي', 1],
        ['KMF', 'فرنك قمري', 'ف.ق', 'جزر القمر', 1],
        ['YER', 'يمني', 'ر.ي', 'اليمن', 1],
        
        // العملات الأجنبية الرئيسية
        ['USD', 'دولار أمريكي', '$', 'الولايات المتحدة الأمريكية', 1],
        ['EUR', 'يورو', '€', 'الاتحاد الأوروبي', 1],
        ['GBP', 'جنيه إسترليني', '£', 'المملكة المتحدة', 1],
        ['JPY', 'ين ياباني', '¥', 'اليابان', 1],
        ['CHF', 'فرنك سويسري', 'CHF', 'سويسرا', 1],
        ['CAD', 'دولار كندي', 'C$', 'كندا', 1],
        ['AUD', 'دولار أسترالي', 'A$', 'أستراليا', 1],
        ['NZD', 'دولار نيوزيلندي', 'NZ$', 'نيوزيلندا', 1],
        ['CNY', 'يوان صيني', '¥', 'الصين', 1],
        ['INR', 'روبية هندية', '₹', 'الهند', 1],
        ['KRW', 'وون كوري جنوبي', '₩', 'كوريا الجنوبية', 1],
        ['SGD', 'دولار سنغافوري', 'S$', 'سنغافورة', 1],
        ['HKD', 'دولار هونغ كونغ', 'HK$', 'هونغ كونغ', 1],
        ['NOK', 'كرونة نرويجية', 'kr', 'النرويج', 1],
        ['SEK', 'كرونة سويدية', 'kr', 'السويد', 1],
        ['DKK', 'كرونة دنماركية', 'kr', 'الدنمارك', 1],
        ['PLN', 'زلوتي بولندي', 'zł', 'بولندا', 1],
        ['CZK', 'كرونة تشيكية', 'Kč', 'التشيك', 1],
        ['HUF', 'فورنت هنغاري', 'Ft', 'المجر', 1],
        ['RUB', 'روبل روسي', '₽', 'روسيا', 1],
        ['TRY', 'ليرة تركية', '₺', 'تركيا', 1],
        ['ZAR', 'راند جنوب أفريقي', 'R', 'جنوب أفريقيا', 1],
        ['BRL', 'برازيلي', 'R$', 'البرازيل', 1],
        ['MXN', 'بيزو مكسيكي', '$', 'المكسيك', 1],
        ['ARS', 'بيزو أرجنتيني', '$', 'الأرجنتين', 1],
        ['CLP', 'بيزو تشيلي', '$', 'تشيلي', 1],
        ['COP', 'بيزو كولومبي', '$', 'كولومبيا', 1],
        ['PEN', 'سول بيروفي', 'S/', 'بيرو', 1],
        ['UYU', 'بيزو أوروغوياني', '$U', 'الأوروغواي', 1],
        ['THB', 'بات تايلاندي', '฿', 'تايلاند', 1],
        ['MYR', 'رينغت ماليزي', 'RM', 'ماليزيا', 1],
        ['IDR', 'روبية إندونيسية', 'Rp', 'إندونيسيا', 1],
        ['PHP', 'بيزو فلبيني', '₱', 'الفلبين', 1],
        ['VND', 'دونغ فيتنامي', '₫', 'فيتنام', 1],
        ['TWD', 'دولار تايواني', 'NT$', 'تايوان', 1],
        ['PKR', 'روبية باكستانية', '₨', 'باكستان', 1],
        ['BDT', 'تاكا بنغلاديشية', '৳', 'بنغلاديش', 1],
        ['LKR', 'روبية سريلانكية', '₨', 'سريلانكا', 1],
        ['NPR', 'روبية نيبالية', '₨', 'نيبال', 1],
        ['AFN', 'أفغاني', '؋', 'أفغانستان', 1],
        ['IRR', 'إيراني', '﷼', 'إيران', 1],
        ['ILS', 'شيكل إسرائيلي', '₪', 'إسرائيل', 1],
        ['GEL', 'لاري جورجي', '₾', 'جورجيا', 1],
        ['AMD', 'درام أرميني', '֏', 'أرمينيا', 1],
        ['AZN', 'مانات أذربيجاني', '₼', 'أذربيجان', 1],
        ['KZT', 'تنغة كازاخستاني', '₸', 'كازاخستان', 1],
        ['UZS', 'سوم أوزبكستاني', 'лв', 'أوزبكستان', 1],
        ['KGS', 'سوم قيرغيزستاني', 'лв', 'قيرغيزستان', 1],
        ['TJS', 'سوموني طاجيكستاني', 'SM', 'طاجيكستان', 1],
        ['TMT', 'مانات تركمانستاني', 'T', 'تركمانستان', 1],
        ['ETB', 'بير إثيوبي', 'Br', 'إثيوبيا', 1],
        ['KES', 'شلن كيني', 'KSh', 'كينيا', 1],
        ['UGX', 'شلن أوغندي', 'USh', 'أوغندا', 1],
        ['TZS', 'شلن تنزاني', 'TSh', 'تنزانيا', 1],
        ['RWF', 'فرنك رواندي', 'RF', 'رواندا', 1],
        ['BIF', 'فرنك بوروندي', 'FBu', 'بوروندي', 1],
        ['NGN', 'نايرا نيجيرية', '₦', 'نيجيريا', 1],
        ['GHS', 'سيدي غاني', '₵', 'غانا', 1],
        ['XOF', 'فرنك أفريقي غربي', 'CFA', 'غرب أفريقيا', 1],
        ['XAF', 'فرنك أفريقي وسطي', 'FCFA', 'وسط أفريقيا', 1],
        ['MGA', 'أرياري مدغشقري', 'Ar', 'مدغشقر', 1],
        ['MUR', 'روبية موريشيوسية', '₨', 'موريشيوس', 1],
        ['SCR', 'روبية سيشيلية', '₨', 'سيشيل', 1],

        // عملات أوروبية إضافية
        ['RON', 'ليو روماني', 'lei', 'رومانيا', 1],
        ['BGN', 'ليف بلغاري', 'лв', 'بلغاريا', 1],
        ['HRK', 'كونا كرواتية', 'kn', 'كرواتيا', 1],
        ['RSD', 'دينار صربي', 'РСД', 'صربيا', 1],
        ['BAM', 'مارك بوسني', 'KM', 'البوسنة والهرسك', 1],
        ['MKD', 'دينار مقدوني', 'ден', 'مقدونيا الشمالية', 1],
        ['ALL', 'ليك ألباني', 'L', 'ألبانيا', 1],
        ['MDL', 'ليو مولدوفي', 'L', 'مولدوفا', 1],
        ['UAH', 'هريفنيا أوكرانية', '₴', 'أوكرانيا', 1],
        ['BYN', 'روبل بيلاروسي', 'Br', 'بيلاروسيا', 1],
        ['ISK', 'كرونة آيسلندية', 'kr', 'آيسلندا', 1],

        // عملات آسيوية إضافية
        ['MMK', 'كيات ميانماري', 'K', 'ميانمار', 1],
        ['LAK', 'كيب لاوسي', '₭', 'لاوس', 1],
        ['KHR', 'كمبودي', '៛', 'كمبوديا', 1],
        ['BND', 'دولار بروناي', 'B$', 'بروناي', 1],
        ['MNT', 'توغريك منغولي', '₮', 'منغوليا', 1],
        ['BTN', 'نولترم بوتاني', 'Nu.', 'بوتان', 1],
        ['MVR', 'روفيا مالديفية', '.ރ', 'المالديف', 1],

        // عملات أمريكية إضافية
        ['GTQ', 'كتزال غواتيمالي', 'Q', 'غواتيمالا', 1],
        ['HNL', 'ليمبيرا هندوراسية', 'L', 'هندوراس', 1],
        ['NIO', 'كوردوبا نيكاراغوية', 'C$', 'نيكاراغوا', 1],
        ['CRC', 'كولون كوستاريكي', '₡', 'كوستاريكا', 1],
        ['PAB', 'بالبوا بنمية', 'B/.', 'بنما', 1],
        ['JMD', 'دولار جامايكي', 'J$', 'جامايكا', 1],
        ['HTG', 'غورد هايتي', 'G', 'هايتي', 1],
        ['DOP', 'بيزو دومينيكاني', 'RD$', 'جمهورية الدومينيكان', 1],
        ['CUP', 'بيزو كوبي', '₱', 'كوبا', 1],
        ['BBD', 'دولار بربادوسي', 'Bds$', 'بربادوس', 1],
        ['TTD', 'دولار ترينيداد وتوباغو', 'TT$', 'ترينيداد وتوباغو', 1],
        ['GYD', 'دولار غياني', 'G$', 'غيانا', 1],
        ['SRD', 'دولار سورينامي', '$', 'سورينام', 1],
        ['BOB', 'بوليفيانو بوليفي', 'Bs.', 'بوليفيا', 1],
        ['PYG', 'غواراني باراغوياني', '₲', 'باراغواي', 1],

        // عملات أفريقية إضافية
        ['BWP', 'بولا بوتسوانية', 'P', 'بوتسوانا', 1],
        ['SZL', 'ليلانغيني سوازيلاندية', 'L', 'إسواتيني', 1],
        ['LSL', 'لوتي ليسوتو', 'L', 'ليسوتو', 1],
        ['NAD', 'دولار ناميبي', 'N$', 'ناميبيا', 1],
        ['ZMW', 'كواشا زامبية', 'ZK', 'زامبيا', 1],
        ['ZWL', 'دولار زيمبابوي', 'Z$', 'زيمبابوي', 1],
        ['MWK', 'كواشا مالاوية', 'MK', 'مالاوي', 1],
        ['MZN', 'ميتيكال موزمبيقي', 'MT', 'موزمبيق', 1],
        ['AOA', 'كوانزا أنغولية', 'Kz', 'أنغولا', 1],
        ['CVE', 'إسكودو الرأس الأخضر', '$', 'الرأس الأخضر', 1],
        ['GMD', 'دالاسي غامبية', 'D', 'غامبيا', 1],
        ['GNF', 'فرنك غيني', 'FG', 'غينيا', 1],
        ['LRD', 'دولار ليبيري', 'L$', 'ليبيريا', 1],
        ['SLL', 'ليون سيراليوني', 'Le', 'سيراليون', 1],

        // عملات أوقيانوسية
        ['FJD', 'دولار فيجي', 'FJ$', 'فيجي', 1],
        ['TOP', 'بانغا تونغية', 'T$', 'تونغا', 1],
        ['WST', 'تالا ساموية', 'WS$', 'ساموا', 1],
        ['VUV', 'فاتو فانواتو', 'VT', 'فانواتو', 1],
        ['SBD', 'دولار جزر سليمان', 'SI$', 'جزر سليمان', 1],
        ['PGK', 'كينا بابوا غينيا الجديدة', 'K', 'بابوا غينيا الجديدة', 1],

        // عملات رقمية مشهورة (اختيارية)
        ['BTC', 'بيتكوين', '₿', 'عملة رقمية', 0],
        ['ETH', 'إيثريوم', 'Ξ', 'عملة رقمية', 0],
        ['USDT', 'تيثر', '₮', 'عملة رقمية مستقرة', 0],
        ['BNB', 'بينانس كوين', 'BNB', 'عملة رقمية', 0],
        ['ADA', 'كاردانو', '₳', 'عملة رقمية', 0],
        ['XRP', 'ريبل', 'XRP', 'عملة رقمية', 0],
        ['SOL', 'سولانا', 'SOL', 'عملة رقمية', 0],
        ['DOT', 'بولكادوت', 'DOT', 'عملة رقمية', 0],
        ['DOGE', 'دوجكوين', 'Ð', 'عملة رقمية', 0],
        ['AVAX', 'أفالانش', 'AVAX', 'عملة رقمية', 0]
    ];
    
    echo "<h3>1. فحص الجدول الحالي</h3>\n";
    
    // Check current currencies
    $result = $conn->query("SELECT COUNT(*) as count FROM currencies");
    $currentCount = $result->fetch_assoc()['count'];
    echo "<p><strong>عدد العملات الحالية:</strong> $currentCount</p>\n";
    
    echo "<h3>2. إدخال العملات الجديدة</h3>\n";
    
    // Prepare insert statement
    $insertSql = "INSERT INTO currencies (code, name, symbol, country, is_active, created_at, updated_at) 
                  VALUES (?, ?, ?, ?, ?, NOW(), NOW()) 
                  ON DUPLICATE KEY UPDATE 
                  name = VALUES(name), 
                  symbol = VALUES(symbol), 
                  country = VALUES(country),
                  updated_at = NOW()";
    
    $stmt = $conn->prepare($insertSql);
    if (!$stmt) {
        throw new Exception("فشل في تحضير الاستعلام: " . $conn->error);
    }
    
    $insertedCount = 0;
    $updatedCount = 0;
    $arabCurrencies = 0;
    $foreignCurrencies = 0;
    
    foreach ($currencies as $currency) {
        list($code, $name, $symbol, $country, $isActive) = $currency;
        
        // Check if currency exists
        $checkSql = "SELECT id FROM currencies WHERE code = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param('s', $code);
        $checkStmt->execute();
        $exists = $checkStmt->get_result()->num_rows > 0;
        $checkStmt->close();
        
        $stmt->bind_param('ssssi', $code, $name, $symbol, $country, $isActive);
        
        if ($stmt->execute()) {
            if ($exists) {
                $updatedCount++;
                echo "<p style='color: orange;'>⚠ تم تحديث: $code - $name ($symbol)</p>\n";
            } else {
                $insertedCount++;
                echo "<p style='color: green;'>✓ تم إدخال: $code - $name ($symbol)</p>\n";
            }
            
            // Count Arab vs Foreign currencies
            $arabCountries = ['الإمارات', 'السعودية', 'الكويت', 'قطر', 'البحرين', 'عمان', 'الأردن', 'لبنان', 'سوريا', 'العراق', 'مصر', 'ليبيا', 'تونس', 'الجزائر', 'المغرب', 'موريتانيا', 'السودان', 'الصومال', 'جيبوتي', 'القمر', 'اليمن'];
            $isArab = false;
            foreach ($arabCountries as $arabCountry) {
                if (strpos($country, $arabCountry) !== false) {
                    $isArab = true;
                    break;
                }
            }
            
            if ($isArab) {
                $arabCurrencies++;
            } else {
                $foreignCurrencies++;
            }
            
        } else {
            echo "<p style='color: red;'>✗ فشل في إدخال: $code - " . $stmt->error . "</p>\n";
        }
    }
    
    $stmt->close();
    
    echo "<h3>3. ملخص العملية</h3>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
    echo "<p><strong>إحصائيات الإدخال:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><strong>عملات جديدة:</strong> $insertedCount</li>\n";
    echo "<li><strong>عملات محدثة:</strong> $updatedCount</li>\n";
    echo "<li><strong>عملات عربية:</strong> $arabCurrencies</li>\n";
    echo "<li><strong>عملات أجنبية:</strong> $foreignCurrencies</li>\n";
    echo "<li><strong>إجمالي العملات:</strong> " . ($arabCurrencies + $foreignCurrencies) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>4. فحص النتيجة النهائية</h3>\n";
    
    // Check final count
    $result = $conn->query("SELECT COUNT(*) as count FROM currencies");
    $finalCount = $result->fetch_assoc()['count'];
    echo "<p><strong>العدد النهائي للعملات:</strong> $finalCount</p>\n";
    
    // Show sample currencies
    $sampleResult = $conn->query("
        SELECT code, name, symbol, country 
        FROM currencies 
        ORDER BY 
            CASE 
                WHEN country LIKE '%العربية%' OR country LIKE '%الإمارات%' OR country LIKE '%السعودية%' OR country LIKE '%الكويت%' OR country LIKE '%قطر%' OR country LIKE '%البحرين%' OR country LIKE '%عمان%' OR country LIKE '%الأردن%' OR country LIKE '%لبنان%' OR country LIKE '%سوريا%' OR country LIKE '%العراق%' OR country LIKE '%مصر%' OR country LIKE '%ليبيا%' OR country LIKE '%تونس%' OR country LIKE '%الجزائر%' OR country LIKE '%المغرب%' OR country LIKE '%موريتانيا%' OR country LIKE '%السودان%' OR country LIKE '%الصومال%' OR country LIKE '%جيبوتي%' OR country LIKE '%القمر%' OR country LIKE '%اليمن%' THEN 1 
                ELSE 2 
            END,
            name
        LIMIT 20
    ");
    
    if ($sampleResult) {
        echo "<p><strong>عينة من العملات المدخلة:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>الرمز</th><th>اسم العملة</th><th>الرمز المختصر</th><th>البلد</th>\n";
        echo "</tr>\n";
        
        while ($row = $sampleResult->fetch_assoc()) {
            echo "<tr>\n";
            echo "<td><strong>{$row['code']}</strong></td>\n";
            echo "<td>{$row['name']}</td>\n";
            echo "<td>{$row['symbol']}</td>\n";
            echo "<td>{$row['country']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إدخال جميع العملات بنجاح!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>اختبر إنشاء صندوق بعملة جديدة</a></li>\n";
    echo "<li><a href='../dashboard/exchanges.php' target='_blank'>اختبر عمليات الصرافة</a></li>\n";
    echo "<li>يمكنك الآن استخدام أي عملة من العملات المدخلة في النظام</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

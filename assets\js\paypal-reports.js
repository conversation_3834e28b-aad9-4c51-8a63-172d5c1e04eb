/**
 * تحسينات JavaScript لصفحة تقارير PayPal
 * PayPal Reports JavaScript Enhancements
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // تحسين تجربة الفلاتر
    initializeFilters();
    
    // تحسين بطاقات الإحصائيات
    animateStatsCards();
    
    // تحسين الجداول
    enhanceTables();
    
    // إضافة تأثيرات التحميل
    addLoadingEffects();
    
    // تحسين الرسوم البيانية
    enhanceCharts();
    
    console.log('📊 PayPal Reports enhancements loaded successfully!');
});

// تحسين الفلاتر
function initializeFilters() {
    const filterForm = document.querySelector('form[method="GET"]');
    const filterInputs = filterForm.querySelectorAll('input, select');
    
    // إضافة تأثيرات للمدخلات
    filterInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            this.style.transform = 'translateY(-2px)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            this.style.transform = '';
        });
        
        // تحديث تلقائي للتواريخ
        if (input.type === 'date') {
            input.addEventListener('change', function() {
                validateDateRange();
            });
        }
    });
    
    // إضافة أزرار سريعة للفترات
    addQuickDateButtons();
}

// إضافة أزرار الفترات السريعة
function addQuickDateButtons() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (!dateFromInput || !dateToInput) return;
    
    const quickButtonsContainer = document.createElement('div');
    quickButtonsContainer.className = 'quick-date-buttons mt-2';
    quickButtonsContainer.innerHTML = `
        <div class="btn-group btn-group-sm" role="group">
            <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('today')">اليوم</button>
            <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('week')">هذا الأسبوع</button>
            <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('month')">هذا الشهر</button>
            <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('quarter')">هذا الربع</button>
            <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('year')">هذا العام</button>
        </div>
    `;
    
    dateToInput.parentElement.appendChild(quickButtonsContainer);
}

// تعيين نطاقات التواريخ السريعة
window.setDateRange = function(period) {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    const today = new Date();
    let fromDate, toDate;
    
    switch(period) {
        case 'today':
            fromDate = toDate = today;
            break;
        case 'week':
            fromDate = new Date(today.setDate(today.getDate() - today.getDay()));
            toDate = new Date();
            break;
        case 'month':
            fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
            toDate = new Date();
            break;
        case 'quarter':
            const quarter = Math.floor(today.getMonth() / 3);
            fromDate = new Date(today.getFullYear(), quarter * 3, 1);
            toDate = new Date();
            break;
        case 'year':
            fromDate = new Date(today.getFullYear(), 0, 1);
            toDate = new Date();
            break;
    }
    
    dateFromInput.value = fromDate.toISOString().split('T')[0];
    dateToInput.value = toDate.toISOString().split('T')[0];
    
    // تأثير بصري للتحديث
    [dateFromInput, dateToInput].forEach(input => {
        input.style.background = '#e3f2fd';
        setTimeout(() => {
            input.style.background = '';
        }, 1000);
    });
};

// التحقق من صحة نطاق التواريخ
function validateDateRange() {
    const dateFrom = document.getElementById('date_from').value;
    const dateTo = document.getElementById('date_to').value;
    
    if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
        showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
        return false;
    }
    return true;
}

// تحريك بطاقات الإحصائيات
function animateStatsCards() {
    const statsCards = document.querySelectorAll('.col-xl-3 .card, .col-md-6 .card');
    
    statsCards.forEach((card, index) => {
        // تأثير الظهور التدريجي
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
        
        // تأثير التمرير
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0, 112, 186, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });
        
        // تحريك الأرقام
        animateNumber(card);
    });
}

// تحريك الأرقام في البطاقات
function animateNumber(card) {
    const numberElement = card.querySelector('h4');
    if (!numberElement) return;
    
    const finalNumber = numberElement.textContent;
    const numericValue = parseFloat(finalNumber.replace(/[^0-9.-]+/g, ''));
    
    if (isNaN(numericValue)) return;
    
    let currentNumber = 0;
    const increment = numericValue / 50;
    const timer = setInterval(() => {
        currentNumber += increment;
        if (currentNumber >= numericValue) {
            currentNumber = numericValue;
            clearInterval(timer);
        }
        
        // تحديث النص مع الحفاظ على التنسيق
        if (finalNumber.includes('$')) {
            numberElement.textContent = '$' + Math.floor(currentNumber).toLocaleString();
        } else {
            numberElement.textContent = Math.floor(currentNumber).toLocaleString();
        }
    }, 30);
}

// تحسين الجداول
function enhanceTables() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // إضافة تأثيرات للصفوف
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                row.style.transition = 'all 0.4s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateX(0)';
            }, index * 50);
            
            // تأثير التمرير
            row.addEventListener('mouseenter', function() {
                this.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)';
                this.style.transform = 'scale(1.01)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.background = '';
                this.style.transform = '';
            });
        });
    });
}

// إضافة تأثيرات التحميل
function addLoadingEffects() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير النقر
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
            ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
            
            this.style.position = 'relative';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
            
            // إضافة حالة التحميل للأزرار المناسبة
            if (this.type === 'submit' || this.onclick) {
                addLoadingState(this);
            }
        });
    });
}

// إضافة حالة التحميل للزر
function addLoadingState(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="loading"></span> جاري التحميل...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 2000);
}

// تحسين الرسوم البيانية
function enhanceCharts() {
    // إضافة تأثيرات للرسوم البيانية عند التحميل
    const chartContainers = document.querySelectorAll('canvas');
    
    chartContainers.forEach((canvas, index) => {
        canvas.style.opacity = '0';
        canvas.style.transform = 'scale(0.8)';
        
        setTimeout(() => {
            canvas.style.transition = 'all 0.8s ease';
            canvas.style.opacity = '1';
            canvas.style.transform = 'scale(1)';
        }, 500 + (index * 200));
    });
}

// عرض التنبيهات
function showAlert(message, type = 'info') {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertContainer.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    `;
    
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertContainer.parentNode) {
            alertContainer.remove();
        }
    }, 5000);
}

// تحسين دوال التصدير
window.exportReport = function(format) {
    const button = event.target.closest('.btn');
    const originalText = button.innerHTML;
    
    // إضافة حالة التحميل
    button.innerHTML = '<span class="loading"></span> جاري التصدير...';
    button.disabled = true;
    
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    
    // فتح نافذة التصدير
    const exportWindow = window.open('export_paypal_report.php?' + params.toString(), '_blank');
    
    // استعادة الزر بعد فترة
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        if (exportWindow) {
            showAlert(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`, 'success');
        } else {
            showAlert('فشل في فتح نافذة التصدير. تأكد من السماح للنوافذ المنبثقة', 'warning');
        }
    }, 2000);
};

// تحسين دالة الطباعة
window.printReport = function() {
    const button = event.target.closest('.btn');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<span class="loading"></span> جاري التحضير...';
    button.disabled = true;
    
    // إخفاء العناصر غير المرغوب في طباعتها
    const elementsToHide = document.querySelectorAll('.btn, .filters-card .card-body, .sidebar-container');
    elementsToHide.forEach(el => el.style.display = 'none');
    
    setTimeout(() => {
        window.print();
        
        // إظهار العناصر مرة أخرى
        elementsToHide.forEach(el => el.style.display = '');
        
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
};

// إضافة الأنماط المطلوبة
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .loading {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 5px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .focused {
        transform: scale(1.02);
    }
    
    .quick-date-buttons .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .quick-date-buttons .btn:hover {
        background-color: #0070ba;
        color: white;
        border-color: #0070ba;
    }
`;

document.head.appendChild(style);

        </div> <!-- /.main-content -->
    </div> <!-- /.page-wrapper -->

    <footer class="bg-light text-center py-3 mt-auto">
        <div class="container">
            <span class="text-muted">&copy; <?php echo date('Y'); ?> <?php echo SYSTEM_NAME; ?>. جميع الحقوق محفوظة.</span>
        </div>
    </footer>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript Files -->
    <script src="<?php echo BASE_URL; ?>assets/js/tables-responsive.js"></script>

    <!-- Unified Sidebar JS - الحل الموحد للشريط الجانبي -->
    <?php if (strpos($_SERVER['REQUEST_URI'], '/dashboard/') !== false): ?>
    <script>
    // منع تحميل ملفات JavaScript المتضاربة
    window.SIDEBAR_UNIFIED_LOADED = true;
    window.PREVENT_OLD_SIDEBAR_SCRIPTS = true;
    </script>
    <script src="<?php echo BASE_URL; ?>assets/js/sidebar-unified.js"></script>
    <?php endif; ?>

    <!-- PayPal Reports JS - تحميل في صفحة التقارير فقط -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'paypal_reports.php'): ?>
    <script src="<?php echo BASE_URL; ?>assets/js/paypal-reports.js"></script>
    <?php endif; ?>

    <!-- Dashboard JS - وظائف لوحة التحكم الأساسية -->
    <?php if (strpos($_SERVER['REQUEST_URI'], '/dashboard/') !== false): ?>
    <script>
    // تهيئة وظائف لوحة التحكم الأساسية فقط (بدون تضارب مع الشريط الجانبي)
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة التلميحات
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        console.log('✅ تم تحميل وظائف لوحة التحكم الأساسية');
    });
    </script>
    <?php endif; ?>

    <?php if (basename($_SERVER['PHP_SELF']) == 'transfers.php'): ?>
    <!-- Transfers-specific JavaScript -->
    <script src="<?php echo BASE_URL; ?>assets/js/transfers-page.js"></script>
    <?php endif; ?>

    <?php if (basename($_SERVER['PHP_SELF']) == 'exchange.php'): ?>
    <!-- Exchange-specific JavaScript -->
    <script src="<?php echo BASE_URL; ?>assets/js/exchange.js"></script>
    <?php endif; ?>
</body>
</html>
<?php
require_once __DIR__ . '/database.php';

/**
 * CurrencyManager – minimal helper to retrieve currencies list.
 */
class CurrencyManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Get list of currencies.
     *
     * @param bool $onlyActive If true, filter to is_active = 1
     * @return array<int, array<string,mixed>>
     */
    public function getAllCurrencies(bool $onlyActive = false): array
    {
        $sql = 'SELECT id, name, code, symbol FROM currencies';
        if ($onlyActive) {
            $sql .= ' WHERE is_active = 1';
        }
        $sql .= ' ORDER BY name';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get currency by ID.
     *
     * @param int $id Currency ID
     * @return array|null Currency data or null if not found
     */
    public function getCurrencyById(int $id): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM currencies WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $res = $stmt->get_result();
        $currency = $res ? $res->fetch_assoc() : null;
        $stmt->close();
        return $currency ?: null;
    }

    /**
     * Add new currency.
     *
     * @param array $data Currency data
     * @return int|false New currency ID or false on failure
     */
    public function addCurrency(array $data)
    {
        $sql = 'INSERT INTO currencies (name, code, symbol, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, NOW(), NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $isActive = isset($data['is_active']) ? (int)$data['is_active'] : 1;
        $stmt->bind_param('sssi',
            $data['name'],
            $data['code'],
            $data['symbol'],
            $isActive
        );

        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }

    /**
     * Update currency.
     *
     * @param int $id Currency ID
     * @param array $data Currency data
     * @return bool Success status
     */
    public function updateCurrency(int $id, array $data): bool
    {
        if (empty($data)) return false;

        $fields = [];
        $params = [];
        $types = '';

        foreach ($data as $key => $value) {
            if (in_array($key, ['name', 'code', 'symbol', 'is_active'])) {
                $fields[] = "$key = ?";
                $params[] = $value;
                $types .= is_int($value) ? 'i' : 's';
            }
        }

        if (empty($fields)) return false;

        $sql = 'UPDATE currencies SET ' . implode(', ', $fields) . ', updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $types .= 'i';
        $params[] = $id;
        $stmt->bind_param($types, ...$params);
        $success = $stmt->execute();
        $stmt->close();

        return $success;
    }

    /**
     * Delete currency.
     *
     * @param int $id Currency ID
     * @return bool Success status
     */
    public function deleteCurrency(int $id): bool
    {
        $stmt = $this->db->prepare('DELETE FROM currencies WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $id);
        $success = $stmt->execute();
        $stmt->close();
        return $success;
    }
} 
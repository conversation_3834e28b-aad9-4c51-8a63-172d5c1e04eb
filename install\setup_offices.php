<?php
/**
 * Setup Offices System
 * This script creates the offices tables and sample data
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إعداد نظام المكاتب وأسعار القص</h2>";

try {
    $db = Database::getConnection();
    
    // Start transaction
    $db->begin_transaction();
    
    echo "<p>جاري إنشاء جداول المكاتب...</p>";
    
    // Read SQL file
    $sql = file_get_contents(__DIR__ . '/create_offices_tables.sql');
    
    // Execute multi-query SQL
    if ($db->multi_query($sql)) {
        do {
            // Store first result set
            if ($result = $db->store_result()) {
                $result->free();
            }
            // Check for more results
        } while ($db->more_results() && $db->next_result());
        
        echo "<p style='color: green;'>✓ تم إنشاء جداول المكاتب وأسعار القص بنجاح</p>";
    } else {
        throw new Exception("فشل في إنشاء جداول المكاتب: " . $db->error);
    }
    
    // Commit transaction
    $db->commit();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ تم إعداد نظام المكاتب بنجاح</h3>";
    echo "<p style='color: #155724; margin: 0;'>تم إنشاء جداول المكاتب وأسعار القص وإضافة بيانات افتراضية.</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ ملاحظات مهمة:</h4>";
    echo "<ul style='color: #856404; margin: 0;'>";
    echo "<li>تم إنشاء 3 مكاتب افتراضية</li>";
    echo "<li>تم إضافة أسعار قص افتراضية</li>";
    echo "<li>تم إضافة الصلاحيات اللازمة</li>";
    echo "<li>يمكنك الآن استخدام نظام المكاتب</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/offices.php' style='color: #007bff; text-decoration: none;'>→ الذهاب إلى صفحة المكاتب</a></p>";
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($db)) {
        $db->rollback();
    }
    
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في إعداد نظام المكاتب</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
}
?> 
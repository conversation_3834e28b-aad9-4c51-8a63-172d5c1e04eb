<?php
/**
 * إصلاح مشكلة role_id في النظام
 * Fix role_id undefined issue
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/auth.php';

echo "<h2>إصلاح مشكلة role_id في النظام</h2>\n";

try {
    // التحقق من الجلسة الحالية
    session_start();
    
    echo "<h3>1. التحقق من الجلسة الحالية</h3>\n";
    if (isset($_SESSION['user_id'])) {
        echo "<p>✅ المستخدم مسجل دخول: " . ($_SESSION['username'] ?? 'غير محدد') . "</p>\n";
        echo "<p>معرف المستخدم: " . $_SESSION['user_id'] . "</p>\n";
        echo "<p>role_id في الجلسة: " . ($_SESSION['role_id'] ?? 'غير موجود') . "</p>\n";
    } else {
        echo "<p>⚠️ لا يوجد مستخدم مسجل دخول</p>\n";
    }
    
    // التحقق من قاعدة البيانات
    echo "<h3>2. التحقق من قاعدة البيانات</h3>\n";
    
    $db = Database::getConnection();
    
    // التحقق من جدول المستخدمين
    $usersCheck = $db->query("DESCRIBE users");
    $userColumns = [];
    while ($row = $usersCheck->fetch_assoc()) {
        $userColumns[] = $row['Field'];
    }
    
    if (in_array('role_id', $userColumns)) {
        echo "<p>✅ عمود role_id موجود في جدول users</p>\n";
    } else {
        echo "<p>❌ عمود role_id غير موجود في جدول users</p>\n";
        
        // إضافة عمود role_id إذا لم يكن موجوداً
        $addColumn = $db->query("ALTER TABLE users ADD COLUMN role_id INT UNSIGNED NULL AFTER phone");
        if ($addColumn) {
            echo "<p>✅ تم إضافة عمود role_id</p>\n";
        } else {
            echo "<p>❌ فشل في إضافة عمود role_id: " . $db->error . "</p>\n";
        }
    }
    
    // التحقق من وجود الأدوار
    echo "<h3>3. التحقق من الأدوار</h3>\n";
    
    $rolesCheck = $db->query("SHOW TABLES LIKE 'roles'");
    if ($rolesCheck && $rolesCheck->num_rows > 0) {
        echo "<p>✅ جدول roles موجود</p>\n";
        
        // عرض الأدوار الموجودة
        $rolesResult = $db->query("SELECT id, name FROM roles ORDER BY id");
        if ($rolesResult && $rolesResult->num_rows > 0) {
            echo "<p><strong>الأدوار الموجودة:</strong></p>\n";
            echo "<ul>\n";
            while ($role = $rolesResult->fetch_assoc()) {
                echo "<li>ID: {$role['id']} - {$role['name']}</li>\n";
            }
            echo "</ul>\n";
        }
    } else {
        echo "<p>❌ جدول roles غير موجود</p>\n";
        
        // إنشاء جدول الأدوار
        $createRoles = $db->query("
            CREATE TABLE IF NOT EXISTS roles (
                id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                status ENUM('active','inactive') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        if ($createRoles) {
            echo "<p>✅ تم إنشاء جدول roles</p>\n";
            
            // إضافة الأدوار الأساسية
            $insertRoles = $db->query("
                INSERT IGNORE INTO roles (id, name, description) VALUES
                (1, 'System Admin', 'مدير النظام'),
                (2, 'Branch Manager', 'مدير فرع'),
                (3, 'Cashier', 'أمين صندوق'),
                (4, 'User', 'مستخدم عادي')
            ");
            
            if ($insertRoles) {
                echo "<p>✅ تم إضافة الأدوار الأساسية</p>\n";
            }
        }
    }
    
    // التحقق من المستخدمين بدون role_id
    echo "<h3>4. إصلاح المستخدمين بدون role_id</h3>\n";
    
    $usersWithoutRole = $db->query("SELECT id, username FROM users WHERE role_id IS NULL");
    if ($usersWithoutRole && $usersWithoutRole->num_rows > 0) {
        echo "<p>⚠️ يوجد " . $usersWithoutRole->num_rows . " مستخدم بدون role_id</p>\n";
        
        while ($user = $usersWithoutRole->fetch_assoc()) {
            // تعيين دور الأدمن للمستخدم الأول، ودور عادي للباقي
            $roleId = ($user['id'] == 1) ? 1 : 4;
            $updateRole = $db->prepare("UPDATE users SET role_id = ? WHERE id = ?");
            $updateRole->bind_param('ii', $roleId, $user['id']);
            
            if ($updateRole->execute()) {
                $roleName = ($roleId == 1) ? 'System Admin' : 'User';
                echo "<p>✅ تم تعيين دور '$roleName' للمستخدم: {$user['username']}</p>\n";
            }
        }
    } else {
        echo "<p>✅ جميع المستخدمين لديهم role_id</p>\n";
    }
    
    // اختبار دالة getCurrentUser
    echo "<h3>5. اختبار دالة getCurrentUser</h3>\n";
    
    if (isset($_SESSION['user_id'])) {
        $auth = new Auth();
        $currentUser = $auth->getCurrentUser();
        
        if ($currentUser) {
            echo "<p>✅ دالة getCurrentUser تعمل بشكل صحيح</p>\n";
            echo "<p><strong>بيانات المستخدم الحالي:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>ID: " . ($currentUser['id'] ?? 'غير محدد') . "</li>\n";
            echo "<li>Username: " . ($currentUser['username'] ?? 'غير محدد') . "</li>\n";
            echo "<li>Role ID: " . ($currentUser['role_id'] ?? 'غير محدد') . "</li>\n";
            echo "<li>Role Name: " . ($currentUser['role_name'] ?? 'غير محدد') . "</li>\n";
            echo "<li>Full Name: " . ($currentUser['full_name'] ?? 'غير محدد') . "</li>\n";
            echo "</ul>\n";
            
            // التحقق من صلاحية الأدمن
            if (isset($currentUser['role_id']) && (int)$currentUser['role_id'] === 1) {
                echo "<p style='color: green;'>✅ المستخدم الحالي هو أدمن - يمكنه الوصول لسجل العمليات</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠️ المستخدم الحالي ليس أدمن</p>\n";
            }
        } else {
            echo "<p>❌ فشل في جلب بيانات المستخدم</p>\n";
        }
    } else {
        echo "<p>⚠️ لا يمكن اختبار getCurrentUser - لا يوجد مستخدم مسجل دخول</p>\n";
    }
    
    // إنشاء مستخدم أدمن تجريبي إذا لم يوجد
    echo "<h3>6. التحقق من وجود مستخدم أدمن</h3>\n";
    
    $adminCheck = $db->query("SELECT id FROM users WHERE role_id = 1 LIMIT 1");
    if ($adminCheck && $adminCheck->num_rows > 0) {
        echo "<p>✅ يوجد مستخدم أدمن في النظام</p>\n";
    } else {
        echo "<p>⚠️ لا يوجد مستخدم أدمن - سيتم إنشاء واحد</p>\n";
        
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $createAdmin = $db->prepare("
            INSERT INTO users (username, password, full_name, role_id, status, created_at) 
            VALUES ('admin', ?, 'System Administrator', 1, 'active', NOW())
            ON DUPLICATE KEY UPDATE role_id = 1
        ");
        $createAdmin->bind_param('s', $adminPassword);
        
        if ($createAdmin->execute()) {
            echo "<p>✅ تم إنشاء/تحديث مستخدم الأدمن</p>\n";
            echo "<p><strong>بيانات الدخول:</strong></p>\n";
            echo "<p>اسم المستخدم: admin</p>\n";
            echo "<p>كلمة المرور: admin123</p>\n";
        }
    }
    
    // النتيجة النهائية
    echo "<hr>\n";
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h3>🎉 تم إصلاح مشكلة role_id بنجاح!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>قم بتسجيل الخروج وإعادة تسجيل الدخول</li>\n";
    echo "<li>انتقل إلى <a href='../dashboard/system_activity_logs.php'>صفحة سجل العمليات</a></li>\n";
    echo "<li>تأكد من عدم ظهور أخطاء role_id</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h3>❌ خطأ في الإصلاح</h3>\n";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><a href='../auth/logout.php'>تسجيل الخروج</a> | ";
echo "<a href='../auth/login.php'>تسجيل الدخول</a> | ";
echo "<a href='../dashboard/'>لوحة التحكم</a></p>\n";
?>

<?php
/**
 * Test script for exchange functionality after lock fixes
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/exchange_manager.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';
require_once __DIR__ . '/../includes/cash_manager.php';

echo "<h2>اختبار وظائف الصرافة بعد إصلاح الأقفال</h2>\n";

try {
    $db = Database::getConnection();
    
    echo "<h3>1. فحص اتصال قاعدة البيانات</h3>\n";
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    echo "<h3>2. فحص الجداول المطلوبة</h3>\n";
    
    $requiredTables = [
        'exchanges',
        'cash_movements', 
        'bank_movements',
        'customers',
        'currencies',
        'cash_boxes',
        'bank_accounts'
    ];
    
    foreach ($requiredTables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ جدول $table موجود</p>\n";
        } else {
            echo "<p style='color: red;'>✗ جدول $table مفقود</p>\n";
        }
    }
    
    echo "<h3>3. فحص أعمدة exchange_id</h3>\n";
    
    $exchangeColumns = [
        'cash_movements' => 'exchange_id',
        'bank_movements' => 'exchange_id'
    ];
    
    foreach ($exchangeColumns as $table => $column) {
        $result = $db->query("SHOW COLUMNS FROM $table LIKE '$column'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ عمود $column في جدول $table موجود</p>\n";
        } else {
            echo "<p style='color: red;'>✗ عمود $column في جدول $table مفقود</p>\n";
        }
    }
    
    echo "<h3>4. فحص البيانات الأساسية</h3>\n";
    
    // Check customers
    $result = $db->query("SELECT COUNT(*) as count FROM customers WHERE status = 'active'");
    $customerCount = $result->fetch_assoc()['count'];
    echo "<p><strong>العملاء النشطين:</strong> $customerCount</p>\n";
    
    // Check currencies
    $result = $db->query("SELECT COUNT(*) as count FROM currencies WHERE is_active = 1");
    $currencyCount = $result->fetch_assoc()['count'];
    echo "<p><strong>العملات النشطة:</strong> $currencyCount</p>\n";
    
    // Check cash boxes
    $result = $db->query("SELECT COUNT(*) as count FROM cash_boxes WHERE status = 'active'");
    $cashBoxCount = $result->fetch_assoc()['count'];
    echo "<p><strong>الصناديق النشطة:</strong> $cashBoxCount</p>\n";
    
    // Check exchange rates
    $result = $db->query("SELECT COUNT(*) as count FROM exchange_rates WHERE is_active = 1");
    $rateCount = $result->fetch_assoc()['count'];
    echo "<p><strong>أسعار الصرف النشطة:</strong> $rateCount</p>\n";
    
    echo "<h3>5. اختبار إنشاء مدراء النظام</h3>\n";
    
    try {
        $exManager = new ExchangeManager(new Database());
        echo "<p style='color: green;'>✓ ExchangeManager تم إنشاؤه بنجاح</p>\n";
        
        $customerMgr = new CustomerManager(new Database());
        echo "<p style='color: green;'>✓ CustomerManager تم إنشاؤه بنجاح</p>\n";
        
        $curManager = new CurrencyManager(new Database());
        echo "<p style='color: green;'>✓ CurrencyManager تم إنشاؤه بنجاح</p>\n";
        
        $cashMgr = new CashManager(new Database());
        echo "<p style='color: green;'>✓ CashManager تم إنشاؤه بنجاح</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في إنشاء المدراء: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h3>6. فحص إعدادات MySQL</h3>\n";
    
    $mysqlSettings = [
        "SELECT @@innodb_lock_wait_timeout as value, 'innodb_lock_wait_timeout' as setting",
        "SELECT @@wait_timeout as value, 'wait_timeout' as setting",
        "SELECT @@max_connections as value, 'max_connections' as setting",
        "SELECT @@autocommit as value, 'autocommit' as setting"
    ];
    
    foreach ($mysqlSettings as $query) {
        try {
            $result = $db->query($query);
            if ($result) {
                $row = $result->fetch_assoc();
                echo "<p><strong>{$row['setting']}:</strong> {$row['value']}</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: orange;'>تعذر فحص إعداد MySQL</p>\n";
        }
    }
    
    echo "<h3>7. اختبار عملية صرف تجريبية (محاكاة)</h3>\n";
    
    // Get sample data for testing
    $customerResult = $db->query("SELECT id FROM customers WHERE status = 'active' LIMIT 1");
    $currencyResult = $db->query("SELECT id FROM currencies WHERE is_active = 1 LIMIT 2");
    $cashBoxResult = $db->query("SELECT id FROM cash_boxes WHERE status = 'active' LIMIT 1");
    
    if ($customerResult && $customerResult->num_rows > 0 &&
        $currencyResult && $currencyResult->num_rows >= 2 &&
        $cashBoxResult && $cashBoxResult->num_rows > 0) {
        
        echo "<p style='color: green;'>✓ البيانات الأساسية متوفرة لاختبار عملية الصرف</p>\n";
        echo "<p style='color: blue;'>ℹ️ يمكنك الآن اختبار إنشاء عملية صرف من الواجهة</p>\n";
        
    } else {
        echo "<p style='color: orange;'>⚠ البيانات الأساسية غير مكتملة:</p>\n";
        if (!$customerResult || $customerResult->num_rows === 0) {
            echo "<p style='color: red;'>- لا توجد عملاء نشطين</p>\n";
        }
        if (!$currencyResult || $currencyResult->num_rows < 2) {
            echo "<p style='color: red;'>- تحتاج إلى عملتين نشطتين على الأقل</p>\n";
        }
        if (!$cashBoxResult || $cashBoxResult->num_rows === 0) {
            echo "<p style='color: red;'>- لا توجد صناديق نشطة</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى فحص النظام</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/exchange.php' target='_blank'>اختبر إنشاء عملية صرف جديدة</a></li>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>تحقق من الصناديق</a></li>\n";
    echo "<li><a href='../dashboard/exchange_rates.php' target='_blank'>تحقق من أسعار الصرف</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الاختبار: " . $e->getMessage() . "</p>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

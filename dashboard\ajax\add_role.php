<?php
/**
 * إضافة دور جديد عبر AJAX
 * Add New Role via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/role_manager.php';
require_once __DIR__ . '/../../includes/activity_helper.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.create')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لإضافة أدوار جديدة'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

try {
    // استلام البيانات
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $status = trim($_POST['status'] ?? 'نشط');
    
    // التحقق من صحة البيانات
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم الدور مطلوب';
    } elseif (strlen($name) < 3) {
        $errors[] = 'اسم الدور يجب أن يكون 3 أحرف على الأقل';
    }
    
    if (empty($description)) {
        $errors[] = 'وصف الدور مطلوب';
    } elseif (strlen($description) < 10) {
        $errors[] = 'وصف الدور يجب أن يكون 10 أحرف على الأقل';
    }
    
    if (!in_array($status, ['نشط', 'غير نشط'])) {
        $errors[] = 'حالة الدور غير صحيحة';
    }
    
    // التحقق من عدم تكرار اسم الدور
    $db = new Database();
    $roleMgr = new RoleManager($db);
    
    if ($roleMgr->roleNameExists($name)) {
        $errors[] = 'اسم الدور موجود بالفعل';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'أخطاء في البيانات',
            'errors' => $errors
        ]);
        exit;
    }
    
    // إضافة الدور
    $data = [
        'name' => $name,
        'description' => $description,
        'status' => $status
    ];
    
    $newId = $roleMgr->addRole($data);
    
    if ($newId) {
        // تسجيل العملية
        ActivityHelper::logCreate(
            'roles',
            $name,
            $data,
            $newId
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة الدور بنجاح',
            'role' => [
                'id' => $newId,
                'name' => $name,
                'description' => $description,
                'status' => $status
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'فشل في إضافة الدور'
        ]);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    ActivityHelper::logError(
        'roles',
        'add_role',
        'فشل في إضافة دور جديد: ' . $e->getMessage(),
        [
            'attempted_data' => $_POST,
            'error' => $e->getMessage()
        ]
    );
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

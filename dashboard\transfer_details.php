<?php
/**
 * Transfer Details AJAX Page
 * صفحة تفاصيل الحوالة عبر AJAX
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';

// Check authentication
$auth = new Auth();
if (!$auth->checkSession()) {
    echo '<div class="alert alert-danger">غير مصرح لك بالوصول</div>';
    exit;
}

// Check permissions
if (!$auth->hasPermission('transfers.view')) {
    echo '<div class="alert alert-danger">ليس لديك الإذن المطلوب</div>';
    exit;
}

$transferId = (int)($_GET['id'] ?? 0);
if ($transferId <= 0) {
    echo '<div class="alert alert-danger">معرف الحوالة غير صحيح</div>';
    exit;
}

$conn = Database::getConnection();

// Get transfer details with all related information
$sql = "SELECT t.*, 
               c.full_name AS beneficiary_full_name,
               c.phone AS beneficiary_phone,
               c.email AS beneficiary_email,
               b.name AS branch_name,
               u.full_name AS created_by_name,
               sc.code AS sending_currency_code,
               sc.symbol AS sending_currency_symbol,
               sc.name AS sending_currency_name,
               rc.code AS receiving_currency_code,
               rc.symbol AS receiving_currency_symbol,
               rc.name AS receiving_currency_name
        FROM transfers t
        LEFT JOIN customers c ON c.id = t.beneficiary_id
        LEFT JOIN branches b ON b.id = t.branch_id
        LEFT JOIN users u ON u.id = t.created_by
        LEFT JOIN currencies sc ON sc.id = t.sending_currency_id
        LEFT JOIN currencies rc ON rc.id = t.receiving_currency_id
        WHERE t.id = ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $transferId);
$stmt->execute();
$result = $stmt->get_result();
$transfer = $result->fetch_assoc();
$stmt->close();

if (!$transfer) {
    echo '<div class="alert alert-danger">الحوالة غير موجودة</div>';
    exit;
}

// Get comprehensive activity log using enhanced manager
$db = new Database();
$enhancedManager = new EnhancedTransferManager($db);

// تم إزالة تسجيل نشاط العرض لتقليل الفوضى
// $enhancedManager->logViewActivity($transferId, $_SESSION['user_id'] ?? null, 'details');

// Get activity log - فقط الأنشطة المهمة
$activityLog = $enhancedManager->getTransferActivityLog($transferId, 100);

// تصفية الأنشطة لعرض المهم فقط
$importantActivities = array_filter($activityLog, function($activity) {
    $importantTypes = ['created', 'status_changed', 'tracking_assigned', 'delivery_updated'];
    return in_array($activity['activity_type'], $importantTypes);
});

$activityLog = $importantActivities;

// Get delivery history
$deliveryHistory = $enhancedManager->getDeliveryHistory($transferId);

// Get traditional status history for backward compatibility
$historyResult = $conn->query("
    SELECT tsh.*, u.full_name AS changed_by_name
    FROM transfer_status_history tsh
    LEFT JOIN users u ON u.id = tsh.changed_by_user_id
    WHERE tsh.transfer_id = $transferId
    ORDER BY tsh.status_date DESC
");

$statusHistory = [];
if ($historyResult) {
    while ($row = $historyResult->fetch_assoc()) {
        $statusHistory[] = $row;
    }
}

// Status colors
$statusColors = [
    'معلقة' => 'warning',
    'مكتملة' => 'success',
    'مرسلة' => 'info',
    'مستلمة' => 'primary',
    'ملغاة' => 'danger'
];
$statusColor = $statusColors[$transfer['status']] ?? 'secondary';
?>

<div class="row">
    <!-- Transfer Information -->
    <div class="col-md-8">
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> معلومات الحوالة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">رقم الحوالة:</td>
                                <td><?php echo htmlspecialchars($transfer['transaction_number']); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">نوع الحوالة:</td>
                                <td>
                                    <span class="badge <?php echo $transfer['transfer_type'] === 'صادرة' ? 'bg-primary' : 'bg-info'; ?>">
                                        <?php echo htmlspecialchars($transfer['transfer_type']); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الحالة:</td>
                                <td>
                                    <span class="badge bg-<?php echo $statusColor; ?>">
                                        <?php echo htmlspecialchars($transfer['status']); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">رقم التتبع:</td>
                                <td>
                                    <?php if (!empty($transfer['tracking_number'])): ?>
                                        <span class="fw-bold text-success"><?php echo htmlspecialchars($transfer['tracking_number']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">طريقة التسليم:</td>
                                <td><?php echo htmlspecialchars($transfer['delivery_method']); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">تاريخ الإنشاء:</td>
                                <td><?php echo date('Y-m-d H:i', strtotime($transfer['created_at'])); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">تاريخ الإكمال:</td>
                                <td>
                                    <?php if (!empty($transfer['completed_at'])): ?>
                                        <?php echo date('Y-m-d H:i', strtotime($transfer['completed_at'])); ?>
                                    <?php else: ?>
                                        <span class="text-muted">غير مكتمل</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">الفرع:</td>
                                <td><?php echo htmlspecialchars($transfer['branch_name'] ?? 'غير محدد'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">أنشأ بواسطة:</td>
                                <td><?php echo htmlspecialchars($transfer['created_by_name'] ?? 'غير محدد'); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sender Information -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-globe"></i> معلومات الحوالة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">دولة الحوالة:</td>
                                <td><?php echo htmlspecialchars($transfer['beneficiary_country'] ?? 'غير محدد'); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">طريقة التسليم:</td>
                                <td><?php echo htmlspecialchars($transfer['delivery_method']); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">رقم التتبع:</td>
                                <td>
                                    <?php if (!empty($transfer['tracking_number'])): ?>
                                        <span class="fw-bold text-success"><?php echo htmlspecialchars($transfer['tracking_number']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                            <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-money-bill-wave"></i> المعلومات المالية</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">المبلغ الأساسي</h6>
                                <h4 class="text-success">
                                    <?php echo number_format($transfer['sending_amount'], 2); ?>
                                    <?php echo htmlspecialchars($transfer['sending_currency_code']); ?>
                                </h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">مبلغ التسليم</h6>
                                <h4 class="text-primary">
                                    <?php echo number_format($transfer['receiving_amount'], 2); ?>
                                    <?php echo htmlspecialchars($transfer['receiving_currency_code']); ?>
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="text-center">
                            <small class="text-muted">الربح</small>
                            <div class="fw-bold text-warning">
                                <?php echo number_format($transfer['profit'], 2); ?>
                                <?php echo htmlspecialchars($transfer['sending_currency_code']); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notes -->
        <?php if (!empty($transfer['notes'])): ?>
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-sticky-note"></i> ملاحظات</h6>
            </div>
            <div class="card-body">
                <p class="mb-0"><?php echo nl2br(htmlspecialchars($transfer['notes'])); ?></p>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Activity History -->
    <div class="col-md-4">
        <!-- Activity Log -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list-alt"></i> سجل الأنشطة
                    <span class="badge bg-primary"><?php echo count($activityLog); ?></span>
                </h6>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                <?php if (empty($activityLog)): ?>
                    <p class="text-muted text-center">لا يوجد سجل أنشطة</p>
                <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($activityLog as $activity): ?>
                            <div class="timeline-item">
                                <?php
                                $activityColors = [
                                    'created' => 'success',
                                    'status_changed' => 'warning',
                                    'tracking_assigned' => 'info',
                                    'delivery_updated' => 'primary',
                                    'viewed' => 'secondary',
                                    'amount_updated' => 'danger',
                                    'notes_added' => 'dark'
                                ];
                                $activityColor = $activityColors[$activity['activity_type']] ?? 'secondary';
                                ?>
                                <div class="timeline-marker bg-<?php echo $activityColor; ?>"></div>
                                <div class="timeline-content">
                                    <?php
                                    // تبسيط العرض - عرض المعلومات الأساسية فقط
                                    if ($activity['activity_type'] === 'status_changed' && $activity['new_value_decoded']):
                                        $newStatus = $activity['new_value_decoded']['status'] ?? 'غير محدد';
                                    ?>
                                        <h6 class="mb-1">
                                            <span class="badge bg-<?php echo $activityColor; ?>">
                                                <?php echo htmlspecialchars($newStatus); ?>
                                            </span>
                                        </h6>
                                    <?php else: ?>
                                        <h6 class="mb-1">
                                            <span class="badge bg-<?php echo $activityColor; ?>">
                                                <?php
                                                $activityLabels = [
                                                    'created' => 'تم إنشاء الحوالة',
                                                    'tracking_assigned' => 'تم تعيين رقم التتبع',
                                                    'delivery_updated' => 'تم تحديث التسليم',
                                                    'amount_updated' => 'تم تحديث المبلغ',
                                                    'notes_added' => 'تم إضافة ملاحظة'
                                                ];
                                                echo $activityLabels[$activity['activity_type']] ?? $activity['activity_type'];
                                                ?>
                                            </span>
                                        </h6>
                                    <?php endif; ?>

                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        <?php echo date('Y-m-d H:i', strtotime($activity['created_at'])); ?>
                                    </small>

                                    <?php if (!empty($activity['user_name'])): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-user"></i>
                                            بواسطة: <?php echo htmlspecialchars($activity['user_name']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Delivery History -->
        <?php if (!empty($deliveryHistory)): ?>
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-truck"></i> تاريخ التسليم
                    <span class="badge bg-info"><?php echo count($deliveryHistory); ?></span>
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <?php foreach ($deliveryHistory as $delivery): ?>
                        <div class="timeline-item">
                            <?php
                            $deliveryColors = [
                                'pending' => 'warning',
                                'picked_up' => 'info',
                                'in_transit' => 'primary',
                                'out_for_delivery' => 'success',
                                'delivered' => 'success',
                                'failed' => 'danger',
                                'returned' => 'secondary'
                            ];
                            $deliveryColor = $deliveryColors[$delivery['delivery_status']] ?? 'secondary';
                            ?>
                            <div class="timeline-marker bg-<?php echo $deliveryColor; ?>"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">
                                    <span class="badge bg-<?php echo $deliveryColor; ?>">
                                        <?php
                                        $deliveryLabels = [
                                            'pending' => 'في الانتظار',
                                            'picked_up' => 'تم الاستلام',
                                            'in_transit' => 'في الطريق',
                                            'out_for_delivery' => 'خرج للتسليم',
                                            'delivered' => 'تم التسليم',
                                            'failed' => 'فشل التسليم',
                                            'returned' => 'تم الإرجاع'
                                        ];
                                        echo $deliveryLabels[$delivery['delivery_status']] ?? $delivery['delivery_status'];
                                        ?>
                                    </span>
                                </h6>

                                <?php if (!empty($delivery['delivery_notes'])): ?>
                                    <p class="mb-1"><?php echo htmlspecialchars($delivery['delivery_notes']); ?></p>
                                <?php endif; ?>

                                <small class="text-muted">
                                    <i class="fas fa-clock"></i>
                                    <?php echo date('Y-m-d H:i', strtotime($delivery['created_at'])); ?>
                                </small>

                                <?php if (!empty($delivery['delivery_agent'])): ?>
                                    <br><small class="text-muted">
                                        <i class="fas fa-user-tie"></i>
                                        <?php echo htmlspecialchars($delivery['delivery_agent']); ?>
                                    </small>
                                <?php endif; ?>

                                <?php if (!empty($delivery['delivery_location'])): ?>
                                    <br><small class="text-muted">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <?php echo htmlspecialchars($delivery['delivery_location']); ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #dee2e6;
}
</style>

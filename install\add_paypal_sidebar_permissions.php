<?php
/**
 * إضافة صلاحيات PayPal لإظهار القائمة في الشريط الجانبي
 * Add PayPal Permissions for Sidebar Menu Display
 */

// منع الوصول المباشر
if (!defined('INSTALL_MODE')) {
    define('INSTALL_MODE', true);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// إعداد الترميز
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إضافة صلاحيات PayPal للشريط الجانبي</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .permission-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin: 20px 0; }
        .permission-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .permission-name { font-weight: bold; color: #0d6efd; font-family: monospace; }
        .permission-desc { color: #6c757d; margin-top: 5px; }
        h1, h2, h3 { color: #333; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        .role-section { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 8px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>💳 إضافة صلاحيات PayPal للشريط الجانبي</h1>";
echo "<p>هذا الملف سيقوم بإضافة جميع صلاحيات PayPal المطلوبة لإظهار قائمة PayPal في الشريط الجانبي حسب دور المستخدم.</p>";

try {
    // الاتصال بقاعدة البيانات
    $db = new Database();
    $conn = Database::getConnection();
    
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

    // قراءة ملف صلاحيات PayPal
    $sqlFile = __DIR__ . '/quick_arabic_permissions.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception('ملف صلاحيات PayPal غير موجود: ' . $sqlFile);
    }
    
    $sqlContent = file_get_contents($sqlFile);
    if ($sqlContent === false) {
        throw new Exception('فشل في قراءة ملف صلاحيات PayPal');
    }
    
    echo "<div class='info'>📄 تم قراءة ملف صلاحيات PayPal بنجاح</div>";

    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sqlContent)));
    
    $addedPermissions = 0;
    $addedRolePermissions = 0;
    $errors = [];
    
    echo "<h2>🔄 تنفيذ إضافة صلاحيات PayPal...</h2>";
    
    foreach ($queries as $query) {
        if (!empty($query) && !preg_match('/^--/', $query) && !preg_match('/^SELECT/', $query)) {
            try {
                if ($conn->query($query)) {
                    if (stripos($query, 'INSERT') !== false) {
                        if (stripos($query, 'permissions (name') !== false) {
                            $addedPermissions += $conn->affected_rows;
                        } elseif (stripos($query, 'role_permissions') !== false) {
                            $addedRolePermissions += $conn->affected_rows;
                        }
                    }
                } else {
                    $errors[] = 'خطأ في الاستعلام: ' . $conn->error;
                }
            } catch (Exception $e) {
                $errors[] = 'استثناء: ' . $e->getMessage();
            }
        }
    }
    
    // عرض النتائج
    echo "<h2>📊 نتائج إضافة صلاحيات PayPal</h2>";
    
    echo "<div class='stats'>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>$addedPermissions</div>";
    echo "<div class='stat-label'>صلاحية PayPal جديدة</div>";
    echo "</div>";
    
    echo "<div class='stat-card' style='background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);'>";
    echo "<div class='stat-number'>$addedRolePermissions</div>";
    echo "<div class='stat-label'>ربط دور-صلاحية</div>";
    echo "</div>";
    
    echo "<div class='stat-card' style='background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);'>";
    echo "<div class='stat-number'>" . count($errors) . "</div>";
    echo "<div class='stat-label'>أخطاء</div>";
    echo "</div>";
    echo "</div>";
    
    if (!empty($errors)) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ تحذيرات وأخطاء:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // عرض صلاحيات PayPal المضافة
    $paypalPermissions = $conn->query("SELECT name, description FROM permissions WHERE module = 'PayPal' ORDER BY name");
    if ($paypalPermissions && $paypalPermissions->num_rows > 0) {
        echo "<h2>📋 صلاحيات PayPal المضافة:</h2>";
        echo "<div class='permission-grid'>";
        while ($row = $paypalPermissions->fetch_assoc()) {
            echo "<div class='permission-card'>";
            echo "<div class='permission-name'>" . htmlspecialchars($row['name']) . "</div>";
            echo "<div class='permission-desc'>" . htmlspecialchars($row['description']) . "</div>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // إحصائيات الأدوار
    echo "<h2>👥 توزيع صلاحيات PayPal على الأدوار:</h2>";
    
    $roles = [
        1 => 'مدير النظام',
        2 => 'مدير الفرع', 
        3 => 'الصراف',
        4 => 'موظف العمليات',
        5 => 'المحاسب'
    ];
    
    foreach ($roles as $roleId => $roleName) {
        $rolePermissions = $conn->query("
            SELECT COUNT(*) as count 
            FROM role_permissions rp 
            INNER JOIN permissions p ON rp.permission_id = p.id 
            WHERE rp.role_id = $roleId AND p.module = 'PayPal'
        ")->fetch_assoc();
        
        $count = $rolePermissions['count'];
        
        echo "<div class='role-section'>";
        echo "<h4>$roleName (ID: $roleId)</h4>";
        echo "<p><strong>عدد صلاحيات PayPal:</strong> $count صلاحية</p>";
        
        if ($count > 0) {
            $permissions = $conn->query("
                SELECT p.name, p.description 
                FROM role_permissions rp 
                INNER JOIN permissions p ON rp.permission_id = p.id 
                WHERE rp.role_id = $roleId AND p.module = 'PayPal'
                ORDER BY p.name
            ");
            
            echo "<div class='permission-grid'>";
            while ($perm = $permissions->fetch_assoc()) {
                echo "<div class='permission-card'>";
                echo "<div class='permission-name'>" . htmlspecialchars($perm['name']) . "</div>";
                echo "<div class='permission-desc'>" . htmlspecialchars($perm['description']) . "</div>";
                echo "</div>";
            }
            echo "</div>";
        }
        echo "</div>";
    }
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إنجاز إضافة صلاحيات PayPal بنجاح!</h3>";
    echo "<p>تم إضافة جميع الصلاحيات المطلوبة لإظهار قائمة PayPal في الشريط الجانبي وربطها بالأدوار المناسبة.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في إضافة صلاحيات PayPal</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>انتقل إلى لوحة التحكم: <a href='../dashboard/' target='_blank'>dashboard/</a></li>";
echo "<li>تحقق من ظهور قائمة 'حوالات PayPal' في الشريط الجانبي</li>";
echo "<li>اختبر الوصول لصفحات PayPal المختلفة</li>";
echo "<li>تأكد من عمل القائمة المنسدلة لـ PayPal</li>";
echo "<li>اختبر مع أدوار مختلفة للتأكد من الصلاحيات</li>";
echo "</ol>";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>🔗 صفحات PayPal المتاحة:</h4>";
echo "<ul>";
echo "<li><strong>لوحة تحكم PayPal:</strong> <a href='../dashboard/paypal_transfers.php' target='_blank'>paypal_transfers.php</a></li>";
echo "<li><strong>الحوالات الواردة:</strong> <a href='../dashboard/paypal_incoming_transfers.php' target='_blank'>paypal_incoming_transfers.php</a></li>";
echo "<li><strong>إضافة حوالة جديدة:</strong> <a href='../dashboard/add_paypal_transfer.php' target='_blank'>add_paypal_transfer.php</a></li>";
echo "<li><strong>إدارة السحوبات:</strong> <a href='../dashboard/withdrawals.php' target='_blank'>withdrawals.php</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='info'>";
echo "<h4>🎯 الصلاحيات المطلوبة لكل قسم:</h4>";
echo "<ul>";
echo "<li><strong>لوحة تحكم PayPal:</strong> paypal.dashboard</li>";
echo "<li><strong>الحوالات الواردة:</strong> paypal.incoming.view</li>";
echo "<li><strong>إضافة حوالة:</strong> paypal.create</li>";
echo "<li><strong>إدارة السحوبات:</strong> paypal.withdrawals.view</li>";
echo "<li><strong>تقارير PayPal:</strong> paypal.reports</li>";
echo "</ul>";
echo "</div>";

echo "</div></body></html>";
?>

<?php
/**
 * إصلاح الإجراءات المخزنة المفقودة
 * Fix Missing Stored Procedures
 */

require_once __DIR__ . '/../config/database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الإجراءات المخزنة المفقودة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-tools text-primary"></i>
            إصلاح الإجراءات المخزنة المفقودة
        </h1>

        <?php
        try {
            // الاتصال بقاعدة البيانات
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
            
            echo "<div class='result success'>";
            echo "<i class='fas fa-check-circle me-2'></i>";
            echo "<strong>✓ تم الاتصال بقاعدة البيانات بنجاح</strong>";
            echo "</div>";
            
            // قراءة ملف الإجراءات المخزنة المفقودة
            $sql_file = __DIR__ . '/missing_procedures.sql';
            
            if (!file_exists($sql_file)) {
                throw new Exception('ملف الإجراءات المخزنة غير موجود: ' . $sql_file);
            }
            
            $sql = file_get_contents($sql_file);
            
            echo "<div class='result info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo "تم تحميل ملف الإجراءات المخزنة المفقودة";
            echo "</div>";
            
            // تنفيذ الاستعلامات
            $queries = explode('DELIMITER ;', $sql);
            $success_count = 0;
            $error_count = 0;
            
            foreach ($queries as $query_block) {
                $query_block = trim($query_block);
                if (empty($query_block) || strpos($query_block, '--') === 0) {
                    continue;
                }
                
                // إزالة DELIMITER // من بداية الكتلة
                $query_block = str_replace('DELIMITER //', '', $query_block);
                
                // تقسيم الاستعلامات بناءً على //
                $individual_queries = explode('//', $query_block);
                
                foreach ($individual_queries as $query) {
                    $query = trim($query);
                    if (empty($query) || strpos($query, '--') === 0) {
                        continue;
                    }
                    
                    try {
                        $pdo->exec($query);
                        $success_count++;
                    } catch (PDOException $e) {
                        // تجاهل أخطاء "already exists"
                        if (strpos($e->getMessage(), 'already exists') === false && 
                            strpos($e->getMessage(), 'Duplicate') === false) {
                            echo "<div class='result error'>";
                            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                            echo "<strong>خطأ:</strong> " . $e->getMessage();
                            echo "</div>";
                            $error_count++;
                        }
                    }
                }
            }
            
            echo "<div class='result success'>";
            echo "<i class='fas fa-check me-2'></i>";
            echo "<strong>تم تنفيذ {$success_count} استعلام بنجاح</strong>";
            if ($error_count > 0) {
                echo " مع {$error_count} خطأ";
            }
            echo "</div>";
            
            // التحقق من الإجراءات المخزنة
            echo "<h3>التحقق من الإجراءات المخزنة:</h3>";
            
            $procedures = ['DeleteDailyTransaction', 'UpdateDailyTransaction', 'GetDeletedDailyTransactions'];
            
            foreach ($procedures as $procedure) {
                try {
                    $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = '{$procedure}' AND Db = '" . DB_NAME . "'");
                    $exists = $stmt->rowCount() > 0;
                    
                    if ($exists) {
                        echo "<div class='result success'>";
                        echo "<i class='fas fa-check-circle me-2'></i>";
                        echo "<strong>✓ إجراء {$procedure} موجود</strong>";
                        echo "</div>";
                    } else {
                        echo "<div class='result error'>";
                        echo "<i class='fas fa-times-circle me-2'></i>";
                        echo "<strong>✗ إجراء {$procedure} غير موجود</strong>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='result error'>";
                    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                    echo "<strong>خطأ في فحص إجراء {$procedure}:</strong> " . $e->getMessage();
                    echo "</div>";
                }
            }
            
            // إنشاء المشغل (Trigger) إذا لم يكن موجوداً
            echo "<h3>إنشاء المشغل (Trigger):</h3>";
            
            try {
                // فحص وجود المشغل
                $stmt = $pdo->query("SHOW TRIGGERS FROM " . DB_NAME . " WHERE Trigger = 'before_daily_transaction_delete'");
                $trigger_exists = $stmt->rowCount() > 0;
                
                if (!$trigger_exists) {
                    // إنشاء المشغل
                    $trigger_sql = "
                    CREATE TRIGGER before_daily_transaction_delete
                    BEFORE DELETE ON daily_transactions
                    FOR EACH ROW
                    BEGIN
                        INSERT INTO deleted_daily_transactions (
                            original_id, transaction_number, country_id, base_amount, customer_rate,
                            operation_type, calculated_amount, exchange_rate, recipient_amount,
                            delivery_type, transfer_amount, recipient_name, notes, branch_id,
                            created_by, created_at, updated_by, updated_at, deleted_by
                        ) VALUES (
                            OLD.id, OLD.transaction_number, OLD.country_id, OLD.base_amount, OLD.customer_rate,
                            OLD.operation_type, OLD.calculated_amount, OLD.exchange_rate, OLD.recipient_amount,
                            OLD.delivery_type, OLD.transfer_amount, OLD.recipient_name, OLD.notes, OLD.branch_id,
                            OLD.created_by, OLD.created_at, OLD.updated_by, OLD.updated_at, @current_user_id
                        );
                        
                        INSERT INTO daily_transaction_history (
                            transaction_id, action_type, old_values, changed_by, ip_address
                        ) VALUES (
                            OLD.id, 'deleted',
                            JSON_OBJECT(
                                'transaction_number', OLD.transaction_number,
                                'country_id', OLD.country_id,
                                'base_amount', OLD.base_amount,
                                'delivery_type', OLD.delivery_type
                            ),
                            @current_user_id, NULL
                        );
                    END";
                    
                    $pdo->exec($trigger_sql);
                    
                    echo "<div class='result success'>";
                    echo "<i class='fas fa-check-circle me-2'></i>";
                    echo "<strong>✓ تم إنشاء المشغل before_daily_transaction_delete بنجاح</strong>";
                    echo "</div>";
                } else {
                    echo "<div class='result info'>";
                    echo "<i class='fas fa-info-circle me-2'></i>";
                    echo "<strong>المشغل before_daily_transaction_delete موجود بالفعل</strong>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='result error'>";
                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                echo "<strong>خطأ في إنشاء المشغل:</strong> " . $e->getMessage();
                echo "</div>";
            }
            
            echo "<hr>";
            echo "<div class='alert alert-success text-center'>";
            echo "<h4><i class='fas fa-trophy me-2'></i>تم الإصلاح بنجاح!</h4>";
            echo "<p>يمكنك الآن اختبار النظام مرة أخرى</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='result error'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>خطأ عام:</strong> " . $e->getMessage();
            echo "</div>";
        }
        ?>

        <div class="text-center mt-4">
            <a href="test_daily_transactions_system.php" class="btn btn-primary btn-lg">
                <i class="fas fa-vial me-2"></i>
                اختبار النظام مرة أخرى
            </a>
            <a href="../dashboard/daily_transactions.php" class="btn btn-success btn-lg ms-2">
                <i class="fas fa-list me-2"></i>
                الذهاب إلى المعاملات اليومية
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

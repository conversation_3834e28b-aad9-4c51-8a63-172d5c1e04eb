<?php
/**
 * صفحة عرض المعاملات اليومية المحذوفة (للمسؤولين فقط)
 * Deleted Daily Transactions Page (Admin Only)
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

// إنشاء كائن المصادقة والتحقق من تسجيل الدخول
$auth = new Auth();
$auth->requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = $auth->getCurrentUser();

// التحقق من الصلاحيات (للمسؤولين فقط)
if (!$auth->hasPermission('daily_transactions.view_deleted') || (int)$current_user['role_id'] !== 1) {
    header('Location: ' . BASE_URL . 'dashboard/index.php?error=no_permission');
    exit;
}

$pageTitle = 'المعاملات اليومية المحذوفة';
$error_message = '';
$success_message = '';

// معالجة استعادة المعاملة
if (isset($_GET['restore']) && $auth->hasPermission('daily_transactions.create')) {
    $deleted_id = (int)$_GET['restore'];
    try {
        // جلب بيانات المعاملة المحذوفة
        $stmt = $pdo->prepare("SELECT * FROM deleted_daily_transactions WHERE id = ?");
        $stmt->execute([$deleted_id]);
        $deleted_transaction = $stmt->fetch();

        if (!$deleted_transaction) {
            $error_message = "المعاملة المحذوفة غير موجودة";
        } else {
            // بدء المعاملة
            $pdo->beginTransaction();

            try {
                // إعادة إدراج المعاملة في الجدول الأساسي
                $restore_sql = "INSERT INTO daily_transactions (
                    transaction_number, country_id, base_amount, customer_rate, operation_type,
                    calculated_amount, exchange_rate, recipient_amount, delivery_type,
                    transfer_amount, recipient_name, notes, branch_id, created_by, created_at,
                    updated_by, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $stmt = $pdo->prepare($restore_sql);
                $stmt->execute([
                    $deleted_transaction['transaction_number'],
                    $deleted_transaction['country_id'],
                    $deleted_transaction['base_amount'],
                    $deleted_transaction['customer_rate'],
                    $deleted_transaction['operation_type'],
                    $deleted_transaction['calculated_amount'],
                    $deleted_transaction['exchange_rate'],
                    $deleted_transaction['recipient_amount'],
                    $deleted_transaction['delivery_type'],
                    $deleted_transaction['transfer_amount'],
                    $deleted_transaction['recipient_name'],
                    $deleted_transaction['notes'],
                    $deleted_transaction['branch_id'],
                    $deleted_transaction['created_by'],
                    $deleted_transaction['created_at'],
                    $current_user['id'], // updated_by
                    date('Y-m-d H:i:s') // updated_at
                ]);

                $restored_id = $pdo->lastInsertId();

                // إضافة سجل في تاريخ المعاملات
                $history_sql = "INSERT INTO daily_transaction_history
                    (transaction_id, action_type, new_values, changed_by, changed_at)
                    VALUES (?, 'restored', ?, ?, CURRENT_TIMESTAMP)";

                $new_values = json_encode([
                    'restored_from_deleted_id' => $deleted_id,
                    'transaction_number' => $deleted_transaction['transaction_number'],
                    'restored_by' => $current_user['id']
                ]);

                $stmt = $pdo->prepare($history_sql);
                $stmt->execute([$restored_id, $new_values, $current_user['id']]);

                // حذف من جدول المحذوفات
                $delete_from_deleted_sql = "DELETE FROM deleted_daily_transactions WHERE id = ?";
                $stmt = $pdo->prepare($delete_from_deleted_sql);
                $stmt->execute([$deleted_id]);

                // تأكيد المعاملة
                $pdo->commit();

                $success_message = "تم استعادة المعاملة #{$deleted_transaction['transaction_number']} بنجاح";

            } catch (Exception $e) {
                $pdo->rollback();
                throw $e;
            }
        }
    } catch (Exception $e) {
        $error_message = "خطأ في استعادة المعاملة: " . $e->getMessage();
    }
}

// معالجة الحذف النهائي
if (isset($_GET['permanent_delete']) && $auth->hasPermission('daily_transactions.delete')) {
    $deleted_id = (int)$_GET['permanent_delete'];
    try {
        // التحقق من وجود المعاملة المحذوفة
        $stmt = $pdo->prepare("SELECT transaction_number FROM deleted_daily_transactions WHERE id = ?");
        $stmt->execute([$deleted_id]);
        $deleted_transaction = $stmt->fetch();

        if (!$deleted_transaction) {
            $error_message = "المعاملة المحذوفة غير موجودة";
        } else {
            // حذف نهائي من قاعدة البيانات
            $permanent_delete_sql = "DELETE FROM deleted_daily_transactions WHERE id = ?";
            $stmt = $pdo->prepare($permanent_delete_sql);
            $stmt->execute([$deleted_id]);

            $success_message = "تم حذف المعاملة #{$deleted_transaction['transaction_number']} نهائياً من النظام";
        }
    } catch (Exception $e) {
        $error_message = "خطأ في الحذف النهائي: " . $e->getMessage();
    }
}

// معاملات البحث والفلترة
$search_params = [
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? ''
];

// إعدادات الصفحات
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// جلب المعاملات المحذوفة باستخدام استعلام مباشر
try {
    // بناء استعلام البحث
    $sql = "SELECT
        ddt.id,
        ddt.original_id,
        ddt.transaction_number,
        ddt.deleted_at,
        c.name_ar as country_name,
        c.currency_code,
        ddt.base_amount,
        ddt.customer_rate,
        ddt.operation_type,
        ddt.calculated_amount,
        ddt.exchange_rate,
        ddt.recipient_amount,
        ddt.delivery_type,
        ddt.transfer_amount,
        ddt.recipient_name,
        ddt.notes,
        b.name as branch_name,
        u_created.full_name as created_by_name,
        u_updated.full_name as updated_by_name,
        u_deleted.full_name as deleted_by_name,
        ddt.created_at,
        ddt.updated_at,
        ddt.deleted_at
    FROM deleted_daily_transactions ddt
    LEFT JOIN countries c ON ddt.country_id = c.id
    LEFT JOIN branches b ON ddt.branch_id = b.id
    LEFT JOIN users u_created ON ddt.created_by = u_created.id
    LEFT JOIN users u_updated ON ddt.updated_by = u_updated.id
    LEFT JOIN users u_deleted ON ddt.deleted_by = u_deleted.id
    WHERE 1=1";

    $params = [];

    if ($search_params['date_from']) {
        $sql .= " AND DATE(ddt.deleted_at) >= ?";
        $params[] = $search_params['date_from'];
    }
    if ($search_params['date_to']) {
        $sql .= " AND DATE(ddt.deleted_at) <= ?";
        $params[] = $search_params['date_to'];
    }

    $sql .= " ORDER BY ddt.deleted_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $deleted_transactions = $stmt->fetchAll();
    
    // حساب العدد الإجمالي للصفحات
    $count_sql = "SELECT COUNT(*) as total FROM deleted_daily_transactions WHERE 1=1";
    $count_params = [];
    
    if ($search_params['date_from']) {
        $count_sql .= " AND DATE(deleted_at) >= ?";
        $count_params[] = $search_params['date_from'];
    }
    if ($search_params['date_to']) {
        $count_sql .= " AND DATE(deleted_at) <= ?";
        $count_params[] = $search_params['date_to'];
    }
    
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($count_params);
    $total_records = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_records / $limit);
    
} catch (Exception $e) {
    $deleted_transactions = [];
    $total_records = 0;
    $total_pages = 0;
    $error_message = "خطأ في جلب المعاملات المحذوفة: " . $e->getMessage();
}

require_once __DIR__ . '/../includes/header.php';
?>

<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-auto p-0">
                <?php require_once __DIR__ . '/../includes/sidebar.php'; ?>
            </div>

            <!-- Main Content -->
            <div class="col">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h3 mb-0">
                            <i class="fas fa-trash-alt text-danger me-2"></i>
                            المعاملات اليومية المحذوفة
                            <span class="badge bg-danger">للمسؤولين فقط</span>
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php">المعاملات اليومية</a></li>
                                <li class="breadcrumb-item active">المعاملات المحذوفة</li>
                            </ol>
                        </nav>
                    </div>

                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- تحذير -->
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذه الصفحة تحتوي على المعاملات التي تم حذفها من النظام. 
                        هذه البيانات محفوظة لأغراض التدقيق والمراجعة فقط.
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-search me-2"></i>
                                فلترة المعاملات المحذوفة
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <!-- الفترة الزمنية -->
                                <div class="col-md-4">
                                    <label for="date_from" class="form-label">من تاريخ الحذف</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" 
                                           value="<?php echo htmlspecialchars($search_params['date_from']); ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="date_to" class="form-label">إلى تاريخ الحذف</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" 
                                           value="<?php echo htmlspecialchars($search_params['date_to']); ?>">
                                </div>

                                <!-- أزرار التحكم -->
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fas fa-search me-2"></i>
                                        بحث
                                    </button>
                                    <a href="<?php echo BASE_URL; ?>dashboard/deleted_daily_transactions.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        مسح
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">إجمالي المحذوفة</h6>
                                            <h4><?php echo number_format($total_records); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-trash-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">محذوفة اليوم</h6>
                                            <h4><?php echo count(array_filter($deleted_transactions, function($t) { return date('Y-m-d', strtotime($t['deleted_at'])) == date('Y-m-d'); })); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-day fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">محذوفة هذا الأسبوع</h6>
                                            <h4><?php echo count(array_filter($deleted_transactions, function($t) { return date('W', strtotime($t['deleted_at'])) == date('W'); })); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-week fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">محذوفة هذا الشهر</h6>
                                            <h4><?php echo count(array_filter($deleted_transactions, function($t) { return date('Y-m', strtotime($t['deleted_at'])) == date('Y-m'); })); ?></h4>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-calendar-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المعاملات المحذوفة -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    قائمة المعاملات المحذوفة
                                    <?php if ($total_records > 0): ?>
                                        <span class="badge bg-danger"><?php echo number_format($total_records); ?></span>
                                    <?php endif; ?>
                                </h6>
                                <div class="d-flex align-items-center">
                                    <span class="text-muted me-3">
                                        عرض <?php echo count($deleted_transactions); ?> من أصل <?php echo $total_records; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($deleted_transactions)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                    <h5 class="text-muted">لا توجد معاملات محذوفة</h5>
                                    <p class="text-muted">لم يتم حذف أي معاملات تطابق معايير البحث المحددة</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>رقم المعاملة الأصلي</th>
                                                <th>رقم المعاملة</th>
                                                <th>تاريخ الحذف</th>
                                                <th>الدولة/العملة</th>
                                                <th>المبلغ الأساسي</th>
                                                <th>المبلغ للمستلم</th>
                                                <th>نوع التسليم</th>
                                                <th>اسم المستلم</th>
                                                <th>تم الإنشاء بواسطة</th>
                                                <th>تم الحذف بواسطة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($deleted_transactions as $transaction): ?>
                                                <tr class="table-danger">
                                                    <td>
                                                        <span class="badge bg-secondary">#<?php echo $transaction['original_id']; ?></span>
                                                    </td>
                                                    <td>
                                                        <strong class="text-danger"><?php echo htmlspecialchars($transaction['transaction_number']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <small>
                                                            <?php echo date('Y-m-d', strtotime($transaction['deleted_at'])); ?><br>
                                                            <span class="text-muted"><?php echo date('H:i', strtotime($transaction['deleted_at'])); ?></span>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">
                                                            <?php echo htmlspecialchars($transaction['currency_code']); ?>
                                                        </span><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($transaction['country_name']); ?></small>
                                                    </td>
                                                    <td>
                                                        <strong><?php echo number_format($transaction['base_amount'], 2); ?></strong>
                                                    </td>
                                                    <td>
                                                        <strong class="text-success"><?php echo number_format($transaction['recipient_amount']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $delivery_badges = [
                                                            'cash' => 'bg-success',
                                                            'bank' => 'bg-primary',
                                                            'usdt' => 'bg-warning'
                                                        ];
                                                        $delivery_names = [
                                                            'cash' => 'كاش',
                                                            'bank' => 'بنكي',
                                                            'usdt' => 'USDT'
                                                        ];
                                                        ?>
                                                        <span class="badge <?php echo $delivery_badges[$transaction['delivery_type']]; ?>">
                                                            <?php echo $delivery_names[$transaction['delivery_type']]; ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($transaction['recipient_name']): ?>
                                                            <?php echo htmlspecialchars($transaction['recipient_name']); ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <small>
                                                            <?php echo htmlspecialchars($transaction['created_by_name'] ?? 'غير محدد'); ?><br>
                                                            <span class="text-muted"><?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?></span>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <small>
                                                            <?php echo htmlspecialchars($transaction['deleted_by_name'] ?? 'غير محدد'); ?><br>
                                                            <span class="text-muted"><?php echo date('Y-m-d H:i', strtotime($transaction['deleted_at'])); ?></span>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <!-- عرض التفاصيل -->
                                                            <button type="button" class="btn btn-sm btn-outline-info"
                                                                    onclick="viewDeletedTransaction(<?php echo $transaction['id']; ?>)"
                                                                    title="عرض التفاصيل">
                                                                <i class="fas fa-eye"></i>
                                                            </button>

                                                            <?php if ($auth->hasPermission('daily_transactions.create')): ?>
                                                                <!-- استعادة المعاملة -->
                                                                <button type="button" class="btn btn-sm btn-outline-success"
                                                                        onclick="restoreTransaction(<?php echo $transaction['id']; ?>, '<?php echo htmlspecialchars($transaction['transaction_number']); ?>')"
                                                                        title="استعادة المعاملة">
                                                                    <i class="fas fa-undo"></i>
                                                                </button>
                                                            <?php endif; ?>

                                                            <?php if ($auth->hasPermission('daily_transactions.delete')): ?>
                                                                <!-- حذف نهائي -->
                                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                                        onclick="permanentDeleteTransaction(<?php echo $transaction['id']; ?>, '<?php echo htmlspecialchars($transaction['transaction_number']); ?>')"
                                                                        title="حذف نهائي">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- التصفح -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="تصفح المعاملات المحذوفة" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <!-- الصفحة السابقة -->
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <!-- أرقام الصفحات -->
                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);
                                
                                for ($i = $start_page; $i <= $end_page; $i++): ?>
                                    <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <!-- الصفحة التالية -->
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function viewDeletedTransaction(transactionId) {
            // يمكن إضافة modal لعرض تفاصيل المعاملة المحذوفة
            alert('عرض تفاصيل المعاملة المحذوفة رقم: ' + transactionId);
        }

        function restoreTransaction(transactionId, transactionNumber) {
            if (confirm('هل أنت متأكد من استعادة المعاملة #' + transactionNumber + '؟\n\nسيتم إعادة المعاملة إلى قائمة المعاملات النشطة.')) {
                // إظهار مؤشر التحميل
                const button = event.target.closest('button');
                const originalContent = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                button.disabled = true;

                // إعادة التوجيه لاستعادة المعاملة
                window.location.href = '?restore=' + transactionId;
            }
        }

        function permanentDeleteTransaction(transactionId, transactionNumber) {
            if (confirm('⚠️ تحذير: حذف نهائي!\n\nهل أنت متأكد من حذف المعاملة #' + transactionNumber + ' نهائياً؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد نهائي: سيتم حذف المعاملة نهائياً من قاعدة البيانات.\n\nهل تريد المتابعة؟')) {
                    // إظهار مؤشر التحميل
                    const button = event.target.closest('button');
                    const originalContent = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    button.disabled = true;

                    // إعادة التوجيه للحذف النهائي
                    window.location.href = '?permanent_delete=' + transactionId;
                }
            }
        }

        // إضافة تأثيرات بصرية للأزرار
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للأزرار
            const restoreButtons = document.querySelectorAll('button[onclick*="restoreTransaction"]');
            restoreButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.classList.remove('btn-outline-success');
                    this.classList.add('btn-success');
                });
                button.addEventListener('mouseleave', function() {
                    this.classList.remove('btn-success');
                    this.classList.add('btn-outline-success');
                });
            });

            const deleteButtons = document.querySelectorAll('button[onclick*="permanentDeleteTransaction"]');
            deleteButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.classList.remove('btn-outline-danger');
                    this.classList.add('btn-danger');
                });
                button.addEventListener('mouseleave', function() {
                    this.classList.remove('btn-danger');
                    this.classList.add('btn-outline-danger');
                });
            });
        });
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * إنشاء الإجراءات المخزنة المفقودة - نسخة مبسطة
 * Create Missing Stored Procedures - Simple Version
 */

require_once __DIR__ . '/../config/database.php';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<h2>إنشاء الإجراءات المخزنة المفقودة</h2>";
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // 1. إنشاء إجراء UpdateDailyTransaction
    echo "<h3>إنشاء إجراء UpdateDailyTransaction:</h3>";
    
    $updateProcedure = "
    DROP PROCEDURE IF EXISTS UpdateDailyTransaction;
    
    CREATE PROCEDURE UpdateDailyTransaction(
        IN p_transaction_id INT UNSIGNED,
        IN p_country_id INT UNSIGNED,
        IN p_base_amount DECIMAL(18,2),
        IN p_customer_rate DECIMAL(18,6),
        IN p_operation_type ENUM('multiply', 'divide'),
        IN p_exchange_rate DECIMAL(18,6),
        IN p_delivery_type ENUM('cash', 'bank', 'usdt'),
        IN p_transfer_amount DECIMAL(18,2),
        IN p_recipient_name VARCHAR(255),
        IN p_notes TEXT,
        IN p_updated_by INT UNSIGNED,
        OUT p_result_message VARCHAR(500)
    )
    BEGIN
        DECLARE v_error_count INT DEFAULT 0;
        DECLARE v_transaction_exists INT DEFAULT 0;
        DECLARE v_country_exists INT DEFAULT 0;
        DECLARE v_user_exists INT DEFAULT 0;
        DECLARE v_calculated_amount DECIMAL(18,2);
        DECLARE v_recipient_amount DECIMAL(18,2);
        
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            ROLLBACK;
            GET DIAGNOSTICS CONDITION 1
                p_result_message = MESSAGE_TEXT;
        END;
        
        START TRANSACTION;
        
        -- التحقق من وجود المعاملة
        SELECT COUNT(*) INTO v_transaction_exists 
        FROM daily_transactions 
        WHERE id = p_transaction_id;
        
        IF v_transaction_exists = 0 THEN
            SET p_result_message = 'المعاملة المحددة غير موجودة';
            ROLLBACK;
            LEAVE UpdateDailyTransaction;
        END IF;
        
        -- التحقق من وجود الدولة
        SELECT COUNT(*) INTO v_country_exists 
        FROM countries 
        WHERE id = p_country_id AND is_active = 1;
        
        IF v_country_exists = 0 THEN
            SET p_result_message = 'الدولة المحددة غير موجودة أو غير نشطة';
            ROLLBACK;
            LEAVE UpdateDailyTransaction;
        END IF;
        
        -- التحقق من صحة المبالغ
        IF p_base_amount <= 0 THEN
            SET p_result_message = 'مبلغ الحوالة الأساسي يجب أن يكون أكبر من صفر';
            ROLLBACK;
            LEAVE UpdateDailyTransaction;
        END IF;
        
        IF p_customer_rate <= 0 THEN
            SET p_result_message = 'سعر القص للزبون يجب أن يكون أكبر من صفر';
            ROLLBACK;
            LEAVE UpdateDailyTransaction;
        END IF;
        
        IF p_exchange_rate <= 0 THEN
            SET p_result_message = 'سعر الصرف يجب أن يكون أكبر من صفر';
            ROLLBACK;
            LEAVE UpdateDailyTransaction;
        END IF;
        
        -- التحقق من البيانات المطلوبة للتحويل البنكي أو USDT
        IF p_delivery_type IN ('bank', 'usdt') THEN
            IF p_transfer_amount IS NULL OR p_transfer_amount <= 0 THEN
                SET p_result_message = 'المبلغ المراد تحويله مطلوب للتحويل البنكي أو USDT';
                ROLLBACK;
                LEAVE UpdateDailyTransaction;
            END IF;
            
            IF p_recipient_name IS NULL OR TRIM(p_recipient_name) = '' THEN
                SET p_result_message = 'اسم المستلم مطلوب للتحويل البنكي أو USDT';
                ROLLBACK;
                LEAVE UpdateDailyTransaction;
            END IF;
        END IF;
        
        -- حساب المبلغ الناتج
        IF p_operation_type = 'multiply' THEN
            SET v_calculated_amount = p_base_amount * p_customer_rate;
        ELSE
            SET v_calculated_amount = p_base_amount / p_customer_rate;
        END IF;
        
        -- حساب المبلغ للمستلم
        SET v_recipient_amount = FLOOR(v_calculated_amount * p_exchange_rate);
        
        -- تحديث المعاملة
        UPDATE daily_transactions SET
            country_id = p_country_id,
            base_amount = p_base_amount,
            customer_rate = p_customer_rate,
            operation_type = p_operation_type,
            calculated_amount = v_calculated_amount,
            exchange_rate = p_exchange_rate,
            recipient_amount = v_recipient_amount,
            delivery_type = p_delivery_type,
            transfer_amount = p_transfer_amount,
            recipient_name = p_recipient_name,
            notes = p_notes,
            updated_by = p_updated_by,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = p_transaction_id;
        
        SET p_result_message = 'تم تحديث المعاملة بنجاح';
        
        COMMIT;
    END;
    ";
    
    try {
        $pdo->exec($updateProcedure);
        echo "<p style='color: green;'>✓ تم إنشاء إجراء UpdateDailyTransaction بنجاح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في إنشاء إجراء UpdateDailyTransaction: " . $e->getMessage() . "</p>";
    }
    
    // 2. إنشاء إجراء DeleteDailyTransaction
    echo "<h3>إنشاء إجراء DeleteDailyTransaction:</h3>";
    
    $deleteProcedure = "
    DROP PROCEDURE IF EXISTS DeleteDailyTransaction;
    
    CREATE PROCEDURE DeleteDailyTransaction(
        IN p_transaction_id INT UNSIGNED,
        IN p_deleted_by INT UNSIGNED,
        OUT p_result_message VARCHAR(500)
    )
    BEGIN
        DECLARE v_transaction_exists INT DEFAULT 0;
        
        DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            ROLLBACK;
            GET DIAGNOSTICS CONDITION 1
                p_result_message = MESSAGE_TEXT;
        END;
        
        START TRANSACTION;
        
        -- التحقق من وجود المعاملة
        SELECT COUNT(*) INTO v_transaction_exists 
        FROM daily_transactions 
        WHERE id = p_transaction_id;
        
        IF v_transaction_exists = 0 THEN
            SET p_result_message = 'المعاملة المحددة غير موجودة';
            ROLLBACK;
            LEAVE DeleteDailyTransaction;
        END IF;
        
        -- تعيين المستخدم الحالي للاستخدام في الـ Trigger
        SET @current_user_id = p_deleted_by;
        
        -- حذف المعاملة (سيتم تنفيذ الـ Trigger تلقائياً)
        DELETE FROM daily_transactions WHERE id = p_transaction_id;
        
        SET p_result_message = 'تم حذف المعاملة بنجاح';
        
        COMMIT;
    END;
    ";
    
    try {
        $pdo->exec($deleteProcedure);
        echo "<p style='color: green;'>✓ تم إنشاء إجراء DeleteDailyTransaction بنجاح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في إنشاء إجراء DeleteDailyTransaction: " . $e->getMessage() . "</p>";
    }
    
    // 3. إنشاء إجراء GetDeletedDailyTransactions
    echo "<h3>إنشاء إجراء GetDeletedDailyTransactions:</h3>";
    
    $getDeletedProcedure = "
    DROP PROCEDURE IF EXISTS GetDeletedDailyTransactions;
    
    CREATE PROCEDURE GetDeletedDailyTransactions(
        IN p_date_from DATE,
        IN p_date_to DATE,
        IN p_limit_count INT,
        IN p_offset_count INT
    )
    BEGIN
        SELECT 
            ddt.id,
            ddt.original_id,
            ddt.transaction_number,
            ddt.deleted_at,
            c.name_ar as country_name,
            c.currency_code,
            ddt.base_amount,
            ddt.customer_rate,
            ddt.operation_type,
            ddt.calculated_amount,
            ddt.exchange_rate,
            ddt.recipient_amount,
            ddt.delivery_type,
            ddt.transfer_amount,
            ddt.recipient_name,
            ddt.notes,
            b.name as branch_name,
            u_created.full_name as created_by_name,
            u_updated.full_name as updated_by_name,
            u_deleted.full_name as deleted_by_name,
            ddt.created_at,
            ddt.updated_at,
            ddt.deleted_at
        FROM deleted_daily_transactions ddt
        LEFT JOIN countries c ON ddt.country_id = c.id
        LEFT JOIN branches b ON ddt.branch_id = b.id
        LEFT JOIN users u_created ON ddt.created_by = u_created.id
        LEFT JOIN users u_updated ON ddt.updated_by = u_updated.id
        LEFT JOIN users u_deleted ON ddt.deleted_by = u_deleted.id
        WHERE 1=1
        AND (p_date_from IS NULL OR DATE(ddt.deleted_at) >= p_date_from)
        AND (p_date_to IS NULL OR DATE(ddt.deleted_at) <= p_date_to)
        ORDER BY ddt.deleted_at DESC
        LIMIT p_limit_count OFFSET p_offset_count;
    END;
    ";
    
    try {
        $pdo->exec($getDeletedProcedure);
        echo "<p style='color: green;'>✓ تم إنشاء إجراء GetDeletedDailyTransactions بنجاح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في إنشاء إجراء GetDeletedDailyTransactions: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>تم الانتهاء من إنشاء الإجراءات المخزنة!</h3>";
    echo "<p><a href='test_daily_transactions_system.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار النظام</a></p>";
    echo "<p><a href='../dashboard/daily_transactions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى المعاملات اليومية</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>خطأ في الإعداد</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

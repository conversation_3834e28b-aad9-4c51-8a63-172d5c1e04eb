<?php
/**
 * جلب جميع الصلاحيات عبر AJAX
 * Get All Permissions via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/permission_manager.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.permissions')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لإدارة صلاحيات الأدوار'
    ]);
    exit;
}

try {
    $db = new Database();
    $permMgr = new PermissionManager($db);
    
    $permissions = $permMgr->getAllPermissions();
    
    echo json_encode([
        'success' => true,
        'permissions' => $permissions
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

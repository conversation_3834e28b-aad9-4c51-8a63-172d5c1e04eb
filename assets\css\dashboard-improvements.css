/* 
 * Trust Plus Financial System - Dashboard Improvements CSS
 * Specific styling for enhanced UI elements like stat cards, action button groups, 
 * profile page elements, sidebar icons/hover effects
 */

/* ===== STATS CARDS ===== */
.stats-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: var(--bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-gradient);
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 1rem; /* RTL: icon on the left */
  font-size: 24px;
  color: white;
  background: var(--primary-gradient);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.stats-icon.success {
  background: var(--success-gradient);
  box-shadow: 0 4px 12px rgba(var(--success-rgb), 0.3);
}

.stats-icon.warning {
  background: var(--warning-gradient);
  box-shadow: 0 4px 12px rgba(var(--warning-rgb), 0.3);
}

.stats-icon.danger {
  background: var(--danger-gradient);
  box-shadow: 0 4px 12px rgba(var(--danger-rgb), 0.3);
}

.stats-icon.info {
  background: var(--info-gradient);
  box-shadow: 0 4px 12px rgba(var(--info-rgb), 0.3);
}

.stats-content {
  flex: 1;
  text-align: right; /* RTL */
}

.stats-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  line-height: 1.2;
  margin-bottom: 0.25rem;
}

.stats-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stats-change {
  font-size: 0.75rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.stats-change.positive {
  color: var(--success);
}

.stats-change.negative {
  color: var(--danger);
}

/* ===== ENHANCED CUSTOMER ACTION BUTTONS ===== */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 200px;
}

.action-buttons .btn {
  font-size: 0.875rem;
  padding: 8px 16px;
  border-radius: 6px;
  text-align: right; /* RTL */
  white-space: nowrap;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.action-buttons .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: width 0.3s ease;
}

.action-buttons .btn:hover::before {
  width: 100%;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-buttons .btn i {
  margin-left: 8px; /* RTL: icon spacing */
  font-size: 14px;
}

/* Button color variants */
.action-buttons .btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.action-buttons .btn-success {
  background: var(--success-gradient);
  color: white;
}

.action-buttons .btn-warning {
  background: var(--warning-gradient);
  color: white;
}

.action-buttons .btn-danger {
  background: var(--danger-gradient);
  color: white;
}

.action-buttons .btn-info {
  background: var(--info-gradient);
  color: white;
}

.action-buttons .btn-secondary {
  background: var(--secondary-gradient);
  color: white;
}

/* ===== RESPONSIVE ACTION BUTTONS ===== */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    min-width: auto;
  }
  
  .action-buttons .btn {
    flex: 1;
    min-width: 120px;
    font-size: 0.75rem;
    padding: 6px 12px;
  }
  
  .action-buttons .btn span {
    display: none; /* Hide button text on mobile */
  }
  
  .action-buttons .btn i {
    margin-left: 0;
    font-size: 16px;
  }
}

/* ===== USER PROFILE PAGE ELEMENTS ===== */
.profile-card {
  background: var(--bg-color);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid var(--border-light);
}

.profile-header {
  background: var(--primary-gradient);
  padding: 2rem;
  text-align: center;
  color: white;
  position: relative;
}

.profile-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: var(--bg-color);
  border-radius: 20px 20px 0 0;
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2.5rem;
  color: white;
  border: 4px solid rgba(255, 255, 255, 0.3);
}

.profile-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.profile-role {
  font-size: 1rem;
  opacity: 0.9;
}

.profile-body {
  padding: 2rem;
}

.profile-section {
  margin-bottom: 2rem;
}

.profile-section h5 {
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-light);
}

.profile-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-light);
}

.profile-info-item:last-child {
  border-bottom: none;
}

.profile-info-label {
  font-weight: 500;
  color: var(--text-muted);
}

.profile-info-value {
  font-weight: 600;
  color: var(--text-color);
}

/* ===== ENHANCED SIDEBAR ICONS/HOVER EFFECTS ===== */
.sidebar .sidebar-link {
  position: relative;
}

.sidebar .sidebar-link::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 0;
  background: white;
  border-radius: 3px 0 0 3px;
  transition: height 0.3s ease;
}

.sidebar .sidebar-link.active::before {
  height: 70%;
}

.sidebar .sidebar-link:hover::before {
  height: 50%;
}

.sidebar .sidebar-link i {
  transition: all 0.3s ease;
}

.sidebar .sidebar-link:hover i {
  transform: scale(1.1);
}

.sidebar .sidebar-link.active i {
  transform: scale(1.15);
}

/* Icon pulse animation for active links */
.sidebar .sidebar-link.active i {
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1.15);
  }
  50% {
    transform: scale(1.25);
  }
}

/* ===== BADGE STYLES FOR STATUS INDICATORS ===== */
.badge-kyc-verified {
  background: var(--success-gradient);
  color: white;
  font-weight: 500;
}

.badge-kyc-pending {
  background: var(--warning-gradient);
  color: white;
  font-weight: 500;
}

.badge-kyc-rejected {
  background: var(--danger-gradient);
  color: white;
  font-weight: 500;
}

.badge-risk-low {
  background: var(--success-gradient);
  color: white;
  font-weight: 500;
}

.badge-risk-medium {
  background: var(--warning-gradient);
  color: white;
  font-weight: 500;
}

.badge-risk-high {
  background: var(--danger-gradient);
  color: white;
  font-weight: 500;
}

/* ===== LOADING INDICATORS ===== */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: var(--text-muted);
  font-style: italic;
}

/* ===== CURRENCY MANAGEMENT STYLES ===== */
.sidebar .sidebar-link i.fa-coins {
  color: #ffd700;
}

.sidebar .sidebar-link:hover i.fa-coins,
.sidebar .sidebar-link.active i.fa-coins {
  color: #fff;
}

/* Currency table enhancements */
.currency-table .badge {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

.currency-table .btn-group .btn {
  border-radius: 0;
}

.currency-table .btn-group .btn:first-child {
  border-radius: 0.375rem 0 0 0.375rem;
}

.currency-table .btn-group .btn:last-child {
  border-radius: 0 0.375rem 0.375rem 0;
}

.currency-stats-card {
  transition: transform 0.2s ease;
}

.currency-stats-card:hover {
  transform: translateY(-2px);
}

/* ===== ALL TRANSFERS PAGE STYLES ===== */
.transfers-table {
  font-size: 0.9rem;
}

.transfers-table th {
  background: #f8f9fa;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
}

.transfers-table td {
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
}

.transfers-table .transaction-number {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #0066cc;
}

.transfers-table .btn-group-vertical .btn {
  margin-bottom: 2px;
  font-size: 0.75rem;
  padding: 4px 8px;
}

.transfers-table .btn-group-vertical .btn:last-child {
  margin-bottom: 0;
}

/* Transfer status badges */
.badge.bg-warning {
  color: #000;
}

/* Transfer details modal */
.modal-lg .modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Timeline styles for status history */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -22px;
  top: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  border-left: 3px solid #dee2e6;
}

/* Print styles */
@media print {
  .btn, .pagination, .modal, .sidebar {
    display: none !important;
  }

  .container-fluid {
    padding: 0 !important;
  }

  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }

  .table {
    font-size: 12px !important;
  }

  .badge {
    border: 1px solid #000 !important;
    color: #000 !important;
    background: transparent !important;
  }
}

/* Sidebar Profile Image Specific Styling */
.sidebar .profile-avatar {
    flex-shrink: 0;
    margin-right: 15px;
}

.sidebar .profile-avatar img {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
    object-position: center !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    display: block !important;
}

.sidebar .profile-avatar .default-avatar {
    width: 50px !important;
    height: 50px !important;
    background-color: rgba(255,255,255,0.1) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    color: rgba(255,255,255,0.8) !important;
}

.sidebar .profile-avatar .default-avatar i {
    font-size: 1.2em !important;
}

/* Additional specific rules for sidebar images */
.sidebar img[alt="صورة المستخدم"] {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
    object-position: center !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    display: block !important;
}

/* Responsive sidebar profile images */
@media (max-width: 768px) {
    .sidebar .profile-avatar img {
        width: 40px !important;
        height: 40px !important;
    }
    
    .sidebar .profile-avatar .default-avatar {
        width: 40px !important;
        height: 40px !important;
    }
    
    .sidebar .profile-avatar .default-avatar i {
        font-size: 1em !important;
    }
    
    .sidebar img[alt="صورة المستخدم"] {
        width: 40px !important;
        height: 40px !important;
    }
}

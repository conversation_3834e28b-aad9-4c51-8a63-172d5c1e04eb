<?php
/**
 * Final comprehensive test for the complete system
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';

echo "<h2>اختبار شامل نهائي للنظام المالي</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص وإصلاح أرصدة الصناديق</h3>\n";
    
    // Get all cash boxes and fix their balances
    $result = $conn->query("SELECT id, name FROM cash_boxes ORDER BY id");
    if ($result) {
        while ($cashBox = $result->fetch_assoc()) {
            $success = $cashManager->updateCashBoxBalance($cashBox['id']);
            if ($success) {
                echo "<p style='color: green;'>✓ تم تصحيح رصيد صندوق '{$cashBox['name']}'</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في تصحيح رصيد صندوق '{$cashBox['name']}'</p>\n";
            }
        }
    }
    
    echo "<h3>2. إنشاء حركات تجريبية شاملة</h3>\n";
    
    // Get the first cash box
    $cashBoxResult = $conn->query("SELECT id, name FROM cash_boxes ORDER BY id LIMIT 1");
    if ($cashBoxResult && $cashBoxResult->num_rows > 0) {
        $cashBox = $cashBoxResult->fetch_assoc();
        $cashBoxId = $cashBox['id'];
        
        echo "<p><strong>الصندوق المستخدم:</strong> {$cashBox['name']} (ID: $cashBoxId)</p>\n";
        
        // Create comprehensive test movements
        $testMovements = [
            [
                'type' => 'deposit',
                'amount' => 1000.00,
                'description' => 'إيداع ابتدائي كبير للاختبار الشامل',
                'reference' => 'FINAL_TEST_DEP_' . time() . '_001'
            ],
            [
                'type' => 'withdrawal',
                'amount' => 150.75,
                'description' => 'سحب تجريبي مع مبلغ عشري',
                'reference' => 'FINAL_TEST_WTH_' . time() . '_002'
            ],
            [
                'type' => 'deposit',
                'amount' => 250.50,
                'description' => 'إيداع إضافي مع وصف متوسط الطول لاختبار العرض',
                'reference' => 'FINAL_TEST_DEP_' . time() . '_003'
            ],
            [
                'type' => 'withdrawal',
                'amount' => 75.25,
                'description' => 'سحب صغير مع وصف قصير',
                'reference' => 'FINAL_TEST_WTH_' . time() . '_004'
            ],
            [
                'type' => 'deposit',
                'amount' => 500.00,
                'description' => 'إيداع نهائي مع وصف طويل جداً لاختبار عرض النصوص الطويلة في الجدول والتأكد من أن النص يتم قطعه بشكل صحيح',
                'reference' => 'FINAL_TEST_DEP_LONG_' . time() . '_005'
            ]
        ];
        
        $createdMovements = [];
        $totalDeposits = 0;
        $totalWithdrawals = 0;
        
        foreach ($testMovements as $i => $movement) {
            $result = $cashManager->addCashMovement(
                $cashBoxId,
                $movement['type'],
                $movement['amount'],
                $movement['description'],
                $movement['reference'],
                1 // User ID
            );
            
            if ($result['success']) {
                $createdMovements[] = $result['movement_id'];
                if ($movement['type'] === 'deposit') {
                    $totalDeposits += $movement['amount'];
                } else {
                    $totalWithdrawals += $movement['amount'];
                }
                echo "<p style='color: green;'>✓ حركة " . ($i + 1) . ": {$movement['type']} - " . number_format($movement['amount'], 2) . " (ID: {$result['movement_id']})</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إنشاء حركة " . ($i + 1) . ": " . ($result['error'] ?? 'خطأ غير معروف') . "</p>\n";
            }
        }
        
        echo "<p><strong>ملخص الحركات المنشأة:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>إجمالي الإيداعات: " . number_format($totalDeposits, 2) . "</li>\n";
        echo "<li>إجمالي السحوبات: " . number_format($totalWithdrawals, 2) . "</li>\n";
        echo "<li>صافي التغيير: " . number_format($totalDeposits - $totalWithdrawals, 2) . "</li>\n";
        echo "</ul>\n";
        
        echo "<h3>3. اختبار استرجاع وعرض الحركات</h3>\n";
        
        $movements = $cashManager->getCashBoxMovements($cashBoxId, [], 10, 0);
        
        echo "<p><strong>عدد الحركات المسترجعة:</strong> " . count($movements) . "</p>\n";
        
        if (!empty($movements)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>ID</th><th>النوع</th><th>المبلغ</th><th>الوصف</th><th>المرجع</th><th>المستخدم</th><th>الرصيد بعد</th><th>التاريخ</th>\n";
            echo "</tr>\n";
            
            foreach (array_slice($movements, 0, 5) as $movement) {
                $movementType = $movement['type'] ?? $movement['movement_type'] ?? '';
                $amount = (float)($movement['amount'] ?? 0);
                $description = $movement['description'] ?? '';
                $reference = $movement['reference_number'] ?? '';
                $userName = $movement['user_name'] ?? '';
                $balanceAfter = (float)($movement['balance_after'] ?? 0);
                $createdAt = $movement['created_at'] ?? '';
                
                $typeColor = ($movementType === 'deposit') ? 'green' : 'orange';
                $amountSign = ($movementType === 'deposit') ? '+' : '-';
                
                echo "<tr>\n";
                echo "<td>{$movement['id']}</td>\n";
                echo "<td style='color: $typeColor;'><strong>$movementType</strong></td>\n";
                echo "<td style='color: $typeColor;'>$amountSign" . number_format($amount, 2) . "</td>\n";
                echo "<td>" . htmlspecialchars(substr($description, 0, 40) . (strlen($description) > 40 ? '...' : '')) . "</td>\n";
                echo "<td><code>" . htmlspecialchars($reference) . "</code></td>\n";
                echo "<td>" . htmlspecialchars($userName ?: 'غير محدد') . "</td>\n";
                echo "<td><strong>" . number_format($balanceAfter, 2) . "</strong></td>\n";
                echo "<td>" . date('Y-m-d H:i', strtotime($createdAt)) . "</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
        
        echo "<h3>4. اختبار التصفية</h3>\n";
        
        // Test different filters
        $filters = [
            ['type' => 'deposit', 'name' => 'الإيداعات فقط'],
            ['type' => 'withdrawal', 'name' => 'السحوبات فقط'],
            ['date_from' => date('Y-m-d'), 'name' => 'حركات اليوم']
        ];
        
        foreach ($filters as $filter) {
            $filteredMovements = $cashManager->getCashBoxMovements($cashBoxId, $filter, 10, 0);
            echo "<p><strong>{$filter['name']}:</strong> " . count($filteredMovements) . " حركة</p>\n";
        }
        
        echo "<h3>5. فحص الرصيد النهائي</h3>\n";
        
        // Update and check final balance
        $cashManager->updateCashBoxBalance($cashBoxId);
        $updatedCashBox = $cashManager->getCashBoxById($cashBoxId);
        
        if ($updatedCashBox) {
            echo "<p><strong>الرصيد النهائي للصندوق:</strong> " . number_format($updatedCashBox['current_balance'], 2) . " {$updatedCashBox['currency_symbol']}</p>\n";
            echo "<p><strong>الرصيد الابتدائي:</strong> " . number_format($updatedCashBox['initial_balance'], 2) . " {$updatedCashBox['currency_symbol']}</p>\n";
            
            $netChange = $updatedCashBox['current_balance'] - $updatedCashBox['initial_balance'];
            echo "<p><strong>صافي التغيير:</strong> " . number_format($netChange, 2) . " {$updatedCashBox['currency_symbol']}</p>\n";
        }
        
        echo "<h3>6. روابط الاختبار</h3>\n";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
        echo "<p><strong>اختبر الصفحات التالية:</strong></p>\n";
        echo "<ul>\n";
        echo "<li><a href='../dashboard/cash.php' target='_blank' style='color: #007bff; font-weight: bold;'>📊 صفحة الصناديق الرئيسية</a></li>\n";
        echo "<li><a href='../dashboard/cash_box_history.php?id=$cashBoxId' target='_blank' style='color: #007bff; font-weight: bold;'>📈 تاريخ الصندوق المفصل</a></li>\n";
        if (!empty($createdMovements)) {
            echo "<li><a href='../dashboard/edit_movement.php?id={$createdMovements[0]}' target='_blank' style='color: #007bff; font-weight: bold;'>✏️ تعديل حركة مالية</a></li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
        
        echo "<h3>7. فحص الوظائف المتقدمة</h3>\n";
        
        $advancedChecks = [
            'تحديث الرصيد التلقائي' => method_exists($cashManager, 'updateCashBoxBalance'),
            'جلب الحركات مع التصفية' => method_exists($cashManager, 'getCashBoxMovements'),
            'تحديث الحركات' => method_exists($cashManager, 'updateCashMovement'),
            'جلب حركة بالرقم' => method_exists($cashManager, 'getCashMovementById'),
            'البحث بالمرجع' => method_exists($cashManager, 'getMovementByReference')
        ];
        
        foreach ($advancedChecks as $feature => $available) {
            $status = $available ? '✓' : '✗';
            $color = $available ? 'green' : 'red';
            echo "<p style='color: $color;'>$status $feature</p>\n";
        }
        
    } else {
        echo "<p style='color: red;'>✗ لا توجد صناديق للاختبار</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>🎉 انتهى الاختبار الشامل للنظام!</h3>\n";
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<p><strong>✅ النتيجة النهائية: النظام جاهز للاستخدام!</strong></p>\n";
    echo "<p>جميع الوظائف تعمل بشكل صحيح:</p>\n";
    echo "<ul>\n";
    echo "<li>✅ إنشاء وإدارة الحركات المالية</li>\n";
    echo "<li>✅ حساب الأرصدة بدقة</li>\n";
    echo "<li>✅ عرض التفاصيل بوضوح</li>\n";
    echo "<li>✅ التصفية والبحث</li>\n";
    echo "<li>✅ التعديل والتحديث</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

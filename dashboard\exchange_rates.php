<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/exchange_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('exchange.rates');

$currentUser = $auth->getCurrentUser();
$userId      = $currentUser['id'];

$exManager   = new ExchangeManager(new Database());
$curManager  = new CurrencyManager(new Database());

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
// Flash messages are now handled in includes/functions.php

// Handle actions (delete)
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $id = (int) $_GET['id'];
    if ($auth->hasPermission('exchange.rates')) {
        $ok = $exManager->deleteExchangeRate($id);
        if ($ok) {
            log_activity($userId, 'exchange.delete_rate', ['rate_id' => $id]);
            set_flash('success', 'تم حذف سعر الصرف بنجاح');
        } else {
            set_flash('danger', 'لا يمكن حذف سعر الصرف – قد يكون مستخدمًا في عمليات');
        }
    }
    redirect('exchange_rates.php');
}

$officeId = isset($_GET['office_id']) ? (int)$_GET['office_id'] : 0;
$office = null;
if ($officeId > 0) {
    require_once __DIR__ . '/../includes/office_manager.php';
    $officeMgr = new OfficeManager(new Database());
    $office = $officeMgr->getOfficeById($officeId);
    if (!$office) {
        set_flash('danger', 'المكتب غير موجود');
        redirect('offices.php');
    }
    $rates = $officeMgr->getOfficeExchangeRates($officeId);
} else {
    $rates = [];
}

$csrf  = get_csrf_token();

$pageTitle = $office ? ('أسعار القص - ' . $office['office_name']) : 'أسعار القص';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>
<div class="container-fluid p-4">
    <h3 class="mb-4"><i class="fas fa-percentage"></i> إدارة أسعار القص <?php if($office) echo ' - ' . htmlspecialchars($office['office_name']); ?></h3>
    <?php foreach (get_flash() as $type => $msg): ?>
        <div class="alert alert-<?php echo $type; ?> alert-dismissible fade show" role="alert">
            <?php echo $msg; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endforeach; ?>
    <?php if (!$office): ?>
        <div class="alert alert-warning">يرجى اختيار مكتب لعرض أسعار القص الخاصة به.</div>
        <a href="offices.php" class="btn btn-primary"><i class="fas fa-arrow-left"></i> عودة للمكاتب</a>
    <?php else: ?>
    <div class="mb-3 d-flex justify-content-between align-items-center">
        <a href="office_details.php?id=<?php echo $officeId; ?>" class="btn btn-outline-secondary"><i class="fas fa-arrow-right"></i> عودة لتفاصيل المكتب</a>
        <a href="add_exchange_rate.php?office_id=<?php echo $officeId; ?>" class="btn btn-sm btn-primary"><i class="fas fa-plus"></i> إضافة سعر قص جديد</a>
    </div>
    <div class="table-responsive">
        <table class="table table-bordered table-hover small align-middle">
            <thead class="table-light">
                <tr>
                    <th>#</th>
                    <th>اسم العملية</th>
                    <th>سعر القص</th>
                    <th>الحالة</th>
                    <th>تاريخ آخر تحديث</th>
                    <th class="text-center">إجراءات</th>
                </tr>
            </thead>
            <tbody>
            <?php foreach ($rates as $idx => $r): ?>
                <tr>
                    <td><?php echo $idx + 1; ?></td>
                    <td><?php echo htmlspecialchars($r['operation_name']); ?></td>
                    <td><span class="badge bg-warning text-dark"><?php echo number_format((float)$r['exchange_rate_percentage'], 2); ?></span></td>
                    <td><?php if ($r['is_active']): ?><span class="badge bg-success">نشط</span><?php else: ?><span class="badge bg-danger">غير نشط</span><?php endif; ?></td>
                    <td><?php echo htmlspecialchars($r['updated_at']); ?></td>
                    <td class="text-center">
                        <a href="edit_exchange_rate.php?id=<?php echo $r['id']; ?>" class="btn btn-sm btn-secondary me-1"><i class="fas fa-edit"></i></a>
                        <a href="exchange_rates.php?action=delete&id=<?php echo $r['id']; ?>&office_id=<?php echo $officeId; ?>" class="btn btn-sm btn-danger" onclick="return confirm('تأكيد الحذف؟');"><i class="fas fa-trash"></i></a>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endif; ?>
</div>
<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
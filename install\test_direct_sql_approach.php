<?php
/**
 * اختبار الطريقة الجديدة باستخدام SQL مباشر
 * Test New Direct SQL Approach
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطريقة الجديدة - SQL مباشر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .test-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-database text-success"></i>
            اختبار الطريقة الجديدة - SQL مباشر
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>نبذة عن التحديث</h5>
            <p>تم تحديث النظام ليستخدم استعلامات SQL مباشرة بدلاً من الإجراءات المخزنة لضمان الاستقرار وتجنب مشاكل التوافق.</p>
        </div>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '', $type = 'normal') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                $class = $type == 'warning' ? 'test-warning' : 'test-error';
                $icon = $type == 'warning' ? 'exclamation-triangle' : 'times-circle';
                echo "<div class='test-result {$class}'>";
                echo "<i class='fas fa-{$icon} me-2'></i>";
                echo "<strong>" . ($type == 'warning' ? '⚠' : '✗') . " {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
            
            // اختبار 1: فحص الجداول المطلوبة
            showInfo("<strong>اختبار الجداول الأساسية:</strong>");
            
            $required_tables = [
                'daily_transactions' => 'جدول المعاملات اليومية',
                'daily_transaction_history' => 'جدول تاريخ المعاملات',
                'deleted_daily_transactions' => 'جدول المعاملات المحذوفة',
                'countries' => 'جدول الدول',
                'users' => 'جدول المستخدمين'
            ];
            
            foreach ($required_tables as $table => $description) {
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) FROM {$table} LIMIT 1");
                    showResult($description, true, "الجدول موجود ويمكن الوصول إليه");
                } catch (Exception $e) {
                    showResult($description, false, $e->getMessage());
                }
            }
            
            // اختبار 2: فحص وجود دول نشطة
            showInfo("<strong>اختبار البيانات الأساسية:</strong>");
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM countries WHERE is_active = 1");
                $count = $stmt->fetch()['count'];
                showResult("الدول النشطة", $count > 0, "يوجد {$count} دولة نشطة");
            } catch (Exception $e) {
                showResult("الدول النشطة", false, $e->getMessage());
            }
            
            // اختبار 3: اختبار إنشاء معاملة تجريبية
            showInfo("<strong>اختبار العمليات الأساسية:</strong>");
            
            try {
                // الحصول على أول دولة نشطة
                $stmt = $pdo->query("SELECT id FROM countries WHERE is_active = 1 LIMIT 1");
                $country = $stmt->fetch();
                
                if ($country) {
                    // بيانات المعاملة التجريبية
                    $transaction_number = 'TEST' . date('YmdHis');
                    $country_id = $country['id'];
                    $base_amount = 1000.00;
                    $customer_rate = 1.5;
                    $operation_type = 'multiply';
                    $calculated_amount = $base_amount * $customer_rate;
                    $exchange_rate = 250.0;
                    $recipient_amount = floor($calculated_amount * $exchange_rate);
                    $delivery_type = 'cash';
                    $recipient_name = 'اختبار النظام';
                    $notes = 'معاملة تجريبية لاختبار النظام الجديد';
                    $branch_id = 1;
                    $created_by = 1;
                    
                    // بدء المعاملة
                    $pdo->beginTransaction();
                    
                    // إدراج المعاملة
                    $insert_sql = "INSERT INTO daily_transactions (
                        transaction_number, country_id, base_amount, customer_rate, operation_type,
                        calculated_amount, exchange_rate, recipient_amount, delivery_type,
                        transfer_amount, recipient_name, notes, branch_id, created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $stmt = $pdo->prepare($insert_sql);
                    $stmt->execute([
                        $transaction_number, $country_id, $base_amount, $customer_rate, $operation_type,
                        $calculated_amount, $exchange_rate, $recipient_amount, $delivery_type,
                        null, $recipient_name, $notes, $branch_id, $created_by
                    ]);
                    
                    $transaction_id = $pdo->lastInsertId();
                    
                    if ($transaction_id > 0) {
                        showResult("إنشاء معاملة تجريبية", true, "تم إنشاء المعاملة رقم: {$transaction_number} (ID: {$transaction_id})");
                        
                        // اختبار التحديث
                        $update_sql = "UPDATE daily_transactions SET 
                            notes = ?, updated_by = ?, updated_at = CURRENT_TIMESTAMP 
                            WHERE id = ?";
                        
                        $stmt = $pdo->prepare($update_sql);
                        $stmt->execute(['تم تحديث المعاملة التجريبية', 1, $transaction_id]);
                        
                        if ($stmt->rowCount() > 0) {
                            showResult("تحديث المعاملة التجريبية", true, "تم تحديث المعاملة بنجاح");
                        } else {
                            showResult("تحديث المعاملة التجريبية", false, "فشل في تحديث المعاملة");
                        }
                        
                        // اختبار الحذف (نقل إلى المحذوفات)
                        $stmt = $pdo->prepare("SELECT * FROM daily_transactions WHERE id = ?");
                        $stmt->execute([$transaction_id]);
                        $transaction_to_delete = $stmt->fetch();
                        
                        if ($transaction_to_delete) {
                            // نقل إلى المحذوفات
                            $insert_deleted_sql = "INSERT INTO deleted_daily_transactions (
                                original_id, transaction_number, country_id, base_amount, customer_rate,
                                operation_type, calculated_amount, exchange_rate, recipient_amount,
                                delivery_type, transfer_amount, recipient_name, notes, branch_id,
                                created_by, created_at, updated_by, updated_at, deleted_by
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                            
                            $stmt = $pdo->prepare($insert_deleted_sql);
                            $stmt->execute([
                                $transaction_to_delete['id'],
                                $transaction_to_delete['transaction_number'],
                                $transaction_to_delete['country_id'],
                                $transaction_to_delete['base_amount'],
                                $transaction_to_delete['customer_rate'],
                                $transaction_to_delete['operation_type'],
                                $transaction_to_delete['calculated_amount'],
                                $transaction_to_delete['exchange_rate'],
                                $transaction_to_delete['recipient_amount'],
                                $transaction_to_delete['delivery_type'],
                                $transaction_to_delete['transfer_amount'],
                                $transaction_to_delete['recipient_name'],
                                $transaction_to_delete['notes'],
                                $transaction_to_delete['branch_id'],
                                $transaction_to_delete['created_by'],
                                $transaction_to_delete['created_at'],
                                $transaction_to_delete['updated_by'],
                                $transaction_to_delete['updated_at'],
                                1
                            ]);
                            
                            // حذف من الجدول الأساسي
                            $delete_sql = "DELETE FROM daily_transactions WHERE id = ?";
                            $stmt = $pdo->prepare($delete_sql);
                            $stmt->execute([$transaction_id]);
                            
                            showResult("حذف المعاملة التجريبية", true, "تم نقل المعاملة إلى المحذوفات وحذفها من الجدول الأساسي");
                        }
                        
                        // تأكيد المعاملة
                        $pdo->commit();
                        
                    } else {
                        $pdo->rollback();
                        showResult("إنشاء معاملة تجريبية", false, "فشل في الحصول على معرف المعاملة");
                    }
                } else {
                    showResult("إنشاء معاملة تجريبية", false, "لا توجد دول نشطة في قاعدة البيانات");
                }
            } catch (Exception $e) {
                $pdo->rollback();
                showResult("إنشاء معاملة تجريبية", false, $e->getMessage());
            }
            
            // اختبار 4: فحص ملفات الواجهة
            showInfo("<strong>اختبار ملفات الواجهة:</strong>");
            
            $interface_files = [
                'dashboard/daily_transactions.php' => 'صفحة قائمة المعاملات',
                'dashboard/add_daily_transaction.php' => 'صفحة إضافة معاملة',
                'dashboard/edit_daily_transaction.php' => 'صفحة تعديل معاملة',
                'dashboard/view_daily_transaction.php' => 'صفحة عرض معاملة',
                'dashboard/deleted_daily_transactions.php' => 'صفحة المعاملات المحذوفة'
            ];
            
            foreach ($interface_files as $file => $description) {
                $file_path = __DIR__ . '/../' . $file;
                $exists = file_exists($file_path);
                showResult($description, $exists, $exists ? "الملف موجود" : "الملف غير موجود");
            }
            
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 95) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> النظام الجديد يعمل بشكل مثالي";
            echo "</div>";
        } elseif ($success_rate >= 80) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> النظام يعمل مع بعض المشاكل البسيطة";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج إصلاح!</strong> هناك مشاكل تحتاج إلى حل";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <div class="text-center mt-4">
            <a href="../dashboard/daily_transactions.php" class="btn btn-primary btn-lg">
                <i class="fas fa-list me-2"></i>
                قائمة المعاملات
            </a>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-success btn-lg ms-2">
                <i class="fas fa-plus me-2"></i>
                إضافة معاملة
            </a>
            <a href="../dashboard/deleted_daily_transactions.php" class="btn btn-warning btn-lg ms-2">
                <i class="fas fa-trash me-2"></i>
                المعاملات المحذوفة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

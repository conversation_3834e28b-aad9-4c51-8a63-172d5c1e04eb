<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paypal_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('transfers.view');

$id = (int)($_GET['id'] ?? 0);
$paypalMgr = new PayPalManager(new Database());
$transfer = $paypalMgr->getTransferById($id);
if (!$transfer) {
    set_flash('danger', 'الحوالة غير موجودة');
    redirect('paypal_transfers.php');
}

$pageTitle = 'عرض حوالة #' . $id;
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>
<div class="container p-4">
    <h3 class="mb-4"><i class="fas fa-eye"></i> تفاصيل حوالة PayPal</h3>
    <table class="table table-bordered w-50">
        <tr><th>ID</th><td><?php echo $transfer['id']; ?></td></tr>
        <tr><th>اسم المستلم</th><td><?php echo htmlspecialchars($transfer['recipient_name']); ?></td></tr>
        <tr><th>رقم الجوال</th><td><?php echo htmlspecialchars($transfer['recipient_phone']); ?></td></tr>
        <tr><th>رمز المعاملة</th><td><?php echo htmlspecialchars($transfer['transaction_code']); ?></td></tr>
        <tr><th>اسم المرسل</th><td><?php echo htmlspecialchars($transfer['sender_name']); ?></td></tr>
        <tr><th>المبلغ</th><td><?php echo number_format($transfer['amount'],2); ?></td></tr>
        <tr><th>الحالة</th><td><?php echo $transfer['status']; ?></td></tr>
        <tr><th>التاريخ</th><td><?php echo $transfer['created_at']; ?></td></tr>
    </table>
    <a href="paypal_transfers.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> رجوع</a>
</div>
<?php require_once __DIR__ . '/../includes/footer.php'; ?>

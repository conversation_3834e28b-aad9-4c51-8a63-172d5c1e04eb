<?php
/**
 * إصلاح مشكلة تحديث حالة الأدوار
 * Fix Role Status Update Issue
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// إعداد الترميز
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح مشكلة تحديث حالة الأدوار</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔧 إصلاح مشكلة تحديث حالة الأدوار</h1>";

try {
    $db = new Database();
    $conn = Database::getConnection();
    
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // 1. فحص بنية عمود status
    echo "<h3>1. فحص بنية عمود status</h3>";
    $result = $conn->query("SHOW COLUMNS FROM roles LIKE 'status'");
    $statusColumn = $result->fetch_assoc();
    
    if ($statusColumn) {
        echo "<div class='info'>📋 نوع عمود status: " . htmlspecialchars($statusColumn['Type']) . "</div>";
        echo "<div class='info'>📋 القيمة الافتراضية: " . htmlspecialchars($statusColumn['Default']) . "</div>";
        
        // إذا كان العمود من نوع ENUM، نحتاج لتغييره
        if (strpos($statusColumn['Type'], 'enum') !== false) {
            echo "<div class='warning'>⚠️ عمود status من نوع ENUM، سيتم تغييره إلى VARCHAR</div>";
            
            $alterQuery = "ALTER TABLE roles MODIFY COLUMN status VARCHAR(20) DEFAULT 'نشط'";
            if ($conn->query($alterQuery)) {
                echo "<div class='success'>✅ تم تغيير نوع عمود status إلى VARCHAR</div>";
            } else {
                echo "<div class='error'>❌ فشل في تغيير نوع العمود: " . $conn->error . "</div>";
            }
        }
    } else {
        echo "<div class='error'>❌ عمود status غير موجود</div>";
    }
    
    // 2. توحيد قيم الحالة
    echo "<h3>2. توحيد قيم الحالة</h3>";
    
    // تحديث القيم المختلطة
    $updates = [
        "UPDATE roles SET status = 'نشط' WHERE status IN ('active', 'Active', 'ACTIVE', '1', 'enabled')",
        "UPDATE roles SET status = 'غير نشط' WHERE status IN ('inactive', 'Inactive', 'INACTIVE', '0', 'disabled')"
    ];
    
    foreach ($updates as $query) {
        if ($conn->query($query)) {
            $affected = $conn->affected_rows;
            if ($affected > 0) {
                echo "<div class='success'>✅ تم تحديث $affected سجل</div>";
            }
        } else {
            echo "<div class='error'>❌ فشل في التحديث: " . $conn->error . "</div>";
        }
    }
    
    // 3. إضافة فهرس للعمود إذا لم يكن موجوداً
    echo "<h3>3. فحص وإضافة فهرس للعمود</h3>";
    
    $indexCheck = $conn->query("SHOW INDEX FROM roles WHERE Column_name = 'status'");
    if ($indexCheck->num_rows == 0) {
        if ($conn->query("ALTER TABLE roles ADD INDEX idx_status (status)")) {
            echo "<div class='success'>✅ تم إضافة فهرس لعمود status</div>";
        } else {
            echo "<div class='warning'>⚠️ فشل في إضافة الفهرس: " . $conn->error . "</div>";
        }
    } else {
        echo "<div class='info'>📋 فهرس عمود status موجود بالفعل</div>";
    }
    
    // 4. إضافة عمود updated_at إذا لم يكن موجوداً
    echo "<h3>4. فحص عمود updated_at</h3>";
    
    $updatedAtCheck = $conn->query("SHOW COLUMNS FROM roles LIKE 'updated_at'");
    if ($updatedAtCheck->num_rows == 0) {
        $addUpdatedAt = "ALTER TABLE roles ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
        if ($conn->query($addUpdatedAt)) {
            echo "<div class='success'>✅ تم إضافة عمود updated_at</div>";
        } else {
            echo "<div class='warning'>⚠️ فشل في إضافة عمود updated_at: " . $conn->error . "</div>";
        }
    } else {
        echo "<div class='info'>📋 عمود updated_at موجود بالفعل</div>";
    }
    
    // 5. إنشاء trigger لتحديث updated_at
    echo "<h3>5. إنشاء trigger للتحديث التلقائي</h3>";
    
    $triggerQuery = "
    CREATE TRIGGER IF NOT EXISTS roles_update_timestamp 
    BEFORE UPDATE ON roles 
    FOR EACH ROW 
    SET NEW.updated_at = CURRENT_TIMESTAMP
    ";
    
    if ($conn->query($triggerQuery)) {
        echo "<div class='success'>✅ تم إنشاء trigger للتحديث التلقائي</div>";
    } else {
        echo "<div class='warning'>⚠️ فشل في إنشاء trigger: " . $conn->error . "</div>";
    }
    
    // 6. اختبار التحديث
    echo "<h3>6. اختبار التحديث</h3>";
    
    $testRole = $conn->query("SELECT * FROM roles WHERE id != 1 LIMIT 1")->fetch_assoc();
    if ($testRole) {
        $testId = $testRole['id'];
        $originalStatus = $testRole['status'];
        $newStatus = ($originalStatus === 'نشط') ? 'غير نشط' : 'نشط';
        
        echo "<div class='info'>📋 اختبار الدور: " . htmlspecialchars($testRole['name']) . "</div>";
        echo "<div class='info'>📋 الحالة الأصلية: " . htmlspecialchars($originalStatus) . "</div>";
        
        // تحديث الحالة
        $stmt = $conn->prepare("UPDATE roles SET status = ? WHERE id = ?");
        $stmt->bind_param('si', $newStatus, $testId);
        
        if ($stmt->execute()) {
            echo "<div class='success'>✅ تم تحديث الحالة بنجاح</div>";
            
            // التحقق من التحديث
            $checkStmt = $conn->prepare("SELECT status, updated_at FROM roles WHERE id = ?");
            $checkStmt->bind_param('i', $testId);
            $checkStmt->execute();
            $result = $checkStmt->get_result();
            $updated = $result->fetch_assoc();
            
            echo "<div class='info'>📋 الحالة الجديدة: " . htmlspecialchars($updated['status']) . "</div>";
            if (isset($updated['updated_at'])) {
                echo "<div class='info'>📋 وقت التحديث: " . htmlspecialchars($updated['updated_at']) . "</div>";
            }
            
            // إعادة الحالة الأصلية
            $restoreStmt = $conn->prepare("UPDATE roles SET status = ? WHERE id = ?");
            $restoreStmt->bind_param('si', $originalStatus, $testId);
            $restoreStmt->execute();
            echo "<div class='info'>🔄 تم إعادة الحالة الأصلية</div>";
            
            $checkStmt->close();
            $restoreStmt->close();
        } else {
            echo "<div class='error'>❌ فشل في تحديث الحالة: " . $stmt->error . "</div>";
        }
        $stmt->close();
    }
    
    // 7. عرض الحالة النهائية
    echo "<h3>7. الحالة النهائية للأدوار</h3>";
    
    $finalRoles = $conn->query("SELECT id, name, status FROM roles ORDER BY id");
    if ($finalRoles) {
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f2f2f2;'><th style='border: 1px solid #ddd; padding: 8px;'>ID</th><th style='border: 1px solid #ddd; padding: 8px;'>الاسم</th><th style='border: 1px solid #ddd; padding: 8px;'>الحالة</th></tr>";
        while ($role = $finalRoles->fetch_assoc()) {
            $statusColor = ($role['status'] === 'نشط') ? '#28a745' : '#ffc107';
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($role['id']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($role['name']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; color: $statusColor; font-weight: bold;'>" . htmlspecialchars($role['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إنجاز الإصلاح بنجاح!</h3>";
    echo "<p>تم إصلاح جميع المشاكل المحتملة في تحديث حالة الأدوار.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في الإصلاح</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>انتقل إلى صفحة الأدوار: <a href='../dashboard/roles.php' target='_blank'>dashboard/roles.php</a></li>";
echo "<li>جرب تغيير حالة أي دور بالنقر على شارة الحالة</li>";
echo "<li>تأكد من أن التغيير يتم حفظه بشكل صحيح</li>";
echo "<li>تحقق من تحديث الإحصائيات تلقائياً</li>";
echo "</ol>";
echo "</div>";

echo "</div></body></html>";
?>

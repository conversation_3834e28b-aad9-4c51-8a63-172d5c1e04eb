<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/exchange_manager.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('exchange.view');

$currentUser = $auth->getCurrentUser();

if (!isset($_GET['id'])) {
    set_flash('danger', 'معرف العملية غير صالح');
    redirect('exchange.php');
    exit;
}

$exchangeId = (int)$_GET['id'];

$exManager = new ExchangeManager(new Database());
$customerMgr = new CustomerManager(new Database());
$curManager = new CurrencyManager(new Database());

// Get exchange details
try {
    $exchange = $exManager->getExchangeById($exchangeId);
    if (!$exchange) {
        set_flash('danger', 'عملية الصرف غير موجودة');
        redirect('exchange.php');
        exit;
    }


    
    // Get related data
    $customer = $customerMgr->getCustomerById($exchange['customer_id']);

    // Determine currency information based on source type
    $fromCurrency = null;
    $toCurrency = null;

    if (!empty($exchange['from_currency_code'])) {
        $fromCurrency = ['code' => $exchange['from_currency_code'], 'symbol' => $exchange['from_currency_code']];
    } elseif (!empty($exchange['from_bank_currency_code'])) {
        $fromCurrency = ['code' => $exchange['from_bank_currency_code'], 'symbol' => $exchange['from_bank_currency_code']];
    }

    if (!empty($exchange['to_currency_code'])) {
        $toCurrency = ['code' => $exchange['to_currency_code'], 'symbol' => $exchange['to_currency_code']];
    } elseif (!empty($exchange['to_bank_currency_code'])) {
        $toCurrency = ['code' => $exchange['to_bank_currency_code'], 'symbol' => $exchange['to_bank_currency_code']];
    }
    
} catch (Exception $e) {
    set_flash('danger', 'خطأ في تحميل بيانات العملية: ' . $e->getMessage());
    redirect('exchange.php');
    exit;
}

$pageTitle = 'تفاصيل عملية الصرف #' . $exchangeId;
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3><i class="fas fa-exchange-alt"></i> تفاصيل عملية الصرف #<?php echo $exchangeId; ?></h3>
                <div>
                    <a href="exchange.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة
                    </a>
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Exchange Details Card -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> تفاصيل العملية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">رقم العملية:</td>
                                    <td>#<?php echo $exchange['id']; ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">العميل:</td>
                                    <td>
                                        <?php if ($customer): ?>
                                            <a href="view_customer.php?id=<?php echo $customer['id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($customer['full_name']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">من عملة:</td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo htmlspecialchars($fromCurrency['code'] ?? 'غير محدد'); ?>
                                        </span>
                                        <?php echo htmlspecialchars($fromCurrency['name'] ?? ''); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">إلى عملة:</td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?php echo htmlspecialchars($toCurrency['code'] ?? 'غير محدد'); ?>
                                        </span>
                                        <?php echo htmlspecialchars($toCurrency['name'] ?? ''); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">المبلغ المستلم:</td>
                                    <td class="text-primary fw-bold">
                                        <?php
                                        // Debug
                                        echo "<!-- Debug: from_amount = " . ($exchange['from_amount'] ?? 'NOT SET') . " -->";
                                        echo number_format($exchange['from_amount'] ?? 0, 2);
                                        ?>
                                        <?php echo htmlspecialchars($fromCurrency['symbol'] ?? $fromCurrency['code'] ?? ''); ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">المبلغ المدفوع:</td>
                                    <td class="text-success fw-bold">
                                        <?php
                                        // Debug
                                        echo "<!-- Debug: to_amount = " . ($exchange['to_amount'] ?? 'NOT SET') . " -->";
                                        echo number_format($exchange['to_amount'] ?? 0, 2);
                                        ?>
                                        <?php echo htmlspecialchars($toCurrency['symbol'] ?? $toCurrency['code'] ?? ''); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">سعر الصرف:</td>
                                    <td class="fw-bold">
                                        <?php echo number_format($exchange['exchange_rate'] ?? 0, 6); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">العمولة:</td>
                                    <td>
                                        <?php echo number_format($exchange['commission'], 2); ?>
                                        <?php echo htmlspecialchars($fromCurrency['symbol'] ?? $fromCurrency['code'] ?? ''); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الربح:</td>
                                    <td class="<?php echo $exchange['profit'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo number_format($exchange['profit'], 2); ?>
                                        <?php echo htmlspecialchars($fromCurrency['symbol'] ?? $fromCurrency['code'] ?? ''); ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">الحالة:</td>
                                    <td>
                                        <?php
                                        $statusClass = '';
                                        $statusText = '';
                                        switch ($exchange['status']) {
                                            case 'completed':
                                                $statusClass = 'bg-success';
                                                $statusText = 'مكتملة';
                                                break;
                                            case 'pending':
                                                $statusClass = 'bg-warning';
                                                $statusText = 'معلقة';
                                                break;
                                            case 'cancelled':
                                                $statusClass = 'bg-danger';
                                                $statusText = 'ملغية';
                                                break;
                                            default:
                                                $statusClass = 'bg-secondary';
                                                $statusText = $exchange['status'];
                                        }
                                        ?>
                                        <span class="badge <?php echo $statusClass; ?>">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Source Information -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-arrow-down"></i> مصدر التمويل (استلام)</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($exchange['from_cash_box_id'])): ?>
                                        <p class="mb-1"><strong>النوع:</strong> صندوق نقدي</p>
                                        <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($exchange['from_cash_box_name'] ?? 'غير محدد'); ?></p>
                                        <p class="mb-0"><strong>العملة:</strong> <?php echo htmlspecialchars($exchange['from_currency_code'] ?? 'غير محدد'); ?></p>
                                    <?php elseif (!empty($exchange['from_bank_account_id'])): ?>
                                        <p class="mb-1"><strong>النوع:</strong> حساب بنكي</p>
                                        <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($exchange['from_bank_account_name'] ?? 'غير محدد'); ?></p>
                                        <p class="mb-0"><strong>العملة:</strong> <?php echo htmlspecialchars($exchange['from_bank_currency_code'] ?? 'غير محدد'); ?></p>
                                    <?php else: ?>
                                        <p class="text-muted">غير محدد</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-arrow-up"></i> مصدر الصرف (دفع)</h6>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($exchange['to_cash_box_id'])): ?>
                                        <p class="mb-1"><strong>النوع:</strong> صندوق نقدي</p>
                                        <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($exchange['to_cash_box_name'] ?? 'غير محدد'); ?></p>
                                        <p class="mb-0"><strong>العملة:</strong> <?php echo htmlspecialchars($exchange['to_currency_code'] ?? 'غير محدد'); ?></p>
                                    <?php elseif (!empty($exchange['to_bank_account_id'])): ?>
                                        <p class="mb-1"><strong>النوع:</strong> حساب بنكي</p>
                                        <p class="mb-1"><strong>الاسم:</strong> <?php echo htmlspecialchars($exchange['to_bank_account_name'] ?? 'غير محدد'); ?></p>
                                        <p class="mb-0"><strong>العملة:</strong> <?php echo htmlspecialchars($exchange['to_bank_currency_code'] ?? 'غير محدد'); ?></p>
                                    <?php else: ?>
                                        <p class="text-muted">غير محدد</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($exchange['notes'])): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>ملاحظات:</strong><br>
                                <?php echo nl2br(htmlspecialchars($exchange['notes'])); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Metadata Card -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock"></i> معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless small">
                        <tr>
                            <td class="fw-bold">تاريخ الإنشاء:</td>
                            <td>
                                <?php echo date('Y-m-d H:i:s', strtotime($exchange['created_at'])); ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">منشئ العملية:</td>
                            <td>
                                <?php echo htmlspecialchars($exchange['created_by_name'] ?? 'غير محدد'); ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">الفرع:</td>
                            <td>
                                <?php echo htmlspecialchars($exchange['branch_name'] ?? 'غير محدد'); ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools"></i> إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="cash_box_history.php?exchange_id=<?php echo $exchangeId; ?>" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-history"></i> عرض حركات الصناديق
                        </a>
                        <?php if ($customer): ?>
                        <a href="view_customer.php?id=<?php echo $customer['id']; ?>" 
                           class="btn btn-outline-info btn-sm">
                            <i class="fas fa-user"></i> ملف العميل
                        </a>
                        <?php endif; ?>
                        <a href="exchange_rates.php" 
                           class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-chart-line"></i> أسعار الصرف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

-- إن<PERSON>اء جدول سحب المبالغ
CREATE TABLE `withdrawals` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `amount` DECIMAL(10,2) NOT NULL COMMENT 'المبلغ',
  `beneficiary` VARCHAR(255) NOT NULL COMMENT 'المستفيد (المرسل إليه)',
  `commission` DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT 'العمولة (نسبة مئوية)',
  `net_amount` DECIMAL(10,2) NOT NULL COMMENT 'صافي الوصول (المبلغ - العمولة)',
  `status` ENUM('معلق', 'مكتمل') NOT NULL DEFAULT 'معلق' COMMENT 'الحالة',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` INT UNSIGNED NULL,
  `notes` TEXT NULL COMMENT 'ملاحظات',
  PRIMARY KEY (`id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_created_by` (`created_by`),
  FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سحب المبالغ'; 
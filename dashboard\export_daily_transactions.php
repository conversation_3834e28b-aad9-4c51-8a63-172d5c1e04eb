<?php
/**
 * صفحة تصدير المعاملات اليومية
 * Export Daily Transactions Page
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

// إنشاء كائن المصادقة والتحقق من تسجيل الدخول
$auth = new Auth();
$auth->requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = $auth->getCurrentUser();

// التحقق من الصلاحيات
if (!$auth->hasPermission('daily_transactions.view')) {
    header('Location: ' . BASE_URL . 'dashboard/index.php?error=no_permission');
    exit;
}

// معاملات التصدير
$export_params = [
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'country_id' => $_GET['country_id'] ?? '',
    'delivery_type' => $_GET['delivery_type'] ?? '',
    'branch_id' => $_GET['branch_id'] ?? '',
    'format' => $_GET['format'] ?? 'excel'
];

// جلب البيانات للتصدير
try {
    $sql = "SELECT 
        dt.id,
        dt.transaction_number,
        dt.created_at,
        c.name_ar as country_name,
        c.currency_code,
        dt.base_amount,
        dt.customer_rate,
        dt.operation_type,
        dt.calculated_amount,
        dt.exchange_rate,
        dt.recipient_amount,
        dt.delivery_type,
        dt.transfer_amount,
        dt.recipient_name,
        dt.notes,
        b.name as branch_name,
        u_created.full_name as created_by_name,
        u_updated.full_name as updated_by_name,
        dt.updated_at
    FROM daily_transactions dt
    LEFT JOIN countries c ON dt.country_id = c.id
    LEFT JOIN branches b ON dt.branch_id = b.id
    LEFT JOIN users u_created ON dt.created_by = u_created.id
    LEFT JOIN users u_updated ON dt.updated_by = u_updated.id
    WHERE 1=1";
    
    $params = [];
    
    if ($export_params['date_from']) {
        $sql .= " AND DATE(dt.created_at) >= ?";
        $params[] = $export_params['date_from'];
    }
    if ($export_params['date_to']) {
        $sql .= " AND DATE(dt.created_at) <= ?";
        $params[] = $export_params['date_to'];
    }
    if ($export_params['country_id']) {
        $sql .= " AND dt.country_id = ?";
        $params[] = $export_params['country_id'];
    }
    if ($export_params['delivery_type']) {
        $sql .= " AND dt.delivery_type = ?";
        $params[] = $export_params['delivery_type'];
    }
    if ($export_params['branch_id']) {
        $sql .= " AND dt.branch_id = ?";
        $params[] = $export_params['branch_id'];
    }
    
    $sql .= " ORDER BY dt.created_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $transactions = $stmt->fetchAll();
    
} catch (Exception $e) {
    die("خطأ في جلب البيانات: " . $e->getMessage());
}

// تحديد اسم الملف
$filename = 'daily_transactions_' . date('Y-m-d_H-i-s');
if ($export_params['date_from'] && $export_params['date_to']) {
    $filename = 'daily_transactions_' . $export_params['date_from'] . '_to_' . $export_params['date_to'];
}

// التصدير حسب النوع المطلوب
switch ($export_params['format']) {
    case 'excel':
        exportToExcel($transactions, $filename);
        break;
    case 'csv':
        exportToCSV($transactions, $filename);
        break;
    case 'pdf':
        exportToPDF($transactions, $filename, $export_params);
        break;
    default:
        exportToExcel($transactions, $filename);
        break;
}

/**
 * تصدير إلى Excel
 */
function exportToExcel($transactions, $filename) {
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');
    
    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";
    
    echo '<table border="1">';
    echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
    echo '<th>رقم المعاملة</th>';
    echo '<th>التاريخ</th>';
    echo '<th>الدولة</th>';
    echo '<th>العملة</th>';
    echo '<th>المبلغ الأساسي</th>';
    echo '<th>سعر القص</th>';
    echo '<th>نوع العملية</th>';
    echo '<th>المبلغ المحسوب</th>';
    echo '<th>سعر الصرف</th>';
    echo '<th>المبلغ للمستلم</th>';
    echo '<th>نوع التسليم</th>';
    echo '<th>المبلغ المراد تحويله</th>';
    echo '<th>اسم المستلم</th>';
    echo '<th>الملاحظات</th>';
    echo '<th>الفرع</th>';
    echo '<th>أنشأ بواسطة</th>';
    echo '</tr>';
    
    foreach ($transactions as $transaction) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($transaction['transaction_number']) . '</td>';
        echo '<td>' . date('Y-m-d H:i', strtotime($transaction['created_at'])) . '</td>';
        echo '<td>' . htmlspecialchars($transaction['country_name']) . '</td>';
        echo '<td>' . htmlspecialchars($transaction['currency_code']) . '</td>';
        echo '<td>' . number_format($transaction['base_amount'], 2) . '</td>';
        echo '<td>' . number_format($transaction['customer_rate'], 6) . '</td>';
        echo '<td>' . ($transaction['operation_type'] == 'multiply' ? 'ضرب' : 'قسمة') . '</td>';
        echo '<td>' . number_format($transaction['calculated_amount'], 2) . '</td>';
        echo '<td>' . number_format($transaction['exchange_rate'], 6) . '</td>';
        echo '<td>' . number_format($transaction['recipient_amount']) . '</td>';
        echo '<td>' . getDeliveryTypeName($transaction['delivery_type']) . '</td>';
        echo '<td>' . ($transaction['transfer_amount'] ? number_format($transaction['transfer_amount'], 2) : '-') . '</td>';
        echo '<td>' . htmlspecialchars($transaction['recipient_name'] ?? '-') . '</td>';
        echo '<td>' . htmlspecialchars($transaction['notes'] ?? '-') . '</td>';
        echo '<td>' . htmlspecialchars($transaction['branch_name'] ?? '-') . '</td>';
        echo '<td>' . htmlspecialchars($transaction['created_by_name'] ?? '-') . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    exit;
}

/**
 * تصدير إلى CSV
 */
function exportToCSV($transactions, $filename) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: max-age=0');
    
    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // العناوين
    fputcsv($output, [
        'رقم المعاملة', 'التاريخ', 'الدولة', 'العملة', 'المبلغ الأساسي',
        'سعر القص', 'نوع العملية', 'المبلغ المحسوب', 'سعر الصرف',
        'المبلغ للمستلم', 'نوع التسليم', 'المبلغ المراد تحويله',
        'اسم المستلم', 'الملاحظات', 'الفرع', 'أنشأ بواسطة'
    ]);
    
    // البيانات
    foreach ($transactions as $transaction) {
        fputcsv($output, [
            $transaction['transaction_number'],
            date('Y-m-d H:i', strtotime($transaction['created_at'])),
            $transaction['country_name'],
            $transaction['currency_code'],
            number_format($transaction['base_amount'], 2),
            number_format($transaction['customer_rate'], 6),
            ($transaction['operation_type'] == 'multiply' ? 'ضرب' : 'قسمة'),
            number_format($transaction['calculated_amount'], 2),
            number_format($transaction['exchange_rate'], 6),
            number_format($transaction['recipient_amount']),
            getDeliveryTypeName($transaction['delivery_type']),
            ($transaction['transfer_amount'] ? number_format($transaction['transfer_amount'], 2) : '-'),
            ($transaction['recipient_name'] ?? '-'),
            ($transaction['notes'] ?? '-'),
            ($transaction['branch_name'] ?? '-'),
            ($transaction['created_by_name'] ?? '-')
        ]);
    }
    
    fclose($output);
    exit;
}

/**
 * تصدير إلى PDF
 */
function exportToPDF($transactions, $filename, $export_params) {
    // تحديد نوع المحتوى كـ HTML مع إعدادات الطباعة
    header('Content-Type: text/html; charset=utf-8');

    // إنشاء HTML محسن للطباعة والتحويل إلى PDF
    $html = '<!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تقرير المعاملات اليومية</title>
        <style>
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
            }

            body {
                font-family: "Arial", "Tahoma", sans-serif;
                direction: rtl;
                margin: 20px;
                font-size: 12px;
            }

            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .info {
                margin-bottom: 20px;
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #dee2e6;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
                font-size: 11px;
            }

            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
                vertical-align: middle;
            }

            th {
                background-color: #007bff;
                color: white;
                font-weight: bold;
                font-size: 12px;
            }

            tr:nth-child(even) {
                background-color: #f8f9fa;
            }

            .summary {
                margin-top: 20px;
                padding: 15px;
                background-color: #e7f3ff;
                border: 1px solid #b3d9ff;
                border-radius: 5px;
            }

            .print-button {
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1000;
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            }

            .print-button:hover {
                background: #0056b3;
            }
        </style>
        <script>
            function printReport() {
                window.print();
            }

            function downloadPDF() {
                // يمكن إضافة مكتبة jsPDF هنا للتحويل الفعلي إلى PDF
                alert("لتحويل إلى PDF، استخدم خيار الطباعة في المتصفح واختر حفظ كـ PDF");
                window.print();
            }
        </script>
    </head>
    <body>
        <button class="print-button no-print" onclick="printReport()">
            🖨️ طباعة / حفظ كـ PDF
        </button>

        <div class="header">
            <h1>📊 تقرير المعاملات اليومية</h1>
            <p><strong>تاريخ التقرير:</strong> ' . date('Y-m-d H:i:s') . '</p>';

    if ($export_params['date_from'] || $export_params['date_to']) {
        $html .= '<p><strong>فترة التقرير:</strong> ';
        if ($export_params['date_from']) $html .= 'من ' . $export_params['date_from'];
        if ($export_params['date_to']) $html .= ' إلى ' . $export_params['date_to'];
        $html .= '</p>';
    }

    // حساب الإحصائيات
    $total_base_amount = array_sum(array_column($transactions, 'base_amount'));
    $total_recipient_amount = array_sum(array_column($transactions, 'recipient_amount'));
    $delivery_types = array_count_values(array_column($transactions, 'delivery_type'));

    $html .= '</div>

        <div class="info">
            <div style="display: flex; justify-content: space-between; flex-wrap: wrap;">
                <div><strong>📈 إجمالي المعاملات:</strong> ' . number_format(count($transactions)) . '</div>
                <div><strong>💰 إجمالي المبلغ الأساسي:</strong> ' . number_format($total_base_amount, 2) . '</div>
                <div><strong>🎯 إجمالي المبلغ للمستلم:</strong> ' . number_format($total_recipient_amount) . '</div>
            </div>';

    if (!empty($delivery_types)) {
        $html .= '<div style="margin-top: 10px;"><strong>📋 توزيع أنواع التسليم:</strong> ';
        $delivery_names = ['cash' => 'كاش', 'bank' => 'بنكي', 'usdt' => 'USDT'];
        $delivery_summary = [];
        foreach ($delivery_types as $type => $count) {
            $type_name = $delivery_names[$type] ?? $type;
            $delivery_summary[] = $type_name . ': ' . $count;
        }
        $html .= implode(' | ', $delivery_summary) . '</div>';
    }

    $html .= '</div>

        <table>
            <thead>
                <tr>
                    <th style="width: 12%;">رقم المعاملة</th>
                    <th style="width: 10%;">التاريخ</th>
                    <th style="width: 12%;">الدولة</th>
                    <th style="width: 10%;">العملة</th>
                    <th style="width: 12%;">المبلغ الأساسي</th>
                    <th style="width: 12%;">المبلغ للمستلم</th>
                    <th style="width: 10%;">نوع التسليم</th>
                    <th style="width: 22%;">اسم المستلم</th>
                </tr>
            </thead>
            <tbody>';

    foreach ($transactions as $index => $transaction) {
        $html .= '<tr>
            <td><strong>' . htmlspecialchars($transaction['transaction_number']) . '</strong></td>
            <td>' . date('Y-m-d', strtotime($transaction['created_at'])) . '</td>
            <td>' . htmlspecialchars($transaction['country_name']) . '</td>
            <td>' . htmlspecialchars($transaction['currency_code']) . '</td>
            <td>' . number_format($transaction['base_amount'], 2) . '</td>
            <td><strong>' . number_format($transaction['recipient_amount']) . '</strong></td>
            <td>' . getDeliveryTypeName($transaction['delivery_type']) . '</td>
            <td>' . htmlspecialchars($transaction['recipient_name'] ?? '-') . '</td>
        </tr>';
    }

    $html .= '</tbody>
        </table>

        <div class="summary">
            <h3>📊 ملخص التقرير</h3>
            <p>تم إنشاء هذا التقرير في ' . date('Y-m-d H:i:s') . ' ويحتوي على ' . count($transactions) . ' معاملة.</p>
            <p>إجمالي المبلغ الأساسي: <strong>' . number_format($total_base_amount, 2) . '</strong></p>
            <p>إجمالي المبلغ للمستلم: <strong>' . number_format($total_recipient_amount) . '</strong></p>
        </div>

        <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666;">
            تم إنشاء هذا التقرير بواسطة نظام إدارة المعاملات اليومية
        </div>
    </body>
    </html>';

    echo $html;
    exit;
}

/**
 * الحصول على اسم نوع التسليم
 */
function getDeliveryTypeName($type) {
    $types = [
        'cash' => 'كاش',
        'bank' => 'بنكي',
        'usdt' => 'USDT'
    ];
    return $types[$type] ?? $type;
}
?>

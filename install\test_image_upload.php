<?php
/**
 * Test Simple Image Upload System
 * This script tests the new image upload system without GD dependency
 */

require_once __DIR__ . '/../includes/image_helper.php';

echo "<h2>اختبار نظام رفع الصور البسيط</h2>";

// Check system status
$resizingAvailable = ImageHelper::isResizingAvailable();
echo "<h3>حالة النظام:</h3>";
echo "<p>إعادة تحجيم الصور: " . ($resizingAvailable ? "متاحة" : "غير متاحة (مطلوب)") . "</p>";

// Test upload directory
$uploadDir = __DIR__ . '/../uploads/profiles/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
    echo "<p>✓ تم إنشاء مجلد الرفع: {$uploadDir}</p>";
} else {
    echo "<p>✓ مجلد الرفع موجود: {$uploadDir}</p>";
}

// Test with a sample image (if available)
$testImagePath = __DIR__ . '/../assets/images/default-avatar.png';
if (file_exists($testImagePath)) {
    echo "<h3>اختبار رفع الصورة:</h3>";
    
    // Simulate file upload
    $testFile = [
        'name' => 'test_avatar.png',
        'type' => 'image/png',
        'tmp_name' => $testImagePath,
        'error' => UPLOAD_ERR_OK,
        'size' => filesize($testImagePath)
    ];
    
    $userId = 999; // Test user ID
    $result = ImageHelper::uploadProfileImage($testFile, $userId);
    
    if ($result) {
        echo "<p>✓ تم رفع الصورة بنجاح!</p>";
        echo "<p>تم الرفع إلى: {$result}</p>";
        
        // Test image display
        $imageUrl = ImageHelper::getProfileImageUrl($result, $userId);
        echo "<p>رابط الصورة: {$imageUrl}</p>";
        
        // Show image with CSS styling
        echo "<h4>عرض الصورة بحجم دائري:</h4>";
        echo ImageHelper::getProfileImageHtml($result, $userId, '150px');
        
        // Clean up test file
        $fullPath = __DIR__ . '/../' . $result;
        if (file_exists($fullPath)) {
            unlink($fullPath);
            echo "<p>✓ تم تنظيف ملف الاختبار</p>";
        }
    } else {
        echo "<p>✗ فشل رفع الصورة</p>";
    }
} else {
    echo "<p>لا توجد صورة اختبار في: {$testImagePath}</p>";
}

echo "<h3>نتائج الاختبار:</h3>";
echo "<ul>";
echo "<li>نظام إعادة التحجيم: " . ($resizingAvailable ? "متاح" : "غير متاح") . "</li>";
echo "<li>مجلد الرفع: " . (is_dir($uploadDir) ? "موجود" : "غير موجود") . "</li>";
echo "<li>فئة ImageHelper: " . (class_exists('ImageHelper') ? "محملة" : "فشل") . "</li>";
echo "</ul>";

echo "<h3>مميزات النظام الجديد:</h3>";
echo "<ul>";
echo "<li>✅ رفع بسيط بدون إعادة تحجيم</li>";
echo "<li>✅ عرض دائري باستخدام CSS</li>";
echo "<li>✅ لا حاجة لـ GD extension</li>";
echo "<li>✅ دعم جميع أنواع الصور</li>";
echo "<li>✅ نظام آمن وموثوق</li>";
echo "</ul>";

echo "<hr>";
echo "<p><a href='../dashboard/'>← العودة للوحة التحكم</a></p>";
?> 
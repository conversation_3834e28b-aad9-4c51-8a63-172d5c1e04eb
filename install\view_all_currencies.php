<?php
/**
 * View all currencies in the database organized by regions
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>عرض جميع العملات المدخلة في النظام</h2>\n";

try {
    $conn = Database::getConnection();
    
    // Get all currencies
    $result = $conn->query("
        SELECT code, name, symbol, country, is_active, created_at 
        FROM currencies 
        ORDER BY 
            CASE 
                WHEN country LIKE '%العربية%' OR country LIKE '%الإمارات%' OR country LIKE '%السعودية%' OR country LIKE '%الكويت%' OR country LIKE '%قطر%' OR country LIKE '%البحرين%' OR country LIKE '%عمان%' OR country LIKE '%الأردن%' OR country LIKE '%لبنان%' OR country LIKE '%سوريا%' OR country LIKE '%العراق%' OR country LIKE '%مصر%' OR country LIKE '%ليبيا%' OR country LIKE '%تونس%' OR country LIKE '%الجزائر%' OR country LIKE '%المغرب%' OR country LIKE '%موريتانيا%' OR country LIKE '%السودان%' OR country LIKE '%الصومال%' OR country LIKE '%جيبوتي%' OR country LIKE '%القمر%' OR country LIKE '%اليمن%' THEN 1 
                WHEN country LIKE '%رقمية%' THEN 4
                WHEN country LIKE '%أمريك%' OR country LIKE '%كندا%' OR country LIKE '%المكسيك%' OR country LIKE '%البرازيل%' OR country LIKE '%الأرجنتين%' OR country LIKE '%تشيلي%' OR country LIKE '%كولومبيا%' OR country LIKE '%بيرو%' OR country LIKE '%الأوروغواي%' OR country LIKE '%غواتيمالا%' OR country LIKE '%هندوراس%' OR country LIKE '%نيكاراغوا%' OR country LIKE '%كوستاريكا%' OR country LIKE '%بنما%' OR country LIKE '%جامايكا%' OR country LIKE '%هايتي%' OR country LIKE '%الدومينيكان%' OR country LIKE '%كوبا%' OR country LIKE '%بربادوس%' OR country LIKE '%ترينيداد%' OR country LIKE '%غيانا%' OR country LIKE '%سورينام%' OR country LIKE '%بوليفيا%' OR country LIKE '%باراغواي%' THEN 3
                ELSE 2 
            END,
            name
    ");
    
    if (!$result) {
        throw new Exception("فشل في جلب العملات: " . $conn->error);
    }
    
    $currencies = [];
    $totalCount = 0;
    $activeCount = 0;
    $regions = [
        'arab' => [],
        'international' => [],
        'americas' => [],
        'crypto' => []
    ];
    
    while ($row = $result->fetch_assoc()) {
        $totalCount++;
        if ($row['is_active']) {
            $activeCount++;
        }
        
        // Categorize by region
        $country = $row['country'];
        $arabCountries = ['العربية', 'الإمارات', 'السعودية', 'الكويت', 'قطر', 'البحرين', 'عمان', 'الأردن', 'لبنان', 'سوريا', 'العراق', 'مصر', 'ليبيا', 'تونس', 'الجزائر', 'المغرب', 'موريتانيا', 'السودان', 'الصومال', 'جيبوتي', 'القمر', 'اليمن'];
        $americanCountries = ['أمريك', 'كندا', 'المكسيك', 'البرازيل', 'الأرجنتين', 'تشيلي', 'كولومبيا', 'بيرو', 'الأوروغواي', 'غواتيمالا', 'هندوراس', 'نيكاراغوا', 'كوستاريكا', 'بنما', 'جامايكا', 'هايتي', 'الدومينيكان', 'كوبا', 'بربادوس', 'ترينيداد', 'غيانا', 'سورينام', 'بوليفيا', 'باراغواي'];
        
        $isArab = false;
        $isAmerican = false;
        $isCrypto = strpos($country, 'رقمية') !== false;
        
        foreach ($arabCountries as $arabCountry) {
            if (strpos($country, $arabCountry) !== false) {
                $isArab = true;
                break;
            }
        }
        
        if (!$isArab && !$isCrypto) {
            foreach ($americanCountries as $americanCountry) {
                if (strpos($country, $americanCountry) !== false) {
                    $isAmerican = true;
                    break;
                }
            }
        }
        
        if ($isArab) {
            $regions['arab'][] = $row;
        } elseif ($isCrypto) {
            $regions['crypto'][] = $row;
        } elseif ($isAmerican) {
            $regions['americas'][] = $row;
        } else {
            $regions['international'][] = $row;
        }
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h3>📊 إحصائيات العملات</h3>\n";
    echo "<div style='display: flex; flex-wrap: wrap; gap: 20px;'>\n";
    echo "<div style='background: white; padding: 10px; border-radius: 5px; min-width: 150px;'>\n";
    echo "<strong>إجمالي العملات:</strong><br><span style='font-size: 24px; color: #007bff;'>$totalCount</span>\n";
    echo "</div>\n";
    echo "<div style='background: white; padding: 10px; border-radius: 5px; min-width: 150px;'>\n";
    echo "<strong>العملات النشطة:</strong><br><span style='font-size: 24px; color: #28a745;'>$activeCount</span>\n";
    echo "</div>\n";
    echo "<div style='background: white; padding: 10px; border-radius: 5px; min-width: 150px;'>\n";
    echo "<strong>العملات العربية:</strong><br><span style='font-size: 24px; color: #17a2b8;'>" . count($regions['arab']) . "</span>\n";
    echo "</div>\n";
    echo "<div style='background: white; padding: 10px; border-radius: 5px; min-width: 150px;'>\n";
    echo "<strong>العملات الأجنبية:</strong><br><span style='font-size: 24px; color: #6f42c1;'>" . count($regions['international']) . "</span>\n";
    echo "</div>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    // Function to display currency table
    function displayCurrencyTable($currencies, $title, $bgColor = '#f8f9fa') {
        if (empty($currencies)) return;
        
        echo "<div style='margin: 20px 0;'>\n";
        echo "<h3 style='background: $bgColor; padding: 10px; margin: 0; border-radius: 5px 5px 0 0;'>$title (" . count($currencies) . " عملة)</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 0;'>\n";
        echo "<tr style='background: #e9ecef;'>\n";
        echo "<th style='padding: 8px;'>الرمز</th>\n";
        echo "<th style='padding: 8px;'>اسم العملة</th>\n";
        echo "<th style='padding: 8px;'>الرمز المختصر</th>\n";
        echo "<th style='padding: 8px;'>البلد/المنطقة</th>\n";
        echo "<th style='padding: 8px;'>الحالة</th>\n";
        echo "<th style='padding: 8px;'>تاريخ الإضافة</th>\n";
        echo "</tr>\n";
        
        foreach ($currencies as $currency) {
            $statusText = $currency['is_active'] ? 'نشط' : 'غير نشط';
            $statusColor = $currency['is_active'] ? 'green' : 'red';
            $date = date('Y-m-d', strtotime($currency['created_at']));
            
            echo "<tr>\n";
            echo "<td style='padding: 8px; font-weight: bold; font-family: monospace;'>{$currency['code']}</td>\n";
            echo "<td style='padding: 8px;'>{$currency['name']}</td>\n";
            echo "<td style='padding: 8px; text-align: center; font-weight: bold;'>{$currency['symbol']}</td>\n";
            echo "<td style='padding: 8px;'>{$currency['country']}</td>\n";
            echo "<td style='padding: 8px; color: $statusColor; font-weight: bold;'>$statusText</td>\n";
            echo "<td style='padding: 8px; font-size: 12px;'>$date</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        echo "</div>\n";
    }
    
    // Display currencies by region
    displayCurrencyTable($regions['arab'], '🇸🇦 العملات العربية', '#d4edda');
    displayCurrencyTable($regions['international'], '🌍 العملات الدولية (أوروبا، آسيا، أفريقيا، أوقيانوسيا)', '#d1ecf1');
    displayCurrencyTable($regions['americas'], '🌎 عملات الأمريكتين', '#fff3cd');
    displayCurrencyTable($regions['crypto'], '₿ العملات الرقمية', '#f8d7da');
    
    echo "<hr>\n";
    
    echo "<h3>🔧 أدوات إضافية</h3>\n";
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<p><strong>روابط مفيدة:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank' style='color: #007bff;'>📊 إنشاء صندوق بعملة جديدة</a></li>\n";
    echo "<li><a href='../dashboard/exchanges.php' target='_blank' style='color: #007bff;'>💱 إدارة عمليات الصرافة</a></li>\n";
    echo "<li><a href='manage_currencies.php' target='_blank' style='color: #007bff;'>⚙️ إدارة العملات</a></li>\n";
    echo "</ul>\n";
    
    echo "<p><strong>إحصائيات سريعة:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>أكثر العملات استخداماً: USD, EUR, SAR, AED</li>\n";
    echo "<li>العملات الرقمية متوفرة للمستقبل (غير نشطة حالياً)</li>\n";
    echo "<li>جميع العملات العربية نشطة ومتاحة للاستخدام</li>\n";
    echo "<li>يمكن تفعيل/إلغاء تفعيل أي عملة حسب الحاجة</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ تم إدخال جميع العملات بنجاح!</h4>\n";
    echo "<p style='margin: 0; color: #155724;'>النظام الآن يدعم <strong>$totalCount عملة</strong> من جميع أنحاء العالم، بما في ذلك جميع العملات العربية والعملات الدولية الرئيسية.</p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    direction: rtl; 
    background: #f8f9fa;
}
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { 
    margin: 10px 0; 
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
th, td { 
    padding: 8px; 
    text-align: right; 
    border: 1px solid #dee2e6;
}
th { 
    background: #e9ecef; 
    font-weight: bold;
}
tr:nth-child(even) {
    background: #f8f9fa;
}
tr:hover {
    background: #e3f2fd;
}
a { 
    color: #007bff; 
    text-decoration: none; 
}
a:hover { 
    text-decoration: underline; 
}
pre { 
    font-size: 12px; 
}
</style>

<?php
/**
 * Trust Plus - Installation Script (Phase 1)
 *
 * Creates the database (if not exists) and the initial schema.
 */

require_once __DIR__ . '/../config/database.php';

// Connect to MySQL server (no DB selected yet)
$mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS);

if ($mysqli->connect_error) {
    die('Connection failed: ' . $mysqli->connect_error);
}

// Create the database if it does not exist
$dbName  = DB_NAME;
$charset = DB_CHARSET;
$collate = DB_COLLATE;

if (!$mysqli->query("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET {$charset} COLLATE {$collate}")) {
    die('Database creation failed: ' . $mysqli->error);
}

echo "Database '{$dbName}' ensured.<br>";

// Select the newly created (or existing) database
$mysqli->select_db($dbName);

// ---------------------------------------------------------
// Build SQL schema (multiple statements allowed with multi_query)
// ---------------------------------------------------------

$sql = <<<SQL
-- branches
CREATE TABLE IF NOT EXISTS branches (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    status ENUM('active','inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- currencies
CREATE TABLE IF NOT EXISTS currencies (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) NOT NULL UNIQUE,
    symbol VARCHAR(10),
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- roles
CREATE TABLE IF NOT EXISTS roles (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    status ENUM('active','inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- permissions
CREATE TABLE IF NOT EXISTS permissions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(150) NOT NULL UNIQUE,
    description TEXT,
    module VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- role_permissions
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT UNSIGNED NOT NULL,
    permission_id INT UNSIGNED NOT NULL,
    PRIMARY KEY(role_id, permission_id),
    CONSTRAINT fk_role_permissions_role FOREIGN KEY(role_id) REFERENCES roles(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_role_permissions_permission FOREIGN KEY(permission_id) REFERENCES permissions(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- users
CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    email VARCHAR(150),
    phone VARCHAR(50),
    role_id INT UNSIGNED,
    branch_id INT UNSIGNED,
    status ENUM('active','inactive','blocked') DEFAULT 'active',
    last_login DATETIME,
    failed_attempts INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_users_role FOREIGN KEY(role_id) REFERENCES roles(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_users_branch FOREIGN KEY(branch_id) REFERENCES branches(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- system_settings
CREATE TABLE IF NOT EXISTS system_settings (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(150) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};
SQL;

// Execute the SQL statements
if ($mysqli->multi_query($sql)) {
    do {
        // Flush results to prepare for the next query (if any)
        if ($result = $mysqli->store_result()) {
            $result->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());

    echo "Initial tables created successfully.<br>";
} else {
    die('Error executing SQL statements: ' . $mysqli->error);
}

// ------- Phase 2 additions -------

// Prepare admin password hash
$adminPassHash = $mysqli->real_escape_string(password_hash('admin123', PASSWORD_DEFAULT));

$phase2Sql = <<<SQL2
-- audit_logs
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NULL,
    action VARCHAR(50) NOT NULL,
    module VARCHAR(50) NULL,
    record_id INT UNSIGNED NULL,
    old_value JSON NULL,
    new_value JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_audit_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- Default branch
INSERT IGNORE INTO branches (id, name, code, status) VALUES (1, 'Main Branch', 'MAIN', 'active');

-- Default role
INSERT IGNORE INTO roles (id, name, description, status) VALUES (1, 'System Admin', 'Full system administrator', 'active');

-- Default permissions
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (1, 'dashboard.view', 'View dashboard', NULL),
    (2, 'users.view', 'View users', NULL),
    (3, 'roles.view', 'View roles', NULL),
    (4, 'permissions.view', 'View permissions', NULL);

-- Role permissions mapping
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,1), (1,2), (1,3), (1,4);

-- Default admin user
INSERT IGNORE INTO users (id, username, password, full_name, email, phone, role_id, branch_id, status, created_at) VALUES
    (1, 'admin', '{$adminPassHash}', 'System Administrator', '<EMAIL>', '0000000000', 1, 1, 'active', NOW());
SQL2;

if ($mysqli->multi_query($phase2Sql)) {
    do {
        if ($res = $mysqli->store_result()) {
            $res->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());
    echo "Phase 2 tables & seed data created.<br>";
} else {
    die('Error executing Phase 2 SQL: ' . $mysqli->error);
}

// ------- Phase 3 seed data (sample records) -------

// Prepare default password hash for sample users
$defaultPassHash = $mysqli->real_escape_string(password_hash('password123', PASSWORD_DEFAULT));

$phase3Sql = <<<SQL3
-- Additional sample branch
INSERT IGNORE INTO branches (id, name, code, status) VALUES 
    (2, 'Branch B', 'B001', 'active');

-- Sample roles
INSERT IGNORE INTO roles (id, name, description, status) VALUES
    (2, 'Branch Manager', 'Manages branch operations', 'active'),
    (3, 'Cashier', 'Handles cash transactions', 'active'),
    (4, 'Operations Staff', 'Supports operations', 'active');

-- Additional permissions
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (5, 'customers.view', 'View customers', 'customers'),
    (6, 'exchange.create', 'Create exchange transactions', 'exchange'),
    (7, 'transfers.view', 'View transfers', 'transfers'),
    (8, 'cash.view', 'View cash balances', 'cash'),
    (9, 'reports.view', 'View reports', 'reports');

-- Role permissions (System Admin gets all new ones too)
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,5),(1,6),(1,7),(1,8),(1,9),
    -- Branch Manager
    (2,1),(2,5),(2,7),(2,8),(2,9),
    -- Cashier
    (3,1),(3,6),(3,8),
    -- Operations Staff
    (4,1),(4,5),(4,7);

-- Sample users
INSERT IGNORE INTO users (id, username, password, full_name, email, phone, role_id, branch_id, status, created_at) VALUES
    (2, 'manager', '{$defaultPassHash}', 'Branch Manager User', '<EMAIL>', '1111111111', 2, 1, 'active', NOW()),
    (3, 'cashier', '{$defaultPassHash}', 'Cashier User', '<EMAIL>', '2222222222', 3, 1, 'active', NOW()),
    (4, 'ops', '{$defaultPassHash}', 'Operations Staff User', '<EMAIL>', '3333333333', 4, 2, 'active', NOW());
SQL3;

if ($mysqli->multi_query($phase3Sql)) {
    do {
        if ($res = $mysqli->store_result()) {
            $res->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());
    echo "Phase 3 seed data created.<br>";
} else {
    die('Error executing Phase 3 SQL: ' . $mysqli->error);
}

// ------- Phase 6: Customer management tables & seed -------

$phase6Sql = <<<SQL6
-- customers table
CREATE TABLE IF NOT EXISTS customers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    id_number VARCHAR(100) UNIQUE,
    id_type ENUM('national_id','passport','residence') NOT NULL,
    nationality VARCHAR(100) NULL,
    birth_date DATE NULL,
    phone VARCHAR(50) NULL,
    email VARCHAR(150) NULL,
    address TEXT NULL,
    occupation VARCHAR(150) NULL,
    risk_level ENUM('low','medium','high') DEFAULT 'low',
    status ENUM('active','inactive') DEFAULT 'active',
    kyc_status ENUM('pending','verified','rejected') DEFAULT 'pending',
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_customer_creator FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
    INDEX idx_customer_name (full_name),
    INDEX idx_customer_idnum (id_number),
    INDEX idx_customer_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- documents table (minimal for this phase)
CREATE TABLE IF NOT EXISTS documents (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    customer_id INT UNSIGNED NOT NULL,
    type VARCHAR(100) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_doc_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- new permissions
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (10, 'customers.view', 'View customers', 'customers'),
    (11, 'customers.create', 'Create customers', 'customers'),
    (12, 'customers.edit', 'Edit customers', 'customers'),
    (13, 'customers.delete', 'Delete customers', 'customers');

-- map permissions to roles
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    -- System Admin (role 1) gets all
    (1,10),(1,11),(1,12),(1,13),
    -- Branch Manager (role 2) gets view, create, edit
    (2,10),(2,11),(2,12),
    -- Cashier (role 3) gets view, create
    (3,10),(3,11);

-- sample customers
INSERT IGNORE INTO customers (id, full_name, id_number, id_type, nationality, birth_date, phone, email, address, occupation, risk_level, status, kyc_status, created_by, created_at)
VALUES
    (1,'أحمد محمد','A1234567','national_id','Yemen','1990-05-12','*********','<EMAIL>','صنعاء - حدة','موظف','low','active','verified',1,NOW()),
    (2,'خالد علي','B998877','passport','Saudi Arabia','1985-11-02','0555123456','<EMAIL>','الرياض - العليا','تاجر','medium','active','pending',2,NOW()),
    (3,'سارة حسن',NULL,'residence','Egypt','1993-03-22','***********','<EMAIL>','القاهرة - مدينة نصر','مهندسة','low','inactive','pending',1,NOW());
SQL6;

if ($mysqli->multi_query($phase6Sql)) {
    do {
        if ($res = $mysqli->store_result()) {
            $res->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());
    echo "Phase 6 tables & seed data created.<br>";
} else {
    die('Error executing Phase 6 SQL: ' . $mysqli->error);
}

// ------- Phase 8: Cash & Bank management tables & seed -------

$phase8Sql = <<<SQL8
-- ======= Cash & Bank Core Tables =======
CREATE TABLE IF NOT EXISTS cash_boxes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    currency_id INT UNSIGNED NOT NULL,
    initial_balance DECIMAL(18,2) DEFAULT 0,
    current_balance DECIMAL(18,2) DEFAULT 0,
    branch_id INT UNSIGNED NOT NULL,
    responsible_user_id INT UNSIGNED NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_cash_currency FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_cash_branch   FOREIGN KEY (branch_id)   REFERENCES branches(id)   ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_cash_user     FOREIGN KEY (responsible_user_id) REFERENCES users(id) ON DELETE SET NULL   ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

CREATE TABLE IF NOT EXISTS bank_accounts (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    account_name VARCHAR(255) NOT NULL,
    bank_name VARCHAR(255) NOT NULL,
    account_number VARCHAR(100) NOT NULL,
    iban VARCHAR(34) UNIQUE NULL,
    swift_code VARCHAR(15) NULL,
    currency_id INT UNSIGNED NOT NULL,
    initial_balance DECIMAL(18,2) DEFAULT 0,
    current_balance DECIMAL(18,2) DEFAULT 0,
    branch_id INT UNSIGNED NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_bank_currency FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_bank_branch   FOREIGN KEY (branch_id)   REFERENCES branches(id)   ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

CREATE TABLE IF NOT EXISTS cash_movements (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    cash_box_id INT UNSIGNED NOT NULL,
    movement_type ENUM('deposit','withdrawal') NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    currency_id INT UNSIGNED NULL,
    description TEXT NULL,
    reference_number VARCHAR(100) NULL,
    user_id INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_cmov_cash FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_cmov_curr FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_cmov_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

CREATE TABLE IF NOT EXISTS bank_movements (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    bank_account_id INT UNSIGNED NOT NULL,
    movement_type ENUM('deposit','withdrawal') NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    currency_id INT UNSIGNED NULL,
    description TEXT NULL,
    reference_number VARCHAR(100) NULL,
    user_id INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_bmov_bank FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_bmov_curr FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_bmov_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- ======= Permissions =======
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (14, 'cash.edit', 'Edit cash boxes & bank accounts', 'cash'),
    (15, 'cash.movements', 'Record cash/bank deposits & withdrawals', 'cash');

INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,14),(1,15),  -- System Admin
    (2,14),(2,15),  -- Branch Manager
    (3,15);         -- Cashier

-- ======= Sample currencies (if not present) =======
INSERT IGNORE INTO currencies (id, name, code, symbol, is_active) VALUES
    (1,'US Dollar','USD','$',1),
    (2,'Yemeni','YER','﷼',1);

-- ======= Sample cash boxes =======
INSERT IGNORE INTO cash_boxes (id, name, currency_id, initial_balance, current_balance, branch_id, responsible_user_id, is_active, created_at)
VALUES
    (1,'Main Cash Box',1,1000.00,1000.00,1,1,1,NOW()),
    (2,'Branch B Cash',1,500.00,500.00,2,2,1,NOW());

-- ======= Sample bank accounts =======
INSERT IGNORE INTO bank_accounts (id, account_name, bank_name, account_number, iban, swift_code, currency_id, initial_balance, current_balance, branch_id, is_active, created_at)
VALUES
    (1,'Main USD Account','Central Bank','*********','US00BANK000001','CBUSUS33',1,50000.00,50000.00,1,1,NOW());
SQL8;

if ($mysqli->multi_query($phase8Sql)) {
    do {
        if ($res = $mysqli->store_result()) {
            $res->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());
    echo "Phase 8 tables & seed data created.<br>";
} else {
    die('Error executing Phase 8 SQL: ' . $mysqli->error);
}

// ------- Phase 9: Exchange module tables & seed -------

$phase9Sql = <<<SQL9
-- ======= Exchange Rates =======
CREATE TABLE IF NOT EXISTS exchange_rates (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    from_currency_id INT UNSIGNED NOT NULL,
    to_currency_id   INT UNSIGNED NOT NULL,
    buy_rate  DECIMAL(18,6) NOT NULL,
    sell_rate DECIMAL(18,6) NOT NULL,
    updated_by INT UNSIGNED NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_pair (from_currency_id, to_currency_id),
    CONSTRAINT fk_exrate_from FOREIGN KEY (from_currency_id) REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_exrate_to   FOREIGN KEY (to_currency_id)   REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_exrate_user FOREIGN KEY (updated_by)       REFERENCES users(id)      ON DELETE SET NULL   ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- ======= Exchange Transactions =======
CREATE TABLE IF NOT EXISTS exchanges (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    customer_id INT UNSIGNED NOT NULL,
    from_currency_id INT UNSIGNED NOT NULL,
    to_currency_id   INT UNSIGNED NOT NULL,
    amount_from DECIMAL(18,2) NOT NULL,
    amount_to   DECIMAL(18,2) NOT NULL,
    rate_used   DECIMAL(18,6) NOT NULL,
    commission  DECIMAL(18,2) DEFAULT 0.00,
    profit      DECIMAL(18,2) DEFAULT 0.00,
    status ENUM('completed','cancelled','pending') DEFAULT 'completed',
    notes TEXT NULL,
    branch_id INT UNSIGNED NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_exch_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_exch_from_curr FOREIGN KEY (from_currency_id) REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_exch_to_curr   FOREIGN KEY (to_currency_id)   REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_exch_branch    FOREIGN KEY (branch_id)        REFERENCES branches(id)   ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_exch_user      FOREIGN KEY (created_by)       REFERENCES users(id)      ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- ======= Permissions =======
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (16, 'exchange.view',   'View exchange module', 'exchange'),
    (17, 'exchange.create', 'Create exchange transactions', 'exchange'),
    (18, 'exchange.rates',  'Manage exchange rates', 'exchange');

INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,16),(1,17),(1,18), -- Admin
    (2,16),(2,17),(2,18), -- Branch Manager
    (3,16),(3,17);        -- Cashier (view & create)

-- ======= Sample exchange rate =======
INSERT IGNORE INTO exchange_rates (id, from_currency_id, to_currency_id, buy_rate, sell_rate, updated_by, updated_at) VALUES
    (1, 1, 2, 250.0000, 255.0000, 1, NOW());
SQL9;

if ($mysqli->multi_query($phase9Sql)) {
    do {
        if ($res = $mysqli->store_result()) {
            $res->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());
    echo "Phase 9 tables & seed data created.<br>";
} else {
    die('Error executing Phase 9 SQL: ' . $mysqli->error);
}

// ------- Phase 11: Transfer module tables & seed -------
$phase11Sql = <<<SQL11


-- ======= Transfers =======
CREATE TABLE IF NOT EXISTS transfers (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    transaction_number VARCHAR(50) NOT NULL UNIQUE,
    transfer_type ENUM('صادرة','واردة') NOT NULL,
    sender_name VARCHAR(255) NOT NULL,
    sender_id_type VARCHAR(50),
    sender_id_number VARCHAR(100),
    sender_phone VARCHAR(50),
    sender_address VARCHAR(255),
    beneficiary_id INT UNSIGNED,
    beneficiary_name VARCHAR(255),
    beneficiary_country VARCHAR(100),
    beneficiary_bank VARCHAR(150),
    beneficiary_account VARCHAR(150),
    sending_currency_id INT UNSIGNED NOT NULL,
    sending_amount DECIMAL(18,2) NOT NULL,
    receiving_currency_id INT UNSIGNED NOT NULL,
    receiving_amount DECIMAL(18,2) NOT NULL,
    exchange_rate_used DECIMAL(18,6) DEFAULT 1.0,
    transfer_fee DECIMAL(18,2) DEFAULT 0,
    profit DECIMAL(18,2) DEFAULT 0,
    delivery_method ENUM('بنكي','نقدي','شركة تحويل') DEFAULT 'نقدي',
    status ENUM('معلقة','مكتملة','ملغاة','مرسلة','مستلمة') DEFAULT 'معلقة',
    tracking_number VARCHAR(100) UNIQUE NULL,
    notes TEXT NULL,
    branch_id INT UNSIGNED NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME NULL,
    CONSTRAINT fk_trans_beneficiary FOREIGN KEY (beneficiary_id) REFERENCES customers(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_trans_send_curr FOREIGN KEY (sending_currency_id) REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_trans_recv_curr FOREIGN KEY (receiving_currency_id) REFERENCES currencies(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_trans_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_trans_user FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
    INDEX idx_transfers_search (transaction_number, sender_name, beneficiary_name)
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- ======= Transfer Status History =======
CREATE TABLE IF NOT EXISTS transfer_status_history (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    transfer_id INT UNSIGNED NOT NULL,
    old_status ENUM('معلقة','مكتملة','ملغاة','مرسلة','مستلمة') NULL,
    new_status ENUM('معلقة','مكتملة','ملغاة','مرسلة','مستلمة') NOT NULL,
    status_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by_user_id INT UNSIGNED NULL,
    notes TEXT NULL,
    location VARCHAR(150) NULL,
    CONSTRAINT fk_tsh_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_tsh_user FOREIGN KEY (changed_by_user_id) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- ======= Permissions =======
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (19, 'transfers.view', 'View transfers list', 'transfers'),
    (20, 'transfers.create', 'Create transfers', 'transfers');

INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,19),(1,20),
    (2,19),(2,20),
    (3,19),(3,20);


SQL11;

if ($mysqli->multi_query($phase11Sql)) {
    do {
        if ($res = $mysqli->store_result()) {
            $res->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());
    echo "Phase 11 tables & seed data created.<br>";
} else {
    die('Error executing Phase 11 SQL: ' . $mysqli->error);
}

// ------- Phase 12: Reporting & Audit enhancements -------
$phase12Sql = <<<SQL12
-- ======= Saved Reports Table =======
CREATE TABLE IF NOT EXISTS saved_reports (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    report_name VARCHAR(255) NOT NULL,
    report_type VARCHAR(100) NOT NULL,
    report_parameters JSON NULL,
    generated_by INT UNSIGNED NULL,
    file_path VARCHAR(255) NULL,
    file_type VARCHAR(20) NULL,
    status ENUM('pending','completed','failed') DEFAULT 'pending',
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NULL,
    CONSTRAINT fk_report_user FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET={$charset} COLLATE={$collate};

-- ======= Audit logs JSON columns (ensure correct) =======
ALTER TABLE audit_logs MODIFY old_value JSON NULL, MODIFY new_value JSON NULL;

-- ======= Add exchange_id columns to movements & accounting =======
ALTER TABLE cash_movements ADD COLUMN IF NOT EXISTS exchange_id INT UNSIGNED NULL AFTER user_id;
ALTER TABLE bank_movements ADD COLUMN IF NOT EXISTS exchange_id INT UNSIGNED NULL AFTER user_id;
ALTER TABLE accounting_entries ADD COLUMN IF NOT EXISTS exchange_id INT UNSIGNED NULL AFTER created_by;

-- ======= Add transfer_id columns to movements & accounting =======
ALTER TABLE cash_movements ADD COLUMN IF NOT EXISTS transfer_id INT UNSIGNED NULL AFTER exchange_id;
ALTER TABLE bank_movements ADD COLUMN IF NOT EXISTS transfer_id INT UNSIGNED NULL AFTER exchange_id;
ALTER TABLE accounting_entries ADD COLUMN IF NOT EXISTS transfer_id INT UNSIGNED NULL AFTER exchange_id;

-- ======= Add foreign key constraints for exchange_id =======
ALTER TABLE cash_movements ADD CONSTRAINT IF NOT EXISTS fk_cmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE bank_movements ADD CONSTRAINT IF NOT EXISTS fk_bmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE accounting_entries ADD CONSTRAINT IF NOT EXISTS fk_acc_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- ======= Add foreign key constraints for transfer_id =======
ALTER TABLE cash_movements ADD CONSTRAINT IF NOT EXISTS fk_cmov_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE bank_movements ADD CONSTRAINT IF NOT EXISTS fk_bmov_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE accounting_entries ADD CONSTRAINT IF NOT EXISTS fk_acc_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- ======= New Permissions =======
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (25, 'audit.view', 'View audit logs', 'audit'),
    (26, 'reports.view', 'Access reports module', 'reports'),
    (27, 'reports.export', 'Export reports', 'reports'),
    (28, 'reports.financial.view', 'View financial reports', 'reports'),
    (29, 'reports.compliance.view', 'View compliance reports', 'reports');

INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,25),(1,26),(1,27),(1,28),(1,29), -- Admin all
    (2,26),(2,27), -- Branch manager view/export
    (5,26),(5,28),(5,29); -- Accountant role id 5 example (ensure exists)
SQL12;

if ($mysqli->multi_query($phase12Sql)) {
    do {
        if ($res = $mysqli->store_result()) {
            $res->free();
        }
    } while ($mysqli->more_results() && $mysqli->next_result());
    echo "Phase 12 tables & seed data created.<br>";
} else {
    // Continue if columns already exist – ignore duplicate errors
    // In production, handle specific error numbers
    echo "Phase 12: some SQL failed or columns already present.<br>";
}

$mysqli->close();

echo "Setup completed successfully."; 
<?php
/**
 * إضافة جميع الصلاحيات الشاملة لجميع صفحات النظام
 * Add All Comprehensive Permissions for All System Pages
 */

// منع الوصول المباشر
if (!defined('INSTALL_MODE')) {
    define('INSTALL_MODE', true);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// إعداد الترميز
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إضافة جميع الصلاحيات - نظام TrustPlus</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .module { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .permission-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin: 10px 0; }
        .permission-item { background: white; padding: 8px; border-radius: 4px; border: 1px solid #dee2e6; }
        h1, h2, h3 { color: #333; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #007bff; color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔐 إضافة جميع الصلاحيات الشاملة للنظام</h1>";
echo "<p>هذا الملف سيقوم بإضافة جميع الصلاحيات المطلوبة لجميع صفحات النظام وربطها بالأدوار المناسبة.</p>";

try {
    // الاتصال بقاعدة البيانات
    $db = new Database();
    $conn = Database::getConnection();
    
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

    // قراءة ملف الصلاحيات
    $sqlFile = __DIR__ . '/add_all_permissions.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception('ملف الصلاحيات غير موجود: ' . $sqlFile);
    }
    
    $sqlContent = file_get_contents($sqlFile);
    if ($sqlContent === false) {
        throw new Exception('فشل في قراءة ملف الصلاحيات');
    }
    
    echo "<div class='info'>📄 تم قراءة ملف الصلاحيات بنجاح</div>";

    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sqlContent)));
    
    $addedPermissions = 0;
    $addedRolePermissions = 0;
    $errors = [];
    
    echo "<h2>🔄 تنفيذ الاستعلامات...</h2>";
    
    foreach ($queries as $query) {
        if (!empty($query) && !preg_match('/^--/', $query) && !preg_match('/^SELECT/', $query)) {
            try {
                if ($conn->query($query)) {
                    if (stripos($query, 'INSERT') !== false) {
                        if (stripos($query, 'permissions (name') !== false) {
                            $addedPermissions += $conn->affected_rows;
                        } elseif (stripos($query, 'role_permissions') !== false) {
                            $addedRolePermissions += $conn->affected_rows;
                        }
                    }
                } else {
                    $errors[] = 'خطأ في الاستعلام: ' . $conn->error;
                }
            } catch (Exception $e) {
                $errors[] = 'استثناء: ' . $e->getMessage();
            }
        }
    }
    
    // عرض النتائج
    echo "<h2>📊 نتائج التحديث</h2>";
    
    echo "<div class='stats'>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>$addedPermissions</div>";
    echo "<div class='stat-label'>صلاحية جديدة</div>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>$addedRolePermissions</div>";
    echo "<div class='stat-label'>ربط دور-صلاحية</div>";
    echo "</div>";
    
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>" . count($errors) . "</div>";
    echo "<div class='stat-label'>أخطاء</div>";
    echo "</div>";
    echo "</div>";
    
    if (!empty($errors)) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ تحذيرات وأخطاء:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // إحصائيات مفصلة
    echo "<h2>📈 إحصائيات مفصلة</h2>";
    
    // إحصائيات الصلاحيات حسب الوحدة
    $moduleStats = $conn->query("SELECT module, COUNT(*) as count FROM permissions GROUP BY module ORDER BY count DESC");
    if ($moduleStats) {
        echo "<div class='module'>";
        echo "<h3>الصلاحيات حسب الوحدة:</h3>";
        echo "<div class='permission-list'>";
        while ($row = $moduleStats->fetch_assoc()) {
            echo "<div class='permission-item'>";
            echo "<strong>" . htmlspecialchars($row['module']) . "</strong><br>";
            echo "<span style='color: #666;'>" . $row['count'] . " صلاحية</span>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
    }
    
    // إحصائيات الأدوار
    $roleStats = $conn->query("
        SELECT r.name, r.description, COUNT(rp.permission_id) as permission_count 
        FROM roles r 
        LEFT JOIN role_permissions rp ON r.id = rp.role_id 
        GROUP BY r.id, r.name, r.description 
        ORDER BY permission_count DESC
    ");
    
    if ($roleStats) {
        echo "<div class='module'>";
        echo "<h3>الصلاحيات حسب الدور:</h3>";
        echo "<div class='permission-list'>";
        while ($row = $roleStats->fetch_assoc()) {
            echo "<div class='permission-item'>";
            echo "<strong>" . htmlspecialchars($row['name']) . "</strong><br>";
            echo "<span style='color: #666;'>" . htmlspecialchars($row['description']) . "</span><br>";
            echo "<span style='color: #007bff;'>" . $row['permission_count'] . " صلاحية</span>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
    }
    
    // عرض بعض الصلاحيات الجديدة
    $newPermissions = $conn->query("
        SELECT name, description, module 
        FROM permissions 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY module, name 
        LIMIT 20
    ");
    
    if ($newPermissions && $newPermissions->num_rows > 0) {
        echo "<div class='module'>";
        echo "<h3>أحدث الصلاحيات المضافة:</h3>";
        echo "<div class='permission-list'>";
        while ($row = $newPermissions->fetch_assoc()) {
            echo "<div class='permission-item'>";
            echo "<strong>" . htmlspecialchars($row['name']) . "</strong><br>";
            echo "<span style='color: #666;'>" . htmlspecialchars($row['description']) . "</span><br>";
            echo "<span style='color: #28a745; font-size: 0.8em;'>" . htmlspecialchars($row['module']) . "</span>";
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
    }
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إنجاز التحديث بنجاح!</h3>";
    echo "<p>تم إضافة جميع الصلاحيات المطلوبة وربطها بالأدوار المناسبة.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في التحديث</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>انتقل إلى صفحة الصلاحيات: <a href='../dashboard/permissions.php' target='_blank'>dashboard/permissions.php</a></li>";
echo "<li>تحقق من ظهور جميع الصلاحيات الجديدة</li>";
echo "<li>انتقل إلى صفحة الأدوار: <a href='../dashboard/roles.php' target='_blank'>dashboard/roles.php</a></li>";
echo "<li>تحقق من ربط الصلاحيات بالأدوار</li>";
echo "<li>اختبر الوصول للصفحات المختلفة بأدوار مختلفة</li>";
echo "</ol>";
echo "</div>";

echo "</div></body></html>";
?>

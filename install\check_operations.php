<?php
/**
 * Check Operations
 * This script checks the is_credit values in the office_operations table
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>فحص قيم is_credit في جدول عمليات المكاتب</h2>";

try {
    $db = Database::getConnection();
    
    $sql = "SELECT id, operation_name, base_amount, amount, is_credit FROM office_operations";
    $result = $db->query($sql);
    
    if ($result) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>العملية</th><th>المبلغ الأساسي</th><th>المبلغ النهائي</th><th>is_credit</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['operation_name']) . "</td>";
            echo "<td>" . number_format($row['base_amount'], 2) . "</td>";
            echo "<td>" . number_format($row['amount'], 2) . "</td>";
            echo "<td>" . ($row['is_credit'] == 1 ? 'لنا (1)' : 'لكم (0)') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        $result->free();
    } else {
        throw new Exception("فشل في استعلام قاعدة البيانات: " . $db->error);
    }
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في فحص قيم is_credit</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
}
?> 
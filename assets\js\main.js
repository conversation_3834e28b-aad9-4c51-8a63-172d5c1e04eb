/*
 * Trust Plus Financial System - Main JavaScript
 * Basic global JavaScript utilities and UI interactions not covered by Bootstrap or specific modules
 */

// ===== GLOBAL TRUSTPLUS OBJECT =====
window.TrustPlus = {
  utils: {},
  ui: {}
};

// ===== UTILITY FUNCTIONS =====
TrustPlus.utils.formatCurrency = function(amount, currencySymbol = 'ر.س') {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00 ' + currencySymbol;
  }

  const formattedAmount = parseFloat(amount).toLocaleString('ar-SA', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  return formattedAmount + ' ' + currencySymbol;
};

TrustPlus.utils.formatDate = function(dateString, format = 'short') {
  if (!dateString) return '';

  const date = new Date(dateString);
  const options = {
    year: 'numeric',
    month: format === 'long' ? 'long' : '2-digit',
    day: '2-digit'
  };

  if (format === 'full') {
    options.hour = '2-digit';
    options.minute = '2-digit';
  }

  return date.toLocaleDateString('ar-SA', options);
};

TrustPlus.utils.formatNumber = function(number, decimals = 0) {
  if (number === null || number === undefined || isNaN(number)) {
    return '0';
  }

  return parseFloat(number).toLocaleString('ar-SA', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

TrustPlus.utils.debounce = function(func, wait, immediate) {
  let timeout;
  return function executedFunction() {
    const context = this;
    const args = arguments;
    const later = function() {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
};

// ===== UI FUNCTIONS =====
TrustPlus.ui.showLoading = function(selector) {
  const elements = document.querySelectorAll(selector);
  elements.forEach(element => {
    element.classList.add('loading');
    element.disabled = true;

    // Add loading spinner if it's a button
    if (element.tagName === 'BUTTON') {
      const originalText = element.innerHTML;
      element.setAttribute('data-original-text', originalText);
      element.innerHTML = '<span class="loading-spinner"></span> جاري التحميل...';
    }
  });
};

TrustPlus.ui.hideLoading = function(selector) {
  const elements = document.querySelectorAll(selector);
  elements.forEach(element => {
    element.classList.remove('loading');
    element.disabled = false;

    // Restore original text if it's a button
    if (element.tagName === 'BUTTON' && element.hasAttribute('data-original-text')) {
      element.innerHTML = element.getAttribute('data-original-text');
      element.removeAttribute('data-original-text');
    }
  });
};

TrustPlus.ui.showToast = function(message, type = 'info', duration = 5000) {
  // Create toast element
  const toast = document.createElement('div');
  toast.className = `alert alert-${type} toast-notification`;
  toast.innerHTML = `
    <div class="d-flex align-items-center">
      <i class="fas fa-${getToastIcon(type)} me-2"></i>
      <span>${message}</span>
      <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
    </div>
  `;

  // Add to toast container or create one
  let container = document.getElementById('toast-container');
  if (!container) {
    container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
  }

  container.appendChild(toast);

  // Auto remove after duration
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, duration);

  function getToastIcon(type) {
    switch (type) {
      case 'success': return 'check-circle';
      case 'danger': return 'exclamation-circle';
      case 'warning': return 'exclamation-triangle';
      default: return 'info-circle';
    }
  }
};

// ===== SIDEBAR TOGGLE FUNCTIONALITY =====
TrustPlus.ui.initSidebarToggle = function() {
  const toggleBtn = document.getElementById('mobileSidebarToggle');
  const sidebar = document.getElementById('sidebar');
  const overlay = document.createElement('div');
  overlay.className = 'sidebar-overlay';
  document.body.appendChild(overlay);

  if (toggleBtn && sidebar) {
    toggleBtn.addEventListener('click', function() {
      sidebar.classList.toggle('show');
      overlay.classList.toggle('show');
      document.body.classList.toggle('sidebar-open');
    });

    // Close sidebar when clicking overlay
    overlay.addEventListener('click', function() {
      sidebar.classList.remove('show');
      overlay.classList.remove('show');
      document.body.classList.remove('sidebar-open');
    });

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && sidebar.classList.contains('show')) {
        sidebar.classList.remove('show');
        overlay.classList.remove('show');
        document.body.classList.remove('sidebar-open');
      }
    });
  }
};

// ===== SESSION MESSAGE HANDLING =====
TrustPlus.ui.handleSessionMessages = function() {
  // Look for session messages in hidden elements
  const successMsg = document.getElementById('session-success-message');
  const errorMsg = document.getElementById('session-error-message');
  const warningMsg = document.getElementById('session-warning-message');
  const infoMsg = document.getElementById('session-info-message');

  if (successMsg && successMsg.textContent.trim()) {
    TrustPlus.ui.showToast(successMsg.textContent.trim(), 'success');
  }

  if (errorMsg && errorMsg.textContent.trim()) {
    TrustPlus.ui.showToast(errorMsg.textContent.trim(), 'danger');
  }

  if (warningMsg && warningMsg.textContent.trim()) {
    TrustPlus.ui.showToast(warningMsg.textContent.trim(), 'warning');
  }

  if (infoMsg && infoMsg.textContent.trim()) {
    TrustPlus.ui.showToast(infoMsg.textContent.trim(), 'info');
  }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
  // Initialize sidebar toggle
  TrustPlus.ui.initSidebarToggle();

  // Handle session messages
  TrustPlus.ui.handleSessionMessages();

  // Initialize tooltips if Bootstrap is available
  if (typeof bootstrap !== 'undefined') {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }
});
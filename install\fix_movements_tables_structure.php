<?php
/**
 * Fix movements tables structure - remove currency_id columns
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إصلاح هيكل جداول الحركات</h2>\n";

try {
    $conn = Database::getConnection();
    
    $tables = ['cash_movements', 'bank_movements'];
    
    foreach ($tables as $table) {
        echo "<h3>فحص جدول $table</h3>\n";
        
        // Check if table exists
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows == 0) {
            echo "<p style='color: orange;'>⚠ جدول $table غير موجود</p>\n";
            continue;
        }
        
        // Check current structure
        $result = $conn->query("DESCRIBE $table");
        if ($result) {
            echo "<p><strong>الهيكل الحالي:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th>\n";
            echo "</tr>\n";
            
            $columns = [];
            while ($row = $result->fetch_assoc()) {
                $columns[] = $row['Field'];
                $highlight = (strpos($row['Field'], 'currency') !== false) ? 'background: #fff3cd;' : '';
                echo "<tr style='$highlight'>\n";
                echo "<td>{$row['Field']}</td>\n";
                echo "<td>{$row['Type']}</td>\n";
                echo "<td>{$row['Null']}</td>\n";
                echo "<td>{$row['Key']}</td>\n";
                echo "<td>{$row['Default']}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
            
            // Check if currency_id exists
            if (in_array('currency_id', $columns)) {
                echo "<p style='color: orange;'>⚠ العمود currency_id موجود في جدول $table</p>\n";
                
                // Remove currency_id column
                $dropSql = "ALTER TABLE $table DROP COLUMN currency_id";
                if ($conn->query($dropSql)) {
                    echo "<p style='color: green;'>✓ تم إزالة عمود currency_id من جدول $table</p>\n";
                } else {
                    echo "<p style='color: red;'>✗ فشل في إزالة عمود currency_id من جدول $table: " . $conn->error . "</p>\n";
                }
            } else {
                echo "<p style='color: green;'>✓ عمود currency_id غير موجود في جدول $table</p>\n";
            }
        }
        
        echo "<hr>\n";
    }
    
    echo "<h3>التحقق النهائي</h3>\n";
    
    // Final verification
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $result = $conn->query("DESCRIBE $table");
            if ($result) {
                echo "<p><strong>الهيكل النهائي لجدول $table:</strong></p>\n";
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 15px;'>\n";
                echo "<tr style='background: #f8f9fa;'>\n";
                echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th>\n";
                echo "</tr>\n";
                
                while ($row = $result->fetch_assoc()) {
                    $highlight = (strpos($row['Field'], 'currency') !== false) ? 'background: #fff3cd;' : '';
                    echo "<tr style='$highlight'>\n";
                    echo "<td>{$row['Field']}</td>\n";
                    echo "<td>{$row['Type']}</td>\n";
                    echo "<td>{$row['Null']}</td>\n";
                    echo "<td>{$row['Key']}</td>\n";
                    echo "</tr>\n";
                }
                echo "</table>\n";
            }
        }
    }
    
    echo "<h3>اختبار إنشاء صندوق</h3>\n";
    
    // Test creating a cash box
    require_once __DIR__ . '/../includes/cash_manager.php';
    
    $db = new Database();
    $cashManager = new CashManager($db);
    
    $testData = [
        'name' => 'صندوق اختبار إصلاح الهيكل',
        'currency_code' => 'USD',
        'initial_balance' => 1000.00,
        'branch_id' => 1,
        'responsible_user_id' => 1,
        'is_active' => 1
    ];
    
    $result = $cashManager->addCashBox($testData);
    if ($result) {
        echo "<p style='color: green;'>✓ تم إنشاء صندوق اختبار بنجاح (ID: $result)</p>\n";
        
        // Test adding a movement
        $movementResult = $cashManager->addCashMovement(
            $result,
            'deposit',
            500.00,
            'إيداع اختبار بعد إصلاح الهيكل',
            'TEST_STRUCTURE_FIX_' . time(),
            1
        );
        
        if ($movementResult['success']) {
            echo "<p style='color: green;'>✓ تم إضافة حركة اختبار بنجاح (ID: {$movementResult['movement_id']})</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في إضافة حركة اختبار: " . ($movementResult['error'] ?? 'خطأ غير معروف') . "</p>\n";
        }
        
        // Clean up test data
        $conn->query("DELETE FROM cash_movements WHERE cash_box_id = $result");
        $conn->query("DELETE FROM cash_boxes WHERE id = $result");
        echo "<p style='color: green;'>✓ تم حذف البيانات التجريبية</p>\n";
        
    } else {
        echo "<p style='color: red;'>✗ فشل في إنشاء صندوق اختبار</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح هيكل جداول الحركات!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='final_currencies_test.php' target='_blank'>اختبار نظام العملات الشامل</a></li>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>اختبار صفحة الصناديق</a></li>\n";
    echo "<li><a href='view_all_currencies.php' target='_blank'>عرض جميع العملات</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

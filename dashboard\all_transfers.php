<?php
/**
 * All Transfers Management Page
 * صفحة عرض جميع الحوالات
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/transfer_manager.php';
require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

// Check authentication
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// Check permissions
if (!$auth->hasPermission('transfers.view')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب');
    redirect('dashboard.php');
}

$db = new Database();
$transferManager = new EnhancedTransferManager($db);
$conn = Database::getConnection();

// Handle status updates
if ($_POST && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    switch ($action) {
        case 'update_status':
            if ($auth->hasPermission('transfers.manage')) {
                $transferId = (int)($_POST['transfer_id'] ?? 0);
                $newStatus = sanitize_input($_POST['status'] ?? '');
                $notes = sanitize_input($_POST['notes'] ?? '');

                if ($transferId > 0 && !empty($newStatus)) {
                    $result = $transferManager->updateStatusWithLogging(
                        $transferId,
                        $newStatus,
                        $notes,
                        $_SESSION['user_id'] ?? null,
                        ['location' => 'إدارة الحوالات']
                    );

                    if ($result['success']) {
                        // تسجيل عملية تحديث حالة الحوالة
                        ActivityHelper::logStatusChange(
                            'transfers',
                            "حوالة #$transferId",
                            $result['old_status'] ?? 'غير محدد',
                            $newStatus,
                            $transferId
                        );

                        set_flash('success', $result['message'] ?? 'تم تحديث حالة الحوالة بنجاح');
                    } else {
                        set_flash('danger', $result['error'] ?? 'فشل في تحديث حالة الحوالة');
                    }
                }
            }
            break;
            
        case 'assign_tracking':
            if ($auth->hasPermission('transfers.manage')) {
                $transferId = (int)($_POST['transfer_id'] ?? 0);

                if ($transferId > 0) {
                    $result = $transferManager->assignTrackingNumberWithLogging(
                        $transferId,
                        $_SESSION['user_id'] ?? null
                    );

                    if ($result['success']) {
                        set_flash('success', 'تم تعيين رقم التتبع: ' . $result['tracking_number']);
                    } else {
                        set_flash('danger', $result['error'] ?? 'فشل في تعيين رقم التتبع');
                    }
                }
            }
            break;
    }
    
    redirect('all_transfers.php');
}

// Get filters
$filters = [];
$search = sanitize_input($_GET['search'] ?? '');
$status = sanitize_input($_GET['status'] ?? '');
$type = sanitize_input($_GET['type'] ?? '');
$country = sanitize_input($_GET['country'] ?? '');
$dateFrom = sanitize_input($_GET['date_from'] ?? '');
$dateTo = sanitize_input($_GET['date_to'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20; // Allow changing the number of records per page
$offset = ($page - 1) * $limit;

if (!empty($search)) $filters['search'] = $search;
if (!empty($status)) $filters['status'] = $status;
if (!empty($type)) $filters['transfer_type'] = $type;
if (!empty($country)) $filters['country'] = $country;
if (!empty($dateFrom)) $filters['date_from'] = $dateFrom;
if (!empty($dateTo)) $filters['date_to'] = $dateTo;

// Get transfers with enhanced query
$sql = "SELECT t.*, 
               c.full_name AS beneficiary_full_name,
               b.name AS branch_name,
               u.full_name AS created_by_name,
               sc.code AS sending_currency_code,
               sc.symbol AS sending_currency_symbol,
               rc.code AS receiving_currency_code,
               rc.symbol AS receiving_currency_symbol,
               (t.sending_amount - t.receiving_amount) AS calculated_profit,
               (SELECT COUNT(*) FROM transfer_status_history WHERE transfer_id = t.id) as status_changes_count
        FROM transfers t
        LEFT JOIN customers c ON c.id = t.beneficiary_id
        LEFT JOIN branches b ON b.id = t.branch_id
        LEFT JOIN users u ON u.id = t.created_by
        LEFT JOIN currencies sc ON sc.id = t.sending_currency_id
        LEFT JOIN currencies rc ON rc.id = t.receiving_currency_id
        WHERE 1=1";

$params = [];
$types = '';

if (!empty($search)) {
    $sql .= " AND (t.transaction_number LIKE ? OR t.beneficiary_name LIKE ? OR t.tracking_number LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    $types .= 'sss';
}

if (!empty($status)) {
    $sql .= " AND t.status = ?";
    $params[] = $status;
    $types .= 's';
}

if (!empty($type)) {
    $sql .= " AND t.transfer_type = ?";
    $params[] = $type;
    $types .= 's';
}

if (!empty($country)) {
    $sql .= " AND t.beneficiary_country LIKE ?";
    $params[] = "%$country%";
    $types .= 's';
}

if (!empty($dateFrom)) {
    $sql .= " AND DATE(t.created_at) >= ?";
    $params[] = $dateFrom;
    $types .= 's';
}

if (!empty($dateTo)) {
    $sql .= " AND DATE(t.created_at) <= ?";
    $params[] = $dateTo;
    $types .= 's';
}

// Get total count for pagination
$countSql = "SELECT COUNT(*) as total FROM ($sql) as count_query";
$countStmt = $conn->prepare($countSql);
if (!empty($params)) {
    $countStmt->bind_param($types, ...$params);
}
$countStmt->execute();
$totalRecords = $countStmt->get_result()->fetch_assoc()['total'];
$totalPages = ceil($totalRecords / $limit);
$countStmt->close();

// Get transfers for current page
$sql .= " ORDER BY t.created_at DESC LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;
$types .= 'ii';

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$transfers = [];
while ($row = $result->fetch_assoc()) {
    $transfers[] = $row;
}
$stmt->close();

// تسجيل عملية عرض قائمة جميع الحوالات
ActivityHelper::logView('transfers', 'عرض قائمة جميع الحوالات', [
    'filters' => [
        'status' => $status,
        'country' => $country,
        'date_from' => $dateFrom,
        'date_to' => $dateTo,
        'search' => $search
    ],
    'total_records' => $totalRecords,
    'page' => $page
]);

// Calculate total profit for the filtered transfers
$totalProfitSql = "SELECT SUM(t.sending_amount - t.receiving_amount) as total_profit 
                  FROM transfers t 
                  LEFT JOIN customers c ON c.id = t.beneficiary_id
                  LEFT JOIN branches b ON b.id = t.branch_id
                  LEFT JOIN users u ON u.id = t.created_by
                  LEFT JOIN currencies sc ON sc.id = t.sending_currency_id
                  LEFT JOIN currencies rc ON rc.id = t.receiving_currency_id
                  WHERE 1=1";

// Apply the same filters to the total profit calculation
if (!empty($search)) {
    $totalProfitSql .= " AND (t.transaction_number LIKE ? OR t.beneficiary_name LIKE ? OR t.tracking_number LIKE ?)";
}

if (!empty($status)) {
    $totalProfitSql .= " AND t.status = ?";
}

if (!empty($type)) {
    $totalProfitSql .= " AND t.transfer_type = ?";
}

if (!empty($country)) {
    $totalProfitSql .= " AND t.beneficiary_country LIKE ?";
}

if (!empty($dateFrom)) {
    $totalProfitSql .= " AND DATE(t.created_at) >= ?";
}

if (!empty($dateTo)) {
    $totalProfitSql .= " AND DATE(t.created_at) <= ?";
}

$totalProfitStmt = $conn->prepare($totalProfitSql);
if (!empty($params)) {
    // We need to exclude the limit and offset parameters that were added for the main query
    if (count($params) > 2) {
        $profitParams = array_slice($params, 0, count($params) - 2);
        $profitTypes = substr($types, 0, strlen($types) - 2);
        $totalProfitStmt->bind_param($profitTypes, ...$profitParams);
    }
}
$totalProfitStmt->execute();
$totalProfitResult = $totalProfitStmt->get_result();
$totalProfit = $totalProfitResult->fetch_assoc()['total_profit'] ?? 0;
$totalProfitStmt->close();

// Get statistics
$stats = [
    'total' => $totalRecords,
    'pending' => 0,
    'completed' => 0,
    'cancelled' => 0,
    'sent' => 0,
    'received' => 0
];

$statsResult = $conn->query("SELECT status, COUNT(*) as count FROM transfers GROUP BY status");
if ($statsResult) {
    while ($row = $statsResult->fetch_assoc()) {
        switch ($row['status']) {
            case 'معلقة': $stats['pending'] = $row['count']; break;
            case 'مكتملة': $stats['completed'] = $row['count']; break;
            case 'ملغاة': $stats['cancelled'] = $row['count']; break;
            case 'مرسلة': $stats['sent'] = $row['count']; break;
            case 'مستلمة': $stats['received'] = $row['count']; break;
        }
    }
}

$pageTitle = 'جميع الحوالات';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-paper-plane"></i> جميع الحوالات</h3>
        <div>
            <a href="transfers.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة حوالة جديدة
            </a>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>

    <?php foreach (get_flash() as $type => $msg): ?>
        <div class="alert alert-<?php echo $type; ?> alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($msg); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endforeach; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['total']; ?></h4>
                    <p class="mb-0">إجمالي الحوالات</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['pending']; ?></h4>
                    <p class="mb-0">معلقة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['completed']; ?></h4>
                    <p class="mb-0">مكتملة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['sent']; ?></h4>
                    <p class="mb-0">مرسلة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['received']; ?></h4>
                    <p class="mb-0">مستلمة</p>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4><?php echo $stats['cancelled']; ?></h4>
                    <p class="mb-0">ملغاة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter"></i> البحث والتصفية
                <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </h5>
        </div>
        <div class="collapse show" id="filtersCollapse">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" name="search" class="form-control" 
                               placeholder="رقم الحوالة، المستفيد، رقم التتبع..." 
                               value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="معلقة" <?php echo $status === 'معلقة' ? 'selected' : ''; ?>>معلقة</option>
                            <option value="مكتملة" <?php echo $status === 'مكتملة' ? 'selected' : ''; ?>>مكتملة</option>
                            <option value="مرسلة" <?php echo $status === 'مرسلة' ? 'selected' : ''; ?>>مرسلة</option>
                            <option value="مستلمة" <?php echo $status === 'مستلمة' ? 'selected' : ''; ?>>مستلمة</option>
                            <option value="ملغاة" <?php echo $status === 'ملغاة' ? 'selected' : ''; ?>>ملغاة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">النوع</label>
                        <select name="type" class="form-select">
                            <option value="">جميع الأنواع</option>
                            <option value="صادرة" <?php echo $type === 'صادرة' ? 'selected' : ''; ?>>صادرة</option>
                            <option value="واردة" <?php echo $type === 'واردة' ? 'selected' : ''; ?>>واردة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الدولة</label>
                        <input type="text" name="country" class="form-control" 
                               placeholder="دولة الحوالة" 
                               value="<?php echo htmlspecialchars($country); ?>">
                    </div>
                    <div class="col-md-3">
                        <div class="row">
                            <div class="col-6">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($dateFrom); ?>">
                    </div>
                            <div class="col-6">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($dateTo); ?>">
                    </div>
                        </div>
                    </div>
                    <div class="col-md-12 text-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <a href="all_transfers.php" class="btn btn-secondary">
                            <i class="fas fa-redo me-1"></i> إعادة تعيين
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Transfers Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة الحوالات (<?php echo $totalRecords; ?> حوالة)</h5>
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <form method="GET" class="d-flex align-items-center">
                        <!-- Preserve existing filters when changing limit -->
                        <?php if (!empty($search)): ?><input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>"><?php endif; ?>
                        <?php if (!empty($status)): ?><input type="hidden" name="status" value="<?php echo htmlspecialchars($status); ?>"><?php endif; ?>
                        <?php if (!empty($type)): ?><input type="hidden" name="type" value="<?php echo htmlspecialchars($type); ?>"><?php endif; ?>
                        <?php if (!empty($country)): ?><input type="hidden" name="country" value="<?php echo htmlspecialchars($country); ?>"><?php endif; ?>
                        <?php if (!empty($dateFrom)): ?><input type="hidden" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>"><?php endif; ?>
                        <?php if (!empty($dateTo)): ?><input type="hidden" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>"><?php endif; ?>
                        
                        <label class="me-2">عرض:</label>
                        <select name="limit" class="form-select form-select-sm" onchange="this.form.submit()">
                            <option value="20" <?php echo $limit === 20 ? 'selected' : ''; ?>>20</option>
                            <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50</option>
                            <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100</option>
                            <option value="500" <?php echo $limit === 500 ? 'selected' : ''; ?>>500</option>
                            <option value="1000" <?php echo $limit === 1000 ? 'selected' : ''; ?>>1000</option>
                        </select>
                    </form>
                </div>
            <div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-1"></i> تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="export_transfers.php?format=pdf<?php echo !empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : ''; ?>">
                                    <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="export_transfers.php?format=excel<?php echo !empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : ''; ?>">
                                    <i class="fas fa-file-excel me-1"></i> تصدير Excel
                                </a>
                            </li>
                        </ul>
                    </div>
                    <small class="text-muted ms-2">
                    صفحة <?php echo $page; ?> من <?php echo $totalPages; ?>
                </small>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-bordered align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 120px;">رقم الحوالة</th>
                            <th style="width: 80px;">النوع</th>
                            <th style="width: 150px;">المستفيد</th>
                            <th style="width: 120px;">الدولة</th>
                            <th style="width: 120px;">المبلغ الأساسي</th>
                            <th style="width: 120px;">مبلغ التسليم</th>
                            <th style="width: 80px;">الربح</th>
                            <th style="width: 100px;">الحالة</th>
                            <th style="width: 120px;">رقم التتبع</th>
                            <th style="width: 100px;">الفرع</th>
                            <th style="width: 120px;">تاريخ الإنشاء</th>
                            <th style="width: 150px;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($transfers)): ?>
                            <tr>
                                <td colspan="12" class="text-center text-muted py-4">
                                    <i class="fas fa-paper-plane fa-3x mb-3"></i>
                                    <p>لا توجد حوالات مطابقة للبحث</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($transfers as $transfer): ?>
                                <tr>
                                    <td>
                                        <span class="fw-bold text-primary">
                                            <?php echo htmlspecialchars($transfer['transaction_number']); ?>
                                        </span>
                                        <?php if ($transfer['status_changes_count'] > 0): ?>
                                            <br><small class="text-muted">
                                                <i class="fas fa-history"></i> <?php echo $transfer['status_changes_count']; ?> تغيير
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $transfer['transfer_type'] === 'صادرة' ? 'bg-primary' : 'bg-info'; ?>">
                                            <?php echo htmlspecialchars($transfer['transfer_type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo htmlspecialchars($transfer['beneficiary_name']); ?></div>
                                        <?php if (!empty($transfer['beneficiary_id'])): ?>
                                            <small class="text-muted">
                                                <i class="fas fa-user"></i> عميل مسجل
                                            </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($transfer['beneficiary_country'])): ?>
                                            <span class="fw-bold">
                                                <i class="fas fa-globe"></i> <?php echo htmlspecialchars($transfer['beneficiary_country']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success">
                                            <?php echo number_format($transfer['sending_amount'], 2); ?>
                                        </span>
                                        <br><small class="text-muted">
                                            <?php echo htmlspecialchars($transfer['sending_currency_code']); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-primary">
                                            <?php echo number_format($transfer['receiving_amount'], 2); ?>
                                        </span>
                                        <br><small class="text-muted">
                                            <?php echo htmlspecialchars($transfer['receiving_currency_code']); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="text-warning fw-bold">
                                            <?php echo number_format($transfer['profit'] ?? $transfer['calculated_profit'], 2); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusColors = [
                                            'معلقة' => 'warning',
                                            'مكتملة' => 'success',
                                            'مرسلة' => 'info',
                                            'مستلمة' => 'primary',
                                            'ملغاة' => 'danger'
                                        ];
                                        $statusColor = $statusColors[$transfer['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $statusColor; ?>">
                                            <?php echo htmlspecialchars($transfer['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($transfer['tracking_number'])): ?>
                                            <span class="fw-bold text-success">
                                                <?php echo htmlspecialchars($transfer['tracking_number']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($transfer['branch_name'] ?? 'غير محدد'); ?>
                                    </td>
                                    <td>
                                        <div><?php echo date('Y-m-d', strtotime($transfer['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('H:i', strtotime($transfer['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group-vertical btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary btn-sm"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#viewTransferModal"
                                                    data-transfer-id="<?php echo $transfer['id']; ?>"
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i> عرض
                                            </button>

                                            <?php if ($auth->hasPermission('transfers.manage')): ?>
                                                <button class="btn btn-outline-warning btn-sm"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#updateStatusModal"
                                                        data-transfer-id="<?php echo $transfer['id']; ?>"
                                                        data-current-status="<?php echo htmlspecialchars($transfer['status']); ?>"
                                                        title="تحديث الحالة">
                                                    <i class="fas fa-edit"></i> حالة
                                                </button>

                                                <?php if (empty($transfer['tracking_number']) && $transfer['status'] !== 'ملغاة'): ?>
                                                    <form method="post" class="d-inline">
                                                        <input type="hidden" name="action" value="assign_tracking">
                                                        <input type="hidden" name="transfer_id" value="<?php echo $transfer['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-success btn-sm"
                                                                onclick="return confirm('هل تريد تعيين رقم تتبع لهذه الحوالة؟')"
                                                                title="تعيين رقم تتبع">
                                                            <i class="fas fa-barcode"></i> تتبع
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            <!-- Total Profit Row -->
                            <tr class="table-active">
                                <td colspan="6" class="text-end fw-bold">إجمالي الربح:</td>
                                <td class="fw-bold text-warning"><?php echo number_format($totalProfit, 2); ?></td>
                                <td colspan="5"></td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="card-footer">
                <nav aria-label="صفحات الحوالات">
                    <ul class="pagination justify-content-center mb-0">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- View Transfer Modal -->
<div class="modal fade" id="viewTransferModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الحوالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="transferDetails">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الحوالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="transfer_id" id="updateTransferId">

                    <div class="mb-3">
                        <label class="form-label">الحالة الحالية</label>
                        <input type="text" id="currentStatus" class="form-control" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الحالة الجديدة <span class="text-danger">*</span></label>
                        <select name="status" class="form-select" required>
                            <option value="">اختر الحالة الجديدة</option>
                            <option value="معلقة">معلقة</option>
                            <option value="مكتملة">مكتملة</option>
                            <option value="مرسلة">مرسلة</option>
                            <option value="مستلمة">مستلمة</option>
                            <option value="ملغاة">ملغاة</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="3"
                                  placeholder="ملاحظات حول تغيير الحالة (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle view transfer modal
    const viewModal = document.getElementById('viewTransferModal');
    if (viewModal) {
        viewModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const transferId = button.getAttribute('data-transfer-id');

            // Load transfer details via AJAX
            fetch(`transfer_details.php?id=${transferId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('transferDetails').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('transferDetails').innerHTML =
                        '<div class="alert alert-danger">فشل في تحميل تفاصيل الحوالة</div>';
                });
        });
    }

    // Handle update status modal
    const updateModal = document.getElementById('updateStatusModal');
    if (updateModal) {
        updateModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const transferId = button.getAttribute('data-transfer-id');
            const currentStatus = button.getAttribute('data-current-status');

            document.getElementById('updateTransferId').value = transferId;
            document.getElementById('currentStatus').value = currentStatus;
        });
    }
});
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

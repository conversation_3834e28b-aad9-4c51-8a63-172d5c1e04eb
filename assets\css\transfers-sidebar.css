/*
 * تصميم قائمة الحوالات في الشريط الجانبي
 * Transfers Sidebar Menu Design
 */

/* القائمة الرئيسية للحوالات */
.transfers-menu {
    margin: 8px 0;
    border-radius: 12px;
    overflow: hidden;
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
}

.transfers-menu:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* الرابط الرئيسي للحوالات */
.transfers-main-link {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.transfers-main-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.transfers-main-link:hover::before {
    left: 100%;
}

.transfers-main-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
    text-decoration: none;
}

/* أيقونة الحوالات الرئيسية */
.transfers-icon-wrapper {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.transfers-icon-wrapper i {
    font-size: 18px;
    color: white;
    transition: all 0.3s ease;
}

.transfers-main-link:hover .transfers-icon-wrapper {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1) rotate(5deg);
}

.transfers-main-link:hover .transfers-icon-wrapper i {
    transform: scale(1.1);
}

/* نص الحوالات */
.transfers-text {
    flex: 1;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* أيقونة السهم */
.transfers-chevron {
    font-size: 14px;
    transition: all 0.3s ease;
    margin-right: 8px;
}

.transfers-main-link[aria-expanded="true"] .transfers-chevron {
    transform: rotate(180deg);
}

/* القائمة الفرعية */
.transfers-submenu {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0 0 12px 12px;
    padding: 8px 0;
    margin-top: -1px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* الروابط الفرعية */
.transfers-sublink {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    margin: 2px 8px;
    border-radius: 8px;
    font-weight: 500;
}

.transfers-sublink:hover {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    text-decoration: none;
    transform: translateX(-3px);
}

.transfers-sublink.active {
    color: #28a745;
    background: rgba(40, 167, 69, 0.15);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

/* أيقونات الروابط الفرعية */
.transfers-subicon-wrapper {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    transition: all 0.3s ease;
}

.transfers-subicon-wrapper i {
    font-size: 14px;
    transition: all 0.3s ease;
}

/* ألوان مختلفة للأيقونات */
.customers-icon {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.transfers-icon {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.all-transfers-icon {
    background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
    color: white;
}

.add-transfer-icon {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
    color: white;
}



/* تأثيرات hover للأيقونات */
.transfers-sublink:hover .transfers-subicon-wrapper {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.transfers-sublink:hover .customers-icon {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.transfers-sublink:hover .transfers-icon {
    background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
}

.transfers-sublink:hover .all-transfers-icon {
    background: linear-gradient(135deg, #5a32a3 0%, #4c2a85 100%);
}

.transfers-sublink:hover .add-transfer-icon {
    background: linear-gradient(135deg, #e55a00 0%, #cc4900 100%);
}



/* مؤشر النشاط */
.transfers-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: transparent;
    transition: all 0.3s ease;
    margin-right: 8px;
}

.transfers-sublink.active .transfers-indicator {
    background: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

/* تأثيرات الرسوم المتحركة */
@keyframes transfersPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.transfers-menu:hover {
    animation: transfersPulse 2s infinite;
}

/* تأثير الانزلاق للقائمة الفرعية */
.transfers-submenu {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: top;
}

.transfers-submenu.show {
    animation: slideInTransfers 0.4s ease-out;
}

@keyframes slideInTransfers {
    from {
        opacity: 0;
        transform: translateY(-10px) scaleY(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scaleY(1);
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .transfers-main-link {
        padding: 14px 16px;
        font-size: 14px;
    }
    
    .transfers-icon-wrapper {
        width: 36px;
        height: 36px;
        margin-left: 10px;
    }
    
    .transfers-icon-wrapper i {
        font-size: 16px;
    }
    
    .transfers-sublink {
        padding: 10px 16px;
        margin: 1px 6px;
    }
    
    .transfers-subicon-wrapper {
        width: 28px;
        height: 28px;
        margin-left: 10px;
    }
    
    .transfers-subicon-wrapper i {
        font-size: 12px;
    }
}

/* تأثيرات إضافية للتفاعل */
.transfers-sublink span {
    transition: all 0.3s ease;
}

.transfers-sublink:hover span {
    transform: translateX(-2px);
}

.transfers-sublink.active span {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(40, 167, 69, 0.2);
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .transfers-submenu {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    }
    
    .transfers-sublink {
        color: #e2e8f0;
    }
    
    .transfers-sublink:hover {
        color: #68d391;
        background: rgba(104, 211, 145, 0.1);
    }
    
    .transfers-sublink.active {
        color: #68d391;
        background: rgba(104, 211, 145, 0.15);
    }
}

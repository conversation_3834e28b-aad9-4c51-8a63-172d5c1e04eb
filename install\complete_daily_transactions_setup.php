<?php
/**
 * Trust Plus - إعداد شامل لنظام المعاملات اليومية
 * Complete Daily Transactions System Setup
 */

require_once __DIR__ . '/../config/database.php';

// إعدادات العرض
header('Content-Type: text/html; charset=utf-8');
ini_set('display_errors', 1);
error_reporting(E_ALL);

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام المعاملات اليومية - Trust Plus</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-content {
            padding: 30px;
        }
        .step {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .step-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
        }
        .step-content {
            padding: 20px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .log-entry {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .log-success { background: #d4edda; border-left: 4px solid #28a745; }
        .log-error { background: #f8d7da; border-left: 4px solid #dc3545; }
        .log-warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        .log-info { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        .progress-bar-custom {
            height: 25px;
            font-size: 14px;
            line-height: 25px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-database"></i> إعداد نظام المعاملات اليومية</h1>
            <p class="mb-0">Trust Plus - Daily Transactions System Setup</p>
        </div>
        
        <div class="setup-content">
            <?php
            try {
                // الاتصال بقاعدة البيانات
                $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
                $mysqli->set_charset(DB_CHARSET);
                
                if ($mysqli->connect_error) {
                    throw new Exception('فشل الاتصال بقاعدة البيانات: ' . $mysqli->connect_error);
                }
                
                echo '<div class="alert alert-success"><i class="fas fa-check-circle"></i> تم الاتصال بقاعدة البيانات بنجاح</div>';
                
                // قائمة الملفات المطلوب تنفيذها
                $sqlFiles = [
                    'create_daily_transactions_system.sql' => 'إنشاء الجداول الأساسية والصلاحيات',
                    'additional_countries_data.sql' => 'إضافة بيانات الدول والعملات الإضافية',
                    'daily_transactions_procedures.sql' => 'إنشاء الإجراءات المخزنة والفهارس'
                ];
                
                $totalSteps = count($sqlFiles);
                $currentStep = 0;
                
                foreach ($sqlFiles as $filename => $description) {
                    $currentStep++;
                    $percentage = ($currentStep / $totalSteps) * 100;
                    
                    echo "<div class='step'>";
                    echo "<div class='step-header'>";
                    echo "<i class='fas fa-cog fa-spin'></i> الخطوة {$currentStep} من {$totalSteps}: {$description}";
                    echo "<div class='progress mt-2'>";
                    echo "<div class='progress-bar progress-bar-custom bg-primary' style='width: {$percentage}%'>{$percentage}%</div>";
                    echo "</div>";
                    echo "</div>";
                    echo "<div class='step-content'>";
                    
                    $filePath = __DIR__ . '/' . $filename;
                    
                    if (!file_exists($filePath)) {
                        echo "<div class='log-entry log-error'><i class='fas fa-exclamation-triangle'></i> ملف غير موجود: {$filename}</div>";
                        continue;
                    }
                    
                    $sql = file_get_contents($filePath);
                    if ($sql === false) {
                        echo "<div class='log-entry log-error'><i class='fas fa-exclamation-triangle'></i> فشل في قراءة الملف: {$filename}</div>";
                        continue;
                    }
                    
                    echo "<div class='log-entry log-info'><i class='fas fa-file-code'></i> تم تحميل الملف: {$filename}</div>";
                    
                    // تنفيذ الاستعلامات
                    if ($mysqli->multi_query($sql)) {
                        $queryCount = 0;
                        $successCount = 0;
                        $errorCount = 0;
                        
                        do {
                            $queryCount++;
                            if ($result = $mysqli->store_result()) {
                                $result->free();
                            }
                            
                            if ($mysqli->error) {
                                $errorCount++;
                                echo "<div class='log-entry log-warning'><i class='fas fa-exclamation-circle'></i> تحذير في الاستعلام رقم {$queryCount}: " . $mysqli->error . "</div>";
                            } else {
                                $successCount++;
                            }
                            
                        } while ($mysqli->more_results() && $mysqli->next_result());
                        
                        echo "<div class='log-entry log-success'><i class='fas fa-check'></i> تم تنفيذ {$successCount} استعلام بنجاح";
                        if ($errorCount > 0) {
                            echo " مع {$errorCount} تحذير";
                        }
                        echo "</div>";
                        
                    } else {
                        echo "<div class='log-entry log-error'><i class='fas fa-times'></i> فشل في تنفيذ الاستعلامات: " . $mysqli->error . "</div>";
                    }
                    
                    echo "</div></div>";
                }
                
                // التحقق من النتائج النهائية
                echo "<div class='step'>";
                echo "<div class='step-header bg-success text-white'>";
                echo "<i class='fas fa-check-circle'></i> التحقق من النتائج النهائية";
                echo "</div>";
                echo "<div class='step-content'>";
                
                // التحقق من الجداول
                $tables = ['countries', 'daily_transactions', 'daily_transaction_history'];
                echo "<h5><i class='fas fa-table'></i> الجداول المنشأة:</h5>";
                
                foreach ($tables as $table) {
                    $result = $mysqli->query("SHOW TABLES LIKE '{$table}'");
                    if ($result && $result->num_rows > 0) {
                        $countResult = $mysqli->query("SELECT COUNT(*) as count FROM {$table}");
                        $count = $countResult ? $countResult->fetch_assoc()['count'] : 0;
                        echo "<div class='log-entry log-success'><i class='fas fa-table'></i> جدول {$table} - {$count} سجل</div>";
                    } else {
                        echo "<div class='log-entry log-error'><i class='fas fa-times'></i> جدول {$table} غير موجود</div>";
                    }
                }
                
                // التحقق من الصلاحيات
                echo "<h5 class='mt-3'><i class='fas fa-key'></i> الصلاحيات:</h5>";
                $permResult = $mysqli->query("SELECT COUNT(*) as count FROM permissions WHERE module = 'daily_transactions'");
                if ($permResult) {
                    $permCount = $permResult->fetch_assoc()['count'];
                    echo "<div class='log-entry log-success'><i class='fas fa-key'></i> تم إضافة {$permCount} صلاحية لنظام المعاملات اليومية</div>";
                }
                
                // التحقق من الإجراءات المخزنة
                echo "<h5 class='mt-3'><i class='fas fa-cogs'></i> الإجراءات المخزنة:</h5>";
                $procResult = $mysqli->query("SHOW PROCEDURE STATUS WHERE Db = '" . DB_NAME . "' AND Name LIKE '%DailyTransaction%'");
                if ($procResult) {
                    $procCount = $procResult->num_rows;
                    echo "<div class='log-entry log-success'><i class='fas fa-cogs'></i> تم إنشاء {$procCount} إجراء مخزن</div>";
                }
                
                // التحقق من الفهارس
                echo "<h5 class='mt-3'><i class='fas fa-search'></i> الفهارس:</h5>";
                $indexResult = $mysqli->query("SHOW INDEX FROM daily_transactions");
                if ($indexResult) {
                    $indexCount = $indexResult->num_rows;
                    echo "<div class='log-entry log-success'><i class='fas fa-search'></i> تم إنشاء {$indexCount} فهرس في جدول المعاملات</div>";
                }
                
                echo "</div></div>";
                
                // رسالة النجاح النهائية
                echo "<div class='alert alert-success alert-dismissible fade show' role='alert'>";
                echo "<h4 class='alert-heading'><i class='fas fa-trophy'></i> تم الإعداد بنجاح!</h4>";
                echo "<p>تم إعداد نظام المعاملات اليومية بنجاح. يمكنك الآن:</p>";
                echo "<ul>";
                echo "<li>إنشاء صفحات إضافة وعرض المعاملات</li>";
                echo "<li>تخصيص الصلاحيات للمستخدمين</li>";
                echo "<li>إضافة المزيد من الدول والعملات</li>";
                echo "<li>تخصيص الحقول حسب احتياجاتك</li>";
                echo "</ul>";
                echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>";
                echo "<h4 class='alert-heading'><i class='fas fa-exclamation-triangle'></i> خطأ في الإعداد</h4>";
                echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
                echo "<p>يرجى التحقق من إعدادات قاعدة البيانات والمحاولة مرة أخرى.</p>";
                echo "</div>";
            } finally {
                if (isset($mysqli)) {
                    $mysqli->close();
                }
            }
            ?>
            
            <div class="text-center mt-4">
                <a href="../dashboard/" class="btn btn-primary btn-lg">
                    <i class="fas fa-tachometer-alt"></i> الذهاب إلى لوحة التحكم
                </a>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary btn-lg">
                    <i class="fas fa-redo"></i> إعادة تشغيل الإعداد
                </a>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

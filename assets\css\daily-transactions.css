/* =====================================================
   تصميم صفحات المعاملات اليومية
   Daily Transactions Pages Styling
   ===================================================== */

/* تحسينات عامة للنماذج */
.form-label.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* تصميم الحقول المحسوبة تلقائياً */
.form-control.bg-light {
    background-color: #f8f9fa !important;
    border-color: #dee2e6;
    color: #6c757d;
    font-weight: 600;
}

/* تحسينات للبطاقات */
.card {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* تصميم الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    padding: 15px 10px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    vertical-align: middle;
    padding: 12px 10px;
    border-color: #e9ecef;
}

/* تصميم الشارات (Badges) */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 6px;
    font-weight: 600;
}

/* تصميم الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

/* تصميم الإحصائيات */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning,
.card.bg-danger,
.card.bg-secondary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%) !important;
    border: none;
    color: white !important;
}

.card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%) !important;
    color: #212529 !important;
}

.card.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%) !important;
}

.card.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%) !important;
}

/* تصميم Timeline للتاريخ */
.timeline {
    position: relative;
    padding: 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    right: 20px;
    height: 100%;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-right: 50px;
}

.timeline-marker {
    position: absolute;
    top: 0;
    right: 10px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    z-index: 1;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-right: 3px solid #dee2e6;
}

/* تحسينات للفلاتر */
.collapse.show {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تصميم التصفح */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border-color: #0d6efd;
}

/* تحسينات للتنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    border-right: 4px solid;
}

.alert-success {
    border-right-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.alert-danger {
    border-right-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.alert-warning {
    border-right-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.alert-info {
    border-right-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

/* تحسينات للنماذج المتقدمة */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* تصميم الحقول الإضافية */
#additional_fields {
    animation: fadeIn 0.3s ease;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 20px;
    background: #f8f9fa;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .btn,
    .pagination,
    .alert,
    nav[aria-label="breadcrumb"] {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table thead th {
        background: #000 !important;
        color: #fff !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        border-radius: 8px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .timeline-item {
        padding-right: 40px;
    }
    
    .timeline-marker {
        right: 5px;
        width: 20px;
        height: 20px;
        font-size: 8px;
    }
    
    .timeline::before {
        right: 15px;
    }
}

/* تأثيرات التحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        right: -100%;
    }
    100% {
        right: 100%;
    }
}

/* تحسينات للتفاعل */
.clickable {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable:hover {
    background-color: #f8f9fa;
    transform: scale(1.02);
}

/* تصميم الحالات الفارغة */
.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-state h5 {
    color: #6c757d;
    margin-bottom: 10px;
}

.empty-state p {
    color: #adb5bd;
    margin-bottom: 30px;
}

/* تحسينات للتركيز */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

/* تصميم الحدود المتدرجة */
.gradient-border {
    position: relative;
    background: white;
    border-radius: 12px;
}

.gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

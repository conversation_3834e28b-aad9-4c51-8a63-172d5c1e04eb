<?php
/**
 * تعديل دور عبر AJAX
 * Edit Role via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/role_manager.php';
require_once __DIR__ . '/../../includes/activity_helper.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.edit')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لتعديل الأدوار'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

try {
    // استلام البيانات
    $id = (int)($_POST['id'] ?? 0);
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $status = trim($_POST['status'] ?? 'نشط');
    
    // التحقق من صحة البيانات
    $errors = [];
    
    if (!$id) {
        $errors[] = 'معرف الدور مطلوب';
    }
    
    if (empty($name)) {
        $errors[] = 'اسم الدور مطلوب';
    } elseif (strlen($name) < 3) {
        $errors[] = 'اسم الدور يجب أن يكون 3 أحرف على الأقل';
    }
    
    if (empty($description)) {
        $errors[] = 'وصف الدور مطلوب';
    } elseif (strlen($description) < 10) {
        $errors[] = 'وصف الدور يجب أن يكون 10 أحرف على الأقل';
    }
    
    if (!in_array($status, ['نشط', 'غير نشط'])) {
        $errors[] = 'حالة الدور غير صحيحة';
    }
    
    $db = new Database();
    $roleMgr = new RoleManager($db);
    
    // التحقق من وجود الدور
    $existingRole = $roleMgr->getRoleById($id);
    if (!$existingRole) {
        $errors[] = 'الدور غير موجود';
    }
    
    // التحقق من عدم تكرار اسم الدور (إذا تم تغييره)
    if ($existingRole && $existingRole['name'] !== $name && $roleMgr->roleNameExists($name)) {
        $errors[] = 'اسم الدور موجود بالفعل';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'أخطاء في البيانات',
            'errors' => $errors
        ]);
        exit;
    }
    
    // تعديل الدور
    $data = [
        'name' => $name,
        'description' => $description,
        'status' => $status
    ];
    
    $success = $roleMgr->updateRole($id, $data);
    
    if ($success) {
        // تسجيل العملية
        ActivityHelper::logUpdate(
            'roles',
            "الدور #$id",
            $existingRole,
            $data,
            $id
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تعديل الدور بنجاح'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'فشل في تعديل الدور'
        ]);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    ActivityHelper::logError(
        'roles',
        'edit_role',
        'فشل في تعديل الدور: ' . $e->getMessage(),
        [
            'attempted_data' => $_POST,
            'error' => $e->getMessage()
        ]
    );
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

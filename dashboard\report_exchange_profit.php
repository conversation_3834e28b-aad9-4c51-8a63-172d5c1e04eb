<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/reports_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';
require_once __DIR__ . '/../includes/branch_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth=new Auth();
$auth->requireLogin();
$auth->requirePermission('reports.financial.view');

$curMgr=new CurrencyManager(new Database());
$branchMgr=new BranchManager(new Database());
$repMgr=new ReportsManager(new Database());

$filters=[
    'from_date'=>$_GET['from_date'] ?? '',
    'to_date'=>$_GET['to_date'] ?? '',
    'branch_id'=>isset($_GET['branch_id'])? (int)$_GET['branch_id']:null,
    'from_currency_id'=>isset($_GET['from_currency_id'])? (int)$_GET['from_currency_id']:null,
    'to_currency_id'=>isset($_GET['to_currency_id'])? (int)$_GET['to_currency_id']:null,
];
$data=$repMgr->getExchangeProfitReportData($filters);
$currencies=$curMgr->getAllCurrencies(true);
$branches=$branchMgr->getAllBranches();

// تسجيل عملية عرض تقرير أرباح الصرافة
ActivityHelper::logView('reports', 'عرض تقرير أرباح الصرافة', $filters);

$csrf=get_csrf_token();
$pageTitle='تقرير أرباح الصرافة';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>
<div class="container p-4">
 <h3><i class="fas fa-coins"></i> تقرير أرباح الصرافة</h3>
 <form method="get" class="row g-2 mb-3">
  <div class="col-md-2"><input type="date" name="from_date" value="<?php echo htmlspecialchars($filters['from_date']); ?>" class="form-control" placeholder="من"></div>
  <div class="col-md-2"><input type="date" name="to_date" value="<?php echo htmlspecialchars($filters['to_date']); ?>" class="form-control" placeholder="إلى"></div>
  <div class="col-md-2"><select name="branch_id" class="form-select"><option value="">كل الفروع</option><?php foreach($branches as $b){?><option value="<?php echo $b['id'];?>" <?php if($filters['branch_id']==$b['id'])echo'selected';?>><?php echo htmlspecialchars($b['name']);?></option><?php }?></select></div>
  <div class="col-md-2"><select name="from_currency_id" class="form-select"><option value="">عملة من</option><?php foreach($currencies as $c){?><option value="<?php echo $c['id']; ?>" <?php if($filters['from_currency_id']==$c['id'])echo'selected';?>><?php echo htmlspecialchars($c['code']);?></option><?php }?></select></div>
  <div class="col-md-2"><select name="to_currency_id" class="form-select"><option value="">عملة إلى</option><?php foreach($currencies as $c){?><option value="<?php echo $c['id']; ?>" <?php if($filters['to_currency_id']==$c['id'])echo'selected';?>><?php echo htmlspecialchars($c['code']);?></option><?php }?></select></div>
  <div class="col-md-2 d-grid"><button class="btn btn-primary"><i class="fas fa-search"></i> عرض</button></div>
 </form>
 <div class="table-responsive">
  <table class="table table-bordered table-sm align-middle">
    <thead class="table-light"><tr><th>التاريخ</th><th>زوج العملة</th><th>الفرع</th><th>عدد العمليات</th><th>حجم من</th><th>حجم إلى</th><th>العمولة</th><th>الربح</th></tr></thead>
    <tbody>
    <?php $totals=['tx'=>0,'comm'=>0,'profit'=>0]; foreach($data as $row): ?>
      <tr>
        <td><?php echo htmlspecialchars($row['report_date']); ?></td>
        <td><?php echo htmlspecialchars($row['pair']); ?></td>
        <td><?php echo htmlspecialchars($row['branch_name'] ?? ''); ?></td>
        <td><?php echo $row['total_tx']; $totals['tx']+=$row['total_tx']; ?></td>
        <td><?php echo number_format($row['volume_from'],2); ?></td>
        <td><?php echo number_format($row['volume_to'],2); ?></td>
        <td><?php echo number_format($row['total_commission'],2); $totals['comm']+=$row['total_commission']; ?></td>
        <td><?php echo number_format($row['total_profit'],2); $totals['profit']+=$row['total_profit']; ?></td>
      </tr>
    <?php endforeach; if(!$data): ?><tr><td colspan="8" class="text-center">لا توجد بيانات</td></tr><?php endif; ?>
    </tbody>
    <tfoot class="table-light"><tr><th colspan="3">الإجمالي</th><th><?php echo $totals['tx']; ?></th><th colspan="2"></th><th><?php echo number_format($totals['comm'],2); ?></th><th><?php echo number_format($totals['profit'],2); ?></th></tr></tfoot>
  </table>
 </div>
</div>
<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
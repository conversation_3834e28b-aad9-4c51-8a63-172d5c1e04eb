<?php
/**
 * <PERSON><PERSON><PERSON> to fix database locks and add missing columns safely
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = Database::getConnection();
    
    echo "<h2>إصلاح أقفال قاعدة البيانات وإضافة الأعمدة المفقودة</h2>\n";
    
    // First, kill any hanging transactions
    echo "<h3>1. إنهاء المعاملات المعلقة</h3>\n";
    
    // Show current processes
    $result = $db->query("SHOW PROCESSLIST");
    if ($result) {
        $processes = $result->fetch_all(MYSQLI_ASSOC);
        foreach ($processes as $process) {
            if ($process['Command'] === 'Sleep' && $process['Time'] > 300) { // 5 minutes
                $killQuery = "KILL " . $process['Id'];
                if ($db->query($killQuery)) {
                    echo "<p style='color: orange;'>تم إنهاء العملية المعلقة: " . $process['Id'] . "</p>\n";
                }
            }
        }
    }
    
    // Reset any locks
    $db->query("UNLOCK TABLES");
    echo "<p style='color: green;'>تم إلغاء جميع أقفال الجداول</p>\n";
    
    echo "<h3>2. إضافة الأعمدة المفقودة</h3>\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    // List of columns to add
    $columns = [
        [
            'table' => 'cash_movements',
            'column' => 'exchange_id',
            'definition' => 'INT UNSIGNED NULL',
            'after' => 'user_id'
        ],
        [
            'table' => 'bank_movements', 
            'column' => 'exchange_id',
            'definition' => 'INT UNSIGNED NULL',
            'after' => 'user_id'
        ],
        [
            'table' => 'cash_movements',
            'column' => 'transfer_id', 
            'definition' => 'INT UNSIGNED NULL',
            'after' => 'exchange_id'
        ],
        [
            'table' => 'bank_movements',
            'column' => 'transfer_id',
            'definition' => 'INT UNSIGNED NULL', 
            'after' => 'exchange_id'
        ]
    ];
    
    foreach ($columns as $col) {
        try {
            // Check if column exists
            $checkSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_SCHEMA = DATABASE() 
                        AND TABLE_NAME = '{$col['table']}' 
                        AND COLUMN_NAME = '{$col['column']}'";
            
            $result = $db->query($checkSql);
            $exists = $result->fetch_assoc()['count'] > 0;
            
            if (!$exists) {
                $addSql = "ALTER TABLE {$col['table']} ADD COLUMN {$col['column']} {$col['definition']} AFTER {$col['after']}";
                
                if ($db->query($addSql)) {
                    echo "<p style='color: green;'>✓ تم إضافة العمود: {$col['table']}.{$col['column']}</p>\n";
                    $successCount++;
                } else {
                    echo "<p style='color: red;'>✗ فشل إضافة العمود: {$col['table']}.{$col['column']} - " . $db->error . "</p>\n";
                    $errorCount++;
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ العمود موجود بالفعل: {$col['table']}.{$col['column']}</p>\n";
                $successCount++;
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ خطأ في العمود {$col['table']}.{$col['column']}: " . $e->getMessage() . "</p>\n";
            $errorCount++;
        }
    }
    
    echo "<h3>3. إضافة القيود الخارجية</h3>\n";
    
    // Add foreign key constraints
    $constraints = [
        [
            'table' => 'cash_movements',
            'constraint' => 'fk_cmov_exchange',
            'sql' => 'ALTER TABLE cash_movements ADD CONSTRAINT fk_cmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE'
        ],
        [
            'table' => 'bank_movements',
            'constraint' => 'fk_bmov_exchange', 
            'sql' => 'ALTER TABLE bank_movements ADD CONSTRAINT fk_bmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE'
        ]
    ];
    
    foreach ($constraints as $constraint) {
        try {
            // Check if constraint exists
            $checkSql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_SCHEMA = DATABASE() 
                        AND TABLE_NAME = '{$constraint['table']}' 
                        AND CONSTRAINT_NAME = '{$constraint['constraint']}'";
            
            $result = $db->query($checkSql);
            $exists = $result->fetch_assoc()['count'] > 0;
            
            if (!$exists) {
                if ($db->query($constraint['sql'])) {
                    echo "<p style='color: green;'>✓ تم إضافة القيد: {$constraint['constraint']}</p>\n";
                    $successCount++;
                } else {
                    echo "<p style='color: red;'>✗ فشل إضافة القيد: {$constraint['constraint']} - " . $db->error . "</p>\n";
                    $errorCount++;
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ القيد موجود بالفعل: {$constraint['constraint']}</p>\n";
                $successCount++;
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ خطأ في القيد {$constraint['constraint']}: " . $e->getMessage() . "</p>\n";
            $errorCount++;
        }
    }
    
    echo "<hr>\n";
    echo "<h3>النتائج النهائية:</h3>\n";
    echo "<p><strong>العمليات الناجحة:</strong> $successCount</p>\n";
    echo "<p><strong>العمليات الفاشلة:</strong> $errorCount</p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 تم إصلاح قاعدة البيانات بنجاح!</p>\n";
        echo "<p style='color: blue;'>يمكنك الآن استخدام نظام الصرافة بدون مشاكل.</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>تم الإصلاح مع بعض التحذيرات. النظام يجب أن يعمل الآن.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>\n";
}
?>

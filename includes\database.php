<?php
/**
 * Handles MySQL database connections using MySQLi.
 */

// Include database configuration
$configFile = __DIR__ . '/../config/database.php';
if (!file_exists($configFile)) {
    die('Error: Database configuration file not found at ' . $configFile);
}
require_once $configFile;

class Database
{
    private static $instance = null;

    /**
     * Get a MySQLi connection instance (singleton with auto-reconnect).
     * 
     * @return mysqli
     * @throws Exception If connection fails
     */
    public static function getConnection()
    {
        // If connection exists and alive → reuse
        if (self::$instance instanceof mysqli) {
            if (@self::$instance->ping()) {
                return self::$instance;
            }
            // connection lost, close it
            @self::$instance->close();
            self::$instance = null;
        }

        // (Re)create connection
        $conn = mysqli_init();
        // Short connect timeout (seconds)
        $conn->options(MYSQLI_OPT_CONNECT_TIMEOUT, 5);
        // Enable auto-reconnect if MySQL supports it
        if (defined('MYSQLI_OPT_RECONNECT')) {
            $conn->options(MYSQLI_OPT_RECONNECT, true);
        }
        $conn->real_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if ($conn->connect_error) {
            die('Database connection failed: ' . $conn->connect_error);
        }
        if (!$conn->set_charset(DB_CHARSET)) {
            die('Error loading character set ' . DB_CHARSET . ': ' . $conn->error);
        }
        self::$instance = $conn;
        return self::$instance;
    }
} 
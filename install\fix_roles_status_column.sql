-- إصل<PERSON><PERSON> مشكلة عمود status في جدول roles
-- Fix roles status column issue

-- 1. تغيير نوع عمود status إلى VARCHAR إذا كان ENUM
ALTER TABLE roles MODIFY COLUMN status VARCHAR(20) DEFAULT 'نشط';

-- 2. تو<PERSON><PERSON><PERSON> قيم الحالة
UPDATE roles SET status = 'نشط' WHERE status IN ('active', 'Active', 'ACTIVE', '1', 'enabled', 'enable');
UPDATE roles SET status = 'غير نشط' WHERE status IN ('inactive', 'Inactive', 'INACTIVE', '0', 'disabled', 'disable');

-- 3. إ<PERSON><PERSON><PERSON><PERSON> عمود updated_at إذا لم يكن موجوداً
ALTER TABLE roles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- 4. إضافة فهرس لعمود status
ALTER TABLE roles ADD INDEX IF NOT EXISTS idx_status (status);

-- 5. <PERSON><PERSON><PERSON><PERSON><PERSON> trigger للتحديث التلقائي
DROP TRIGGER IF EXISTS roles_update_timestamp;
CREATE TRIGGER roles_update_timestamp 
BEFORE UPDATE ON roles 
FOR EACH ROW 
SET NEW.updated_at = CURRENT_TIMESTAMP;

-- 6. التأكد من أن جميع الأدوار لها حالة صحيحة
UPDATE roles SET status = 'نشط' WHERE status IS NULL OR status = '';

-- 7. عرض النتيجة النهائية
SELECT 'تم إصلاح عمود status بنجاح!' as message;
SELECT id, name, status, updated_at FROM roles ORDER BY id;

/**
 * تحسينات JavaScript لقائمة PayPal في الشريط الجانبي
 * PayPal Sidebar JavaScript Enhancements
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // العناصر الرئيسية
    const paypalMenu = document.querySelector('.paypal-menu');
    const paypalMainLink = document.querySelector('.paypal-main-link');
    const paypalSubmenu = document.querySelector('.paypal-submenu');
    const paypalSublinks = document.querySelectorAll('.paypal-sublink');
    
    if (!paypalMenu) return; // إذا لم توجد قائمة PayPal، اخرج من الدالة
    
    // تأثير النبضة عند التحميل
    setTimeout(() => {
        paypalMenu.classList.add('active');
        setTimeout(() => {
            paypalMenu.classList.remove('active');
        }, 2000);
    }, 1000);
    
    // تأثير التمرير على القائمة الرئيسية
    paypalMainLink.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.02) translateY(-2px)';
        
        // تأثير الضوء المتحرك
        const lightEffect = document.createElement('div');
        lightEffect.style.cssText = `
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
            pointer-events: none;
            z-index: 1;
        `;
        
        this.appendChild(lightEffect);
        
        setTimeout(() => {
            lightEffect.style.left = '100%';
        }, 50);
        
        setTimeout(() => {
            if (lightEffect.parentNode) {
                lightEffect.remove();
            }
        }, 650);
    });
    
    paypalMainLink.addEventListener('mouseleave', function() {
        this.style.transform = '';
    });
    
    // تأثيرات الروابط الفرعية
    paypalSublinks.forEach((link, index) => {
        
        // تأثير التمرير
        link.addEventListener('mouseenter', function() {
            // إضافة تأثير الموجة
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(0, 112, 186, 0.3);
                transform: scale(0);
                animation: rippleEffect 0.6s linear;
                pointer-events: none;
                z-index: 1;
            `;
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (rect.width / 2 - size / 2) + 'px';
            ripple.style.top = (rect.height / 2 - size / 2) + 'px';
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.remove();
                }
            }, 600);
            
            // تأثير اهتزاز خفيف للأيقونة
            const icon = this.querySelector('.paypal-subicon-wrapper');
            if (icon) {
                icon.style.animation = 'iconShake 0.5s ease';
                setTimeout(() => {
                    icon.style.animation = '';
                }, 500);
            }
        });
        
        // تأثير النقر
        link.addEventListener('click', function(e) {
            // إضافة تأثير النقر
            const clickEffect = document.createElement('div');
            clickEffect.style.cssText = `
                position: absolute;
                width: 20px;
                height: 20px;
                background: rgba(255, 255, 255, 0.8);
                border-radius: 50%;
                transform: scale(0);
                animation: clickPulse 0.3s ease;
                pointer-events: none;
                z-index: 2;
            `;
            
            const rect = this.getBoundingClientRect();
            clickEffect.style.left = (e.clientX - rect.left - 10) + 'px';
            clickEffect.style.top = (e.clientY - rect.top - 10) + 'px';
            
            this.appendChild(clickEffect);
            
            setTimeout(() => {
                if (clickEffect.parentNode) {
                    clickEffect.remove();
                }
            }, 300);
        });
    });
    
    // تأثير فتح/إغلاق القائمة المنسدلة
    paypalMainLink.addEventListener('click', function() {
        const isExpanded = this.getAttribute('aria-expanded') === 'true';
        
        if (!isExpanded) {
            // عند الفتح - تأثير انزلاق للروابط الفرعية
            setTimeout(() => {
                paypalSublinks.forEach((link, index) => {
                    link.style.opacity = '0';
                    link.style.transform = 'translateY(-20px)';
                    
                    setTimeout(() => {
                        link.style.transition = 'all 0.3s ease';
                        link.style.opacity = '1';
                        link.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 100);
        }
    });
    
    // تأثير التمرير على الصفحة
    let lastScrollTop = 0;
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // التمرير لأسفل - تقليل حجم القائمة قليلاً
            paypalMenu.style.transform = 'scale(0.98)';
            paypalMenu.style.opacity = '0.9';
        } else {
            // التمرير لأعلى - إعادة الحجم الطبيعي
            paypalMenu.style.transform = 'scale(1)';
            paypalMenu.style.opacity = '1';
        }
        
        lastScrollTop = scrollTop;
    });
    
    // تأثير التركيز بلوحة المفاتيح
    paypalSublinks.forEach(link => {
        link.addEventListener('focus', function() {
            this.style.outline = '2px solid #0070ba';
            this.style.outlineOffset = '2px';
        });
        
        link.addEventListener('blur', function() {
            this.style.outline = 'none';
        });
    });
    
    // تأثير الصوت (اختياري - يمكن تفعيله)
    function playClickSound() {
        // يمكن إضافة ملف صوتي هنا
        // const audio = new Audio('/assets/sounds/click.mp3');
        // audio.volume = 0.1;
        // audio.play().catch(() => {}); // تجاهل الأخطاء
    }
    
    // مراقبة تغيير الصفحة النشطة
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const activeLink = document.querySelector('.paypal-sublink.active');
                if (activeLink) {
                    // تأثير خاص للرابط النشط
                    activeLink.style.animation = 'activeGlow 2s ease infinite';
                }
            }
        });
    });
    
    // مراقبة تغييرات الفئات
    paypalSublinks.forEach(link => {
        observer.observe(link, { attributes: true, attributeFilter: ['class'] });
    });
    
    // تنظيف عند مغادرة الصفحة
    window.addEventListener('beforeunload', function() {
        observer.disconnect();
    });
    
    // إضافة الأنماط المطلوبة للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        @keyframes clickPulse {
            to {
                transform: scale(3);
                opacity: 0;
            }
        }
        
        @keyframes iconShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px) rotate(-2deg); }
            75% { transform: translateX(2px) rotate(2deg); }
        }
        
        @keyframes activeGlow {
            0%, 100% { box-shadow: 0 4px 15px rgba(0, 112, 186, 0.3); }
            50% { box-shadow: 0 6px 25px rgba(0, 112, 186, 0.5); }
        }
        
        .paypal-sublink {
            position: relative;
            overflow: hidden;
        }
        
        .paypal-menu {
            position: relative;
            transition: all 0.3s ease;
        }
    `;
    document.head.appendChild(style);
    
    console.log('🎨 PayPal Sidebar enhancements loaded successfully!');
});

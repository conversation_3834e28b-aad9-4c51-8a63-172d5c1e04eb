<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/user_manager.php';
require_once __DIR__ . '/../includes/role_manager.php';
require_once __DIR__ . '/../includes/branch_manager.php';
require_once __DIR__ . '/../includes/image_helper.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('users.create');

$current_user = $auth->getCurrentUser();

$db           = new Database();
$userManager  = new UserManager($db);
$roleManager  = new RoleManager($db);
$branchMgr    = new BranchManager($db);

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF check
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز CSRF غير صالح.';
    }

    // Sanitize inputs
    $username   = sanitize_input($_POST['username'] ?? '');
    $full_name  = sanitize_input($_POST['full_name'] ?? '');
    $email      = sanitize_input($_POST['email'] ?? '');
    $phone      = sanitize_input($_POST['phone'] ?? '');
    $password   = $_POST['password'] ?? '';
    $password2  = $_POST['confirm_password'] ?? '';
    $role_id    = (int) ($_POST['role_id'] ?? 0);
    $branch_id  = (int) ($_POST['branch_id'] ?? 0);
    $status     = sanitize_input($_POST['status'] ?? 'active');

    // Validation
    if (empty($username))   $errors[] = 'اسم المستخدم مطلوب';
    if (empty($password))   $errors[] = 'كلمة المرور مطلوبة';
    if ($password !== $password2) $errors[] = 'كلمتا المرور غير متطابقتين';
    if ($role_id <= 0)      $errors[] = 'الرجاء اختيار دور';
    if ($branch_id <= 0)    $errors[] = 'الرجاء اختيار فرع';

    if ($userManager->userExists($username)) {
        $errors[] = 'اسم المستخدم مستخدم مسبقاً';
    }

    if (empty($errors)) {
        $data = [
            'username'   => $username,
            'password'   => $password,
            'full_name'  => $full_name,
            'email'      => $email,
            'phone'      => $phone,
            'role_id'    => $role_id,
            'branch_id'  => $branch_id,
            'status'     => $status,
        ];

        // Handle profile image upload
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $tempUserId = time(); // Temporary ID for upload
            $imagePath = ImageHelper::uploadProfileImage($_FILES['profile_image'], $tempUserId);
            if ($imagePath) {
                $data['profile_image'] = $imagePath;
            }
        }

        $newId = $userManager->addUser($data);
        if ($newId) {
            // Update image filename with actual user ID
            if (isset($data['profile_image'])) {
                $oldPath = $data['profile_image'];
                $newPath = str_replace('user_' . $tempUserId, 'user_' . $newId, $oldPath);
                $fullOldPath = __DIR__ . '/../' . $oldPath;
                $fullNewPath = __DIR__ . '/../' . $newPath;
                if (file_exists($fullOldPath)) {
                    rename($fullOldPath, $fullNewPath);
                    $userManager->updateUser($newId, ['profile_image' => $newPath]);
                }
            }

            // تسجيل عملية إضافة المستخدم
            ActivityHelper::logCreate(
                'users',
                "$username ($full_name)",
                $data,
                $newId
            );

            header('Location: users.php?msg=created');
            exit;
        } else {
            $errors[] = 'حدث خطأ أثناء إنشاء المستخدم';

            // تسجيل الخطأ
            $activityManager = new SystemActivityManager();
            $activityManager->logError(
                'users',
                'add_user',
                'فشل في إضافة مستخدم جديد: ' . $username,
                [
                    'attempted_data' => $data,
                    'status' => 'FAILED'
                ]
            );
        }
    }
}

// Fetch roles & branches for dropdowns
$roles    = $roleManager->getAllRoles();
$branches = $branchMgr->getAllBranches();

$pageTitle = 'إضافة مستخدم';
include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <h2 class="mb-4">إضافة مستخدم</h2>

    <?php if ($errors): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $e): ?>
                    <li><?php echo $e; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <form method="post" enctype="multipart/form-data" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">

        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" name="username" class="form-control" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                </div>

                <div class="mb-3">
                    <label class="form-label">الاسم الكامل</label>
                    <input type="text" name="full_name" class="form-control" value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>">
                </div>

                <div class="mb-3">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                </div>

                <div class="mb-3">
                    <label class="form-label">الهاتف</label>
                    <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" name="password" class="form-control" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" name="confirm_password" class="form-control" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الدور</label>
                        <select name="role_id" class="form-select" required>
                            <option value="">-- اختر --</option>
                            <?php foreach ($roles as $r): ?>
                                <option value="<?php echo $r['id']; ?>" <?php echo (($_POST['role_id'] ?? '') == $r['id']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($r['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الفرع</label>
                        <select name="branch_id" class="form-select" required>
                            <option value="">-- اختر --</option>
                            <?php foreach ($branches as $b): ?>
                                <option value="<?php echo $b['id']; ?>" <?php echo (($_POST['branch_id'] ?? '') == $b['id']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($b['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="active" <?php echo (($_POST['status'] ?? 'active') === 'active') ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo (($_POST['status'] ?? '') === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                        <option value="blocked" <?php echo (($_POST['status'] ?? '') === 'blocked') ? 'selected' : ''; ?>>محظور</option>
                    </select>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">صورة الملف الشخصي</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">اختر صورة</label>
                            <input type="file" name="profile_image" class="form-control" accept="image/*">
                            <div class="form-text">الأنواع المسموحة: JPG, PNG, GIF. الحد الأقصى: 5MB</div>
                        </div>
                        
                        <div id="image-preview" class="text-center" style="display: none;">
                            <img id="preview-img" src="" alt="معاينة الصورة" class="profile-image profile-image-xxl">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-primary">حفظ</button>
        <a href="users.php" class="btn btn-secondary">إلغاء</a>
    </form>
</main>

<script>
// Image preview functionality
document.querySelector('input[name="profile_image"]').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?> 
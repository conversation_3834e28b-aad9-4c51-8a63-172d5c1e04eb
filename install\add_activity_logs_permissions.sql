-- إضافة صلاحيات خاصة بسجل العمليات الشامل

-- إضافة الصلاحيات الجديدة
INSERT IGNORE INTO permissions (name, description, module) VALUES
    ('system_activity_logs.view', 'عرض سجل العمليات الشامل للنظام', 'system'),
    ('system_activity_logs.export', 'تصدير سجل العمليات', 'system'),
    ('system_activity_logs.details', 'عرض تفاصيل سجل العمليات', 'system'),
    ('system_activity_logs.admin_only', 'الوصول الكامل لسجل العمليات (أدمن فقط)', 'system');

-- ربط الصلاحيات بدور الأدمن (role_id = 1)
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 1, id FROM permissions 
WHERE name IN (
    'system_activity_logs.view',
    'system_activity_logs.export', 
    'system_activity_logs.details',
    'system_activity_logs.admin_only'
);

-- إضافة صلاحيات عامة للنظام
INSERT IGNORE INTO permissions (name, description, module) VALUES
    ('system.maintenance', 'صيانة النظام', 'system'),
    ('system.backup', 'نسخ احتياطي للنظام', 'system'),
    ('system.settings', 'إعدادات النظام', 'system'),
    ('system.logs', 'عرض سجلات النظام', 'system');

-- ربط الصلاحيات العامة بدور الأدمن
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT 1, id FROM permissions 
WHERE name IN (
    'system.maintenance',
    'system.backup',
    'system.settings', 
    'system.logs'
);

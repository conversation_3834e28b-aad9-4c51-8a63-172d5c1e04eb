<?php
/**
 * تغيير حالة الدور عبر AJAX
 * Toggle Role Status via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/role_manager.php';
require_once __DIR__ . '/../../includes/activity_helper.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.edit')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لتعديل حالة الأدوار'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

try {
    $id = (int)($_POST['id'] ?? 0);
    
    if (!$id) {
        echo json_encode([
            'success' => false,
            'message' => 'معرف الدور مطلوب'
        ]);
        exit;
    }
    
    // منع تغيير حالة دور الأدمن
    if ($id == 1) {
        echo json_encode([
            'success' => false,
            'message' => 'لا يمكن تغيير حالة دور مدير النظام'
        ]);
        exit;
    }
    
    $db = new Database();
    $roleMgr = new RoleManager($db);
    
    // التحقق من وجود الدور
    $role = $roleMgr->getRoleById($id);
    if (!$role) {
        echo json_encode([
            'success' => false,
            'message' => 'الدور غير موجود'
        ]);
        exit;
    }
    
    // تحديد الحالة الجديدة
    $currentStatus = $role['status'];
    $newStatus = ($currentStatus === 'نشط' || $currentStatus === 'active') ? 'غير نشط' : 'نشط';
    
    // تحديث حالة الدور
    $data = ['status' => $newStatus];
    $success = $roleMgr->updateRole($id, $data);

    if ($success) {
        // التحقق من التحديث الفعلي في قاعدة البيانات
        $verifyRole = $roleMgr->getRoleById($id);
        if ($verifyRole && $verifyRole['status'] === $newStatus) {
            // تسجيل العملية
            ActivityHelper::logStatusChange(
                'roles',
                "الدور: " . $role['name'],
                $currentStatus,
                $newStatus,
                $id
            );

            echo json_encode([
                'success' => true,
                'message' => 'تم تغيير حالة الدور بنجاح',
                'new_status' => $newStatus,
                'status_class' => ($newStatus === 'نشط') ? 'success' : 'warning',
                'verified' => true
            ]);
        } else {
            // التحديث لم يتم حفظه بشكل صحيح
            echo json_encode([
                'success' => false,
                'message' => 'تم التحديث ولكن لم يتم حفظه في قاعدة البيانات. الحالة الحالية: ' . ($verifyRole ? $verifyRole['status'] : 'غير معروف'),
                'debug_info' => [
                    'expected' => $newStatus,
                    'actual' => $verifyRole ? $verifyRole['status'] : null,
                    'original' => $currentStatus
                ]
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'فشل في تغيير حالة الدور - خطأ في قاعدة البيانات'
        ]);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    ActivityHelper::logError(
        'roles',
        'toggle_role_status',
        'فشل في تغيير حالة الدور: ' . $e->getMessage(),
        [
            'attempted_data' => $_POST,
            'error' => $e->getMessage()
        ]
    );
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

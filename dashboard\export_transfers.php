<?php
/**
 * Export Transfers to PDF or Excel
 * تصدير الحوالات إلى PDF أو Excel
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/transfer_manager.php';
require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';
require_once __DIR__ . '/../vendor/autoload.php'; // For TCPDF

// Check authentication
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// Check permissions
if (!$auth->hasPermission('transfers.view')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب');
    redirect('dashboard.php');
}

$db = new Database();
$transferManager = new EnhancedTransferManager($db);
$conn = Database::getConnection();

// Get export format
$format = sanitize_input($_GET['format'] ?? 'pdf');
if (!in_array($format, ['pdf', 'excel'])) {
    $format = 'pdf';
}

// Get filters
$filters = [];
$search = sanitize_input($_GET['search'] ?? '');
$status = sanitize_input($_GET['status'] ?? '');
$type = sanitize_input($_GET['type'] ?? '');
$country = sanitize_input($_GET['country'] ?? '');
$dateFrom = sanitize_input($_GET['date_from'] ?? '');
$dateTo = sanitize_input($_GET['date_to'] ?? '');
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 1000; // Default to 1000 for exports

if (!empty($search)) $filters['search'] = $search;
if (!empty($status)) $filters['status'] = $status;
if (!empty($type)) $filters['transfer_type'] = $type;
if (!empty($country)) $filters['country'] = $country;
if (!empty($dateFrom)) $filters['date_from'] = $dateFrom;
if (!empty($dateTo)) $filters['date_to'] = $dateTo;

// Get transfers with enhanced query
$sql = "SELECT t.*, 
               c.full_name AS beneficiary_full_name,
               b.name AS branch_name,
               u.full_name AS created_by_name,
               sc.code AS sending_currency_code,
               sc.symbol AS sending_currency_symbol,
               rc.code AS receiving_currency_code,
               rc.symbol AS receiving_currency_symbol,
               (t.sending_amount - t.receiving_amount) AS calculated_profit
        FROM transfers t
        LEFT JOIN customers c ON c.id = t.beneficiary_id
        LEFT JOIN branches b ON b.id = t.branch_id
        LEFT JOIN users u ON u.id = t.created_by
        LEFT JOIN currencies sc ON sc.id = t.sending_currency_id
        LEFT JOIN currencies rc ON rc.id = t.receiving_currency_id
        WHERE 1=1";

$params = [];
$types = '';

if (!empty($search)) {
    $sql .= " AND (t.transaction_number LIKE ? OR t.beneficiary_name LIKE ? OR t.tracking_number LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    $types .= 'sss';
}

if (!empty($status)) {
    $sql .= " AND t.status = ?";
    $params[] = $status;
    $types .= 's';
}

if (!empty($type)) {
    $sql .= " AND t.transfer_type = ?";
    $params[] = $type;
    $types .= 's';
}

if (!empty($country)) {
    $sql .= " AND t.beneficiary_country LIKE ?";
    $params[] = "%$country%";
    $types .= 's';
}

if (!empty($dateFrom)) {
    $sql .= " AND DATE(t.created_at) >= ?";
    $params[] = $dateFrom;
    $types .= 's';
}

if (!empty($dateTo)) {
    $sql .= " AND DATE(t.created_at) <= ?";
    $params[] = $dateTo;
    $types .= 's';
}

// Calculate total profit
$totalProfitSql = "SELECT SUM(t.sending_amount - t.receiving_amount) as total_profit 
                  FROM transfers t 
                  LEFT JOIN customers c ON c.id = t.beneficiary_id
                  LEFT JOIN branches b ON b.id = t.branch_id
                  LEFT JOIN users u ON u.id = t.created_by
                  LEFT JOIN currencies sc ON sc.id = t.sending_currency_id
                  LEFT JOIN currencies rc ON rc.id = t.receiving_currency_id
                  WHERE 1=1";

// Apply the same filters to the total profit calculation
if (!empty($search)) {
    $totalProfitSql .= " AND (t.transaction_number LIKE ? OR t.beneficiary_name LIKE ? OR t.tracking_number LIKE ?)";
}

if (!empty($status)) {
    $totalProfitSql .= " AND t.status = ?";
}

if (!empty($type)) {
    $totalProfitSql .= " AND t.transfer_type = ?";
}

if (!empty($country)) {
    $totalProfitSql .= " AND t.beneficiary_country LIKE ?";
}

if (!empty($dateFrom)) {
    $totalProfitSql .= " AND DATE(t.created_at) >= ?";
}

if (!empty($dateTo)) {
    $totalProfitSql .= " AND DATE(t.created_at) <= ?";
}

$totalProfitStmt = $conn->prepare($totalProfitSql);
if (!empty($params)) {
    // We need to exclude the limit parameter that was added for the main query
    $profitParams = array_slice($params, 0, count($params) - 1);
    $profitTypes = substr($types, 0, strlen($types) - 1);
    $totalProfitStmt->bind_param($profitTypes, ...$profitParams);
}
$totalProfitStmt->execute();
$totalProfitResult = $totalProfitStmt->get_result();
$totalProfit = $totalProfitResult->fetch_assoc()['total_profit'] ?? 0;
$totalProfitStmt->close();

// Get transfers for export
$sql .= " ORDER BY t.created_at DESC LIMIT ?";
$params[] = $limit;
$types .= 'i';

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();
$transfers = [];
while ($row = $result->fetch_assoc()) {
    $transfers[] = $row;
}
$stmt->close();

// Prepare filter description
$filterDescription = "جميع الحوالات";
if (!empty($dateFrom) && !empty($dateTo)) {
    $filterDescription .= " من $dateFrom إلى $dateTo";
} elseif (!empty($dateFrom)) {
    $filterDescription .= " من $dateFrom";
} elseif (!empty($dateTo)) {
    $filterDescription .= " حتى $dateTo";
}

if (!empty($status)) {
    $filterDescription .= " - الحالة: $status";
}

if (!empty($type)) {
    $filterDescription .= " - النوع: $type";
}

if (!empty($country)) {
    $filterDescription .= " - الدولة: $country";
}

// Export to PDF
if ($format === 'pdf') {
    // Create new PDF document
    require_once(__DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
    
    // تعريف فئة PDF مخصصة لدعم اللغة العربية
    class MYPDF extends TCPDF {
        public function Header() {
            // تعيين الخط
            $this->SetFont('dejavusans', 'B', 16);
            // العنوان
            $this->Cell(0, 15, 'تقرير الحوالات', 0, false, 'C', 0, '', 0, false, 'M', 'M');
        }

        public function Footer() {
            // تعيين موضع الصفحة
            $this->SetY(-15);
            // تعيين الخط
            $this->SetFont('dejavusans', 'I', 8);
            // رقم الصفحة
            $this->Cell(0, 10, 'الصفحة '.$this->getAliasNumPage().'/'.$this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
        }
    }

    // إنشاء مستند PDF جديد بدعم RTL
    $pdf = new MYPDF('L', 'mm', 'A4', true, 'UTF-8', false);

    // تعيين معلومات المستند
    $pdf->SetCreator('TrustPlus');
    $pdf->SetAuthor('TrustPlus System');
    $pdf->SetTitle('تقرير الحوالات');
    $pdf->SetSubject('تقرير الحوالات');
    $pdf->SetKeywords('حوالات, تقرير, تصدير');

    // تعيين بيانات الرأس الافتراضية
    $pdf->SetHeaderData('', 0, 'تقرير الحوالات', $filterDescription);

    // تعيين الهوامش
    $pdf->SetMargins(10, 20, 10);
    $pdf->SetHeaderMargin(5);
    $pdf->SetFooterMargin(10);

    // تعيين فواصل الصفحة التلقائية
    $pdf->SetAutoPageBreak(TRUE, 15);

    // تعيين عامل مقياس الصورة
    $pdf->setImageScale(1.25);

    // إضافة صفحة
    $pdf->AddPage();

    // تعيين اتجاه RTL
    $pdf->setRTL(true);
    
    // تعيين الخط
    $pdf->SetFont('dejavusans', 'B', 12);
    
    // إضافة وصف التصفية
    $pdf->Cell(0, 10, $filterDescription, 0, 1, 'C');
    $pdf->Ln(5);

    // إنشاء رأس الجدول
    $pdf->SetFont('dejavusans', 'B', 10);
    $pdf->SetFillColor(240, 240, 240);
    
    // تعيين عرض الأعمدة
    $colWidth1 = 25; // رقم الحوالة
    $colWidth2 = 15; // النوع
    $colWidth3 = 40; // المستفيد
    $colWidth4 = 25; // الدولة
    $colWidth5 = 30; // المبلغ الأساسي
    $colWidth6 = 30; // مبلغ التسليم
    $colWidth7 = 20; // الربح
    $colWidth8 = 20; // الحالة
    $colWidth9 = 25; // رقم التتبع
    $colWidth10 = 25; // تاريخ الإنشاء
    
    // رأس الجدول
    $pdf->Cell($colWidth1, 10, 'رقم الحوالة', 1, 0, 'C', 1);
    $pdf->Cell($colWidth2, 10, 'النوع', 1, 0, 'C', 1);
    $pdf->Cell($colWidth3, 10, 'المستفيد', 1, 0, 'C', 1);
    $pdf->Cell($colWidth4, 10, 'الدولة', 1, 0, 'C', 1);
    $pdf->Cell($colWidth5, 10, 'المبلغ الأساسي', 1, 0, 'C', 1);
    $pdf->Cell($colWidth6, 10, 'مبلغ التسليم', 1, 0, 'C', 1);
    $pdf->Cell($colWidth7, 10, 'الربح', 1, 0, 'C', 1);
    $pdf->Cell($colWidth8, 10, 'الحالة', 1, 0, 'C', 1);
    $pdf->Cell($colWidth9, 10, 'رقم التتبع', 1, 0, 'C', 1);
    $pdf->Cell($colWidth10, 10, 'تاريخ الإنشاء', 1, 1, 'C', 1);

    // إضافة صفوف البيانات
    $pdf->SetFont('dejavusans', '', 9);
    
    // تحديد لون الخلفية البديلة
    $altBg = false;
    
    foreach ($transfers as $transfer) {
        // تبديل لون الخلفية
        if ($altBg) {
            $pdf->SetFillColor(245, 245, 245);
        } else {
            $pdf->SetFillColor(255, 255, 255);
        }
        $altBg = !$altBg;
        
        // بيانات الصف
        $pdf->Cell($colWidth1, 8, $transfer['transaction_number'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth2, 8, $transfer['transfer_type'], 1, 0, 'C', 1);
        
        // التعامل مع النصوص الطويلة
        $beneficiaryName = $transfer['beneficiary_name'];
        if (strlen($beneficiaryName) > 20) {
            $beneficiaryName = substr($beneficiaryName, 0, 18) . '..';
        }
        $pdf->Cell($colWidth3, 8, $beneficiaryName, 1, 0, 'R', 1);
        
        $pdf->Cell($colWidth4, 8, $transfer['beneficiary_country'] ?? '', 1, 0, 'C', 1);
        $pdf->Cell($colWidth5, 8, number_format($transfer['sending_amount'], 2) . ' ' . $transfer['sending_currency_code'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth6, 8, number_format($transfer['receiving_amount'], 2) . ' ' . $transfer['receiving_currency_code'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth7, 8, number_format($transfer['profit'] ?? $transfer['calculated_profit'], 2), 1, 0, 'C', 1);
        $pdf->Cell($colWidth8, 8, $transfer['status'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth9, 8, $transfer['tracking_number'] ?? '', 1, 0, 'C', 1);
        $pdf->Cell($colWidth10, 8, date('Y-m-d', strtotime($transfer['created_at'])), 1, 1, 'C', 1);
    }

    // إضافة صف إجمالي الربح
    $pdf->SetFont('dejavusans', 'B', 10);
    $pdf->SetFillColor(240, 240, 240);
    
    // حساب العرض الإجمالي للأعمدة قبل الربح
    $totalWidth = $colWidth1 + $colWidth2 + $colWidth3 + $colWidth4 + $colWidth5 + $colWidth6;
    
    // حساب العرض الإجمالي للأعمدة بعد الربح
    $remainingWidth = $colWidth8 + $colWidth9 + $colWidth10;
    
    $pdf->Cell($totalWidth, 10, 'إجمالي الربح:', 1, 0, 'R', 1);
    $pdf->Cell($colWidth7, 10, number_format($totalProfit, 2), 1, 0, 'C', 1);
    $pdf->Cell($remainingWidth, 10, '', 1, 1, 'C', 1);

    // إضافة معلومات التذييل
    $pdf->SetFont('dejavusans', 'I', 8);
    $pdf->Cell(0, 10, 'تم إنشاء هذا التقرير بواسطة نظام TrustPlus بتاريخ ' . date('Y-m-d H:i'), 0, 1, 'C');

    // إخراج ملف PDF
    $pdf->Output('تقرير_الحوالات_' . date('Y-m-d') . '.pdf', 'D');
    exit;
}

// Export to Excel
if ($format === 'excel') {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="تقرير_الحوالات_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create Excel content
    echo '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>تقرير الحوالات</title>
        <style>
            table { direction: rtl; border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #000; padding: 5px; text-align: right; }
            th { background-color: #f0f0f0; }
            .total { font-weight: bold; background-color: #f0f0f0; }
        </style>
    </head>
    <body>
        <h1>تقرير الحوالات</h1>
        <p>' . $filterDescription . '</p>
        
        <table>
            <thead>
                <tr>
                    <th>رقم الحوالة</th>
                    <th>النوع</th>
                    <th>المستفيد</th>
                    <th>الدولة</th>
                    <th>المبلغ الأساسي</th>
                    <th>مبلغ التسليم</th>
                    <th>الربح</th>
                    <th>الحالة</th>
                    <th>رقم التتبع</th>
                    <th>الفرع</th>
                    <th>تاريخ الإنشاء</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($transfers as $transfer) {
        echo '<tr>
                <td>' . htmlspecialchars($transfer['transaction_number']) . '</td>
                <td>' . htmlspecialchars($transfer['transfer_type']) . '</td>
                <td>' . htmlspecialchars($transfer['beneficiary_name']) . '</td>
                <td>' . htmlspecialchars($transfer['beneficiary_country'] ?? '') . '</td>
                <td>' . number_format($transfer['sending_amount'], 2) . ' ' . htmlspecialchars($transfer['sending_currency_code']) . '</td>
                <td>' . number_format($transfer['receiving_amount'], 2) . ' ' . htmlspecialchars($transfer['receiving_currency_code']) . '</td>
                <td>' . number_format($transfer['profit'] ?? $transfer['calculated_profit'], 2) . '</td>
                <td>' . htmlspecialchars($transfer['status']) . '</td>
                <td>' . htmlspecialchars($transfer['tracking_number'] ?? '') . '</td>
                <td>' . htmlspecialchars($transfer['branch_name'] ?? '') . '</td>
                <td>' . date('Y-m-d', strtotime($transfer['created_at'])) . '</td>
            </tr>';
    }
    
    echo '</tbody>
            <tfoot>
                <tr class="total">
                    <td colspan="6" style="text-align: left;">إجمالي الربح:</td>
                    <td>' . number_format($totalProfit, 2) . '</td>
                    <td colspan="4"></td>
                </tr>
            </tfoot>
        </table>
    </body>
    </html>';
    
    exit;
}

// Fallback if no valid format is specified
redirect('all_transfers.php'); 
-- Simple fix for exchange_id columns - MariaDB compatible
-- This script safely adds missing columns without errors

-- Add exchange_id to cash_movements if it doesn't exist
ALTER TABLE cash_movements 
ADD COLUMN exchange_id INT UNSIGNED NULL AFTER user_id;

-- Add exchange_id to bank_movements if it doesn't exist  
ALTER TABLE bank_movements 
ADD COLUMN exchange_id INT UNSIGNED NULL AFTER user_id;

-- Add transfer_id to cash_movements if it doesn't exist
ALTER TABLE cash_movements 
ADD COLUMN transfer_id INT UNSIGNED NULL AFTER exchange_id;

-- Add transfer_id to bank_movements if it doesn't exist
ALTER TABLE bank_movements 
ADD COLUMN transfer_id INT UNSIGNED NULL AFTER exchange_id;

-- Add foreign key for cash_movements.exchange_id
ALTER TABLE cash_movements 
ADD CONSTRAINT fk_cmov_exchange 
FOREIGN KEY (exchange_id) REFERENCES exchanges(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Add foreign key for bank_movements.exchange_id
ALTER TABLE bank_movements 
ADD CONSTRAINT fk_bmov_exchange 
FOREIGN KEY (exchange_id) REFERENCES exchanges(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Update Operation Amounts
-- This script updates existing operation amounts to reflect the new calculation method

-- First, make sure we have the exchange rates
SELECT 'Updating operation amounts to reflect new calculation method' AS message;

-- For each operation, update the amount to be (base_amount - fee)
UPDATE office_operations op
JOIN office_exchange_rates er ON op.operation_name = er.operation_name AND op.office_id = er.office_id
SET op.amount = op.base_amount - (op.base_amount * er.exchange_rate_percentage)
WHERE op.base_amount > 0;

-- Update operations without matching exchange rates (keep the same amount)
UPDATE office_operations op
LEFT JOIN office_exchange_rates er ON op.operation_name = er.operation_name AND op.office_id = er.office_id
SET op.amount = op.base_amount
WHERE er.id IS NULL;

COMMIT; 
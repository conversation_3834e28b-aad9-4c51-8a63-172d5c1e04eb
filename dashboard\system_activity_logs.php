<?php
/**
 * صفحة عرض سجل العمليات الشامل للنظام
 * يمكن للأدمن فقط الوصول إلى هذه الصفحة
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/system_activity_manager.php';

$auth = new Auth();
$auth->requireLogin();

// التحقق من صلاحية الأدمن فقط
$currentUser = $auth->getCurrentUser();
if (!$currentUser || !isset($currentUser['role_id']) || (int)$currentUser['role_id'] !== 1) {
    header('HTTP/1.0 403 Forbidden');
    die('غير مسموح لك بالوصول إلى هذه الصفحة. هذه الصفحة مخصصة للأدمن فقط.');
}

$db = Database::getConnection();

// معالجة الفلاتر
$filters = [
    'user_id' => $_GET['user_id'] ?? '',
    'action_type' => $_GET['action_type'] ?? '',
    'module' => $_GET['module'] ?? '',
    'operation' => $_GET['operation'] ?? '',
    'status' => $_GET['status'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'search' => $_GET['search'] ?? ''
];

// إعداد الصفحات
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 50;
$offset = ($page - 1) * $limit;

// بناء الاستعلام
$whereConditions = [];
$params = [];
$types = '';

if (!empty($filters['user_id'])) {
    $whereConditions[] = "user_id = ?";
    $params[] = $filters['user_id'];
    $types .= 'i';
}

if (!empty($filters['action_type'])) {
    $whereConditions[] = "action_type = ?";
    $params[] = $filters['action_type'];
    $types .= 's';
}

if (!empty($filters['module'])) {
    $whereConditions[] = "module = ?";
    $params[] = $filters['module'];
    $types .= 's';
}

if (!empty($filters['operation'])) {
    $whereConditions[] = "operation LIKE ?";
    $params[] = '%' . $filters['operation'] . '%';
    $types .= 's';
}

if (!empty($filters['status'])) {
    $whereConditions[] = "status = ?";
    $params[] = $filters['status'];
    $types .= 's';
}

if (!empty($filters['date_from'])) {
    $whereConditions[] = "DATE(created_at) >= ?";
    $params[] = $filters['date_from'];
    $types .= 's';
}

if (!empty($filters['date_to'])) {
    $whereConditions[] = "DATE(created_at) <= ?";
    $params[] = $filters['date_to'];
    $types .= 's';
}

if (!empty($filters['search'])) {
    $whereConditions[] = "(description LIKE ? OR username LIKE ? OR user_full_name LIKE ?)";
    $searchTerm = '%' . $filters['search'] . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $types .= 'sss';
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// استعلام العد الإجمالي
$countSql = "SELECT COUNT(*) as total FROM system_activity_logs $whereClause";
$countStmt = $db->prepare($countSql);
if (!empty($params)) {
    $countStmt->bind_param($types, ...$params);
}
$countStmt->execute();
$totalRecords = $countStmt->get_result()->fetch_assoc()['total'];
$totalPages = ceil($totalRecords / $limit);

// استعلام البيانات
$sql = "SELECT * FROM system_activity_logs $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
$stmt = $db->prepare($sql);

$params[] = $limit;
$params[] = $offset;
$types .= 'ii';

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();
$logs = $result->fetch_all(MYSQLI_ASSOC);

// جلب قائمة المستخدمين للفلتر
$usersResult = $db->query("SELECT id, username, full_name FROM users ORDER BY username");
$users = $usersResult->fetch_all(MYSQLI_ASSOC);

// جلب قائمة الوحدات المتاحة
$modulesResult = $db->query("SELECT DISTINCT module FROM system_activity_logs ORDER BY module");
$modules = $modulesResult->fetch_all(MYSQLI_ASSOC);

// تسجيل عملية عرض السجل
$activityManager = new SystemActivityManager();
$activityManager->logView('system', 'view_activity_logs', 'عرض سجل العمليات الشامل', [
    'filters' => $filters,
    'page' => $page,
    'total_records' => $totalRecords
]);

include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-history"></i> سجل العمليات الشامل</h2>
        <div class="badge bg-info fs-6">إجمالي السجلات: <?php echo number_format($totalRecords); ?></div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-filter"></i> فلاتر البحث</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">المستخدم</label>
                    <select name="user_id" class="form-select">
                        <option value="">جميع المستخدمين</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?php echo $user['id']; ?>" <?php echo $filters['user_id'] == $user['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['username'] . ' - ' . $user['full_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">نوع العملية</label>
                    <select name="action_type" class="form-select">
                        <option value="">جميع الأنواع</option>
                        <option value="CREATE" <?php echo $filters['action_type'] == 'CREATE' ? 'selected' : ''; ?>>إنشاء</option>
                        <option value="UPDATE" <?php echo $filters['action_type'] == 'UPDATE' ? 'selected' : ''; ?>>تحديث</option>
                        <option value="DELETE" <?php echo $filters['action_type'] == 'DELETE' ? 'selected' : ''; ?>>حذف</option>
                        <option value="VIEW" <?php echo $filters['action_type'] == 'VIEW' ? 'selected' : ''; ?>>عرض</option>
                        <option value="LOGIN" <?php echo $filters['action_type'] == 'LOGIN' ? 'selected' : ''; ?>>دخول</option>
                        <option value="LOGOUT" <?php echo $filters['action_type'] == 'LOGOUT' ? 'selected' : ''; ?>>خروج</option>
                        <option value="TRANSFER" <?php echo $filters['action_type'] == 'TRANSFER' ? 'selected' : ''; ?>>تحويل</option>
                        <option value="EXCHANGE" <?php echo $filters['action_type'] == 'EXCHANGE' ? 'selected' : ''; ?>>صرافة</option>
                        <option value="ERROR" <?php echo $filters['action_type'] == 'ERROR' ? 'selected' : ''; ?>>خطأ</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">الوحدة</label>
                    <select name="module" class="form-select">
                        <option value="">جميع الوحدات</option>
                        <?php foreach ($modules as $module): ?>
                            <option value="<?php echo htmlspecialchars($module['module']); ?>" <?php echo $filters['module'] == $module['module'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($module['module']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="SUCCESS" <?php echo $filters['status'] == 'SUCCESS' ? 'selected' : ''; ?>>نجح</option>
                        <option value="FAILED" <?php echo $filters['status'] == 'FAILED' ? 'selected' : ''; ?>>فشل</option>
                        <option value="PARTIAL" <?php echo $filters['status'] == 'PARTIAL' ? 'selected' : ''; ?>>جزئي</option>
                        <option value="PENDING" <?php echo $filters['status'] == 'PENDING' ? 'selected' : ''; ?>>معلق</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">العملية</label>
                    <input type="text" name="operation" class="form-control" placeholder="اسم العملية..." value="<?php echo htmlspecialchars($filters['operation']); ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($filters['date_from']); ?>">
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($filters['date_to']); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">البحث النصي</label>
                    <input type="text" name="search" class="form-control" placeholder="البحث في الوصف أو المستخدم..." value="<?php echo htmlspecialchars($filters['search']); ?>">
                </div>
                
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i> بحث</button>
                    <a href="system_activity_logs.php" class="btn btn-secondary"><i class="fas fa-times"></i> مسح الفلاتر</a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول السجلات -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>المستخدم</th>
                            <th>نوع العملية</th>
                            <th>الوحدة</th>
                            <th>العملية</th>
                            <th>الوصف</th>
                            <th>الحالة</th>
                            <th>IP</th>
                            <th>التفاصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($logs)): ?>
                            <tr>
                                <td colspan="9" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i><br>
                                    لا توجد سجلات تطابق معايير البحث
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d', strtotime($log['created_at'])); ?><br>
                                            <?php echo date('H:i:s', strtotime($log['created_at'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($log['username'] ?? 'N/A'); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($log['user_full_name'] ?? ''); ?></small>
                                    </td>
                                    <td>
                                        <?php
                                        $actionColors = [
                                            'CREATE' => 'success',
                                            'UPDATE' => 'warning',
                                            'DELETE' => 'danger',
                                            'VIEW' => 'info',
                                            'LOGIN' => 'primary',
                                            'LOGOUT' => 'secondary',
                                            'TRANSFER' => 'success',
                                            'EXCHANGE' => 'warning',
                                            'ERROR' => 'danger'
                                        ];
                                        $color = $actionColors[$log['action_type']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?>"><?php echo htmlspecialchars($log['action_type']); ?></span>
                                    </td>
                                    <td><span class="badge bg-light text-dark"><?php echo htmlspecialchars($log['module']); ?></span></td>
                                    <td><small><?php echo htmlspecialchars($log['operation']); ?></small></td>
                                    <td>
                                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                            <?php echo htmlspecialchars(substr($log['description'], 0, 100)); ?>
                                            <?php if (strlen($log['description']) > 100): ?>...<?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $statusColors = [
                                            'SUCCESS' => 'success',
                                            'FAILED' => 'danger',
                                            'PARTIAL' => 'warning',
                                            'PENDING' => 'info'
                                        ];
                                        $statusColor = $statusColors[$log['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $statusColor; ?>"><?php echo htmlspecialchars($log['status']); ?></span>
                                    </td>
                                    <td><small><?php echo htmlspecialchars($log['ip_address'] ?? 'N/A'); ?></small></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showLogDetails(<?php echo $log['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- الصفحات -->
    <?php if ($totalPages > 1): ?>
        <nav aria-label="صفحات السجلات" class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $page - 1])); ?>">السابق</a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $i])); ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($filters, ['page' => $page + 1])); ?>">التالي</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    <?php endif; ?>
</main>

<!-- Modal لعرض تفاصيل السجل -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل السجل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showLogDetails(logId) {
    const modal = new bootstrap.Modal(document.getElementById('logDetailsModal'));
    const content = document.getElementById('logDetailsContent');
    
    // إظهار المودال مع مؤشر التحميل
    modal.show();
    
    // جلب التفاصيل
    fetch(`ajax/get_log_details.php?id=${logId}`)
        .then(response => response.text())
        .then(data => {
            content.innerHTML = data;
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">خطأ في تحميل التفاصيل</div>';
        });
}
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>

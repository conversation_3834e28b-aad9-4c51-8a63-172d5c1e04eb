<?php
/**
 * تعريب جميع أوصاف الصلاحيات من الإنجليزية إلى العربية
 * Translate All Permission Descriptions from English to Arabic
 */

// منع الوصول المباشر
if (!defined('INSTALL_MODE')) {
    define('INSTALL_MODE', true);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// إعداد الترميز
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تعريب أوصاف الصلاحيات - نظام TrustPlus</title>
    <style>
        body { font-family: '<PERSON><PERSON><PERSON> U<PERSON>', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .permission-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 15px; margin: 20px 0; }
        .permission-card { background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }
        .permission-name { font-weight: bold; color: #0d6efd; font-family: monospace; }
        .permission-desc-old { color: #dc3545; text-decoration: line-through; }
        .permission-desc-new { color: #28a745; font-weight: bold; }
        h1, h2, h3 { color: #333; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🌐 تعريب أوصاف الصلاحيات إلى العربية</h1>";
echo "<p>هذا الملف سيقوم بتعريب جميع أوصاف الصلاحيات من الإنجليزية إلى العربية لتحسين تجربة المستخدم.</p>";

try {
    // الاتصال بقاعدة البيانات
    $db = new Database();
    $conn = Database::getConnection();
    
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";

    // عرض الصلاحيات قبل التعريب
    echo "<h2>📋 الصلاحيات قبل التعريب</h2>";
    $beforeResult = $conn->query("SELECT name, description, module FROM permissions WHERE description REGEXP '[a-zA-Z]' ORDER BY module, name LIMIT 10");
    if ($beforeResult && $beforeResult->num_rows > 0) {
        echo "<div class='permission-grid'>";
        while ($row = $beforeResult->fetch_assoc()) {
            echo "<div class='permission-card'>";
            echo "<div class='permission-name'>" . htmlspecialchars($row['name']) . "</div>";
            echo "<div class='permission-desc-old'>" . htmlspecialchars($row['description']) . "</div>";
            echo "<div style='color: #6c757d; font-size: 0.8em;'>" . htmlspecialchars($row['module']) . "</div>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<div class='info'>📋 جميع الصلاحيات معربة بالفعل أو لا توجد صلاحيات</div>";
    }

    // قراءة ملف التعريب
    $sqlFile = __DIR__ . '/translate_permissions_to_arabic.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception('ملف التعريب غير موجود: ' . $sqlFile);
    }
    
    $sqlContent = file_get_contents($sqlFile);
    if ($sqlContent === false) {
        throw new Exception('فشل في قراءة ملف التعريب');
    }
    
    echo "<div class='info'>📄 تم قراءة ملف التعريب بنجاح</div>";

    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sqlContent)));
    
    $updatedPermissions = 0;
    $updatedModules = 0;
    $errors = [];
    
    echo "<h2>🔄 تنفيذ التعريب...</h2>";
    
    foreach ($queries as $query) {
        if (!empty($query) && !preg_match('/^--/', $query) && !preg_match('/^SELECT/', $query)) {
            try {
                if ($conn->query($query)) {
                    if (stripos($query, 'UPDATE permissions SET description') !== false) {
                        $updatedPermissions += $conn->affected_rows;
                    } elseif (stripos($query, 'UPDATE permissions SET module') !== false) {
                        $updatedModules += $conn->affected_rows;
                    }
                } else {
                    $errors[] = 'خطأ في الاستعلام: ' . $conn->error;
                }
            } catch (Exception $e) {
                $errors[] = 'استثناء: ' . $e->getMessage();
            }
        }
    }
    
    // عرض النتائج
    echo "<h2>📊 نتائج التعريب</h2>";
    
    echo "<div class='stats'>";
    echo "<div class='stat-card'>";
    echo "<div class='stat-number'>$updatedPermissions</div>";
    echo "<div class='stat-label'>وصف صلاحية معرب</div>";
    echo "</div>";
    
    echo "<div class='stat-card' style='background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);'>";
    echo "<div class='stat-number'>$updatedModules</div>";
    echo "<div class='stat-label'>وحدة معربة</div>";
    echo "</div>";
    
    echo "<div class='stat-card' style='background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);'>";
    echo "<div class='stat-number'>" . count($errors) . "</div>";
    echo "<div class='stat-label'>أخطاء</div>";
    echo "</div>";
    echo "</div>";
    
    if (!empty($errors)) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ تحذيرات وأخطاء:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>" . htmlspecialchars($error) . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // عرض الصلاحيات بعد التعريب
    echo "<h2>✅ الصلاحيات بعد التعريب</h2>";
    $afterResult = $conn->query("SELECT name, description, module FROM permissions ORDER BY module, name LIMIT 20");
    if ($afterResult && $afterResult->num_rows > 0) {
        echo "<div class='permission-grid'>";
        while ($row = $afterResult->fetch_assoc()) {
            echo "<div class='permission-card'>";
            echo "<div class='permission-name'>" . htmlspecialchars($row['name']) . "</div>";
            echo "<div class='permission-desc-new'>" . htmlspecialchars($row['description']) . "</div>";
            echo "<div style='color: #6c757d; font-size: 0.8em;'>" . htmlspecialchars($row['module']) . "</div>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    // إحصائيات مفصلة
    echo "<h2>📈 إحصائيات مفصلة</h2>";
    
    // إحصائيات الوحدات
    $moduleStats = $conn->query("SELECT module, COUNT(*) as count FROM permissions GROUP BY module ORDER BY count DESC");
    if ($moduleStats) {
        echo "<table>";
        echo "<tr><th>الوحدة</th><th>عدد الصلاحيات</th></tr>";
        while ($row = $moduleStats->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['module']) . "</td>";
            echo "<td>" . $row['count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // إجمالي الصلاحيات
    $totalResult = $conn->query("SELECT COUNT(*) as total FROM permissions");
    $total = $totalResult->fetch_assoc()['total'];
    
    echo "<div class='success'>";
    echo "<h3>🎉 تم إنجاز التعريب بنجاح!</h3>";
    echo "<p>تم تعريب أوصاف الصلاحيات ووحداتها إلى العربية.</p>";
    echo "<p><strong>إجمالي الصلاحيات:</strong> $total صلاحية</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في التعريب</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>انتقل إلى صفحة الصلاحيات: <a href='../dashboard/permissions.php' target='_blank'>dashboard/permissions.php</a></li>";
echo "<li>تحقق من ظهور جميع الأوصاف باللغة العربية</li>";
echo "<li>تأكد من تعريب أسماء الوحدات في الفلاتر</li>";
echo "<li>اختبر البحث والفلترة للتأكد من عملها بشكل صحيح</li>";
echo "</ol>";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>📝 ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>تم تعريب الأوصاف والوحدات لتحسين تجربة المستخدم العربي</li>";
echo "<li>أسماء الصلاحيات (name) تبقى بالإنجليزية للتوافق مع الكود</li>";
echo "<li>يمكن إضافة المزيد من الترجمات حسب الحاجة</li>";
echo "<li>تأكد من عمل نسخة احتياطية قبل تطبيق أي تغييرات</li>";
echo "</ul>";
echo "</div>";

echo "</div></body></html>";
?>

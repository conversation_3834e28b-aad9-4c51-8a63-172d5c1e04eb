<?php
/**
 * WithdrawalManager – handles withdrawal operations
 */
require_once __DIR__ . '/database.php';

class WithdrawalManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Add new withdrawal record
     */
    public function addWithdrawal(array $data): int|false
    {
        $sql = 'INSERT INTO withdrawals (amount, beneficiary, commission, net_amount, status, created_by, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?)';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $stmt->bind_param(
            'dsddsis',
            $data['amount'],
            $data['beneficiary'],
            $data['commission'],
            $data['net_amount'],
            $data['status'],
            $data['created_by'],
            $data['notes']
        );

        if ($stmt->execute()) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        $stmt->close();
        return false;
    }

    /**
     * Get all withdrawals with optional filters
     */
    public function getWithdrawals(array $filters = []): array
    {
        $sql = 'SELECT w.*, u.full_name as created_by_name 
                FROM withdrawals w 
                LEFT JOIN users u ON u.id = w.created_by 
                WHERE 1=1';
        $params = [];
        $types = '';

        if (!empty($filters['search'])) {
            $sql .= ' AND (w.beneficiary LIKE ? OR w.notes LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm]);
            $types .= 'ss';
        }

        if (!empty($filters['status']) && in_array($filters['status'], ['معلق', 'مكتمل'], true)) {
            $sql .= ' AND w.status = ?';
            $params[] = $filters['status'];
            $types .= 's';
        }

        if (!empty($filters['date_from'])) {
            $sql .= ' AND DATE(w.created_at) >= ?';
            $params[] = $filters['date_from'];
            $types .= 's';
        }

        if (!empty($filters['date_to'])) {
            $sql .= ' AND DATE(w.created_at) <= ?';
            $params[] = $filters['date_to'];
            $types .= 's';
        }

        $sql .= ' ORDER BY w.created_at DESC';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];

        if ($params) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $res = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    /**
     * Get withdrawal by ID
     */
    public function getWithdrawalById(int $id): ?array
    {
        $sql = 'SELECT w.*, u.full_name as created_by_name 
                FROM withdrawals w 
                LEFT JOIN users u ON u.id = w.created_by 
                WHERE w.id = ? LIMIT 1';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return null;

        $stmt->bind_param('i', $id);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    /**
     * Update withdrawal
     */
    public function updateWithdrawal(int $id, array $data): bool
    {
        $sql = 'UPDATE withdrawals SET amount=?, beneficiary=?, commission=?, net_amount=?, status=?, notes=? WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $stmt->bind_param('dsddsis',
            $data['amount'],
            $data['beneficiary'],
            $data['commission'],
            $data['net_amount'],
            $data['status'],
            $data['notes'],
            $id
        );

        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Delete withdrawal
     */
    public function deleteWithdrawal(int $id): bool
    {
        $stmt = $this->db->prepare('DELETE FROM withdrawals WHERE id = ?');
        if (!$stmt) return false;

        $stmt->bind_param('i', $id);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Mark withdrawal as received
     */
    public function markAsReceived(int $id): bool
    {
        $stmt = $this->db->prepare('UPDATE withdrawals SET status = ? WHERE id = ?');
        if (!$stmt) return false;

        $status = 'مستلمة';
        $stmt->bind_param('si', $status, $id);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Calculate net amount
     */
    public function calculateNetAmount(float $amount, float $commission): float
    {
        return ($amount - 5) * (1 - $commission / 100);
    }

    /**
     * Get total amount by status
     */
    public function getTotalAmountByStatus(string $status = null): float
    {
        $sql = 'SELECT SUM(amount) as total FROM withdrawals';
        $params = [];
        $types = '';

        if ($status) {
            $sql .= ' WHERE status = ?';
            $params[] = $status;
            $types = 's';
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 0;

        if ($params) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc();
        $stmt->close();
        return (float)($row['total'] ?? 0);
    }

    /**
     * Get total net amount by status
     */
    public function getTotalNetAmountByStatus(string $status = null): float
    {
        $sql = 'SELECT SUM(net_amount) as total FROM withdrawals';
        $params = [];
        $types = '';

        if ($status) {
            $sql .= ' WHERE status = ?';
            $params[] = $status;
            $types = 's';
        }

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 0;

        if ($params) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc();
        $stmt->close();
        return (float)($row['total'] ?? 0);
    }

    /**
     * Get withdrawal statistics
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_amount' => $this->getTotalAmountByStatus(),
            'total_net_amount' => $this->getTotalNetAmountByStatus(),
            'pending_amount' => $this->getTotalAmountByStatus('معلق'),
            'pending_net_amount' => $this->getTotalNetAmountByStatus('معلق'),
            'completed_amount' => $this->getTotalAmountByStatus('مكتمل'),
            'completed_net_amount' => $this->getTotalNetAmountByStatus('مكتمل'),
            'received_amount' => $this->getTotalAmountByStatus('مستلمة'),
            'received_net_amount' => $this->getTotalNetAmountByStatus('مستلمة'),
            'total_count' => 0,
            'pending_count' => 0,
            'completed_count' => 0,
            'received_count' => 0
        ];

        // Get counts
        $sql = 'SELECT status, COUNT(*) as count FROM withdrawals GROUP BY status';
        $res = $this->db->query($sql);
        if ($res) {
            while ($row = $res->fetch_assoc()) {
                $stats['total_count'] += $row['count'];
                if ($row['status'] === 'معلق') {
                    $stats['pending_count'] = $row['count'];
                } elseif ($row['status'] === 'مكتمل') {
                    $stats['completed_count'] = $row['count'];
                } elseif ($row['status'] === 'مستلمة') {
                    $stats['received_count'] = $row['count'];
                }
            }
        }

        return $stats;
    }
} 
<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/permission_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$current_user = $auth->getCurrentUser();

$pageTitle = 'الصلاحيات';

// Permission check
if (!$auth->hasPermission('permissions.view')) {
    include __DIR__ . '/../includes/header.php';
    echo '<main class="content p-4"><div class="alert alert-danger">تم رفض الوصول</div></main>';
    include __DIR__ . '/../includes/footer.php';
    exit;
}

$db           = new Database();
$permManager  = new PermissionManager($db);
$permissions  = $permManager->getAllPermissions();

// تسجيل عملية عرض قائمة الصلاحيات
ActivityHelper::logView('permissions', 'عرض قائمة الصلاحيات');

include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-shield-alt text-primary me-2"></i>
            إدارة الصلاحيات
        </h2>
        <div class="d-flex gap-2">
            <?php if ($auth->hasPermission('permissions.create')): ?>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPermissionModal">
                <i class="fas fa-plus me-1"></i>
                إضافة صلاحية جديدة
            </button>
            <?php endif; ?>
            <button class="btn btn-info" onclick="exportPermissions()">
                <i class="fas fa-download me-1"></i>
                تصدير الصلاحيات
            </button>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <?php
        $moduleStats = [];
        $totalPermissions = count($permissions);
        foreach ($permissions as $perm) {
            $module = $perm['module'] ?? 'غير محدد';
            $moduleStats[$module] = ($moduleStats[$module] ?? 0) + 1;
        }
        $totalModules = count($moduleStats);
        ?>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $totalPermissions; ?></h4>
                            <p class="mb-0">إجمالي الصلاحيات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $totalModules; ?></h4>
                            <p class="mb-0">عدد الوحدات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cubes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo max($moduleStats); ?></h4>
                            <p class="mb-0">أكبر وحدة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo array_search(max($moduleStats), $moduleStats); ?></h4>
                            <p class="mb-0">الوحدة الأكثر صلاحيات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-crown fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر البحث والتصفية
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <label for="moduleFilter" class="form-label">تصفية حسب الوحدة:</label>
                    <select id="moduleFilter" class="form-select">
                        <option value="">جميع الوحدات</option>
                        <?php foreach (array_keys($moduleStats) as $module): ?>
                        <option value="<?php echo htmlspecialchars($module); ?>">
                            <?php echo getModuleNameInArabic($module); ?> (<?php echo $moduleStats[$module]; ?>)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="searchInput" class="form-label">البحث في الصلاحيات:</label>
                    <input type="text" id="searchInput" class="form-control" placeholder="ابحث في اسم أو وصف الصلاحية...">
                </div>
                <div class="col-md-4">
                    <label for="actionFilter" class="form-label">تصفية حسب نوع العملية:</label>
                    <select id="actionFilter" class="form-select">
                        <option value="">جميع العمليات</option>
                        <option value="view">عرض</option>
                        <option value="create">إنشاء</option>
                        <option value="edit">تعديل</option>
                        <option value="delete">حذف</option>
                        <option value="export">تصدير</option>
                        <option value="admin">إدارة</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الصلاحيات -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة الصلاحيات
                <span id="permissionCount" class="badge bg-primary ms-2"><?php echo $totalPermissions; ?></span>
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="permissionsTable">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 60px;">#</th>
                            <th style="width: 200px;">اسم الصلاحية</th>
                            <th>الوصف</th>
                            <th style="width: 120px;">الوحدة</th>
                            <th style="width: 100px;">نوع العملية</th>
                            <?php if ($auth->hasPermission('permissions.edit') || $auth->hasPermission('permissions.delete')): ?>
                            <th style="width: 120px;">الإجراءات</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($permissions as $p):
                            $actionType = getActionType($p['name']);
                            $moduleNameAr = getModuleNameInArabic($p['module']);
                        ?>
                        <tr data-module="<?php echo htmlspecialchars($p['module']); ?>" data-action="<?php echo $actionType; ?>">
                            <td>
                                <span class="badge bg-secondary"><?php echo $p['id']; ?></span>
                            </td>
                            <td>
                                <code class="text-primary"><?php echo htmlspecialchars($p['name']); ?></code>
                            </td>
                            <td>
                                <span class="text-dark"><?php echo htmlspecialchars($p['description']); ?></span>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo $moduleNameAr; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo getActionBadgeColor($actionType); ?>">
                                    <?php echo getActionNameInArabic($actionType); ?>
                                </span>
                            </td>
                            <?php if ($auth->hasPermission('permissions.edit') || $auth->hasPermission('permissions.delete')): ?>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <?php if ($auth->hasPermission('permissions.edit')): ?>
                                    <button class="btn btn-outline-primary btn-sm" onclick="editPermission(<?php echo $p['id']; ?>)" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php endif; ?>
                                    <?php if ($auth->hasPermission('permissions.delete')): ?>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deletePermission(<?php echo $p['id']; ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <?php endif; ?>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</main>

<?php
// دوال مساعدة لتعريب أسماء الوحدات
function getModuleNameInArabic($module) {
    $moduleNames = [
        'users' => 'المستخدمين',
        'roles' => 'الأدوار',
        'permissions' => 'الصلاحيات',
        'customers' => 'العملاء',
        'transfers' => 'الحوالات',
        'offices' => 'المكاتب',
        'paypal' => 'PayPal',
        'exchange' => 'الصرافة',
        'currencies' => 'العملات',
        'cash' => 'الصناديق',
        'banks' => 'البنوك',
        'reports' => 'التقارير',
        'system' => 'النظام',
        'branches' => 'الفروع',
        'audit' => 'المراجعة',
        'notifications' => 'الإشعارات'
    ];
    return $moduleNames[$module] ?? $module;
}

// دالة لاستخراج نوع العملية من اسم الصلاحية
function getActionType($permissionName) {
    if (strpos($permissionName, '.view') !== false) return 'view';
    if (strpos($permissionName, '.create') !== false) return 'create';
    if (strpos($permissionName, '.edit') !== false) return 'edit';
    if (strpos($permissionName, '.delete') !== false) return 'delete';
    if (strpos($permissionName, '.export') !== false) return 'export';
    if (strpos($permissionName, '.admin') !== false) return 'admin';
    return 'other';
}

// دالة لتعريب أسماء العمليات
function getActionNameInArabic($action) {
    $actionNames = [
        'view' => 'عرض',
        'create' => 'إنشاء',
        'edit' => 'تعديل',
        'delete' => 'حذف',
        'export' => 'تصدير',
        'admin' => 'إدارة',
        'other' => 'أخرى'
    ];
    return $actionNames[$action] ?? $action;
}

// دالة لتحديد لون الشارة حسب نوع العملية
function getActionBadgeColor($action) {
    $colors = [
        'view' => 'primary',
        'create' => 'success',
        'edit' => 'warning',
        'delete' => 'danger',
        'export' => 'info',
        'admin' => 'dark',
        'other' => 'secondary'
    ];
    return $colors[$action] ?? 'secondary';
}
?>

<!-- Modal إضافة صلاحية جديدة -->
<?php if ($auth->hasPermission('permissions.create')): ?>
<div class="modal fade" id="addPermissionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة صلاحية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPermissionForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="permissionName" class="form-label">اسم الصلاحية</label>
                        <input type="text" class="form-control" id="permissionName" required
                               placeholder="مثال: users.view">
                    </div>
                    <div class="mb-3">
                        <label for="permissionDescription" class="form-label">وصف الصلاحية</label>
                        <input type="text" class="form-control" id="permissionDescription" required
                               placeholder="مثال: عرض قائمة المستخدمين">
                    </div>
                    <div class="mb-3">
                        <label for="permissionModule" class="form-label">الوحدة</label>
                        <select class="form-select" id="permissionModule" required>
                            <option value="">اختر الوحدة</option>
                            <option value="users">المستخدمين</option>
                            <option value="roles">الأدوار</option>
                            <option value="permissions">الصلاحيات</option>
                            <option value="customers">العملاء</option>
                            <option value="transfers">الحوالات</option>
                            <option value="offices">المكاتب</option>
                            <option value="paypal">PayPal</option>
                            <option value="exchange">الصرافة</option>
                            <option value="currencies">العملات</option>
                            <option value="cash">الصناديق</option>
                            <option value="banks">البنوك</option>
                            <option value="reports">التقارير</option>
                            <option value="system">النظام</option>
                            <option value="branches">الفروع</option>
                            <option value="audit">المراجعة</option>
                            <option value="notifications">الإشعارات</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة الصلاحية</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // فلترة حسب الوحدة
    document.getElementById('moduleFilter').addEventListener('change', function() {
        filterTable();
    });

    // البحث في النص
    document.getElementById('searchInput').addEventListener('input', function() {
        filterTable();
    });

    // فلترة حسب نوع العملية
    document.getElementById('actionFilter').addEventListener('change', function() {
        filterTable();
    });

    function filterTable() {
        const moduleFilter = document.getElementById('moduleFilter').value;
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        const actionFilter = document.getElementById('actionFilter').value;
        const rows = document.querySelectorAll('#permissionsTable tbody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const module = row.dataset.module;
            const action = row.dataset.action;
            const text = row.textContent.toLowerCase();

            let showRow = true;

            // فلترة الوحدة
            if (moduleFilter && module !== moduleFilter) {
                showRow = false;
            }

            // فلترة البحث
            if (searchInput && !text.includes(searchInput)) {
                showRow = false;
            }

            // فلترة نوع العملية
            if (actionFilter && action !== actionFilter) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleCount++;
        });

        // تحديث عداد الصلاحيات
        document.getElementById('permissionCount').textContent = visibleCount;
    }
});

// دالة تصدير الصلاحيات
function exportPermissions() {
    const table = document.getElementById('permissionsTable');
    const rows = Array.from(table.querySelectorAll('tbody tr')).filter(row => row.style.display !== 'none');

    let csv = 'الرقم,اسم الصلاحية,الوصف,الوحدة,نوع العملية\n';

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        const data = [
            cells[0].textContent.trim(),
            cells[1].textContent.trim(),
            cells[2].textContent.trim(),
            cells[3].textContent.trim(),
            cells[4].textContent.trim()
        ];
        csv += data.join(',') + '\n';
    });

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'permissions_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}

// دالة تعديل الصلاحية
function editPermission(id) {
    // يمكن تطوير هذه الدالة لاحقاً
    alert('ميزة التعديل قيد التطوير');
}

// دالة حذف الصلاحية
function deletePermission(id) {
    if (confirm('هل أنت متأكد من حذف هذه الصلاحية؟')) {
        // يمكن تطوير هذه الدالة لاحقاً
        alert('ميزة الحذف قيد التطوير');
    }
}

// معالج إضافة صلاحية جديدة
<?php if ($auth->hasPermission('permissions.create')): ?>
document.getElementById('addPermissionForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // تعطيل الزر وإظهار التحميل
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإضافة...';

    const formData = new FormData();
    formData.append('name', document.getElementById('permissionName').value);
    formData.append('description', document.getElementById('permissionDescription').value);
    formData.append('module', document.getElementById('permissionModule').value);

    fetch('ajax/add_permission.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('addPermissionModal'));
            modal.hide();

            // إعادة تعيين النموذج
            document.getElementById('addPermissionForm').reset();

            // إظهار رسالة نجاح
            showAlert('success', data.message);

            // إعادة تحميل الصفحة لإظهار الصلاحية الجديدة
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
            if (data.errors) {
                data.errors.forEach(error => {
                    showAlert('warning', error);
                });
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// دالة إظهار التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
<?php endif; ?>
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
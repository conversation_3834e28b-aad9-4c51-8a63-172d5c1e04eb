<?php
/**
 * Trust Plus - إعداد نظام المعاملات اليومية
 * Daily Transactions System Setup
 */

require_once __DIR__ . '/../config/database.php';

// الاتصال بقاعدة البيانات
try {
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    $mysqli->set_charset(DB_CHARSET);
    
    if ($mysqli->connect_error) {
        throw new Exception('فشل الاتصال بقاعدة البيانات: ' . $mysqli->connect_error);
    }
    
    echo "<h2>إعداد نظام المعاملات اليومية</h2>";
    echo "<hr>";
    
    // قراءة ملف SQL
    $sqlFile = __DIR__ . '/create_daily_transactions_system.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception('ملف SQL غير موجود: ' . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception('فشل في قراءة ملف SQL');
    }
    
    // تنفيذ الاستعلامات
    echo "<p>جاري تنفيذ استعلامات إنشاء الجداول...</p>";
    
    if ($mysqli->multi_query($sql)) {
        $queryCount = 0;
        do {
            $queryCount++;
            if ($result = $mysqli->store_result()) {
                $result->free();
            }
            
            if ($mysqli->error) {
                echo "<div style='color: orange;'>تحذير في الاستعلام رقم {$queryCount}: " . $mysqli->error . "</div>";
            }
            
        } while ($mysqli->more_results() && $mysqli->next_result());
        
        echo "<div style='color: green;'>✓ تم تنفيذ {$queryCount} استعلام بنجاح</div>";
    } else {
        throw new Exception('فشل في تنفيذ الاستعلامات: ' . $mysqli->error);
    }
    
    // التحقق من إنشاء الجداول
    echo "<h3>التحقق من الجداول المنشأة:</h3>";
    
    $tables = ['countries', 'daily_transactions', 'daily_transaction_history'];
    foreach ($tables as $table) {
        $result = $mysqli->query("SHOW TABLES LIKE '{$table}'");
        if ($result && $result->num_rows > 0) {
            echo "<div style='color: green;'>✓ جدول {$table} تم إنشاؤه بنجاح</div>";
            
            // عرض عدد السجلات
            $countResult = $mysqli->query("SELECT COUNT(*) as count FROM {$table}");
            if ($countResult) {
                $count = $countResult->fetch_assoc()['count'];
                echo "<div style='margin-left: 20px; color: blue;'>عدد السجلات: {$count}</div>";
            }
        } else {
            echo "<div style='color: red;'>✗ فشل في إنشاء جدول {$table}</div>";
        }
    }
    
    // التحقق من الصلاحيات
    echo "<h3>التحقق من الصلاحيات:</h3>";
    $permResult = $mysqli->query("SELECT COUNT(*) as count FROM permissions WHERE module = 'daily_transactions'");
    if ($permResult) {
        $permCount = $permResult->fetch_assoc()['count'];
        echo "<div style='color: green;'>✓ تم إضافة {$permCount} صلاحية لنظام المعاملات اليومية</div>";
    }
    
    // عرض بنية جدول المعاملات اليومية
    echo "<h3>بنية جدول المعاملات اليومية:</h3>";
    $structureResult = $mysqli->query("DESCRIBE daily_transactions");
    if ($structureResult) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>اسم الحقل</th><th>نوع البيانات</th><th>يمكن أن يكون فارغ</th><th>مفتاح</th><th>القيمة الافتراضية</th><th>إضافي</th>";
        echo "</tr>";
        
        while ($row = $structureResult->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['Field']}</td>";
            echo "<td>{$row['Type']}</td>";
            echo "<td>{$row['Null']}</td>";
            echo "<td>{$row['Key']}</td>";
            echo "<td>{$row['Default']}</td>";
            echo "<td>{$row['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>تم إعداد نظام المعاملات اليومية بنجاح!</h3>";
    echo "<p><strong>الخطوات التالية:</strong></p>";
    echo "<ul>";
    echo "<li>يمكنك الآن إنشاء صفحات إضافة وعرض المعاملات</li>";
    echo "<li>تأكد من إعطاء الصلاحيات المناسبة للمستخدمين</li>";
    echo "<li>يمكنك إضافة المزيد من الدول والعملات حسب الحاجة</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</div>";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام المعاملات اليومية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        h2, h3 {
            color: #333;
        }
        table {
            margin: 10px 0;
            font-size: 12px;
        }
        th, td {
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f8f9fa;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>

<?php
/**
 * إعداد مبسط لنظام المعاملات اليومية
 * Simple Setup for Daily Transactions System
 */

require_once __DIR__ . '/../config/database.php';

// الاتصال بقاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<h2>إعداد نظام المعاملات اليومية</h2>";
    echo "<p style='color: green;'>✓ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // تنفيذ ملفات SQL
    $sql_files = [
        'create_daily_transactions_system.sql' => 'إنشاء الجداول الأساسية',
        'additional_countries_data.sql' => 'إضافة بيانات الدول والعملات',
        'daily_transactions_procedures.sql' => 'إنشاء الإجراءات المخزنة'
    ];
    
    foreach ($sql_files as $file => $description) {
        $file_path = __DIR__ . '/' . $file;
        
        if (file_exists($file_path)) {
            echo "<h3>{$description}</h3>";
            
            $sql = file_get_contents($file_path);
            
            // تقسيم الاستعلامات
            $queries = explode(';', $sql);
            $success_count = 0;
            $error_count = 0;
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (empty($query) || strpos($query, '--') === 0) {
                    continue;
                }
                
                try {
                    $pdo->exec($query);
                    $success_count++;
                } catch (PDOException $e) {
                    // تجاهل أخطاء "already exists"
                    if (strpos($e->getMessage(), 'already exists') === false && 
                        strpos($e->getMessage(), 'Duplicate') === false) {
                        echo "<p style='color: orange;'>تحذير: " . $e->getMessage() . "</p>";
                        $error_count++;
                    }
                }
            }
            
            echo "<p style='color: green;'>✓ تم تنفيذ {$success_count} استعلام";
            if ($error_count > 0) {
                echo " مع {$error_count} تحذير";
            }
            echo "</p>";
            
        } else {
            echo "<p style='color: red;'>✗ ملف غير موجود: {$file}</p>";
        }
    }
    
    // التحقق من النتائج
    echo "<h3>التحقق من النتائج:</h3>";
    
    // فحص الجداول
    $tables = ['countries', 'daily_transactions', 'daily_transaction_history', 'deleted_daily_transactions'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $stmt->fetch()['count'];
            echo "<p style='color: green;'>✓ جدول {$table}: {$count} سجل</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ جدول {$table}: غير موجود</p>";
        }
    }
    
    // فحص الإجراءات المخزنة
    $procedures = ['CreateDailyTransaction', 'SearchDailyTransactions', 'DeleteDailyTransaction'];
    foreach ($procedures as $procedure) {
        try {
            $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = '{$procedure}' AND Db = '" . DB_NAME . "'");
            $exists = $stmt->rowCount() > 0;
            echo "<p style='color: " . ($exists ? 'green' : 'red') . ";'>" . 
                 ($exists ? '✓' : '✗') . " إجراء {$procedure}</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ إجراء {$procedure}: خطأ</p>";
        }
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>تم الإعداد بنجاح!</h3>";
    echo "<p><a href='test_daily_transactions_system.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار النظام</a></p>";
    echo "<p><a href='../dashboard/daily_transactions.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى المعاملات اليومية</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>خطأ في الإعداد</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام المعاملات اليومية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- المحتوى يتم عرضه من PHP أعلاه -->
    </div>
</body>
</html>

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paypal_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('paypal.incoming.view');

$paypalMgr = new PayPalManager(new Database());

// Build filters from GET
$filters = [
    'search' => $_GET['search'] ?? '',
    'status' => $_GET['status'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? ''
];

$transfers = $paypalMgr->getTransfers($filters);

// تسجيل عملية عرض حوالات PayPal الواردة
ActivityHelper::logView('paypal_transfers', 'عرض حوالات PayPal الواردة', $filters);

$pageTitle = 'حوالات PayPal الواردة';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mb-0"><i class="fas fa-download"></i> حوالات PayPal الواردة</h3>
        <div class="d-flex gap-2">
            <a href="paypal_transfers.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لحوالات PayPal
            </a>
            <a href="add_paypal_transfer.php" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة حوالة
            </a>
            <a href="withdrawals.php" class="btn btn-success">
                <i class="fas fa-money-bill-wave me-1"></i>
                سحب المبالغ
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي الحوالات</h6>
                            <h4 class="mb-0"><?php echo count($transfers); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مستلم</h6>
                            <h4 class="mb-0"><?php echo count(array_filter($transfers, function($t) { return $t['status'] === 'مستلم'; })); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">لم يستلم</h6>
                            <h4 class="mb-0"><?php echo count(array_filter($transfers, function($t) { return $t['status'] === 'لم يستلم'; })); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المبلغ</h6>
                            <h4 class="mb-0"><?php 
                                $totalAmount = 0;
                                foreach ($transfers as $t) $totalAmount += (float)$t['amount'];
                                echo number_format($totalAmount, 2);
                            ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Form -->
    <form method="get" class="row g-2 mb-3">
        <div class="col-md-3">
            <input type="text" name="search" class="form-control" placeholder="بحث (اسم/مرسل/رمز)" value="<?php echo htmlspecialchars($filters['search']); ?>">
        </div>
        <div class="col-md-2">
            <select name="status" class="form-select">
                <option value="">-- الحالة --</option>
                <option value="مستلم" <?php echo $filters['status']==='مستلم'?'selected':''; ?>>مستلم</option>
                <option value="لم يستلم" <?php echo $filters['status']==='لم يستلم'?'selected':''; ?>>لم يستلم</option>
            </select>
        </div>
        <div class="col-md-2">
            <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($filters['date_from']); ?>">
        </div>
        <div class="col-md-2">
            <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($filters['date_to']); ?>">
        </div>
        <div class="col-md-3 d-flex">
            <button class="btn btn-secondary me-2" type="submit"><i class="fas fa-filter"></i> فلترة</button>
            <a href="paypal_incoming_transfers.php" class="btn btn-outline-secondary me-2">إعادة ضبط</a>
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-file-export"></i> تصدير
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="export_paypal_incoming.php?format=excel<?php echo !empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : ''; ?>">
                            <i class="fas fa-file-excel me-1"></i> تصدير Excel
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="export_paypal_incoming.php?format=pdf<?php echo !empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : ''; ?>">
                            <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </form>

    <?php if (empty($transfers)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            لا توجد حوالات واردة مسجلة.
        </div>
    <?php else: ?>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead class="table-light">
                <tr>
                    <th>#</th>
                    <th>اسم المستلم</th>
                    <th>رقم الجوال</th>
                    <th>رمز المعاملة</th>
                    <th>اسم المرسل</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($transfers as $row): ?>
                    <tr>
                        <td><?php echo $row['id']; ?></td>
                        <td><?php echo htmlspecialchars($row['recipient_name']); ?></td>
                        <td><?php echo htmlspecialchars($row['recipient_phone']); ?></td>
                        <td><?php echo htmlspecialchars($row['transaction_code']); ?></td>
                        <td><?php echo htmlspecialchars($row['sender_name']); ?></td>
                        <td><?php echo number_format($row['amount'], 2); ?></td>
                        <td>
                            <?php if ($row['status'] === 'مستلم'): ?>
                                <span class="badge bg-success">مستلم</span>
                            <?php else: ?>
                                <span class="badge bg-warning text-dark">لم يستلم</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?></td>
                        <td>
                            <a href="view_paypal_transfer.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" title="عرض">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="edit_paypal_transfer.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php if ($row['status'] === 'لم يستلم'): ?>
                                <button class="btn btn-sm btn-success" onclick="markAsReceived(<?php echo $row['id']; ?>)" title="تحديد كمستلم">
                                    <i class="fas fa-check"></i>
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <?php 
        $totalAmount = 0;
        foreach ($transfers as $t) $totalAmount += (float)$t['amount'];
    ?>
    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <strong>إجمالي المبلغ<?php echo $filters['search']||$filters['status']||$filters['date_from']||$filters['date_to']?' (حسب الفلتر)':''; ?>: 
                    <?php echo number_format($totalAmount, 2); ?>
                </strong>
            </div>
            <div class="col-md-6 text-end">
                <a href="withdrawals.php" class="btn btn-success">
                    <i class="fas fa-money-bill-wave me-1"></i>
                    إدارة سحب المبالغ
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function markAsReceived(id) {
    if (confirm('هل تريد تحديد هذه الحوالة كمستلمة؟')) {
        // يمكن إضافة AJAX هنا لتحديث الحالة
        window.location.href = 'mark_paypal_received.php?id=' + id;
    }
}
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
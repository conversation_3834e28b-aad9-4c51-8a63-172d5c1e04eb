<?php
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/withdrawal_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('withdrawals.view');

$withdrawalMgr = new WithdrawalManager(new Database());

$id = (int)($_GET['id'] ?? 0);
if (!$id) {
    echo json_encode(['success' => false, 'error' => 'معرف غير صحيح']);
    exit;
}

$withdrawal = $withdrawalMgr->getWithdrawalById($id);
if (!$withdrawal) {
    echo json_encode(['success' => false, 'error' => 'سحب المبلغ غير موجود']);
    exit;
}

echo json_encode(['success' => true, 'withdrawal' => $withdrawal]);
?> 
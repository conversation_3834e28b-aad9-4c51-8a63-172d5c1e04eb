<?php
/**
 * إنشاء جدول تسجيل العمليات الشامل للنظام
 * System Activity Logs Setup
 */

require_once __DIR__ . '/../config/database.php';

echo "<h2>إنشاء جدول تسجيل العمليات الشامل</h2>\n";

try {
    // الاتصال بقاعدة البيانات
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($mysqli->connect_error) {
        throw new Exception('فشل الاتصال بقاعدة البيانات: ' . $mysqli->connect_error);
    }
    
    // تعيين الترميز
    $mysqli->set_charset(DB_CHARSET);
    
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    // قراءة ملف SQL
    $sqlFile = __DIR__ . '/create_system_activity_logs.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception('ملف SQL غير موجود: ' . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception('فشل في قراءة ملف SQL');
    }
    
    echo "<p>✅ تم قراءة ملف SQL بنجاح</p>\n";
    
    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $totalQueries = count($queries);
    
    foreach ($queries as $query) {
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        echo "<p>تنفيذ: " . substr($query, 0, 50) . "...</p>\n";
        
        if ($mysqli->query($query)) {
            $successCount++;
            echo "<p style='color: green;'>✅ تم بنجاح</p>\n";
        } else {
            echo "<p style='color: red;'>❌ خطأ: " . $mysqli->error . "</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3>النتائج:</h3>\n";
    echo "<p><strong>إجمالي الاستعلامات:</strong> $totalQueries</p>\n";
    echo "<p><strong>نجح:</strong> $successCount</p>\n";
    echo "<p><strong>فشل:</strong> " . ($totalQueries - $successCount) . "</p>\n";
    
    if ($successCount === $totalQueries) {
        echo "<p style='color: green; font-size: 18px;'><strong>🎉 تم إنشاء جدول تسجيل العمليات بنجاح!</strong></p>\n";
        
        // التحقق من إنشاء الجدول
        $result = $mysqli->query("SHOW TABLES LIKE 'system_activity_logs'");
        if ($result && $result->num_rows > 0) {
            echo "<p>✅ تم التحقق من وجود الجدول</p>\n";
            
            // عرض هيكل الجدول
            $structure = $mysqli->query("DESCRIBE system_activity_logs");
            if ($structure) {
                echo "<h4>هيكل الجدول:</h4>\n";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
                echo "<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
                
                while ($row = $structure->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Default']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                    echo "</tr>\n";
                }
                echo "</table>\n";
            }
            
            // عرض الفهارس
            $indexes = $mysqli->query("SHOW INDEX FROM system_activity_logs");
            if ($indexes) {
                echo "<h4>الفهارس:</h4>\n";
                echo "<ul>\n";
                while ($row = $indexes->fetch_assoc()) {
                    echo "<li>" . htmlspecialchars($row['Key_name']) . " (" . htmlspecialchars($row['Column_name']) . ")</li>\n";
                }
                echo "</ul>\n";
            }
        }
    } else {
        echo "<p style='color: red; font-size: 18px;'><strong>❌ حدثت أخطاء أثناء الإنشاء</strong></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'><strong>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</strong></p>\n";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}

echo "<hr>\n";
echo "<p><a href='../dashboard/'>العودة إلى لوحة التحكم</a></p>\n";
?>

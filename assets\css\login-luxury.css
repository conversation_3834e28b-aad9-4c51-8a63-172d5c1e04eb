/* تصميم بسيط ونظيف لصفحة تسجيل الدخول */

/* إعدادات عامة ومتغيرات */
 

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--white);
    color: var(--text-dark);
    line-height: 1.6;
}

.login-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 100vh;
}

/* -------------------------
   الجزء الأيمن: نموذج الدخول
   ------------------------- */
.login-form-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: var(--white);
}

.form-container {
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 8px;
}

.login-header p {
    font-size: 1rem;
    color: var(--text-light);
}

/* حقول الإدخال */
.input-group {
    position: relative;
    margin-bottom: 25px;
}

.input-group .input-icon {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    color: var(--text-light);
    transition: var(--transition);
}

.input-group input {
    width: 100%;
    padding: 14px 40px 14px 15px; /* مساحة للأيقونة */
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: var(--font-family);
    transition: var(--transition);
    background-color: var(--bg-light);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 105, 189, 0.2);
}

.input-group label {
    position: absolute;
    top: 15px;
    right: 15px;
    color: var(--text-light);
    background-color: var(--bg-light);
    padding: 0 5px;
    transition: var(--transition);
    pointer-events: none;
}

.input-group input:focus + label,
.input-group input:not(:placeholder-shown) + label {
    top: -10px;
    right: 10px;
    font-size: 0.85rem;
    color: var(--primary-color);
}

.input-group input:focus ~ .input-icon {
    color: var(--primary-color);
}

/* خيارات النموذج */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    font-size: 0.9rem;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-left: 8px;
}

a.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a.forgot-password:hover {
    text-decoration: underline;
}

/* زر تسجيل الدخول */
.login-btn {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: var(--border-radius);
    background: var(--primary-gradient);
    color: var(--white);
    font-size: 1rem;
    font-weight: 600;
    font-family: var(--font-family);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(74, 105, 189, 0.3);
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 105, 189, 0.4);
}

.signup-link {
    text-align: center;
    margin-top: 30px;
    color: var(--text-light);
}

.signup-link a {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
}

/* -------------------------
   الجزء الأيسر: العلامة التجارية
   ------------------------- */
.branding-panel {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    color: var(--white);
    padding: 40px;
    text-align: center;
}

.branding-content .logo {
    font-size: 4rem;
    margin-bottom: 20px;
    text-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.branding-content h2 {
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.branding-content p {
    font-size: 1rem;
    max-width: 350px;
    margin: 0 auto;
    opacity: 0.9;
}

/* -------------------------
   التصميم المتجاوب للشاشات الصغيرة
   ------------------------- */
@media (max-width: 992px) {
    .login-container {
        grid-template-columns: 1fr;
    }

    .branding-panel {
        display: none; /* إخفاء الجزء الخاص بالعلامة التجارية في الشاشات الصغيرة للتركيز */
    }

    .login-form-wrapper {
        min-height: 100vh;
    }
}
-- Create Offices and Exchange Rates Tables
-- This script creates the offices system with exchange rates

-- ======= Offices Table =======
CREATE TABLE IF NOT EXISTS offices (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    office_name VA<PERSON>HAR(255) NOT NULL,
    phone_number VA<PERSON>HAR(50),
    address TEXT,
    manager_name VA<PERSON>HAR(255),
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_offices_active (is_active),
    INDEX idx_offices_name (office_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ======= Office Exchange Rates Table =======
CREATE TABLE IF NOT EXISTS office_exchange_rates (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    office_id INT UNSIGNED NOT NULL,
    operation_name VARCHAR(255) NOT NULL,
    exchange_rate_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_oer_office FOREIGN KEY (office_id) REFERENCES offices(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE KEY unique_office_operation (office_id, operation_name),
    INDEX idx_oer_office_active (office_id, is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ======= Permissions =======
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (30, 'offices.view', 'View offices list', 'offices'),
    (31, 'offices.create', 'Create new office', 'offices'),
    (32, 'offices.edit', 'Edit office details', 'offices'),
    (33, 'offices.delete', 'Delete office', 'offices'),
    (34, 'offices.rates.view', 'View office exchange rates', 'offices'),
    (35, 'offices.rates.create', 'Create office exchange rate', 'offices'),
    (36, 'offices.rates.edit', 'Edit office exchange rate', 'offices'),
    (37, 'offices.rates.delete', 'Delete office exchange rate', 'offices');

-- ======= Role Permissions =======
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,30),(1,31),(1,32),(1,33),(1,34),(1,35),(1,36),(1,37), -- Admin all
    (2,30),(2,31),(2,32),(2,34),(2,35),(2,36), -- Branch Manager
    (3,30),(3,34); -- Cashier view only

-- ======= Sample Data =======
INSERT IGNORE INTO offices (id, office_name, phone_number, address, manager_name, is_active) VALUES
    (1, 'المكتب الرئيسي', '+966501234567', 'الرياض، المملكة العربية السعودية', 'أحمد محمد', 1),
    (2, 'مكتب جدة', '+966502345678', 'جدة، المملكة العربية السعودية', 'محمد علي', 1),
    (3, 'مكتب الدمام', '+966503456789', 'الدمام، المملكة العربية السعودية', 'علي أحمد', 1);

INSERT IGNORE INTO office_exchange_rates (id, office_id, operation_name, exchange_rate_percentage, is_active) VALUES
    (1, 1, 'الإمارات العربية المتحدة', 2.00, 1),
    (2, 1, 'مصر', 1.50, 1),
    (3, 1, 'الأردن', 1.75, 1),
    (4, 2, 'الإمارات العربية المتحدة', 2.25, 1),
    (5, 2, 'مصر', 1.60, 1),
    (6, 3, 'الإمارات العربية المتحدة', 2.10, 1),
    (7, 3, 'الأردن', 1.80, 1);

COMMIT; 
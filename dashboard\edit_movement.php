<?php
/**
 * Edit Cash Movement Page
 * Allows users with proper permissions to edit existing cash movements
 */

// Required includes
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/cash_manager.php';

// Initialize classes
$auth = new Auth();
$db = new Database();
$cashManager = new CashManager($db);

// Authentication and permission checks
$auth->requireLogin();
$auth->requirePermission('cash.movements.edit');

// Get current user for logging
$current_user_id = $auth->getCurrentUser()['id'] ?? null;

// Get movement ID from URL
$movement_id = (int)($_GET['id'] ?? 0);

if ($movement_id <= 0) {
    set_flash('danger', 'معرف الحركة غير صالح');
    redirect('cash.php');
    exit;
}

// Get existing movement
try {
    $movement = $cashManager->getCashMovementById($movement_id);
    if (!$movement) {
        set_flash('danger', 'الحركة غير موجودة');
        redirect('cash.php');
        exit;
    }
    
    // Get cash box details
    $cashBox = $cashManager->getCashBoxById($movement['cash_box_id']);
    if (!$cashBox) {
        set_flash('danger', 'الصندوق غير موجود');
        redirect('cash.php');
        exit;
    }
} catch (Exception $e) {
    set_flash('danger', 'خطأ في تحميل بيانات الحركة: ' . $e->getMessage());
    redirect('cash.php');
    exit;
}

// Initialize variables
$errors = [];
$success_message = '';
$error_message = '';
$form_data = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'رمز الحماية غير صالح. يرجى المحاولة مرة أخرى.';
    } else {
        // Sanitize and validate input data
        $form_data = [
            'movement_type' => sanitize_input($_POST['movement_type'] ?? ''),
            'amount' => (float)($_POST['amount'] ?? 0),
            'description' => sanitize_input($_POST['description'] ?? ''),
            'reference_number' => sanitize_input($_POST['reference_number'] ?? '')
        ];

        // Validation
        if (empty($form_data['movement_type'])) {
            $errors[] = 'نوع الحركة مطلوب';
        } elseif (!in_array($form_data['movement_type'], ['deposit', 'withdrawal'])) {
            $errors[] = 'نوع الحركة غير صالح';
        }

        if ($form_data['amount'] <= 0) {
            $errors[] = 'المبلغ يجب أن يكون أكبر من صفر';
        }

        // Check if reference number is unique (excluding current movement)
        if (!empty($form_data['reference_number'])) {
            try {
                $existing = $cashManager->getMovementByReference($form_data['reference_number'], $movement_id);
                if ($existing) {
                    $errors[] = 'رقم المرجع موجود بالفعل';
                }
            } catch (Exception $e) {
                $errors[] = 'خطأ في التحقق من رقم المرجع: ' . $e->getMessage();
            }
        }

        // If no validation errors, attempt to update
        if (empty($errors)) {
            try {
                // Calculate balance difference
                $old_amount = (float)$movement['amount'];
                $old_type = $movement['movement_type'];
                $new_amount = $form_data['amount'];
                $new_type = $form_data['movement_type'];
                
                // Calculate the net change in balance
                $old_effect = ($old_type === 'deposit') ? $old_amount : -$old_amount;
                $new_effect = ($new_type === 'deposit') ? $new_amount : -$new_amount;
                $balance_change = $new_effect - $old_effect;
                
                // Debug information
                error_log("Attempting to update movement $movement_id with data: " . json_encode($form_data));
                error_log("Balance change: $balance_change");

                $update_success = $cashManager->updateCashMovement($movement_id, $form_data, $balance_change);

                if ($update_success) {
                    // Log the activity
                    log_activity($current_user_id, 'cash.update_movement', [
                        'movement_id' => $movement_id,
                        'cash_box_id' => $movement['cash_box_id'],
                        'old_data' => $movement,
                        'new_data' => $form_data,
                        'balance_change' => $balance_change
                    ]);

                    // Set success message and redirect
                    set_flash('success', 'تم تحديث الحركة بنجاح');
                    redirect('cash_box_history.php?id=' . $movement['cash_box_id']);
                    exit;
                } else {
                    $error_message = 'فشل في تحديث الحركة. يرجى المحاولة مرة أخرى.';
                    error_log("Failed to update cash movement $movement_id");
                }
            } catch (Exception $e) {
                $error_message = 'خطأ في تحديث الحركة: ' . $e->getMessage();
                error_log("Error updating cash movement: " . $e->getMessage());
            }
        }
    }
} else {
    // Pre-populate form with existing data
    $form_data = [
        'movement_type' => $movement['movement_type'],
        'amount' => $movement['amount'],
        'description' => $movement['description'],
        'reference_number' => $movement['reference_number']
    ];
}

// Generate CSRF token
$csrf_token = generate_csrf_token();

// Set page title
$page_title = 'تعديل حركة مالية - ' . htmlspecialchars($cashBox['name']);

// Include header
include __DIR__ . '/../includes/header.php';
?>

<!-- Dashboard content starts here -->
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-edit me-2"></i>
                    تعديل حركة مالية
                </h1>
                <p class="text-muted mb-0">
                    تعديل حركة في صندوق: <strong><?php echo htmlspecialchars($cashBox['name']); ?></strong>
                </p>
            </div>
            <div>
                <a href="cash_box_history.php?id=<?php echo $movement['cash_box_id']; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للتاريخ
                </a>
            </div>
        </div>
    </div>

    <!-- Error/Success Messages -->
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>يرجى تصحيح الأخطاء التالية:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Movement Info Card -->
        <div class="col-md-4">
            <div class="dashboard-card movement-info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الحركة
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td>رقم الحركة:</td>
                            <td><code class="transaction-number"><?php echo $movement['id']; ?></code></td>
                        </tr>
                        <tr>
                            <td>تاريخ الإنشاء:</td>
                            <td>
                                <div class="transfer-date"><?php echo date('Y-m-d', strtotime($movement['created_at'])); ?></div>
                                <div class="transfer-time"><?php echo date('H:i:s', strtotime($movement['created_at'])); ?></div>
                            </td>
                        </tr>
                        <tr>
                            <td>الصندوق:</td>
                            <td class="user-info"><?php echo htmlspecialchars($cashBox['name']); ?></td>
                        </tr>
                        <tr>
                            <td>العملة:</td>
                            <td>
                                <span class="badge bg-light text-dark fw-bold">
                                    <?php echo htmlspecialchars($cashBox['currency_code']); ?>
                                    (<?php echo htmlspecialchars($cashBox['currency_symbol']); ?>)
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>المنشئ:</td>
                            <td>
                                <div class="user-info"><?php echo htmlspecialchars($movement['user_name'] ?: 'غير محدد'); ?></div>
                                <?php if (!empty($movement['user_id'])): ?>
                                <div class="user-contact">ID: <?php echo $movement['user_id']; ?></div>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="col-md-8">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات الحركة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="editMovementForm" class="edit-movement-form" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="movement_type" class="form-label">
                                    <i class="fas fa-exchange-alt me-1"></i>
                                    نوع الحركة <span class="text-danger">*</span>
                                </label>
                                <select name="movement_type" id="movement_type" class="form-select" required>
                                    <option value="">اختر نوع الحركة</option>
                                    <option value="deposit" <?php echo ($form_data['movement_type'] ?? '') === 'deposit' ? 'selected' : ''; ?>>
                                        <i class="fas fa-plus"></i> إيداع
                                    </option>
                                    <option value="withdrawal" <?php echo ($form_data['movement_type'] ?? '') === 'withdrawal' ? 'selected' : ''; ?>>
                                        <i class="fas fa-minus"></i> سحب
                                    </option>
                                </select>
                                <div class="invalid-feedback">
                                    يرجى اختيار نوع الحركة
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">
                                    <i class="fas fa-dollar-sign me-1"></i>
                                    المبلغ <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <?php echo htmlspecialchars($cashBox['currency_symbol']); ?>
                                    </span>
                                    <input type="number" step="0.01" name="amount" id="amount" 
                                           class="form-control" required min="0.01" 
                                           value="<?php echo htmlspecialchars($form_data['amount'] ?? ''); ?>"
                                           placeholder="0.00">
                                </div>
                                <div class="invalid-feedback">
                                    يرجى إدخال مبلغ صالح
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-1"></i>
                                الوصف
                            </label>
                            <textarea name="description" id="description" class="form-control" rows="3" 
                                      placeholder="وصف الحركة المالية (اختياري)"><?php echo htmlspecialchars($form_data['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="reference_number" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>
                                رقم المرجع
                            </label>
                            <input type="text" name="reference_number" id="reference_number" 
                                   class="form-control" 
                                   value="<?php echo htmlspecialchars($form_data['reference_number'] ?? ''); ?>"
                                   placeholder="رقم مرجعي للحركة (اختياري)">
                            <div class="form-text">رقم مرجعي فريد للحركة (إيصال، شيك، إلخ)</div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التعديلات
                                </button>
                                <button type="button" class="btn btn-secondary ms-2" onclick="resetForm()">
                                    <i class="fas fa-undo me-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                            <div>
                                <a href="cash_box_history.php?id=<?php echo $movement['cash_box_id']; ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> <!-- /.container-fluid -->

<?php include __DIR__ . '/../includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form elements
    const form = document.getElementById('editMovementForm');
    const movementTypeSelect = document.getElementById('movement_type');
    const amountInput = document.getElementById('amount');

    // Form submission validation
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        } else {
            // Confirmation before submission
            const movementType = movementTypeSelect.value === 'deposit' ? 'إيداع' : 'سحب';
            const amount = parseFloat(amountInput.value);
            const confirmed = confirm(`هل أنت متأكد من تحديث الحركة؟\nالنوع: ${movementType}\nالمبلغ: ${amount.toFixed(2)}`);
            if (!confirmed) {
                e.preventDefault();
                e.stopPropagation();
            }
        }

        form.classList.add('was-validated');
    });

    // Auto-format amount input
    amountInput.addEventListener('blur', function() {
        if (this.value && !isNaN(this.value)) {
            const value = parseFloat(this.value);
            this.value = value.toFixed(2);
        }
    });

    // Update form styling based on movement type
    movementTypeSelect.addEventListener('change', function() {
        const amountGroup = amountInput.closest('.input-group');
        const amountLabel = document.querySelector('label[for="amount"]');

        if (this.value === 'deposit') {
            amountGroup.classList.remove('border-warning');
            amountGroup.classList.add('border-success');
            amountLabel.innerHTML = '<i class="fas fa-plus me-1 text-success"></i>مبلغ الإيداع <span class="text-danger">*</span>';
        } else if (this.value === 'withdrawal') {
            amountGroup.classList.remove('border-success');
            amountGroup.classList.add('border-warning');
            amountLabel.innerHTML = '<i class="fas fa-minus me-1 text-warning"></i>مبلغ السحب <span class="text-danger">*</span>';
        } else {
            amountGroup.classList.remove('border-success', 'border-warning');
            amountLabel.innerHTML = '<i class="fas fa-dollar-sign me-1"></i>المبلغ <span class="text-danger">*</span>';
        }
    });

    // Trigger initial styling
    movementTypeSelect.dispatchEvent(new Event('change'));
});

// Reset form to original values
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        // Reset to original values
        document.getElementById('movement_type').value = '<?php echo htmlspecialchars($movement['movement_type']); ?>';
        document.getElementById('amount').value = '<?php echo $movement['amount']; ?>';
        document.getElementById('description').value = '<?php echo htmlspecialchars($movement['description']); ?>';
        document.getElementById('reference_number').value = '<?php echo htmlspecialchars($movement['reference_number']); ?>';

        // Remove validation classes
        document.getElementById('editMovementForm').classList.remove('was-validated');

        // Clear custom validity
        const inputs = document.querySelectorAll('#editMovementForm input, #editMovementForm select, #editMovementForm textarea');
        inputs.forEach(input => input.setCustomValidity(''));

        // Trigger movement type change
        document.getElementById('movement_type').dispatchEvent(new Event('change'));
    }
}

// Show loading state on form submission
document.getElementById('editMovementForm').addEventListener('submit', function() {
    const submitBtn = this.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';

        // Re-enable after 10 seconds as fallback
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 10000);
    }
});
</script>

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/office_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('offices.view');

$officeMgr = new OfficeManager(new Database());

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $auth->requirePermission('offices.delete');
    if (verify_csrf_token($_GET['token'] ?? '')) {
        $id = (int)$_GET['id'];
        $old = $officeMgr->getOfficeById($id);
        if ($old && $officeMgr->deleteOffice($id)) {
            // تسجيل عملية حذف المكتب
            ActivityHelper::logDelete(
                'offices',
                $old['office_name'],
                $old,
                $id
            );
            set_flash('success', 'تم حذف المكتب بنجاح');
        } else {
            set_flash('danger', 'فشل الحذف');
        }
    } else {
        set_flash('danger', 'CSRF غير صالح');
    }
    redirect('offices.php');
}

// Get offices with search filter
$search = $_GET['search'] ?? '';
$filters = [];
if ($search) {
    $filters['search'] = $search;
}

$offices = $officeMgr->getAllOffices($filters);

// تسجيل عملية عرض قائمة المكاتب
ActivityHelper::logView('offices', 'عرض قائمة المكاتب', $filters);

$csrf = get_csrf_token();

$pageTitle = 'المكاتب';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-building"></i> المكاتب</h3>
        <?php if ($auth->hasPermission('offices.create')): ?>
        <a href="add_office.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مكتب جديد
        </a>
        <?php endif; ?>
    </div>

    <?php foreach(get_flash() as $t=>$m): ?>
        <div class="alert alert-<?php echo $t; ?> alert-dismissible fade show" role="alert">
            <?php echo $m; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endforeach; ?>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <input type="text" name="search" class="form-control" 
                           placeholder="البحث في اسم المكتب، مسؤول المكتب، أو رقم الهاتف" 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <?php if ($search): ?>
                    <a href="offices.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Offices Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                قائمة المكاتب
                <span class="badge bg-primary ms-2"><?php echo count($offices); ?></span>
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 60px;">#</th>
                            <th style="width: 200px;">اسم المكتب</th>
                            <th style="width: 150px;">مسؤول المكتب</th>
                            <th style="width: 150px;">رقم الهاتف</th>
                            <th style="width: 200px;">العنوان</th>
                            <th style="width: 100px;">الحالة</th>
                            <th style="width: 120px;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach($offices as $office): ?>
                        <tr>
                            <td><?php echo $office['id']; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($office['office_name']); ?></strong>
                            </td>
                            <td>
                                <span class="text-muted">
                                    <?php echo htmlspecialchars($office['manager_name']); ?>
                                </span>
                            </td>
                            <td>
                                <a href="tel:<?php echo htmlspecialchars($office['phone_number']); ?>" 
                                   class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>
                                    <?php echo htmlspecialchars($office['phone_number']); ?>
                                </a>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo htmlspecialchars($office['address']); ?>
                                </small>
                            </td>
                            <td>
                                <?php if ($office['is_active']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>نشط
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>غير نشط
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="office_details.php?id=<?php echo $office['id']; ?>" 
                                       class="btn btn-outline-info" title="تفاصيل المكتب">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if ($auth->hasPermission('offices.edit')): ?>
                                    <a href="edit_office.php?id=<?php echo $office['id']; ?>" 
                                       class="btn btn-outline-primary" title="تعديل المكتب">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php endif; ?>
                                    <?php if ($auth->hasPermission('offices.delete')): ?>
                                    <a href="offices.php?action=delete&id=<?php echo $office['id']; ?>&token=<?php echo $csrf; ?>" 
                                       class="btn btn-outline-danger" 
                                       onclick="return confirm('هل أنت متأكد من حذف هذا المكتب؟')"
                                       title="حذف المكتب">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        
                        <?php if (empty($offices)): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-building fa-2x text-muted mb-2"></i>
                                <div class="text-muted">لا توجد مكاتب</div>
                                <?php if ($auth->hasPermission('offices.create')): ?>
                                <a href="add_office.php" class="btn btn-primary btn-sm mt-2">
                                    <i class="fas fa-plus me-1"></i>إضافة مكتب جديد
                                </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
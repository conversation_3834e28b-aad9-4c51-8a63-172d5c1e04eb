-- Fix missing exchange_id columns in cash_movements and bank_movements tables
-- This script adds the missing exchange_id columns that are referenced in the code

-- Check if columns exist before adding them
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'cash_movements'
     AND COLUMN_NAME = 'exchange_id') = 0,
    'ALTER TABLE cash_movements ADD COLUMN exchange_id INT UNSIGNED NULL AFTER user_id',
    'SELECT "Column exchange_id already exists in cash_movements" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'bank_movements'
     AND COLUMN_NAME = 'exchange_id') = 0,
    'ALTER TABLE bank_movements ADD COLUMN exchange_id INT UNSIGNED NULL AFTER user_id',
    'SELECT "Column exchange_id already exists in bank_movements" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if accounting_entries table exists before modifying it
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'accounting_entries') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'accounting_entries'
         AND COLUMN_NAME = 'exchange_id') = 0,
        'ALTER TABLE accounting_entries ADD COLUMN exchange_id INT UNSIGNED NULL AFTER created_by',
        'SELECT "Column exchange_id already exists in accounting_entries" as message'
    )),
    'SELECT "Table accounting_entries does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraints for exchange_id columns (only if they don't exist)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'cash_movements'
     AND CONSTRAINT_NAME = 'fk_cmov_exchange') = 0,
    'ALTER TABLE cash_movements ADD CONSTRAINT fk_cmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE',
    'SELECT "Constraint fk_cmov_exchange already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'bank_movements'
     AND CONSTRAINT_NAME = 'fk_bmov_exchange') = 0,
    'ALTER TABLE bank_movements ADD CONSTRAINT fk_bmov_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE',
    'SELECT "Constraint fk_bmov_exchange already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraint for accounting_entries if table exists
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'accounting_entries') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'accounting_entries'
         AND CONSTRAINT_NAME = 'fk_acc_exchange') = 0,
        'ALTER TABLE accounting_entries ADD CONSTRAINT fk_acc_exchange FOREIGN KEY (exchange_id) REFERENCES exchanges(id) ON DELETE SET NULL ON UPDATE CASCADE',
        'SELECT "Constraint fk_acc_exchange already exists" as message'
    )),
    'SELECT "Table accounting_entries does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Now add transfer_id columns after exchange_id (fixing the original issue)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'cash_movements'
     AND COLUMN_NAME = 'transfer_id') = 0,
    'ALTER TABLE cash_movements ADD COLUMN transfer_id INT UNSIGNED NULL AFTER exchange_id',
    'SELECT "Column transfer_id already exists in cash_movements" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'bank_movements'
     AND COLUMN_NAME = 'transfer_id') = 0,
    'ALTER TABLE bank_movements ADD COLUMN transfer_id INT UNSIGNED NULL AFTER exchange_id',
    'SELECT "Column transfer_id already exists in bank_movements" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'accounting_entries') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'accounting_entries'
         AND COLUMN_NAME = 'transfer_id') = 0,
        'ALTER TABLE accounting_entries ADD COLUMN transfer_id INT UNSIGNED NULL AFTER exchange_id',
        'SELECT "Column transfer_id already exists in accounting_entries" as message'
    )),
    'SELECT "Table accounting_entries does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraints for transfer_id columns (only if transfers table exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'transfers') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'cash_movements'
         AND CONSTRAINT_NAME = 'fk_cmov_transfer') = 0,
        'ALTER TABLE cash_movements ADD CONSTRAINT fk_cmov_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL ON UPDATE CASCADE',
        'SELECT "Constraint fk_cmov_transfer already exists" as message'
    )),
    'SELECT "Table transfers does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'transfers') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'bank_movements'
         AND CONSTRAINT_NAME = 'fk_bmov_transfer') = 0,
        'ALTER TABLE bank_movements ADD CONSTRAINT fk_bmov_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL ON UPDATE CASCADE',
        'SELECT "Constraint fk_bmov_transfer already exists" as message'
    )),
    'SELECT "Table transfers does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'transfers') > 0 AND
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
     AND TABLE_NAME = 'accounting_entries') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
         WHERE TABLE_SCHEMA = DATABASE()
         AND TABLE_NAME = 'accounting_entries'
         AND CONSTRAINT_NAME = 'fk_acc_transfer') = 0,
        'ALTER TABLE accounting_entries ADD CONSTRAINT fk_acc_transfer FOREIGN KEY (transfer_id) REFERENCES transfers(id) ON DELETE SET NULL ON UPDATE CASCADE',
        'SELECT "Constraint fk_acc_transfer already exists" as message'
    )),
    'SELECT "Required tables do not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

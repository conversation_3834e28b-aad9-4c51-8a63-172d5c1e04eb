<?php
/**
 * Daily Summary Report
 * التقرير اليومي
 */
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/reports_manager.php';
require_once __DIR__ . '/../includes/exchange_manager.php';
require_once __DIR__ . '/../includes/transfer_manager.php';
require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';
require_once __DIR__ . '/../includes/branch_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

// التحقق من الصلاحيات
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// التحقق من صلاحيات عرض التقارير
if (!$auth->hasPermission('reports.view')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب لعرض التقارير');
    redirect('dashboard/index.php');
}

// إنشاء مديري البيانات
$db = new Database();
$reportsManager = new ReportsManager($db);
$exchangeManager = new ExchangeManager($db);
$transferManager = new EnhancedTransferManager($db);
$branchManager = new BranchManager($db);
$currencyManager = new CurrencyManager($db);

// تحديد التاريخ
$reportDate = $_GET['date'] ?? date('Y-m-d');
$prevDate = date('Y-m-d', strtotime($reportDate . ' -1 day'));
$nextDate = date('Y-m-d', strtotime($reportDate . ' +1 day'));

// منع اختيار تاريخ في المستقبل
if (strtotime($reportDate) > strtotime(date('Y-m-d'))) {
    $reportDate = date('Y-m-d');
    $nextDate = '';
}

// الحصول على بيانات الفروع
$branches = $branchManager->getAllBranches();
$selectedBranchId = isset($_GET['branch_id']) ? (int)$_GET['branch_id'] : 0;

// الحصول على بيانات الصرافة
$exchangeFilters = [
    'from_date' => $reportDate,
    'to_date' => $reportDate
];

if ($selectedBranchId > 0) {
    $exchangeFilters['branch_id'] = $selectedBranchId;
}

$exchangeData = $reportsManager->getExchangeProfitReportData($exchangeFilters);

// حساب إجمالي الصرافة
$totalExchangeCount = 0;
$totalExchangeProfit = 0;

foreach ($exchangeData as $row) {
    $totalExchangeCount += $row['total_tx'];
    $totalExchangeProfit += $row['total_profit'];
}

// الحصول على بيانات الحوالات
$transferFilters = [
    'from_date' => $reportDate,
    'to_date' => $reportDate
];

if ($selectedBranchId > 0) {
    $transferFilters['branch_id'] = $selectedBranchId;
}

$transferData = $reportsManager->getTransferSummaryReportData($transferFilters);

// تسجيل عملية عرض التقرير اليومي
ActivityHelper::logView('reports', 'عرض التقرير اليومي', [
    'report_date' => $reportDate,
    'branch_id' => $selectedBranchId
]);

// حساب إجمالي الحوالات
$totalTransferCount = 0;
$totalTransferProfit = 0;
$totalTransferFees = 0;

// تحليل البيانات حسب النوع
$transfersByType = [
    'صادرة' => ['count' => 0, 'profit' => 0],
    'واردة' => ['count' => 0, 'profit' => 0]
];

// تحليل البيانات حسب الحالة
$transfersByStatus = [
    'معلقة' => 0,
    'مكتملة' => 0,
    'مرسلة' => 0,
    'مستلمة' => 0,
    'ملغاة' => 0
];

foreach ($transferData as $row) {
    $totalTransferCount += $row['total_tx'];
    $totalTransferProfit += $row['total_profit'];
    $totalTransferFees += $row['total_fees'];
    
    // تجميع حسب النوع
    if (isset($transfersByType[$row['transfer_type']])) {
        $transfersByType[$row['transfer_type']]['count'] += $row['total_tx'];
        $transfersByType[$row['transfer_type']]['profit'] += $row['total_profit'];
    }
    
    // تجميع حسب الحالة
    if (isset($transfersByStatus[$row['status']])) {
        $transfersByStatus[$row['status']] += $row['total_tx'];
    }
}

// إجمالي الأرباح
$totalDailyProfit = $totalExchangeProfit + $totalTransferProfit;

// الحصول على تفاصيل العمليات
$conn = Database::getConnection();

// تفاصيل عمليات الصرافة
$exchangeDetailsSql = "SELECT e.*, 
                      fc.code AS from_currency_code, 
                      tc.code AS to_currency_code,
                      u.full_name AS user_name,
                      b.name AS branch_name
                FROM exchanges e
                LEFT JOIN currencies fc ON fc.id = e.from_currency_id
                LEFT JOIN currencies tc ON tc.id = e.to_currency_id
                LEFT JOIN users u ON u.id = e.created_by
                LEFT JOIN branches b ON b.id = e.branch_id
                WHERE DATE(e.created_at) = ?";

if ($selectedBranchId > 0) {
    $exchangeDetailsSql .= " AND e.branch_id = ?";
}

$exchangeDetailsSql .= " ORDER BY e.created_at DESC";

$stmt = $conn->prepare($exchangeDetailsSql);

if ($selectedBranchId > 0) {
    $stmt->bind_param('si', $reportDate, $selectedBranchId);
} else {
    $stmt->bind_param('s', $reportDate);
}

$stmt->execute();
$exchangeDetailsResult = $stmt->get_result();
$exchangeDetails = [];
while ($row = $exchangeDetailsResult->fetch_assoc()) {
    $exchangeDetails[] = $row;
}
$stmt->close();

// تفاصيل الحوالات
$transferDetailsSql = "SELECT t.*, 
                      c.full_name AS beneficiary_full_name,
                      sc.code AS sending_currency_code,
                      rc.code AS receiving_currency_code,
                      u.full_name AS user_name,
                      b.name AS branch_name
                FROM transfers t
                LEFT JOIN customers c ON c.id = t.beneficiary_id
                LEFT JOIN currencies sc ON sc.id = t.sending_currency_id
                LEFT JOIN currencies rc ON rc.id = t.receiving_currency_id
                LEFT JOIN users u ON u.id = t.created_by
                LEFT JOIN branches b ON b.id = t.branch_id
                WHERE DATE(t.created_at) = ?";

if ($selectedBranchId > 0) {
    $transferDetailsSql .= " AND t.branch_id = ?";
}

$transferDetailsSql .= " ORDER BY t.created_at DESC";

$stmt = $conn->prepare($transferDetailsSql);

if ($selectedBranchId > 0) {
    $stmt->bind_param('si', $reportDate, $selectedBranchId);
} else {
    $stmt->bind_param('s', $reportDate);
}

$stmt->execute();
$transferDetailsResult = $stmt->get_result();
$transferDetails = [];
while ($row = $transferDetailsResult->fetch_assoc()) {
    $transferDetails[] = $row;
}
$stmt->close();

$pageTitle = 'التقرير اليومي';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-calendar-day"></i> التقرير اليومي</h3>
        <div>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>
    
    <!-- اختيار التاريخ والفرع -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="d-flex align-items-center">
                        <?php if (!empty($prevDate)): ?>
                        <a href="?date=<?php echo $prevDate; ?><?php echo $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <?php endif; ?>
                        
                        <div class="flex-grow-1">
                            <label class="form-label">التاريخ</label>
                            <input type="date" name="date" class="form-control" value="<?php echo $reportDate; ?>" max="<?php echo date('Y-m-d'); ?>" onchange="this.form.submit()">
                        </div>
                        
                        <?php if (!empty($nextDate)): ?>
                        <a href="?date=<?php echo $nextDate; ?><?php echo $selectedBranchId ? '&branch_id=' . $selectedBranchId : ''; ?>" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">الفرع</label>
                    <select name="branch_id" class="form-select" onchange="this.form.submit()">
                        <option value="">كل الفروع</option>
                        <?php foreach ($branches as $branch): ?>
                        <option value="<?php echo $branch['id']; ?>" <?php echo $selectedBranchId == $branch['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($branch['name']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-1"></i> عرض التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">عمليات الصرافة</h6>
                            <h4 class="mb-0"><?php echo number_format($totalExchangeCount); ?></h4>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-coins text-primary fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">الحوالات</h6>
                            <h4 class="mb-0"><?php echo number_format($totalTransferCount); ?></h4>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-paper-plane text-info fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">أرباح الصرافة</h6>
                            <h4 class="mb-0"><?php echo number_format($totalExchangeProfit, 2); ?></h4>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-dollar-sign text-success fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">أرباح الحوالات</h6>
                            <h4 class="mb-0"><?php echo number_format($totalTransferProfit, 2); ?></h4>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-money-bill-wave text-warning fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">توزيع الحوالات حسب النوع</h5>
                </div>
                <div class="card-body">
                    <canvas id="transferTypeChart" height="250"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">توزيع الحوالات حسب الحالة</h5>
                </div>
                <div class="card-body">
                    <canvas id="transferStatusChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- ملخص الأرباح -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="mb-0">ملخص الأرباح</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label text-muted">أرباح الصرافة</label>
                        <h4 class="text-success"><?php echo number_format($totalExchangeProfit, 2); ?></h4>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label text-muted">أرباح الحوالات</label>
                        <h4 class="text-warning"><?php echo number_format($totalTransferProfit, 2); ?></h4>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label text-muted">إجمالي الأرباح</label>
                        <h4 class="text-primary"><?php echo number_format($totalDailyProfit, 2); ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تبويبات التفاصيل -->
    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <ul class="nav nav-tabs card-header-tabs" id="detailsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="exchange-tab" data-bs-toggle="tab" data-bs-target="#exchange" type="button" role="tab" aria-controls="exchange" aria-selected="true">
                        <i class="fas fa-coins me-1"></i> عمليات الصرافة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="transfers-tab" data-bs-toggle="tab" data-bs-target="#transfers" type="button" role="tab" aria-controls="transfers" aria-selected="false">
                        <i class="fas fa-paper-plane me-1"></i> الحوالات
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body p-0">
            <div class="tab-content" id="detailsTabsContent">
                <!-- تفاصيل عمليات الصرافة -->
                <div class="tab-pane fade show active" id="exchange" role="tabpanel" aria-labelledby="exchange-tab">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>التاريخ</th>
                                    <th>من</th>
                                    <th>إلى</th>
                                    <th>المبلغ من</th>
                                    <th>المبلغ إلى</th>
                                    <th>السعر</th>
                                    <th>الربح</th>
                                    <th>الفرع</th>
                                    <th>المستخدم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($exchangeDetails)): ?>
                                    <tr>
                                        <td colspan="10" class="text-center py-4 text-muted">
                                            <i class="fas fa-coins fa-3x mb-3"></i>
                                            <p>لا توجد عمليات صرافة في هذا التاريخ</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($exchangeDetails as $index => $exchange): ?>
                                        <tr>
                                            <td><?php echo $index + 1; ?></td>
                                            <td><?php echo date('H:i', strtotime($exchange['created_at'])); ?></td>
                                            <td><?php echo htmlspecialchars($exchange['from_currency_code']); ?></td>
                                            <td><?php echo htmlspecialchars($exchange['to_currency_code']); ?></td>
                                            <td><?php echo number_format($exchange['amount_from'], 2); ?></td>
                                            <td><?php echo number_format($exchange['amount_to'], 2); ?></td>
                                            <td><?php echo number_format($exchange['rate_used'], 4); ?></td>
                                            <td class="text-success"><?php echo number_format($exchange['profit'], 2); ?></td>
                                            <td><?php echo htmlspecialchars($exchange['branch_name'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($exchange['user_name'] ?? ''); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- تفاصيل الحوالات -->
                <div class="tab-pane fade" id="transfers" role="tabpanel" aria-labelledby="transfers-tab">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>رقم الحوالة</th>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>المستفيد</th>
                                    <th>المبلغ الأساسي</th>
                                    <th>مبلغ التسليم</th>
                                    <th>الربح</th>
                                    <th>الحالة</th>
                                    <th>الفرع</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($transferDetails)): ?>
                                    <tr>
                                        <td colspan="10" class="text-center py-4 text-muted">
                                            <i class="fas fa-paper-plane fa-3x mb-3"></i>
                                            <p>لا توجد حوالات في هذا التاريخ</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($transferDetails as $index => $transfer): ?>
                                        <tr>
                                            <td><?php echo $index + 1; ?></td>
                                            <td><?php echo htmlspecialchars($transfer['transaction_number']); ?></td>
                                            <td><?php echo date('H:i', strtotime($transfer['created_at'])); ?></td>
                                            <td>
                                                <span class="badge <?php echo $transfer['transfer_type'] === 'صادرة' ? 'bg-primary' : 'bg-info'; ?>">
                                                    <?php echo htmlspecialchars($transfer['transfer_type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($transfer['beneficiary_name']); ?></td>
                                            <td><?php echo number_format($transfer['sending_amount'], 2) . ' ' . htmlspecialchars($transfer['sending_currency_code']); ?></td>
                                            <td><?php echo number_format($transfer['receiving_amount'], 2) . ' ' . htmlspecialchars($transfer['receiving_currency_code']); ?></td>
                                            <td class="text-warning"><?php echo number_format($transfer['profit'], 2); ?></td>
                                            <td>
                                                <?php
                                                $statusColors = [
                                                    'معلقة' => 'warning',
                                                    'مكتملة' => 'success',
                                                    'مرسلة' => 'info',
                                                    'مستلمة' => 'primary',
                                                    'ملغاة' => 'danger'
                                                ];
                                                $statusColor = $statusColors[$transfer['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?php echo $statusColor; ?>">
                                                    <?php echo htmlspecialchars($transfer['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($transfer['branch_name'] ?? ''); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إضافة مكتبة Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الألوان
    const primaryColor = '#4e73df';
    const successColor = '#1cc88a';
    const infoColor = '#36b9cc';
    const warningColor = '#f6c23e';
    const dangerColor = '#e74a3b';
    const secondaryColor = '#858796';
    
    // رسم بياني لتوزيع الحوالات حسب النوع
    const transferTypeCtx = document.getElementById('transferTypeChart').getContext('2d');
    new Chart(transferTypeCtx, {
        type: 'pie',
        data: {
            labels: ['صادرة', 'واردة'],
            datasets: [{
                data: [
                    <?php echo $transfersByType['صادرة']['count']; ?>,
                    <?php echo $transfersByType['واردة']['count']; ?>
                ],
                backgroundColor: [primaryColor, infoColor],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 15,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    
    // رسم بياني لتوزيع الحوالات حسب الحالة
    const transferStatusCtx = document.getElementById('transferStatusChart').getContext('2d');
    new Chart(transferStatusCtx, {
        type: 'bar',
        data: {
            labels: ['معلقة', 'مكتملة', 'مرسلة', 'مستلمة', 'ملغاة'],
            datasets: [{
                label: 'عدد الحوالات',
                data: [
                    <?php echo $transfersByStatus['معلقة']; ?>,
                    <?php echo $transfersByStatus['مكتملة']; ?>,
                    <?php echo $transfersByStatus['مرسلة']; ?>,
                    <?php echo $transfersByStatus['مستلمة']; ?>,
                    <?php echo $transfersByStatus['ملغاة']; ?>
                ],
                backgroundColor: [warningColor, successColor, infoColor, primaryColor, dangerColor],
                borderWidth: 0,
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
});
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
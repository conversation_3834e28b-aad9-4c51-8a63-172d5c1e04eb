<?php
/**
 * Fix Foreign Key Constraints for Currency System
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إصلاح قيود المفاتيح الخارجية للعملات</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص قيود المفاتيح الخارجية الحالية</h3>\n";
    
    // Get current foreign key constraints
    $result = $conn->query("
        SELECT 
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE CONSTRAINT_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME IS NOT NULL
        AND (TABLE_NAME LIKE '%cash%' OR TABLE_NAME LIKE '%bank%' OR REFERENCED_TABLE_NAME = 'currencies')
        ORDER BY TABLE_NAME, CONSTRAINT_NAME
    ");
    
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>اسم القيد</th><th>الجدول</th><th>العمود</th><th>الجدول المرجعي</th><th>العمود المرجعي</th>\n";
        echo "</tr>\n";
        
        $constraintsToRemove = [];
        while ($row = $result->fetch_assoc()) {
            $highlight = (strpos($row['COLUMN_NAME'], 'currency_id') !== false) ? 'background: #fff3cd;' : '';
            echo "<tr style='$highlight'>\n";
            echo "<td>{$row['CONSTRAINT_NAME']}</td>\n";
            echo "<td>{$row['TABLE_NAME']}</td>\n";
            echo "<td>{$row['COLUMN_NAME']}</td>\n";
            echo "<td>{$row['REFERENCED_TABLE_NAME']}</td>\n";
            echo "<td>{$row['REFERENCED_COLUMN_NAME']}</td>\n";
            echo "</tr>\n";
            
            // Mark currency_id constraints for removal
            if (strpos($row['COLUMN_NAME'], 'currency_id') !== false) {
                $constraintsToRemove[] = [
                    'table' => $row['TABLE_NAME'],
                    'constraint' => $row['CONSTRAINT_NAME']
                ];
            }
        }
        echo "</table>\n";
        
        echo "<h3>2. إزالة قيود المفاتيح الخارجية للعملات</h3>\n";
        
        foreach ($constraintsToRemove as $constraint) {
            echo "<h4>إزالة قيد {$constraint['constraint']} من جدول {$constraint['table']}</h4>\n";
            
            $dropSql = "ALTER TABLE {$constraint['table']} DROP FOREIGN KEY {$constraint['constraint']}";
            if ($conn->query($dropSql)) {
                echo "<p style='color: green;'>✓ تم إزالة قيد {$constraint['constraint']}</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إزالة قيد {$constraint['constraint']}: " . $conn->error . "</p>\n";
            }
        }
    }
    
    echo "<h3>3. إزالة الفهارس المرتبطة بـ currency_id</h3>\n";
    
    $tables = ['cash_boxes', 'bank_accounts', 'cash_movements', 'bank_movements'];
    
    foreach ($tables as $table) {
        // Check if table exists
        $checkTable = $conn->query("SHOW TABLES LIKE '$table'");
        if ($checkTable->num_rows == 0) {
            echo "<p style='color: orange;'>⚠ جدول $table غير موجود</p>\n";
            continue;
        }
        
        echo "<h4>فحص جدول $table</h4>\n";
        
        // Get indexes for this table
        $indexResult = $conn->query("SHOW INDEX FROM $table WHERE Column_name = 'currency_id'");
        if ($indexResult && $indexResult->num_rows > 0) {
            while ($index = $indexResult->fetch_assoc()) {
                $indexName = $index['Key_name'];
                if ($indexName !== 'PRIMARY') {
                    echo "<p>محاولة إزالة فهرس $indexName...</p>\n";
                    
                    $dropIndexSql = "ALTER TABLE $table DROP INDEX $indexName";
                    if ($conn->query($dropIndexSql)) {
                        echo "<p style='color: green;'>✓ تم إزالة فهرس $indexName من جدول $table</p>\n";
                    } else {
                        echo "<p style='color: orange;'>⚠ لم يتم إزالة فهرس $indexName: " . $conn->error . "</p>\n";
                    }
                }
            }
        } else {
            echo "<p style='color: green;'>✓ لا توجد فهارس currency_id في جدول $table</p>\n";
        }
    }
    
    echo "<h3>4. إزالة أعمدة currency_id</h3>\n";
    
    foreach ($tables as $table) {
        // Check if currency_id column exists
        $columnResult = $conn->query("SHOW COLUMNS FROM $table LIKE 'currency_id'");
        if ($columnResult && $columnResult->num_rows > 0) {
            echo "<p>إزالة عمود currency_id من جدول $table...</p>\n";
            
            $dropColumnSql = "ALTER TABLE $table DROP COLUMN currency_id";
            if ($conn->query($dropColumnSql)) {
                echo "<p style='color: green;'>✓ تم إزالة عمود currency_id من جدول $table</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إزالة عمود currency_id من جدول $table: " . $conn->error . "</p>\n";
            }
        } else {
            echo "<p style='color: green;'>✓ عمود currency_id غير موجود في جدول $table</p>\n";
        }
    }
    
    echo "<h3>5. التأكد من وجود أعمدة currency_code</h3>\n";
    
    $tablesNeedingCurrencyCode = ['cash_boxes', 'bank_accounts'];
    
    foreach ($tablesNeedingCurrencyCode as $table) {
        $columnResult = $conn->query("SHOW COLUMNS FROM $table LIKE 'currency_code'");
        if ($columnResult && $columnResult->num_rows > 0) {
            echo "<p style='color: green;'>✓ عمود currency_code موجود في جدول $table</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ إضافة عمود currency_code إلى جدول $table</p>\n";
            
            $addColumnSql = "ALTER TABLE $table ADD COLUMN currency_code VARCHAR(3) DEFAULT 'USD' AFTER id";
            if ($conn->query($addColumnSql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود currency_code إلى جدول $table</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عمود currency_code إلى جدول $table: " . $conn->error . "</p>\n";
            }
        }
    }
    
    echo "<h3>6. إنشاء قيود مفاتيح خارجية جديدة (اختياري)</h3>\n";
    
    // Add new foreign key constraints for currency_code (optional)
    $newConstraints = [
        [
            'table' => 'cash_boxes',
            'column' => 'currency_code',
            'constraint_name' => 'fk_cash_boxes_currency_code'
        ],
        [
            'table' => 'bank_accounts',
            'column' => 'currency_code',
            'constraint_name' => 'fk_bank_accounts_currency_code'
        ]
    ];
    
    foreach ($newConstraints as $constraint) {
        echo "<p>إضافة قيد مفتاح خارجي {$constraint['constraint_name']}...</p>\n";
        
        $addConstraintSql = "ALTER TABLE {$constraint['table']} 
                            ADD CONSTRAINT {$constraint['constraint_name']} 
                            FOREIGN KEY ({$constraint['column']}) 
                            REFERENCES currencies(code) 
                            ON UPDATE CASCADE 
                            ON DELETE RESTRICT";
        
        if ($conn->query($addConstraintSql)) {
            echo "<p style='color: green;'>✓ تم إضافة قيد {$constraint['constraint_name']}</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ لم يتم إضافة قيد {$constraint['constraint_name']}: " . $conn->error . "</p>\n";
        }
    }
    
    echo "<h3>7. التحقق النهائي</h3>\n";
    
    // Final verification
    foreach ($tables as $table) {
        $checkTable = $conn->query("SHOW TABLES LIKE '$table'");
        if ($checkTable->num_rows > 0) {
            echo "<p><strong>هيكل جدول $table:</strong></p>\n";
            
            $columns = $conn->query("SHOW COLUMNS FROM $table");
            if ($columns) {
                echo "<ul>\n";
                while ($column = $columns->fetch_assoc()) {
                    $highlight = (strpos($column['Field'], 'currency') !== false) ? ' style="color: blue; font-weight: bold;"' : '';
                    echo "<li$highlight>{$column['Field']} - {$column['Type']}</li>\n";
                }
                echo "</ul>\n";
            }
        }
    }
    
    echo "<h3>8. اختبار إنشاء صندوق</h3>\n";
    
    // Test creating a cash box
    require_once __DIR__ . '/../includes/cash_manager.php';
    
    $db = new Database();
    $cashManager = new CashManager($db);
    
    $testData = [
        'name' => 'صندوق اختبار إصلاح القيود',
        'currency_code' => 'USD',
        'initial_balance' => 1000.00,
        'branch_id' => 1,
        'responsible_user_id' => 1,
        'is_active' => 1
    ];
    
    $result = $cashManager->addCashBox($testData);
    if ($result) {
        echo "<p style='color: green;'>✓ تم إنشاء صندوق اختبار بنجاح (ID: $result)</p>\n";
        
        // Clean up
        $conn->query("DELETE FROM cash_boxes WHERE id = $result");
        echo "<p style='color: green;'>✓ تم حذف الصندوق التجريبي</p>\n";
    } else {
        echo "<p style='color: red;'>✗ فشل في إنشاء صندوق اختبار</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح قيود المفاتيح الخارجية!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='final_currencies_test.php' target='_blank'>اختبار نظام العملات الشامل</a></li>\n";
    echo "<li><a href='../dashboard/currency_management.php' target='_blank'>صفحة إدارة فئات العملات</a></li>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>اختبار صفحة الصناديق</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

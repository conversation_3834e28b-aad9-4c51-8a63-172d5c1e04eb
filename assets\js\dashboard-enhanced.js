// Enhanced Dashboard JavaScript for TrustPlus

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add loading animations to stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Add hover effects to navigation buttons
    const navButtons = document.querySelectorAll('.nav-btn');
    navButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add click effects to quick action cards
    const quickActionCards = document.querySelectorAll('.quick-action-card');
    quickActionCards.forEach(card => {
        card.addEventListener('click', function() {
            // Add ripple effect
            const ripple = document.createElement('div');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            ripple.style.width = '100px';
            ripple.style.height = '100px';
            ripple.style.marginLeft = '-50px';
            ripple.style.marginTop = '-50px';
            ripple.style.pointerEvents = 'none';
            
            this.style.position = 'relative';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add ripple animation CSS
    if (!document.querySelector('#ripple-styles')) {
        const style = document.createElement('style');
        style.id = 'ripple-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Add real-time updates for exchange rates
    function updateExchangeRates() {
        const rateItems = document.querySelectorAll('.rate-item');
        rateItems.forEach(item => {
            const changeElement = item.querySelector('.change');
            if (changeElement) {
                // Simulate real-time updates
                const currentValue = parseFloat(changeElement.textContent);
                const newValue = currentValue + (Math.random() - 0.5) * 0.1;
                changeElement.textContent = (newValue > 0 ? '+' : '') + newValue.toFixed(2) + '%';
                changeElement.style.color = newValue > 0 ? '#48bb78' : '#f56565';
            }
        });
    }

    // Update rates every 30 seconds
    setInterval(updateExchangeRates, 30000);

    // Add smooth scrolling for navigation
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N for new exchange
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            const exchangeBtn = document.querySelector('a[href="exchange.php"]');
            if (exchangeBtn) {
                exchangeBtn.click();
            }
        }
        
        // Ctrl/Cmd + T for new transfer
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            const transferBtn = document.querySelector('a[href="transfers.php"]');
            if (transferBtn) {
                transferBtn.click();
            }
        }
        
        // Ctrl/Cmd + C for customers
        if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
            e.preventDefault();
            const customersBtn = document.querySelector('a[href="customers.php"]');
            if (customersBtn) {
                customersBtn.click();
            }
        }
    });

    // Add chart animations
    function animateChart() {
        const chartCanvas = document.getElementById('operationsChart');
        if (chartCanvas) {
            const ctx = chartCanvas.getContext('2d');
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, 'rgba(102, 126, 234, 0.3)');
            gradient.addColorStop(1, 'rgba(102, 126, 234, 0.1)');
            
            // Update chart background
            if (window.operationsChart) {
                window.operationsChart.data.datasets[0].backgroundColor = gradient;
                window.operationsChart.update();
            }
        }
    }

    // Initialize chart animations after a delay
    setTimeout(animateChart, 1000);

    // Add responsive behavior for mobile
    function handleMobileLayout() {
        const isMobile = window.innerWidth <= 768;
        const statsGrid = document.querySelector('.stats-grid');
        const navigationButtons = document.querySelector('.navigation-buttons');
        
        if (isMobile) {
            if (statsGrid) {
                statsGrid.style.gridTemplateColumns = '1fr';
            }
            if (navigationButtons) {
                navigationButtons.style.justifyContent = 'center';
            }
        } else {
            if (statsGrid) {
                statsGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(280px, 1fr))';
            }
            if (navigationButtons) {
                navigationButtons.style.justifyContent = 'flex-start';
            }
        }
    }

    // Handle window resize
    window.addEventListener('resize', handleMobileLayout);
    handleMobileLayout();

    // Add theme toggle functionality (if needed)
    const themeToggle = document.querySelector('#theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
        });
    }

    // Load saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }

    // Add activity refresh functionality
    function refreshActivities() {
        const activitiesList = document.querySelector('.activities-list');
        if (activitiesList) {
            // Add loading state
            activitiesList.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">جاري التحديث...</p></div>';
            
            // Simulate refresh (in real app, this would be an AJAX call)
            setTimeout(() => {
                // Reload the page to get fresh data
                window.location.reload();
            }, 2000);
        }
    }

    // Add refresh button functionality
    const refreshBtn = document.querySelector('.refresh-activities');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshActivities);
    }

    // Add search functionality for quick actions
    const searchInput = document.querySelector('#quick-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const quickActions = document.querySelectorAll('.quick-action-card');
            
            quickActions.forEach(action => {
                const title = action.querySelector('.quick-action-title').textContent.toLowerCase();
                const desc = action.querySelector('.quick-action-desc').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || desc.includes(searchTerm)) {
                    action.style.display = 'block';
                } else {
                    action.style.display = 'none';
                }
            });
        });
    }

    // Add export functionality
    function exportDashboardData() {
        const data = {
            date: new Date().toISOString(),
            stats: {
                exchanges: document.querySelector('.stats-card.primary .stats-value')?.textContent,
                transfers: document.querySelector('.stats-card.info .stats-value')?.textContent,
                customers: document.querySelector('.stats-card.success .stats-value')?.textContent,
                profits: document.querySelector('.stats-card.warning .stats-value')?.textContent
            }
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        showNotification('تم تصدير بيانات لوحة التحكم بنجاح', 'success');
    }

    // Add export button functionality
    const exportBtn = document.querySelector('#export-dashboard');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportDashboardData);
    }

    console.log('Enhanced Dashboard JavaScript loaded successfully!');
}); 
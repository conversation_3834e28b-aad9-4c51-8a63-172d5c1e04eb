/**
 * Unified Sidebar JavaScript
 * حل موحد وجذري لجميع مشاكل الشريط الجانبي
 */

class UnifiedSidebar {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.mainContent = document.querySelector('.main-content');
        this.body = document.body;
        this.isInitialized = false;
        
        // حالة الشريط الجانبي
        this.state = {
            isVisible: false,
            isMobile: false,
            isCollapsed: false
        };
        
        // عناصر التحكم
        this.elements = {
            toggleBtn: null,
            overlay: null,
            collapsibleHeaders: []
        };
        
        this.init();
    }

    /**
     * تهيئة الشريط الجانبي
     */
    init() {
        if (this.isInitialized || !this.sidebar) {
            console.log('❌ فشل في العثور على الشريط الجانبي أو تم التهيئة مسبقاً');
            return;
        }

        console.log('🚀 تهيئة الشريط الجانبي الموحد...');

        // إزالة أي تضارب مع ملفات أخرى
        this.removeConflictingElements();

        // فرض إظهار الشريط الجانبي
        this.forceShowSidebar();

        // تحديد حالة الجهاز
        this.updateDeviceState();

        // إنشاء زر التبديل
        this.createToggleButton();

        // إنشاء طبقة التغطية
        this.createOverlay();

        // تهيئة الأقسام القابلة للطي
        this.initCollapsibleSections();

        // إضافة مستمعي الأحداث
        this.attachEventListeners();

        // تطبيق الحالة المحفوظة
        this.applySavedState();

        // استعادة حالة القوائم
        setTimeout(() => {
            this.restoreMenuStates();
        }, 200);

        this.isInitialized = true;
        console.log('✅ تم تهيئة الشريط الجانبي بنجاح');
    }

    /**
     * إزالة العناصر المتضاربة
     */
    removeConflictingElements() {
        // إزالة أي أزرار تبديل قديمة
        const oldToggles = document.querySelectorAll('.sidebar-toggle-btn:not(.unified), .mobile-sidebar-toggle, .old-sidebar-toggle');
        oldToggles.forEach(btn => btn.remove());

        // إزالة أي طبقات تغطية قديمة
        const oldOverlays = document.querySelectorAll('.sidebar-overlay:not(.unified), .mobile-overlay, .old-overlay');
        oldOverlays.forEach(overlay => overlay.remove());

        // إزالة أي كلاسات متضاربة من الشريط الجانبي
        this.sidebar.classList.remove('sidebar-collapsed', 'old-sidebar', 'mobile-sidebar');

        console.log('🧹 تم إزالة العناصر المتضاربة');
    }

    /**
     * فرض إظهار الشريط الجانبي
     */
    forceShowSidebar() {
        if (!this.sidebar) return;

        // فرض الأنماط الأساسية
        this.sidebar.style.display = 'flex';
        this.sidebar.style.visibility = 'visible';
        this.sidebar.style.opacity = '1';
        this.sidebar.style.position = 'fixed';
        this.sidebar.style.top = '0';
        this.sidebar.style.height = '100vh';
        this.sidebar.style.width = '250px';
        this.sidebar.style.zIndex = '1000';

        // على أجهزة سطح المكتب: إظهار دائماً
        if (window.innerWidth >= 992) {
            this.sidebar.style.right = '0';
            this.sidebar.style.transform = 'translateX(0)';
            this.state.isVisible = true;
        } else {
            // على الأجهزة المحمولة: إخفاء افتراضياً
            this.sidebar.style.right = '-250px';
            this.state.isVisible = false;
        }

        console.log('👁️ تم فرض إظهار الشريط الجانبي');
    }

    /**
     * تحديد حالة الجهاز (محمول أم سطح مكتب)
     */
    updateDeviceState() {
        const wasMobile = this.state.isMobile;
        this.state.isMobile = window.innerWidth < 992;
        
        if (wasMobile !== this.state.isMobile) {
            this.handleDeviceChange();
        }
    }

    /**
     * التعامل مع تغيير نوع الجهاز
     */
    handleDeviceChange() {
        if (this.state.isMobile) {
            // على الأجهزة المحمولة: إخفاء الشريط افتراضياً
            this.hideSidebar();
        } else {
            // على أجهزة سطح المكتب: إظهار الشريط
            this.showSidebar();
            this.removeOverlay();
        }
        
        this.updateToggleButton();
    }

    /**
     * إنشاء زر التبديل
     */
    createToggleButton() {
        // إزالة أي أزرار موجودة
        const existingBtns = document.querySelectorAll('.sidebar-toggle-btn, .mobile-sidebar-toggle');
        existingBtns.forEach(btn => btn.remove());
        
        // إنشاء زر جديد
        this.elements.toggleBtn = document.createElement('button');
        this.elements.toggleBtn.className = 'sidebar-toggle-btn unified';
        this.elements.toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        this.elements.toggleBtn.setAttribute('aria-label', 'تبديل القائمة الجانبية');
        this.elements.toggleBtn.setAttribute('type', 'button');
        
        // إضافة الأنماط
        Object.assign(this.elements.toggleBtn.style, {
            position: 'fixed',
            top: '15px',
            right: '15px',
            zIndex: '1001',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '50px',
            height: '50px',
            borderRadius: '50%',
            border: 'none',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            boxShadow: '0 4px 15px rgba(0, 123, 255, 0.3)',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
        });
        
        document.body.appendChild(this.elements.toggleBtn);
        this.updateToggleButton();
    }

    /**
     * تحديث زر التبديل
     */
    updateToggleButton() {
        if (!this.elements.toggleBtn) return;
        
        // إظهار/إخفاء الزر حسب نوع الجهاز
        if (this.state.isMobile) {
            this.elements.toggleBtn.style.display = 'flex';
            this.elements.toggleBtn.innerHTML = this.state.isVisible ? 
                '<i class="fas fa-times"></i>' : '<i class="fas fa-bars"></i>';
        } else {
            this.elements.toggleBtn.style.display = 'none';
        }
    }

    /**
     * إنشاء طبقة التغطية
     */
    createOverlay() {
        // إزالة أي طبقات موجودة
        const existingOverlays = document.querySelectorAll('.sidebar-overlay, .mobile-overlay');
        existingOverlays.forEach(overlay => overlay.remove());
        
        this.elements.overlay = document.createElement('div');
        this.elements.overlay.className = 'sidebar-overlay unified';
        
        Object.assign(this.elements.overlay.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: '999',
            display: 'none',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });
        
        document.body.appendChild(this.elements.overlay);
    }

    /**
     * إظهار الشريط الجانبي
     */
    showSidebar() {
        if (!this.sidebar) return;

        this.state.isVisible = true;

        // فرض الأنماط الأساسية
        this.sidebar.style.display = 'flex';
        this.sidebar.style.visibility = 'visible';
        this.sidebar.style.opacity = '1';

        if (this.state.isMobile) {
            // على الأجهزة المحمولة
            this.sidebar.style.right = '0';
            this.sidebar.style.transform = 'translateX(0)';
            this.showOverlay();
            this.body.style.overflow = 'hidden';
        } else {
            // على أجهزة سطح المكتب
            this.sidebar.style.right = '0';
            this.sidebar.style.transform = 'translateX(0)';
            if (this.mainContent) {
                this.mainContent.style.marginRight = '250px';
                this.mainContent.style.transition = 'margin-right 0.3s ease';
            }
        }

        this.sidebar.classList.add('show', 'active');
        this.sidebar.classList.remove('collapsed', 'sidebar-collapsed');
        this.updateToggleButton();
        this.saveState();

        console.log('📱 تم إظهار الشريط الجانبي');
    }

    /**
     * إخفاء الشريط الجانبي
     */
    hideSidebar() {
        if (!this.sidebar) return;

        this.state.isVisible = false;

        if (this.state.isMobile) {
            // على الأجهزة المحمولة
            this.sidebar.style.right = '-250px';
            this.sidebar.style.transform = 'translateX(100%)';
            this.hideOverlay();
            this.body.style.overflow = '';
        } else {
            // على أجهزة سطح المكتب
            this.sidebar.style.right = '-250px';
            this.sidebar.style.transform = 'translateX(100%)';
            if (this.mainContent) {
                this.mainContent.style.marginRight = '0';
                this.mainContent.style.transition = 'margin-right 0.3s ease';
            }
        }

        this.sidebar.classList.remove('show', 'active');
        this.sidebar.classList.add('collapsed', 'sidebar-collapsed');
        this.updateToggleButton();
        this.saveState();

        console.log('📱 تم إخفاء الشريط الجانبي');
    }

    /**
     * تبديل حالة الشريط الجانبي
     */
    toggleSidebar() {
        if (this.state.isVisible) {
            this.hideSidebar();
        } else {
            this.showSidebar();
        }
    }

    /**
     * إظهار طبقة التغطية
     */
    showOverlay() {
        if (this.elements.overlay && this.state.isMobile) {
            this.elements.overlay.style.display = 'block';
            setTimeout(() => {
                this.elements.overlay.style.opacity = '1';
            }, 10);
        }
    }

    /**
     * إخفاء طبقة التغطية
     */
    hideOverlay() {
        if (this.elements.overlay) {
            this.elements.overlay.style.opacity = '0';
            setTimeout(() => {
                this.elements.overlay.style.display = 'none';
            }, 300);
        }
    }

    /**
     * إزالة طبقة التغطية
     */
    removeOverlay() {
        this.hideOverlay();
    }

    /**
     * تهيئة الأقسام القابلة للطي - نظام موحد جديد
     */
    initCollapsibleSections() {
        console.log('🔧 تهيئة نظام القوائم الموحد...');

        // إزالة جميع مستمعي الأحداث القديمة
        this.removeOldEventListeners();

        // تهيئة النظام الجديد للقوائم
        this.initUnifiedMenuSystem();

        // إضافة تأثيرات بصرية
        this.addMenuHoverEffects();

        console.log('✅ تم تهيئة نظام القوائم الموحد');
    }

    /**
     * إزالة مستمعي الأحداث القديمة
     */
    removeOldEventListeners() {
        // إزالة جميع مستمعي الأحداث من العناصر القديمة
        const oldElements = document.querySelectorAll(`
            .sidebar-section-header,
            .sidebar-dropdown .dropdown-toggle,
            .transfers-main-link,
            .paypal-main-link,
            [data-bs-toggle="collapse"]
        `);

        oldElements.forEach(element => {
            element.replaceWith(element.cloneNode(true));
        });
    }

    /**
     * تهيئة النظام الموحد للقوائم
     */
    initUnifiedMenuSystem() {
        // تحويل جميع القوائم إلى النظام الموحد
        this.convertToUnifiedMenus();

        // إضافة مستمعي الأحداث للنظام الجديد
        this.attachUnifiedMenuEvents();
    }

    /**
     * تحويل القوائم إلى النظام الموحد
     */
    convertToUnifiedMenus() {
        // تحويل الأقسام القابلة للطي
        this.convertCollapsibleSections();

        // تحويل قوائم Bootstrap
        this.convertBootstrapDropdowns();

        // تحويل قوائم الحوالات
        this.convertTransfersMenu();

        // تحويل قوائم PayPal
        this.convertPayPalMenu();
    }

    /**
     * إضافة تأثيرات بصرية للقوائم
     */
    addMenuHoverEffects() {
        // تأثيرات للروابط الرئيسية
        const mainLinks = document.querySelectorAll('#sidebar .sidebar-link');
        mainLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-3px)';
                this.style.boxShadow = '0 2px 8px rgba(255, 255, 255, 0.1)';
            });

            link.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                }
            });
        });

        // تأثيرات للروابط الفرعية
        const subLinks = document.querySelectorAll('#sidebar .sidebar-sub-link, #sidebar .sidebar-sublink, #sidebar .transfers-sublink, #sidebar .paypal-sublink');
        subLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-5px)';
                this.style.boxShadow = '0 2px 8px rgba(255, 255, 255, 0.1)';
            });

            link.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                }
            });
        });

        // تأثيرات للعناوين القابلة للطي
        const collapsibleHeaders = document.querySelectorAll('#sidebar .sidebar-section-header.collapsible');
        collapsibleHeaders.forEach(header => {
            header.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-2px)';
                this.style.boxShadow = '0 2px 8px rgba(255, 255, 255, 0.1)';
            });

            header.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = 'none';
            });
        });

        // تأثيرات للقوائم المنسدلة
        const dropdownToggles = document.querySelectorAll('#sidebar .transfers-main-link, #sidebar .paypal-main-link');
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-2px)';
                this.style.boxShadow = '0 2px 8px rgba(255, 255, 255, 0.1)';
            });

            toggle.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = 'none';
            });
        });
    }

    /**
     * تحويل الأقسام القابلة للطي
     */
    convertCollapsibleSections() {
        const sections = document.querySelectorAll('.sidebar-section');

        sections.forEach(section => {
            const header = section.querySelector('.sidebar-section-header.collapsible');
            const content = section.querySelector('.sidebar-sub-links');

            if (header && content) {
                // تحويل العنوان
                header.className = 'menu-header';
                header.innerHTML = `
                    <i class="menu-icon ${header.querySelector('i').className}"></i>
                    <span class="menu-text">${header.querySelector('span').textContent}</span>
                    <i class="menu-arrow fas fa-chevron-down"></i>
                `;

                // تحويل المحتوى
                content.className = 'menu-content';

                // تحويل الروابط الفرعية
                const subLinks = content.querySelectorAll('.sidebar-sub-link');
                subLinks.forEach(link => {
                    link.className = 'menu-item';
                });
            }
        });
    }

    /**
     * تحويل قوائم Bootstrap
     */
    convertBootstrapDropdowns() {
        const dropdowns = document.querySelectorAll('.sidebar-dropdown');

        dropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.dropdown-toggle');
            const menu = dropdown.querySelector('.sidebar-submenu');

            if (toggle && menu) {
                // تحويل الزر
                toggle.className = 'menu-header';

                // تحويل القائمة
                menu.className = 'menu-content';

                // تحويل الروابط
                const links = menu.querySelectorAll('.sidebar-sublink');
                links.forEach(link => {
                    link.className = 'menu-item';
                });
            }
        });
    }

    /**
     * تحويل قائمة الحوالات
     */
    convertTransfersMenu() {
        const transfersMenu = document.querySelector('.transfers-menu');

        if (transfersMenu) {
            const mainLink = transfersMenu.querySelector('.transfers-main-link');
            const submenu = transfersMenu.querySelector('.transfers-submenu');

            if (mainLink && submenu) {
                // تحويل الرابط الرئيسي
                mainLink.className = 'menu-header';
                mainLink.innerHTML = `
                    <i class="menu-icon fas fa-paper-plane"></i>
                    <span class="menu-text">قائمة الحوالات</span>
                    <i class="menu-arrow fas fa-chevron-down"></i>
                `;

                // تحويل القائمة الفرعية
                submenu.className = 'menu-content';

                // تحويل الروابط الفرعية
                const sublinks = submenu.querySelectorAll('.transfers-sublink');
                sublinks.forEach(link => {
                    link.className = 'menu-item';
                });
            }
        }
    }

    /**
     * تحويل قائمة PayPal
     */
    convertPayPalMenu() {
        const paypalMenu = document.querySelector('.paypal-menu');

        if (paypalMenu) {
            const mainLink = paypalMenu.querySelector('.paypal-main-link');
            const submenu = paypalMenu.querySelector('.paypal-submenu');

            if (mainLink && submenu) {
                // تحويل الرابط الرئيسي
                mainLink.className = 'menu-header';
                mainLink.innerHTML = `
                    <i class="menu-icon fab fa-paypal" style="color: #0070ba;"></i>
                    <span class="menu-text">حوالات PayPal</span>
                    <i class="menu-arrow fas fa-chevron-down"></i>
                `;

                // تحويل القائمة الفرعية
                submenu.className = 'menu-content';

                // تحويل الروابط الفرعية
                const sublinks = submenu.querySelectorAll('.paypal-sublink');
                sublinks.forEach(link => {
                    link.className = 'menu-item';
                });
            }
        }
    }

    /**
     * إضافة مستمعي الأحداث للنظام الموحد
     */
    attachUnifiedMenuEvents() {
        // البحث عن جميع عناوين القوائم
        const menuHeaders = document.querySelectorAll('#sidebar .menu-header');

        menuHeaders.forEach(header => {
            header.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                this.toggleUnifiedMenu(header);
            });
        });

        console.log(`🔗 تم إضافة مستمعي الأحداث لـ ${menuHeaders.length} قائمة`);
    }

    /**
     * تبديل حالة القائمة الموحدة
     */
    toggleUnifiedMenu(header) {
        const content = header.nextElementSibling;
        const arrow = header.querySelector('.menu-arrow');

        if (!content || !content.classList.contains('menu-content')) {
            console.warn('⚠️ لم يتم العثور على محتوى القائمة');
            return;
        }

        const isExpanded = content.classList.contains('expanded');

        if (isExpanded) {
            // إغلاق القائمة
            content.classList.remove('expanded');
            header.classList.remove('expanded');
            if (arrow) arrow.style.transform = 'rotate(0deg)';

            console.log('📁 تم إغلاق القائمة');
        } else {
            // إغلاق جميع القوائم الأخرى أولاً
            this.closeAllUnifiedMenus();

            // فتح القائمة الحالية
            content.classList.add('expanded');
            header.classList.add('expanded');
            if (arrow) arrow.style.transform = 'rotate(180deg)';

            console.log('📂 تم فتح القائمة');
        }

        // حفظ الحالة
        this.saveMenuState(header, !isExpanded);
    }

    /**
     * إغلاق جميع القوائم الموحدة
     */
    closeAllUnifiedMenus() {
        const allContents = document.querySelectorAll('#sidebar .menu-content.expanded');
        const allHeaders = document.querySelectorAll('#sidebar .menu-header.expanded');
        const allArrows = document.querySelectorAll('#sidebar .menu-arrow');

        allContents.forEach(content => content.classList.remove('expanded'));
        allHeaders.forEach(header => header.classList.remove('expanded'));
        allArrows.forEach(arrow => arrow.style.transform = 'rotate(0deg)');
    }

    /**
     * حفظ حالة القائمة
     */
    saveMenuState(header, isExpanded) {
        const menuText = header.querySelector('.menu-text');
        if (menuText) {
            const menuId = menuText.textContent.trim().replace(/\s+/g, '-');
            localStorage.setItem(`unified-menu-${menuId}`, isExpanded ? 'expanded' : 'collapsed');
        }
    }

    /**
     * استعادة حالة القوائم المحفوظة
     */
    restoreMenuStates() {
        const menuHeaders = document.querySelectorAll('#sidebar .menu-header');

        menuHeaders.forEach(header => {
            const menuText = header.querySelector('.menu-text');
            if (menuText) {
                const menuId = menuText.textContent.trim().replace(/\s+/g, '-');
                const savedState = localStorage.getItem(`unified-menu-${menuId}`);

                // فتح القائمة إذا كانت تحتوي على رابط نشط أو كانت محفوظة كمفتوحة
                const content = header.nextElementSibling;
                const hasActiveItem = content && content.querySelector('.menu-item.active');

                if (savedState === 'expanded' || hasActiveItem) {
                    setTimeout(() => {
                        this.toggleUnifiedMenu(header);
                    }, 100);
                }
            }
        });
    }

    /* الدوال القديمة تم استبدالها بالنظام الموحد */

    /**
     * إضافة مستمعي الأحداث
     */
    attachEventListeners() {
        // زر التبديل
        if (this.elements.toggleBtn) {
            this.elements.toggleBtn.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // طبقة التغطية
        if (this.elements.overlay) {
            this.elements.overlay.addEventListener('click', () => {
                if (this.state.isMobile) {
                    this.hideSidebar();
                }
            });
        }

        // تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.updateDeviceState();
        });

        // مفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.state.isVisible && this.state.isMobile) {
                this.hideSidebar();
            }
        });

        // النقر خارج الشريط الجانبي
        document.addEventListener('click', (e) => {
            if (this.state.isMobile && this.state.isVisible &&
                !this.sidebar.contains(e.target) &&
                e.target !== this.elements.toggleBtn &&
                !this.elements.toggleBtn.contains(e.target)) {
                this.hideSidebar();
            }
        });
    }

    /**
     * حفظ الحالة
     */
    saveState() {
        const stateKey = this.state.isMobile ? 'sidebar-mobile-state' : 'sidebar-desktop-state';
        const stateValue = this.state.isVisible ? 'visible' : 'hidden';
        localStorage.setItem(stateKey, stateValue);
    }

    /**
     * تطبيق الحالة المحفوظة
     */
    applySavedState() {
        const stateKey = this.state.isMobile ? 'sidebar-mobile-state' : 'sidebar-desktop-state';
        const savedState = localStorage.getItem(stateKey);

        console.log(`📊 تطبيق الحالة المحفوظة: ${savedState} (${this.state.isMobile ? 'محمول' : 'سطح مكتب'})`);

        if (this.state.isMobile) {
            // على الأجهزة المحمولة: إخفاء افتراضياً
            this.hideSidebar();
        } else {
            // على أجهزة سطح المكتب: إظهار دائماً (إلا إذا كان محفوظ كمخفي)
            if (savedState === 'hidden') {
                this.hideSidebar();
            } else {
                this.showSidebar();
            }
        }

        // التأكد من إظهار الشريط على أجهزة سطح المكتب
        if (!this.state.isMobile) {
            setTimeout(() => {
                this.forceShowSidebar();
                if (savedState !== 'hidden') {
                    this.showSidebar();
                }
            }, 100);
        }
    }

    /**
     * تنظيف الموارد
     */
    destroy() {
        // إزالة مستمعي الأحداث
        if (this.elements.toggleBtn) {
            this.elements.toggleBtn.remove();
        }

        if (this.elements.overlay) {
            this.elements.overlay.remove();
        }

        // إعادة تعيين الأنماط
        if (this.sidebar) {
            this.sidebar.style.right = '';
            this.sidebar.classList.remove('show', 'active', 'collapsed');
        }

        if (this.mainContent) {
            this.mainContent.style.marginRight = '';
        }

        this.body.style.overflow = '';

        this.isInitialized = false;
    }
}

// منع تضارب ملفات JavaScript الأخرى
window.UNIFIED_SIDEBAR_LOADED = true;

// تهيئة الشريط الجانبي عند تحميل الصفحة
let unifiedSidebar = null;

document.addEventListener('DOMContentLoaded', function() {
    // التأكد من عدم وجود تهيئة مسبقة
    if (window.UNIFIED_SIDEBAR_LOADED && !unifiedSidebar) {
        unifiedSidebar = new UnifiedSidebar();

        // جعل الكائن متاحاً عالمياً للتشخيص
        window.unifiedSidebar = unifiedSidebar;

        console.log('🎉 تم تحميل الشريط الجانبي الموحد بنجاح!');
    }
});

// تصدير الكلاس للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedSidebar;
}

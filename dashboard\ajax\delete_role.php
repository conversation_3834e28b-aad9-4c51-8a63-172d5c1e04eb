<?php
/**
 * حذف دور عبر AJAX
 * Delete Role via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/role_manager.php';
require_once __DIR__ . '/../../includes/activity_helper.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.delete')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لحذف الأدوار'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

try {
    $id = (int)($_POST['id'] ?? 0);
    
    if (!$id) {
        echo json_encode([
            'success' => false,
            'message' => 'معرف الدور مطلوب'
        ]);
        exit;
    }
    
    // منع حذف دور الأدمن
    if ($id == 1) {
        echo json_encode([
            'success' => false,
            'message' => 'لا يمكن حذف دور مدير النظام'
        ]);
        exit;
    }
    
    $db = new Database();
    $roleMgr = new RoleManager($db);
    
    // التحقق من وجود الدور
    $role = $roleMgr->getRoleById($id);
    if (!$role) {
        echo json_encode([
            'success' => false,
            'message' => 'الدور غير موجود'
        ]);
        exit;
    }
    
    // التحقق من وجود مستخدمين مرتبطين بهذا الدور
    $userCount = $roleMgr->getRoleUserCount($id);
    if ($userCount > 0) {
        echo json_encode([
            'success' => false,
            'message' => "لا يمكن حذف الدور لأنه مرتبط بـ $userCount مستخدم. يرجى نقل المستخدمين لدور آخر أولاً."
        ]);
        exit;
    }
    
    // حذف الدور
    $success = $roleMgr->deleteRole($id);
    
    if ($success) {
        // تسجيل العملية
        ActivityHelper::logDelete(
            'roles',
            "الدور: " . $role['name'],
            $role,
            $id
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الدور بنجاح'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'فشل في حذف الدور'
        ]);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    ActivityHelper::logError(
        'roles',
        'delete_role',
        'فشل في حذف الدور: ' . $e->getMessage(),
        [
            'attempted_data' => $_POST,
            'error' => $e->getMessage()
        ]
    );
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

<?php
/**
 * Delete Cash Box Movement
 * Handle deletion of cash box movements
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/cash_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('cash.movements.delete');

$db = new Database();
$cashManager = new CashManager($db);

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    set_flash('danger', 'طريقة طلب غير صحيحة');
    redirect('cash.php');
    exit;
}

// Get movement ID and cash box ID
$movementId = (int)($_POST['movement_id'] ?? 0);
$cashBoxId = (int)($_POST['cash_box_id'] ?? 0);

if ($movementId <= 0 || $cashBoxId <= 0) {
    set_flash('danger', 'معرفات غير صحيحة');
    redirect('cash.php');
    exit;
}

try {
    // Get movement details first
    $movement = $cashManager->getCashMovementById($movementId);
    
    if (!$movement) {
        set_flash('danger', 'الحركة غير موجودة');
        redirect('cash_box_history.php?id=' . $cashBoxId);
        exit;
    }
    
    // Check if movement belongs to the specified cash box
    if ($movement['cash_box_id'] != $cashBoxId) {
        set_flash('danger', 'الحركة لا تنتمي لهذا الصندوق');
        redirect('cash_box_history.php?id=' . $cashBoxId);
        exit;
    }
    
    // Check if movement is linked to exchange or transfer
    if (!empty($movement['exchange_id']) || !empty($movement['transfer_id'])) {
        set_flash('danger', 'لا يمكن حذف حركة مرتبطة بعملية صرافة أو تحويل');
        redirect('cash_box_history.php?id=' . $cashBoxId);
        exit;
    }
    
    // Delete the movement
    $success = $cashManager->deleteMovement($movementId);
    
    if ($success) {
        set_flash('success', 'تم حذف الحركة بنجاح');
    } else {
        set_flash('danger', 'فشل في حذف الحركة');
    }
    
} catch (Exception $e) {
    set_flash('danger', 'خطأ في حذف الحركة: ' . $e->getMessage());
}

// Redirect back to cash box history
redirect('cash_box_history.php?id=' . $cashBoxId); 
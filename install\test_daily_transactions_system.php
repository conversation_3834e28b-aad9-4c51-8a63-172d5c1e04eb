<?php
/**
 * اختبار نظام المعاملات اليومية
 * Test Daily Transactions System
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام المعاملات اليومية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-vial text-primary"></i>
            اختبار نظام المعاملات اليومية
        </h1>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                echo "<div class='test-result test-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        // اختبار 1: اتصال قاعدة البيانات
        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // اختبار 2: فحص وجود الجداول
        $required_tables = ['countries', 'daily_transactions', 'daily_transaction_history', 'deleted_daily_transactions'];
        
        foreach ($required_tables as $table) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
                $exists = $stmt->rowCount() > 0;
                showResult("جدول {$table}", $exists, $exists ? "الجدول موجود" : "الجدول غير موجود");
            } catch (Exception $e) {
                showResult("جدول {$table}", false, $e->getMessage());
            }
        }

        // اختبار 3: فحص وجود الإجراءات المخزنة
        $required_procedures = ['CreateDailyTransaction', 'SearchDailyTransactions', 'DeleteDailyTransaction', 'UpdateDailyTransaction', 'GetDeletedDailyTransactions'];
        
        foreach ($required_procedures as $procedure) {
            try {
                $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = '{$procedure}' AND Db = '" . DB_NAME . "'");
                $exists = $stmt->rowCount() > 0;
                showResult("إجراء {$procedure}", $exists, $exists ? "الإجراء موجود" : "الإجراء غير موجود");
            } catch (Exception $e) {
                showResult("إجراء {$procedure}", false, $e->getMessage());
            }
        }

        // اختبار 4: فحص وجود الـ View
        try {
            $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_" . DB_NAME . " = 'v_daily_transactions_full'");
            $exists = $stmt->rowCount() > 0;
            showResult("عرض v_daily_transactions_full", $exists, $exists ? "العرض موجود" : "العرض غير موجود");
        } catch (Exception $e) {
            showResult("عرض v_daily_transactions_full", false, $e->getMessage());
        }

        // اختبار 5: فحص وجود الـ Trigger
        try {
            $stmt = $pdo->query("SHOW TRIGGERS LIKE 'before_daily_transaction_delete'");
            $exists = $stmt->rowCount() > 0;
            showResult("مشغل before_daily_transaction_delete", $exists, $exists ? "المشغل موجود" : "المشغل غير موجود");
        } catch (Exception $e) {
            showResult("مشغل before_daily_transaction_delete", false, $e->getMessage());
        }

        // اختبار 6: فحص بيانات الدول
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM countries WHERE is_active = 1");
            $count = $stmt->fetch()['count'];
            showResult("بيانات الدول", $count > 0, "يوجد {$count} دولة نشطة");
        } catch (Exception $e) {
            showResult("بيانات الدول", false, $e->getMessage());
        }

        // اختبار 7: فحص الصلاحيات
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM permissions WHERE module = 'daily_transactions'");
            $count = $stmt->fetch()['count'];
            showResult("صلاحيات المعاملات اليومية", $count > 0, "يوجد {$count} صلاحية");
        } catch (Exception $e) {
            showResult("صلاحيات المعاملات اليومية", false, $e->getMessage());
        }

        // اختبار 8: اختبار إنشاء معاملة تجريبية
        try {
            // البحث عن دولة نشطة
            $stmt = $pdo->query("SELECT id FROM countries WHERE is_active = 1 LIMIT 1");
            $country = $stmt->fetch();
            
            if ($country) {
                // محاولة إنشاء معاملة تجريبية
                $stmt = $pdo->prepare("CALL CreateDailyTransaction(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, @transaction_id, @transaction_number, @result_message)");
                $stmt->execute([
                    $country['id'], // country_id
                    1000.00, // base_amount
                    1.5, // customer_rate
                    'multiply', // operation_type
                    250.0, // exchange_rate
                    'cash', // delivery_type
                    null, // transfer_amount
                    null, // recipient_name
                    'معاملة تجريبية', // notes
                    1, // branch_id
                    1 // created_by
                ]);
                
                // إغلاق النتائج المعلقة
                while ($stmt->nextRowset()) {
                    // استهلاك جميع مجموعات النتائج
                }
                $stmt->closeCursor();
                
                // الحصول على النتائج
                $result_stmt = $pdo->query("SELECT @transaction_id as transaction_id, @transaction_number as transaction_number, @result_message as result_message");
                $result = $result_stmt->fetch();
                $result_stmt->closeCursor();
                
                if ($result['transaction_id'] > 0) {
                    showResult("إنشاء معاملة تجريبية", true, "تم إنشاء المعاملة رقم: " . $result['transaction_number']);
                    
                    // حذف المعاملة التجريبية
                    $delete_stmt = $pdo->prepare("DELETE FROM daily_transactions WHERE id = ?");
                    $delete_stmt->execute([$result['transaction_id']]);
                    showInfo("تم حذف المعاملة التجريبية");
                } else {
                    showResult("إنشاء معاملة تجريبية", false, $result['result_message']);
                }
            } else {
                showResult("إنشاء معاملة تجريبية", false, "لا توجد دول نشطة في قاعدة البيانات");
            }
        } catch (Exception $e) {
            showResult("إنشاء معاملة تجريبية", false, $e->getMessage());
        }

        // اختبار 9: اختبار البحث
        try {
            $results = PDODatabase::callProcedure($pdo, 'SearchDailyTransactions', [
                null, null, null, null, null, null, null, null, 10, 0
            ]);
            showResult("اختبار البحث", true, "تم تنفيذ البحث بنجاح - عدد النتائج: " . count($results));
        } catch (Exception $e) {
            showResult("اختبار البحث", false, $e->getMessage());
        }

        // اختبار 10: اختبار ملفات الواجهة
        $interface_files = [
            'dashboard/daily_transactions.php',
            'dashboard/add_daily_transaction.php',
            'dashboard/view_daily_transaction.php',
            'dashboard/deleted_daily_transactions.php'
        ];
        
        foreach ($interface_files as $file) {
            $file_path = __DIR__ . '/../' . $file;
            $exists = file_exists($file_path);
            showResult("ملف {$file}", $exists, $exists ? "الملف موجود" : "الملف غير موجود");
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 90) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> النظام جاهز للاستخدام";
            echo "</div>";
        } elseif ($success_rate >= 70) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> النظام يعمل مع بعض المشاكل البسيطة";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج إصلاح!</strong> هناك مشاكل تحتاج إلى حل";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <div class="text-center mt-4">
            <a href="../dashboard/daily_transactions.php" class="btn btn-primary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>
                الذهاب إلى المعاملات اليومية
            </a>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary btn-lg ms-2">
                <i class="fas fa-redo me-2"></i>
                إعادة الاختبار
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

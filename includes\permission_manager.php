<?php
require_once __DIR__ . '/database.php';

class PermissionManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Fetch all permissions.
     *
     * @return array<int, array<string, mixed>>
     */
    public function getAllPermissions(): array
    {
        $sql = "SELECT id, name, description, module FROM permissions ORDER BY id ASC";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return [];
        }
        $stmt->execute();
        $result      = $stmt->get_result();
        $permissions = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $permissions;
    }

    /**
     * Get a permission by ID.
     */
    public function getPermissionById(int $id): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM permissions WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $res = $stmt->get_result();
        $perm = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $perm;
    }

    /**
     * Check if permission name exists.
     */
    public function permissionExists(string $name, int $excludeId = null): bool
    {
        $sql = 'SELECT COUNT(*) FROM permissions WHERE name = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($excludeId !== null) {
            $stmt->bind_param('si', $name, $excludeId);
        } else {
            $stmt->bind_param('s', $name);
        }
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    /**
     * Get permission by name
     */
    public function getPermissionByName(string $name): ?array
    {
        $stmt = $this->db->prepare('SELECT id, name, description, module FROM permissions WHERE name = ?');
        if (!$stmt) return null;
        $stmt->bind_param('s', $name);
        $stmt->execute();
        $result = $stmt->get_result();
        $permission = $result ? $result->fetch_assoc() : null;
        $stmt->close();
        return $permission;
    }

    public function addPermission(array $data)
    {
        $stmt = $this->db->prepare('INSERT INTO permissions (name, description, module, created_at) VALUES (?,?,?, NOW())');
        if (!$stmt) return false;
        $stmt->bind_param('sss', $data['name'], $data['description'], $data['module']);
        if ($stmt->execute()) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        $stmt->close();
        return false;
    }

    public function updatePermission(int $id, array $data): bool
    {
        if (empty($data)) return false;
        $fields = [];
        $params = [];
        $types  = '';
        foreach ($data as $k => $v) {
            $fields[] = "$k = ?";
            $params[] = $v;
            $types   .= 's';
        }
        $sql = 'UPDATE permissions SET ' . implode(', ', $fields) . ' WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $types .= 'i';
        $params[] = $id;
        $stmt->bind_param($types, ...$params);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function deletePermission(int $id): bool
    {
        $stmt = $this->db->prepare('DELETE FROM permissions WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $id);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Get permission IDs linked to a role.
     */
    public function getPermissionsByRoleId(int $roleId): array
    {
        $ids = [];
        $stmt = $this->db->prepare('SELECT permission_id FROM role_permissions WHERE role_id = ?');
        if ($stmt) {
            $stmt->bind_param('i', $roleId);
            $stmt->execute();
            $result = $stmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $ids[] = (int) $row['permission_id'];
            }
            $stmt->close();
        }
        return $ids;
    }

    /**
     * Fetch all permissions grouped by module.
     */
    public function getAllPermissionsGroupedByModule(): array
    {
        $modules = [];
        $stmt = $this->db->prepare('SELECT * FROM permissions ORDER BY module, name');
        if ($stmt) {
            $stmt->execute();
            $result = $stmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $mod = $row['module'] ?? 'other';
                $modules[$mod][] = $row;
            }
            $stmt->close();
        }
        return $modules;
    }

    /**
     * Update role permissions (replace).
     */
    public function updateRolePermissions(int $roleId, array $permIds): bool
    {
        // Begin transaction
        $this->db->begin_transaction();
        $ok = true;
        // Delete existing
        $del = $this->db->prepare('DELETE FROM role_permissions WHERE role_id = ?');
        if ($del) {
            $del->bind_param('i', $roleId);
            $ok = $del->execute();
            $del->close();
        } else {
            $ok = false;
        }
        if ($ok && !empty($permIds)) {
            $ins = $this->db->prepare('INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)');
            if (!$ins) $ok = false;
            else {
                foreach ($permIds as $pid) {
                    $ins->bind_param('ii', $roleId, $pid);
                    if (!$ins->execute()) {
                        $ok = false;
                        break;
                    }
                }
                $ins->close();
            }
        }
        if ($ok) {
            $this->db->commit();
        } else {
            $this->db->rollback();
        }
        return $ok;
    }
} 
<?php
/**
 * إصلاح مشكلة القوائم المنسدلة في الشريط الجانبي
 * Fix Sidebar Collapsible Issues
 */

echo "<h2>🔧 إصلاح مشكلة القوائم المنسدلة في الشريط الجانبي</h2>\n";

$fixes = [];
$errors = [];

try {
    // التحقق من الملفات المطلوبة
    echo "<h3>1. التحقق من الملفات المطلوبة</h3>\n";
    
    $requiredFiles = [
        'assets/css/sidebar.css' => 'ملف CSS للشريط الجانبي',
        'assets/js/sidebar.js' => 'ملف JavaScript للشريط الجانبي',
        'includes/sidebar.php' => 'ملف HTML للشريط الجانبي',
        'includes/header.php' => 'ملف Header',
        'includes/footer.php' => 'ملف Footer'
    ];
    
    foreach ($requiredFiles as $file => $description) {
        $fullPath = __DIR__ . '/../' . $file;
        if (file_exists($fullPath)) {
            echo "<p style='color: green;'>✅ $description موجود</p>\n";
            $fixes[] = "$description موجود";
        } else {
            echo "<p style='color: red;'>❌ $description مفقود: $file</p>\n";
            $errors[] = "$description مفقود";
        }
    }
    
    // التحقق من تحميل CSS في header
    echo "<h3>2. التحقق من تحميل CSS</h3>\n";
    
    $headerContent = file_get_contents(__DIR__ . '/../includes/header.php');
    if (strpos($headerContent, 'sidebar.css') !== false) {
        echo "<p style='color: green;'>✅ ملف sidebar.css محمل في header.php</p>\n";
        $fixes[] = "CSS محمل بشكل صحيح";
    } else {
        echo "<p style='color: red;'>❌ ملف sidebar.css غير محمل في header.php</p>\n";
        $errors[] = "CSS غير محمل";
        
        // إصلاح تلقائي
        $headerContent = str_replace(
            '<link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/main.css">',
            '<link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/main.css">' . "\n" .
            '    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/sidebar.css">',
            $headerContent
        );
        
        if (file_put_contents(__DIR__ . '/../includes/header.php', $headerContent)) {
            echo "<p style='color: green;'>✅ تم إصلاح تحميل CSS تلقائياً</p>\n";
            $fixes[] = "تم إصلاح تحميل CSS";
        }
    }
    
    // التحقق من تحميل JavaScript في footer
    echo "<h3>3. التحقق من تحميل JavaScript</h3>\n";
    
    $footerContent = file_get_contents(__DIR__ . '/../includes/footer.php');
    if (strpos($footerContent, 'sidebar.js') !== false) {
        echo "<p style='color: green;'>✅ ملف sidebar.js محمل في footer.php</p>\n";
        $fixes[] = "JavaScript محمل بشكل صحيح";
    } else {
        echo "<p style='color: red;'>❌ ملف sidebar.js غير محمل في footer.php</p>\n";
        $errors[] = "JavaScript غير محمل";
        
        // إصلاح تلقائي
        $footerContent = str_replace(
            '<script src="<?php echo BASE_URL; ?>assets/js/main.js"></script>',
            '<script src="<?php echo BASE_URL; ?>assets/js/main.js"></script>' . "\n" .
            '    <script src="<?php echo BASE_URL; ?>assets/js/sidebar.js"></script>',
            $footerContent
        );
        
        if (file_put_contents(__DIR__ . '/../includes/footer.php', $footerContent)) {
            echo "<p style='color: green;'>✅ تم إصلاح تحميل JavaScript تلقائياً</p>\n";
            $fixes[] = "تم إصلاح تحميل JavaScript";
        }
    }
    
    // التحقق من هيكل HTML في sidebar
    echo "<h3>4. التحقق من هيكل HTML</h3>\n";
    
    $sidebarContent = file_get_contents(__DIR__ . '/../includes/sidebar.php');
    
    // التحقق من وجود الكلاسات المطلوبة
    $requiredClasses = [
        'sidebar-section-header collapsible' => 'رؤوس القوائم القابلة للطي',
        'sidebar-sub-links' => 'القوائم الفرعية',
        'toggle-icon' => 'أيقونات التبديل'
    ];
    
    foreach ($requiredClasses as $class => $description) {
        if (strpos($sidebarContent, $class) !== false) {
            echo "<p style='color: green;'>✅ $description موجودة</p>\n";
            $fixes[] = "$description موجودة";
        } else {
            echo "<p style='color: red;'>❌ $description مفقودة</p>\n";
            $errors[] = "$description مفقودة";
        }
    }
    
    // التحقق من data-target attributes
    $dataTargetCount = substr_count($sidebarContent, 'data-target=');
    echo "<p>📊 عدد عناصر data-target: $dataTargetCount</p>\n";
    
    if ($dataTargetCount > 0) {
        echo "<p style='color: green;'>✅ توجد عناصر data-target</p>\n";
        $fixes[] = "عناصر data-target موجودة";
    } else {
        echo "<p style='color: red;'>❌ لا توجد عناصر data-target</p>\n";
        $errors[] = "عناصر data-target مفقودة";
    }
    
    // اختبار CSS
    echo "<h3>5. اختبار CSS</h3>\n";
    
    $cssContent = file_get_contents(__DIR__ . '/../assets/css/sidebar.css');
    
    $requiredCSSRules = [
        '.sidebar-section-header.collapsed .toggle-icon' => 'تدوير أيقونة التبديل',
        '.sidebar-sub-links.collapsed' => 'إخفاء القوائم المطوية',
        'max-height: 0' => 'ارتفاع صفر للقوائم المطوية',
        'transform: rotate' => 'تدوير الأيقونات'
    ];
    
    foreach ($requiredCSSRules as $rule => $description) {
        if (strpos($cssContent, $rule) !== false) {
            echo "<p style='color: green;'>✅ $description موجود في CSS</p>\n";
            $fixes[] = "$description في CSS";
        } else {
            echo "<p style='color: orange;'>⚠️ $description قد يكون مفقود في CSS</p>\n";
        }
    }
    
    // اختبار JavaScript
    echo "<h3>6. اختبار JavaScript</h3>\n";
    
    $jsContent = file_get_contents(__DIR__ . '/../assets/js/sidebar.js');
    
    $requiredJSFeatures = [
        'addEventListener' => 'مستمعات الأحداث',
        'classList.toggle' => 'تبديل الكلاسات',
        'data-target' => 'استخدام data-target',
        'DOMContentLoaded' => 'تهيئة عند تحميل DOM'
    ];
    
    foreach ($requiredJSFeatures as $feature => $description) {
        if (strpos($jsContent, $feature) !== false) {
            echo "<p style='color: green;'>✅ $description موجود في JavaScript</p>\n";
            $fixes[] = "$description في JavaScript";
        } else {
            echo "<p style='color: red;'>❌ $description مفقود في JavaScript</p>\n";
            $errors[] = "$description مفقود في JavaScript";
        }
    }
    
    // إنشاء ملف اختبار سريع
    echo "<h3>7. إنشاء ملف اختبار</h3>\n";
    
    $testFile = __DIR__ . '/test_sidebar_quick.html';
    $testHTML = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار الشريط الجانبي</title>
    <link rel="stylesheet" href="../assets/css/sidebar.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>اختبار الشريط الجانبي</h1>
    
    <div class="test-section">
        <h3>اختبار القائمة المنسدلة</h3>
        
        <div class="sidebar-section">
            <div class="sidebar-section-header collapsible" data-target="test-section">
                <i class="fas fa-users"></i>
                <span>قائمة اختبار</span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </div>
            
            <div class="sidebar-sub-links" id="test-section">
                <a href="#" class="sidebar-link sidebar-sub-link">
                    <i class="fas fa-user"></i>
                    <span>عنصر 1</span>
                </a>
                <a href="#" class="sidebar-link sidebar-sub-link">
                    <i class="fas fa-user-plus"></i>
                    <span>عنصر 2</span>
                </a>
            </div>
        </div>
        
        <button onclick="testToggle()">اختبار التبديل</button>
    </div>
    
    <script src="../assets/js/sidebar.js"></script>
    <script>
        function testToggle() {
            const header = document.querySelector(".sidebar-section-header");
            if (header) {
                header.click();
                console.log("تم النقر على الرأس");
            }
        }
        
        console.log("ملف الاختبار محمل");
    </script>
</body>
</html>';
    
    if (file_put_contents($testFile, $testHTML)) {
        echo "<p style='color: green;'>✅ تم إنشاء ملف اختبار: <a href='test_sidebar_quick.html' target='_blank'>test_sidebar_quick.html</a></p>\n";
        $fixes[] = "ملف اختبار تم إنشاؤه";
    }
    
    // النتائج النهائية
    echo "<hr>\n";
    echo "<h3>📊 ملخص النتائج</h3>\n";
    
    $totalFixes = count($fixes);
    $totalErrors = count($errors);
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<p><strong>الإصلاحات الناجحة:</strong> $totalFixes</p>\n";
    echo "<p><strong>المشاكل المتبقية:</strong> $totalErrors</p>\n";
    
    if ($totalErrors === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<h4>🎉 ممتاز! جميع المشاكل تم حلها</h4>\n";
        echo "<p>الشريط الجانبي يجب أن يعمل الآن بشكل صحيح.</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<h4>⚠️ هناك مشاكل تحتاج إلى إصلاح يدوي</h4>\n";
        echo "<ul>\n";
        foreach ($errors as $error) {
            echo "<li>$error</li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
    }
    
    echo "</div>\n";
    
    // خطوات الاختبار
    echo "<h3>🧪 خطوات الاختبار</h3>\n";
    echo "<ol>\n";
    echo "<li>انتقل إلى <a href='../dashboard/'>لوحة التحكم</a></li>\n";
    echo "<li>جرب النقر على أسهم القوائم في الشريط الجانبي</li>\n";
    echo "<li>افتح وحدة تحكم المتصفح (F12) وتحقق من الأخطاء</li>\n";
    echo "<li>اختبر <a href='test_sidebar_functionality.php'>صفحة اختبار الشريط الجانبي</a></li>\n";
    echo "<li>اختبر <a href='test_sidebar_quick.html' target='_blank'>الاختبار السريع</a></li>\n";
    echo "</ol>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h3>❌ خطأ في الإصلاح</h3>\n";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "<hr>\n";
echo "<p><a href='../dashboard/'>العودة إلى لوحة التحكم</a> | ";
echo "<a href='test_sidebar_functionality.php'>اختبار الشريط الجانبي</a></p>\n";
?>

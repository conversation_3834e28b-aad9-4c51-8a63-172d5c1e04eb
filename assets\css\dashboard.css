/*
 * Trust Plus Financial System - Dashboard Layout CSS
 * Collapsible sidebar layout with state persistence and responsive design
 */

/* ===== CSS VARIABLES ===== */
:root {
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 0px;
  --transition-duration: 0.3s;
}

/* ===== MAIN LAYOUT WRAPPER ===== */
.page-wrapper {
  display: flex;
  min-height: 100vh;
  direction: rtl; /* RTL layout for Arabic */
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
  position: fixed;
  top: 0;
  right: 0; /* RTL: sidebar positioned on the right */
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--bg-color);
  background-image: var(--primary-gradient);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  transition: all var(--transition-duration) ease;
  transform: translateX(0); /* Default: visible */
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* ===== MAIN CONTENT AREA ===== */
.main-content {
  flex-grow: 1;
  margin-right: var(--sidebar-width); /* RTL: offset for sidebar on right */
  padding: 2rem;
  background-color: var(--bg-light);
  min-height: 100vh;
  transition: margin-right var(--transition-duration) ease;
  width: calc(100% - var(--sidebar-width)); /* Ensure proper width calculation */
}

/* ===== SIDEBAR COLLAPSED STATE ===== */
/* When sidebar is collapsed on desktop */
.sidebar-collapsed .sidebar {
  transform: translateX(100%); /* RTL: slide out to the right */
}

.sidebar-collapsed .main-content {
  margin-right: 0; /* RTL: remove right margin when sidebar is collapsed */
  width: 100%; /* Take full width when sidebar is collapsed */
}

/* ===== SIDEBAR NAVIGATION LINKS ===== */
.sidebar-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  margin: 4px 12px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
}

.sidebar-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: translateX(-2px); /* RTL: move left on hover */
  text-decoration: none;
}

.sidebar-link.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-weight: 600;
}

.sidebar-link.active::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 70%;
  background: white;
  border-radius: 3px 0 0 3px;
}

.sidebar-link i {
  margin-left: 12px; /* RTL: icon on the left of text */
  width: 20px;
  text-align: center;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.sidebar-link:hover i {
  transform: scale(1.1);
}

.sidebar-link.active i {
  transform: scale(1.15);
}

/* ===== SIDEBAR SECTIONS ===== */
.sidebar-section {
  margin: 8px 0;
}

.sidebar-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  margin: 8px 12px 4px 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.sidebar-section-header:hover {
  color: rgba(255, 255, 255, 0.95);
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.sidebar-section-header.collapsed {
  border-bottom: none;
}

.sidebar-section-header i {
  margin-left: 8px;
  width: 16px;
  text-align: center;
  font-size: 14px;
}

.sidebar-section-header span {
  font-size: 0.85rem;
  flex-grow: 1;
}

.toggle-icon {
  transition: transform 0.3s ease;
  font-size: 12px !important;
  opacity: 0.7;
}

.sidebar-section-header.collapsed .toggle-icon {
  transform: rotate(-90deg);
}

/* ===== SIDEBAR SUB LINKS CONTAINER ===== */
.sidebar-sub-links {
  max-height: 1000px;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease, transform 0.3s ease;
  opacity: 1;
  transform: translateY(0);
  margin-bottom: 8px;
}

.sidebar-sub-links.collapsed {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
  margin-bottom: 0;
}

/* ===== SIDEBAR SUB LINKS ===== */
.sidebar-sub-link {
  padding: 8px 20px 8px 40px; /* More indentation for sub-links */
  margin: 2px 12px;
  font-size: 0.9rem;
  font-weight: 400;
  transition: all 0.3s ease;
  display: block;
}

.sidebar-sub-link i {
  font-size: 14px;
  width: 18px;
}

.sidebar-sub-link:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateX(-3px); /* RTL: more movement for sub-links */
}

.sidebar-sub-link.active {
  background-color: rgba(255, 255, 255, 0.25);
  font-weight: 500;
}

.sidebar-sub-link.active::before {
  width: 4px;
  height: 60%;
}

/* ===== SIDEBAR TOGGLE BUTTON ===== */
.sidebar-toggle-btn {
  position: fixed;
  top: 1rem;
  left: 1rem; /* RTL: button on the left side */
  z-index: 1001;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.sidebar-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Hide toggle button on large screens by default */
@media (min-width: 992px) {
  .sidebar-toggle-btn {
    display: none;
  }

  /* Show toggle button on large screens when sidebar is collapsed */
  .sidebar-collapsed .sidebar-toggle-btn {
    display: block;
  }
}

/* ===== RESPONSIVE LAYOUT ===== */
/* Large screens (desktop - 992px and up) */
@media (min-width: 992px) {
  .sidebar {
    position: fixed;
    transform: translateX(0); /* Always visible by default */
  }

  .main-content {
    margin-right: var(--sidebar-width);
  }

  /* Desktop collapsed state */
  .sidebar-collapsed .sidebar {
    transform: translateX(100%);
  }

  .sidebar-collapsed .main-content {
    margin-right: 0;
  }
}

/* Medium and small screens (tablets and mobile - below 992px) */
@media (max-width: 991.98px) {
  .sidebar {
    transform: translateX(100%); /* Hidden by default on small screens */
    width: var(--sidebar-width);
    max-width: 90vw; /* Don't exceed 90% of viewport width */
  }

  .main-content {
    margin-right: 0; /* Always full width on small screens */
    width: 100%;
  }

  /* Mobile sidebar open state */
  .sidebar-open .sidebar {
    transform: translateX(0); /* Slide in from right */
  }

  /* Overlay for mobile sidebar */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-duration) ease;
  }

  .sidebar-open .sidebar-overlay {
    opacity: 1;
    visibility: visible;
  }

  /* Prevent body scroll when sidebar is open on mobile */
  .sidebar-open {
    overflow: hidden;
  }
}

/* Extra small screens (mobile phones) */
@media (max-width: 576px) {
  .sidebar {
    width: 100%; /* Full width on very small screens */
  }

  .main-content {
    padding: 1rem;
  }
}

/* ===== BASIC DASHBOARD COMPONENTS ===== */
/* Card styling beyond Bootstrap defaults */
.dashboard-card {
  background: var(--bg-color);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Form element padding/margin adjustments */
.dashboard-form .form-control {
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.dashboard-form .form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* ===== RTL LAYOUT ADJUSTMENTS ===== */
/* Ensure proper RTL flow for main content */
.main-content {
  direction: rtl;
  text-align: right;
}

/* RTL adjustments for flex layouts */
.main-content .d-flex {
  direction: rtl;
}

.main-content .justify-content-end {
  justify-content: flex-start !important;
}

.main-content .justify-content-start {
  justify-content: flex-end !important;
}

/* ===== TABLE STYLES ===== */
.table {
  direction: rtl;
  text-align: right;
}

.table th {
  text-align: right;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 2px solid var(--border-color);
}

.table td {
  vertical-align: middle;
  border-bottom: 1px solid var(--border-light);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
  background-color: var(--bg-light);
}

/* ===== BUTTON ENHANCEMENTS ===== */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* ===== PAGE HEADER STYLES ===== */
.page-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-light);
}

.page-header h1 {
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.page-header .breadcrumb {
  background: none;
  padding: 0;
  margin: 0;
}

/* ===== ALERT ENHANCEMENTS ===== */
.alert {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-success {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success);
  border-left: 4px solid var(--success);
}

.alert-danger {
  background-color: rgba(var(--danger-rgb), 0.1);
  color: var(--danger);
  border-left: 4px solid var(--danger);
}

.alert-warning {
  background-color: rgba(var(--warning-rgb), 0.1);
  color: var(--warning);
  border-left: 4px solid var(--warning);
}

.alert-info {
  background-color: rgba(var(--info-rgb), 0.1);
  color: var(--info);
  border-left: 4px solid var(--info);
}

/* ===== ENHANCED TABLE STYLING ===== */
.sortable-table th[data-sort] {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color 0.2s ease;
}

.sortable-table th[data-sort]:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
}

.sortable-table th[data-sort]::after {
  content: '\f0dc'; /* Font Awesome sort icon */
  font-family: 'Font Awesome 6 Free';
  font-weight: 900;
  position: absolute;
  left: 8px; /* RTL: position on the left */
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.sortable-table th[data-sort]:hover::after {
  opacity: 0.6;
}

.sortable-table th.sort-asc::after {
  content: '\f0de'; /* Font Awesome sort-up icon */
  opacity: 1;
  color: var(--primary);
}

.sortable-table th.sort-desc::after {
  content: '\f0dd'; /* Font Awesome sort-down icon */
  opacity: 1;
  color: var(--primary);
}

/* ===== RESPONSIVE TABLE IMPROVEMENTS ===== */
@media (max-width: 768px) {
  .table-responsive {
    border-radius: 8px;
  }

  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* ===== LOADING STATES ===== */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
<?php
/**
 * AJAX endpoint to get transfer details
 */

// Required includes
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/transfer_manager.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize classes
$auth = new Auth();
$db = new Database();
$transferManager = new TransferManager($db);

try {
    // Authentication check
    $auth->requireLogin();
    $auth->requirePermission('transfers.view');
    
    // Get transfer ID
    $transferId = (int)($_GET['id'] ?? 0);
    
    if ($transferId <= 0) {
        throw new Exception('معرف الحوالة غير صالح');
    }
    
    // Get transfer details
    $transfer = $transferManager->getTransferById($transferId);
    
    if (!$transfer) {
        throw new Exception('الحوالة غير موجودة');
    }
    
    // Build HTML content
    $html = '
    <div class="row">
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-info-circle me-2"></i>
                معلومات الحوالة
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">رقم الحوالة:</td>
                    <td><code>' . htmlspecialchars($transfer['transaction_number']) . '</code></td>
                </tr>
                <tr>
                    <td class="fw-medium">نوع الحوالة:</td>
                    <td><span class="badge ' . ($transfer['transfer_type'] === 'صادرة' ? 'bg-primary' : 'bg-info') . '">' . htmlspecialchars($transfer['transfer_type']) . '</span></td>
                </tr>
                <tr>
                    <td class="fw-medium">الحالة:</td>
                    <td>';
    
    // Status badge
    $statusClass = '';
    switch ($transfer['status']) {
        case 'معلقة': $statusClass = 'bg-warning text-dark'; break;
        case 'مقبولة': $statusClass = 'bg-success'; break;
        case 'مرفوضة': $statusClass = 'bg-danger'; break;
        case 'مرسلة': $statusClass = 'bg-info'; break;
        case 'مستلمة': $statusClass = 'bg-primary'; break;
        case 'ملغاة': $statusClass = 'bg-secondary'; break;
        default: $statusClass = 'bg-light text-dark';
    }
    
    $html .= '<span class="badge ' . $statusClass . '">' . htmlspecialchars($transfer['status']) . '</span></td>
                </tr>
                <tr>
                    <td class="fw-medium">حالة التسليم:</td>
                    <td>';
    
    $deliveryStatus = $transfer['delivery_status'] ?? 'غير مستلمة';
    $deliveryClass = $deliveryStatus === 'مستلمة' ? 'bg-success' : 'bg-secondary';
    $html .= '<span class="badge ' . $deliveryClass . '">' . htmlspecialchars($deliveryStatus) . '</span></td>
                </tr>
                <tr>
                    <td class="fw-medium">تاريخ الإنشاء:</td>
                    <td>' . date('Y-m-d H:i:s', strtotime($transfer['created_at'])) . '</td>
                </tr>';
    
    if (!empty($transfer['completed_at'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">تاريخ الإكمال:</td>
                    <td>' . date('Y-m-d H:i:s', strtotime($transfer['completed_at'])) . '</td>
                </tr>';
    }
    
    if (!empty($transfer['tracking_number'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">رقم التتبع:</td>
                    <td><code>' . htmlspecialchars($transfer['tracking_number']) . '</code></td>
                </tr>';
    }
    
    $html .= '
            </table>
        </div>
        
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-dollar-sign me-2"></i>
                التفاصيل المالية
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">مبلغ الإرسال:</td>
                    <td class="fw-bold text-success">
                        ' . number_format($transfer['sending_amount'], 2) . ' 
                        ' . htmlspecialchars($transfer['sending_currency_code'] ?? '') . '
                    </td>
                </tr>
                <tr>
                    <td class="fw-medium">مبلغ الاستلام:</td>
                    <td class="fw-bold text-info">
                        ' . number_format($transfer['receiving_amount'], 2) . ' 
                        ' . htmlspecialchars($transfer['receiving_currency_code'] ?? '') . '
                    </td>
                </tr>
                <tr>
                    <td class="fw-medium">سعر الصرف:</td>
                    <td>' . number_format($transfer['exchange_rate_used'], 6) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">رسوم التحويل:</td>
                    <td class="text-warning fw-medium">' . number_format($transfer['transfer_fee'], 2) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">الربح:</td>
                    <td class="text-primary fw-medium">' . number_format($transfer['profit'], 2) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">طريقة التسليم:</td>
                    <td>' . htmlspecialchars($transfer['delivery_method']) . '</td>
                </tr>
            </table>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-user me-2"></i>
                معلومات المرسل
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">الاسم:</td>
                    <td>' . htmlspecialchars($transfer['sender_name']) . '</td>
                </tr>';
    
    if (!empty($transfer['sender_id_type'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">نوع الهوية:</td>
                    <td>' . htmlspecialchars($transfer['sender_id_type']) . '</td>
                </tr>';
    }
    
    if (!empty($transfer['sender_id_number'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">رقم الهوية:</td>
                    <td>' . htmlspecialchars($transfer['sender_id_number']) . '</td>
                </tr>';
    }
    
    if (!empty($transfer['sender_phone'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">الهاتف:</td>
                    <td>' . htmlspecialchars($transfer['sender_phone']) . '</td>
                </tr>';
    }
    
    if (!empty($transfer['sender_address'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">العنوان:</td>
                    <td>' . htmlspecialchars($transfer['sender_address']) . '</td>
                </tr>';
    }
    
    $html .= '
            </table>
        </div>
        
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-user-check me-2"></i>
                معلومات المستفيد
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">الاسم:</td>
                    <td>' . htmlspecialchars($transfer['beneficiary_name']) . '</td>
                </tr>';
    
    if (!empty($transfer['beneficiary_country'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">البلد:</td>
                    <td>' . htmlspecialchars($transfer['beneficiary_country']) . '</td>
                </tr>';
    }
    
    if (!empty($transfer['beneficiary_bank'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">البنك:</td>
                    <td>' . htmlspecialchars($transfer['beneficiary_bank']) . '</td>
                </tr>';
    }
    
    if (!empty($transfer['beneficiary_account'])) {
        $html .= '
                <tr>
                    <td class="fw-medium">رقم الحساب:</td>
                    <td><code>' . htmlspecialchars($transfer['beneficiary_account']) . '</code></td>
                </tr>';
    }
    
    $html .= '
            </table>
        </div>
    </div>';
    
    // Add notes if available
    if (!empty($transfer['notes']) || !empty($transfer['delivery_notes'])) {
        $html .= '
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-sticky-note me-2"></i>
                    الملاحظات
                </h6>';
        
        if (!empty($transfer['notes'])) {
            $html .= '
                <div class="mb-3">
                    <label class="form-label fw-medium">ملاحظات الحوالة:</label>
                    <div class="p-3 bg-light rounded">
                        ' . nl2br(htmlspecialchars($transfer['notes'])) . '
                    </div>
                </div>';
        }
        
        if (!empty($transfer['delivery_notes'])) {
            $html .= '
                <div class="mb-3">
                    <label class="form-label fw-medium">ملاحظات التسليم:</label>
                    <div class="p-3 bg-light rounded">
                        ' . nl2br(htmlspecialchars($transfer['delivery_notes'])) . '
                    </div>
                </div>';
        }
        
        $html .= '
            </div>
        </div>';
    }
    
    // Add system information
    $html .= '
    <div class="row mt-4">
        <div class="col-12">
            <h6 class="text-primary mb-3">
                <i class="fas fa-cogs me-2"></i>
                معلومات النظام
            </h6>
            <div class="row">
                <div class="col-md-6">
                    <small><strong>الفرع:</strong> ' . htmlspecialchars($transfer['branch_name'] ?? 'غير محدد') . '</small>
                </div>
                <div class="col-md-6">
                    <small><strong>المنشئ:</strong> ' . htmlspecialchars($transfer['created_by_name'] ?? 'غير محدد') . '</small>
                </div>
            </div>
        </div>
    </div>';
    
    // Return success response
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('customers.view');

$db              = new Database();
$customerManager = new CustomerManager($db);
$current_user    = $auth->getCurrentUser();

// Handle search/filter parameters (basic)
$filters = [
    'search'      => $_GET['search']      ?? '',
    'status'      => $_GET['status']      ?? '',
    'risk_level'  => $_GET['risk_level']  ?? '',
];

// Pagination parameters
$limit = isset($_GET['limit']) && (int)$_GET['limit'] > 0 ? (int)$_GET['limit'] : 20;
$page  = isset($_GET['page'])  && (int)$_GET['page']  > 0 ? (int)$_GET['page']  : 1;
$offset = ($page - 1) * $limit;

$allCustomers      = $customerManager->getAllCustomers($filters);
$totalCustomers    = count($allCustomers);
$totalPages        = $limit > 0 ? (int)ceil($totalCustomers / $limit) : 1;
$customersPage     = $limit > 0 ? array_slice($allCustomers, $offset, $limit) : $allCustomers;

// Get customers with their stats (only for current page)
$customersWithStats = [];
foreach ($customersPage as $customer) {
    $stats = $customerManager->getCustomerStats($customer['id']);
    $customer['stats'] = $stats;
    $customersWithStats[] = $customer;
}

// تسجيل عملية عرض قائمة العملاء
ActivityHelper::logView('customers', 'عرض قائمة العملاء', $filters);

$pageTitle = 'العملاء';
include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="m-0">إدارة العملاء</h2>
        <?php if ($auth->hasPermission('customers.create')): ?>
            <a href="add_customer.php" class="btn btn-success">إضافة عميل</a>
        <?php endif; ?>
    </div>

    <!-- Search form -->
    <form class="row g-2 mb-3" method="get">
        <div class="col-md-4">
            <input type="text" name="search" class="form-control" placeholder="بحث بالاسم أو الرقم أو الهاتف" value="<?php echo htmlspecialchars($filters['search']); ?>">
        </div>
        <div class="col-md-2">
            <select name="status" class="form-select">
                <option value="">الحالة</option>
                <option value="active" <?php echo ($filters['status']==='active')?'selected':''; ?>>نشط</option>
                <option value="inactive" <?php echo ($filters['status']==='inactive')?'selected':''; ?>>غير نشط</option>
            </select>
        </div>
        <div class="col-md-2">
            <select name="risk_level" class="form-select">
                <option value="">مستوى المخاطر</option>
                <option value="low" <?php echo ($filters['risk_level']==='low')?'selected':''; ?>>منخفض</option>
                <option value="medium" <?php echo ($filters['risk_level']==='medium')?'selected':''; ?>>متوسط</option>
                <option value="high" <?php echo ($filters['risk_level']==='high')?'selected':''; ?>>مرتفع</option>
            </select>
        </div>
        <div class="col-md-2">
            <select name="limit" class="form-select">
                <option value="20"   <?php echo $limit===20 ? 'selected' : ''; ?>>20</option>
                <option value="50"   <?php echo $limit===50 ? 'selected' : ''; ?>>50</option>
                <option value="100"  <?php echo $limit===100? 'selected' : ''; ?>>100</option>
                <option value="0"    <?php echo $limit===0  ? 'selected' : ''; ?>>الكل</option>
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-primary w-100">بحث</button>
        </div>
    </form>

    <div class="d-flex justify-content-end mb-2">
        <button class="btn btn-sm btn-outline-success" onclick="exportCustomersToExcel()">
            <i class="fas fa-file-excel me-1"></i>
            تصدير Excel
        </button>
    </div>

    <div class="table-responsive">
        <table class="table table-bordered table-striped table-hover">
        <thead class="table-light">
            <tr>
                <th>#</th>
                <th>الاسم الكامل</th>
                <th>رقم الهوية</th>
                <th>الهاتف</th>
                <th>البريد الإلكتروني</th>
                <th>الحالة</th>
                <th>مستوى المخاطر</th>
                <th>المستندات</th>
                <th>العمليات</th>
                <th>تاريخ الإنشاء</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($customersWithStats as $c): ?>
            <tr>
                <td><?php echo $c['id']; ?></td>
                <td><?php echo htmlspecialchars($c['full_name']); ?></td>
                <td><?php echo htmlspecialchars($c['id_number']); ?></td>
                <td><?php echo htmlspecialchars($c['phone']); ?></td>
                <td><?php echo htmlspecialchars($c['email']); ?></td>
                <td>
                    <span class="badge bg-<?php echo $c['status'] === 'active' ? 'success' : 'secondary'; ?>">
                        <?php echo htmlspecialchars($c['status']); ?>
                    </span>
                </td>
                <td>
                    <span class="badge bg-<?php echo $c['risk_level'] === 'high' ? 'danger' : ($c['risk_level'] === 'medium' ? 'warning' : 'success'); ?>">
                        <?php echo htmlspecialchars($c['risk_level']); ?>
                    </span>
                </td>
                <td class="text-center">
                    <span class="badge bg-info">
                        <?php echo $c['stats']['total_documents']; ?>
                    </span>
                </td>
                <td class="text-center">
                    <span class="badge bg-primary">
                        <?php echo $c['stats']['total_exchanges'] + $c['stats']['total_transfers']; ?>
                    </span>
                </td>
                <td><?php echo date('Y-m-d', strtotime($c['created_at'])); ?></td>
                <td>
                    <?php if ($auth->hasPermission('customers.view')): ?>
                        <a href="customer_details.php?id=<?php echo $c['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="customer_details.php?id=<?php echo $c['id']; ?>#documents" class="btn btn-sm btn-warning" title="المستندات">
                            <i class="fas fa-file-alt"></i>
                        </a>
                        <a href="customer_details.php?id=<?php echo $c['id']; ?>#operations" class="btn btn-sm btn-success" title="سجل العمليات">
                            <i class="fas fa-history"></i>
                        </a>
                    <?php endif; ?>
                    <?php if ($auth->hasPermission('customers.edit')): ?>
                        <a href="edit_customer.php?id=<?php echo $c['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                    <?php endif; ?>
                    <?php if ($auth->hasPermission('customers.delete')): ?>
                        <a href="#" class="btn btn-sm btn-danger disabled" title="حذف">
                            <i class="fas fa-trash"></i>
                        </a>
                    <?php endif; ?>
                    <button class="btn btn-sm btn-outline-danger" title="PDF العمليات" onclick="exportCustomerOperationsPdf(<?php echo $c['id']; ?>)">
                        <i class="fas fa-file-pdf"></i>
                    </button>
                </td>
            </tr>
            <?php endforeach; ?>
            <?php if (!$customersWithStats): ?>
            <tr><td colspan="11" class="text-center">لا توجد سجلات</td></tr>
            <?php endif; ?>
        </tbody>
    </table>
    </div>

<?php if ($totalPages > 1): ?>
<nav aria-label="Page navigation" class="mt-3">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page'=>$page-1])); ?>">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        <?php endif; ?>
        <?php for ($i=max(1,$page-2); $i<=min($totalPages,$page+2); $i++): ?>
            <li class="page-item <?php echo $i==$page?'active':''; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page'=>$i])); ?>"><?php echo $i; ?></a>
            </li>
        <?php endfor; ?>
        <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page'=>$page+1])); ?>">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

</main>

<style>
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.table th, .table td {
    vertical-align: middle;
    padding: 0.75rem;
}

.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
}
</style>

<?php include __DIR__ . '/../includes/footer.php'; ?> 

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>
<script>
function exportCustomersToExcel() {
    var table = document.querySelector('table');
    if (!table) return;
    var html = table.outerHTML;
    var url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
    var link = document.createElement('a');
    link.href = url;
    link.download = 'customers_' + new Date().toISOString().slice(0,10) + '.xls';
    link.click();
}

function exportCustomerOperationsPdf(customerId) {
    fetch('ajax/get_customer_operations.php?customer_id=' + customerId)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert(data.error || 'Error fetching operations');
                return;
            }
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF({ orientation: 'landscape' });
            const rows = data.operations.map(op => {
                const isExchange = op.operation_type === 'exchange';
                const amount = isExchange ? op.amount_from : op.amount;
                const currency = isExchange ? (op.from_currency_symbol || op.from_currency_name) : (op.sending_currency_symbol || op.sending_currency_name);
                const details = isExchange ? `${op.from_currency_symbol || ''} → ${op.to_currency_symbol || ''}` : `${op.sending_currency_symbol || ''} → ${op.receiving_currency_symbol || ''}`;
                return [
                    op.id,
                    isExchange ? 'صرافة' : 'تحويل',
                    op.created_at,
                    amount,
                    currency,
                    details
                ];
            });
            doc.autoTable({
                head: [['ID', 'النوع', 'التاريخ', 'المبلغ', 'العملة', 'تفاصيل']],
                body: rows,
                styles: { fontSize: 8 },
                headStyles: { fillColor: [41, 128, 185] }
            });
            doc.save('customer_' + customerId + '_operations.pdf');
        })
        .catch(() => alert('Connection error'));
}
</script> 
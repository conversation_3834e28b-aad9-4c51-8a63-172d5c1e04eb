<?php
/**
 * Export Withdrawals to PDF or Excel
 * تصدير سحب المبالغ إلى PDF أو Excel
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/withdrawal_manager.php';
require_once __DIR__ . '/../vendor/autoload.php'; // For TCPDF

// Check authentication
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// Check permissions
if (!$auth->hasPermission('withdrawals.view')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب');
    redirect('dashboard.php');
}

$db = new Database();
$withdrawalManager = new WithdrawalManager($db);

// Get export format
$format = sanitize_input($_GET['format'] ?? 'pdf');
if (!in_array($format, ['pdf', 'excel'])) {
    $format = 'pdf';
}

// Get filters
$filters = [];
$search = sanitize_input($_GET['search'] ?? '');
$status = sanitize_input($_GET['status'] ?? '');
$date_from = sanitize_input($_GET['date_from'] ?? '');
$date_to = sanitize_input($_GET['date_to'] ?? '');

if (!empty($search)) $filters['search'] = $search;
if (!empty($status)) $filters['status'] = $status;
if (!empty($date_from)) $filters['date_from'] = $date_from;
if (!empty($date_to)) $filters['date_to'] = $date_to;

// Get withdrawals
$withdrawals = $withdrawalManager->getWithdrawals($filters);

// Prepare filter description
$filterDescription = "جميع عمليات سحب المبالغ";
if (!empty($date_from) && !empty($date_to)) {
    $filterDescription .= " من $date_from إلى $date_to";
} elseif (!empty($date_from)) {
    $filterDescription .= " من $date_from";
} elseif (!empty($date_to)) {
    $filterDescription .= " حتى $date_to";
}

if (!empty($status)) {
    $filterDescription .= " - الحالة: $status";
}

if (!empty($search)) {
    $filterDescription .= " - البحث: $search";
}

// Calculate totals
$totalAmount = 0;
$totalNetAmount = 0;
foreach ($withdrawals as $withdrawal) {
    $totalAmount += $withdrawal['amount'];
    $totalNetAmount += $withdrawal['net_amount'];
}

// Export to PDF
if ($format === 'pdf') {
    // Create new PDF document
    require_once(__DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php');
    
    // تعريف فئة PDF مخصصة لدعم اللغة العربية
    class MYPDF extends TCPDF {
        public function Header() {
            // تعيين الخط
            $this->SetFont('dejavusans', 'B', 16);
            // العنوان
            $this->Cell(0, 15, 'تقرير سحب المبالغ', 0, false, 'C', 0, '', 0, false, 'M', 'M');
        }

        public function Footer() {
            // تعيين موضع الصفحة
            $this->SetY(-15);
            // تعيين الخط
            $this->SetFont('dejavusans', 'I', 8);
            // رقم الصفحة
            $this->Cell(0, 10, 'الصفحة '.$this->getAliasNumPage().'/'.$this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
        }
    }

    // إنشاء مستند PDF جديد بدعم RTL
    $pdf = new MYPDF('L', 'mm', 'A4', true, 'UTF-8', false);

    // تعيين معلومات المستند
    $pdf->SetCreator('TrustPlus');
    $pdf->SetAuthor('TrustPlus System');
    $pdf->SetTitle('تقرير سحب المبالغ');
    $pdf->SetSubject('تقرير سحب المبالغ');
    $pdf->SetKeywords('سحب, مبالغ, تقرير, تصدير');

    // تعيين بيانات الرأس الافتراضية
    $pdf->SetHeaderData('', 0, 'تقرير سحب المبالغ', $filterDescription);

    // تعيين الهوامش
    $pdf->SetMargins(10, 20, 10);
    $pdf->SetHeaderMargin(5);
    $pdf->SetFooterMargin(10);

    // تعيين فواصل الصفحة التلقائية
    $pdf->SetAutoPageBreak(TRUE, 15);

    // تعيين عامل مقياس الصورة
    $pdf->setImageScale(1.25);

    // إضافة صفحة
    $pdf->AddPage();

    // تعيين اتجاه RTL
    $pdf->setRTL(true);
    
    // تعيين الخط
    $pdf->SetFont('dejavusans', 'B', 12);
    
    // إضافة وصف التصفية
    $pdf->Cell(0, 10, $filterDescription, 0, 1, 'C');
    $pdf->Ln(5);

    // إنشاء رأس الجدول
    $pdf->SetFont('dejavusans', 'B', 10);
    $pdf->SetFillColor(240, 240, 240);
    
    // تعيين عرض الأعمدة
    $colWidth1 = 20; // ID
    $colWidth2 = 30; // المبلغ
    $colWidth3 = 50; // المستفيد
    $colWidth4 = 25; // العمولة
    $colWidth5 = 30; // صافي الوصول
    $colWidth6 = 25; // الحالة
    $colWidth7 = 30; // التاريخ
    $colWidth8 = 40; // المنشئ
    
    // رأس الجدول
    $pdf->Cell($colWidth1, 10, 'ID', 1, 0, 'C', 1);
    $pdf->Cell($colWidth2, 10, 'المبلغ', 1, 0, 'C', 1);
    $pdf->Cell($colWidth3, 10, 'المستفيد', 1, 0, 'C', 1);
    $pdf->Cell($colWidth4, 10, 'العمولة (%)', 1, 0, 'C', 1);
    $pdf->Cell($colWidth5, 10, 'صافي الوصول', 1, 0, 'C', 1);
    $pdf->Cell($colWidth6, 10, 'الحالة', 1, 0, 'C', 1);
    $pdf->Cell($colWidth7, 10, 'التاريخ', 1, 0, 'C', 1);
    $pdf->Cell($colWidth8, 10, 'المنشئ', 1, 1, 'C', 1);

    // إضافة صفوف البيانات
    $pdf->SetFont('dejavusans', '', 9);
    
    // تحديد لون الخلفية البديلة
    $altBg = false;
    
    foreach ($withdrawals as $withdrawal) {
        // تبديل لون الخلفية
        if ($altBg) {
            $pdf->SetFillColor(245, 245, 245);
        } else {
            $pdf->SetFillColor(255, 255, 255);
        }
        $altBg = !$altBg;
        
        // بيانات الصف
        $pdf->Cell($colWidth1, 8, $withdrawal['id'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth2, 8, number_format($withdrawal['amount'], 2), 1, 0, 'C', 1);
        
        // التعامل مع النصوص الطويلة
        $beneficiary = $withdrawal['beneficiary'];
        if (strlen($beneficiary) > 30) {
            $beneficiary = substr($beneficiary, 0, 28) . '..';
        }
        $pdf->Cell($colWidth3, 8, $beneficiary, 1, 0, 'R', 1);
        
        $pdf->Cell($colWidth4, 8, number_format($withdrawal['commission'], 2) . '%', 1, 0, 'C', 1);
        $pdf->Cell($colWidth5, 8, number_format($withdrawal['net_amount'], 2), 1, 0, 'C', 1);
        $pdf->Cell($colWidth6, 8, $withdrawal['status'], 1, 0, 'C', 1);
        $pdf->Cell($colWidth7, 8, date('Y-m-d', strtotime($withdrawal['created_at'])), 1, 0, 'C', 1);
        
        $creator = $withdrawal['created_by_name'] ?? '';
        if (strlen($creator) > 25) {
            $creator = substr($creator, 0, 23) . '..';
        }
        $pdf->Cell($colWidth8, 8, $creator, 1, 1, 'R', 1);
    }

    // إضافة صف الإجماليات
    $pdf->SetFont('dejavusans', 'B', 10);
    $pdf->SetFillColor(240, 240, 240);
    
    // حساب العرض الإجمالي للأعمدة قبل العمولة
    $totalWidth = $colWidth1 + $colWidth2 + $colWidth3;
    
    // حساب العرض الإجمالي للأعمدة بعد العمولة
    $remainingWidth = $colWidth6 + $colWidth7 + $colWidth8;
    
    $pdf->Cell($totalWidth, 10, 'إجمالي المبلغ:', 1, 0, 'R', 1);
    $pdf->Cell($colWidth4, 10, '', 1, 0, 'C', 1);
    $pdf->Cell($colWidth5, 10, number_format($totalAmount, 2), 1, 0, 'C', 1);
    $pdf->Cell($remainingWidth, 10, '', 1, 1, 'C', 1);
    
    $pdf->Cell($totalWidth, 10, 'إجمالي صافي الوصول:', 1, 0, 'R', 1);
    $pdf->Cell($colWidth4, 10, '', 1, 0, 'C', 1);
    $pdf->Cell($colWidth5, 10, number_format($totalNetAmount, 2), 1, 0, 'C', 1);
    $pdf->Cell($remainingWidth, 10, '', 1, 1, 'C', 1);

    // إضافة معلومات التذييل
    $pdf->SetFont('dejavusans', 'I', 8);
    $pdf->Cell(0, 10, 'تم إنشاء هذا التقرير بواسطة نظام TrustPlus بتاريخ ' . date('Y-m-d H:i'), 0, 1, 'C');

    // إخراج ملف PDF
    $pdf->Output('تقرير_سحب_المبالغ_' . date('Y-m-d') . '.pdf', 'D');
    exit;
}

// Export to Excel
if ($format === 'excel') {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="تقرير_سحب_المبالغ_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create Excel content
    echo '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>تقرير سحب المبالغ</title>
        <style>
            table { direction: rtl; border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #000; padding: 5px; text-align: right; }
            th { background-color: #f0f0f0; }
            .total { font-weight: bold; background-color: #f0f0f0; }
        </style>
    </head>
    <body>
        <h1>تقرير سحب المبالغ</h1>
        <p>' . $filterDescription . '</p>
        
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>المبلغ</th>
                    <th>المستفيد</th>
                    <th>العمولة (%)</th>
                    <th>صافي الوصول</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                    <th>المنشئ</th>
                </tr>
            </thead>
            <tbody>';
    
    foreach ($withdrawals as $withdrawal) {
        echo '<tr>
                <td>' . $withdrawal['id'] . '</td>
                <td>' . number_format($withdrawal['amount'], 2) . '</td>
                <td>' . htmlspecialchars($withdrawal['beneficiary']) . '</td>
                <td>' . number_format($withdrawal['commission'], 2) . '%</td>
                <td>' . number_format($withdrawal['net_amount'], 2) . '</td>
                <td>' . htmlspecialchars($withdrawal['status']) . '</td>
                <td>' . date('Y-m-d', strtotime($withdrawal['created_at'])) . '</td>
                <td>' . htmlspecialchars($withdrawal['created_by_name'] ?? '') . '</td>
            </tr>';
    }
    
    echo '</tbody>
            <tfoot>
                <tr class="total">
                    <td colspan="4" style="text-align: left;">إجمالي المبلغ:</td>
                    <td>' . number_format($totalAmount, 2) . '</td>
                    <td colspan="3"></td>
                </tr>
                <tr class="total">
                    <td colspan="4" style="text-align: left;">إجمالي صافي الوصول:</td>
                    <td>' . number_format($totalNetAmount, 2) . '</td>
                    <td colspan="3"></td>
                </tr>
            </tfoot>
        </table>
    </body>
    </html>';
    
    exit;
}

// Fallback if no valid format is specified
redirect('withdrawals.php');
?> 
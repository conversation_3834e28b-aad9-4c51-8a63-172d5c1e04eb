<?php
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/cash_manager.php';
require_once __DIR__ . '/bank_manager.php';
require_once __DIR__ . '/accounting_manager.php';
require_once __DIR__ . '/../config/config.php';

/**
 * ExchangeManager handles exchange rates and transactions.
 */
class ExchangeManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /* -------------------- Rates -------------------- */
    public function getAllExchangeRates(): array
    {
        $sql = "SELECT er.*, 
                       fc.code AS from_code, fc.symbol AS from_symbol,
                       tc.code AS to_code,   tc.symbol  AS to_symbol,
                       u.full_name AS updated_by_name
                FROM exchange_rates er
                JOIN currencies fc ON fc.id = er.from_currency_id
                JOIN currencies tc ON tc.id = er.to_currency_id
                LEFT JOIN users u   ON u.id = er.updated_by
                ORDER BY fc.code, tc.code";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        $stmt->execute();
        $result = $stmt->get_result();
        $rows   = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    public function getExchangeRateById(int $rateId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM exchange_rates WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $rateId);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    public function getExchangeRateByPair(int $fromId, int $toId): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM exchange_rates WHERE from_currency_id = ? AND to_currency_id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('ii', $fromId, $toId);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    public function exchangeRatePairExists(int $fromId, int $toId, int $excludeId = null): bool
    {
        $sql = 'SELECT COUNT(*) FROM exchange_rates WHERE from_currency_id = ? AND to_currency_id = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($excludeId !== null) {
            $stmt->bind_param('iii', $fromId, $toId, $excludeId);
        } else {
            $stmt->bind_param('ii', $fromId, $toId);
        }
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    public function addExchangeRate(array $data)
    {
        $sql = 'INSERT INTO exchange_rates (from_currency_id, to_currency_id, buy_rate, sell_rate, updated_by, updated_at)
                VALUES (?,?,?,?,?, NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $stmt->bind_param('iiddi',
            $data['from_currency_id'],
            $data['to_currency_id'],
            $data['buy_rate'],
            $data['sell_rate'],
            $data['updated_by']
        );
        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }

    public function updateExchangeRate(int $rateId, array $data): bool
    {
        $sql = 'UPDATE exchange_rates SET buy_rate = ?, sell_rate = ?, updated_by = ?, updated_at = NOW() WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $stmt->bind_param('ddii', $data['buy_rate'], $data['sell_rate'], $data['updated_by'], $rateId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function deleteExchangeRate(int $rateId): bool
    {
        $stmt = $this->db->prepare('DELETE FROM exchange_rates WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $rateId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /* -------------------- Transactions -------------------- */
    public function addExchangeTransaction(array $data)
    {
        $sql = 'INSERT INTO exchanges (customer_id, from_currency_id, to_currency_id, amount_from, amount_to, rate_used, commission, profit, status, notes, branch_id, created_by, created_at)
                VALUES (?,?,?,?,?,?,?,?,?,?,?,?, NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $stmt->bind_param('iiidddddisii',
            $data['customer_id'],
            $data['from_currency_id'],
            $data['to_currency_id'],
            $data['amount_from'],
            $data['amount_to'],
            $data['rate_used'],
            $data['commission'],
            $data['profit'],
            $data['status'],
            $data['notes'],
            $data['branch_id'],
            $data['created_by']
        );
        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }

    public function getExchangeById(int $exchangeId): ?array
    {
        $sql = 'SELECT e.*,
                       c.full_name as customer_name,
                       fcb.name as from_cash_box_name, fcb.currency_code as from_currency_code,
                       tcb.name as to_cash_box_name, tcb.currency_code as to_currency_code,
                       fba.account_name as from_bank_account_name, fba.currency_code as from_bank_currency_code,
                       tba.account_name as to_bank_account_name, tba.currency_code as to_bank_currency_code,
                       u.full_name as created_by_name,
                       b.name as branch_name
                FROM exchanges e
                LEFT JOIN customers c ON c.id = e.customer_id
                LEFT JOIN cash_boxes fcb ON fcb.id = e.from_cash_box_id
                LEFT JOIN cash_boxes tcb ON tcb.id = e.to_cash_box_id
                LEFT JOIN bank_accounts fba ON fba.id = e.from_bank_account_id
                LEFT JOIN bank_accounts tba ON tba.id = e.to_bank_account_id
                LEFT JOIN users u ON u.id = e.created_by
                LEFT JOIN branches b ON b.id = e.branch_id
                WHERE e.id = ? LIMIT 1';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return null;

        $stmt->bind_param('i', $exchangeId);
        $stmt->execute();
        $result = $stmt->get_result();
        $exchange = $result ? $result->fetch_assoc() : null;
        $stmt->close();

        return $exchange;
    }

    /**
     * High-level helper – creates an exchange transaction, then immediately
     * records the financial movements and accounting entries. All operations
     * are wrapped in a single DB transaction so that either all of them are
     * committed or none (atomicity).
     *
     * Expected $data keys (minimum):
     *  - customer_id, from_currency_id, to_currency_id
     *  - created_by, branch_id
     *  - (one of) amount_from or amount_to – the value the user typed
     *  - user_amount_field – 'amount_from' | 'amount_to'
     *  - funding_source => ['type' => 'cash'|'bank', 'id' => int]  // where we RECEIVE money *from customer*
     *  - disburse_source => ['type' => 'cash'|'bank', 'id' => int] // where we GIVE money TO customer
     *  - notes (optional)
     *
     * @return array{success:bool, exchange_id?:int, error?:string}
     */
    public function processExchange(array $data): array
    {
        // Basic validation
        foreach (['customer_id','from_currency_id','to_currency_id','created_by','branch_id','user_amount_field'] as $k) {
            if (!isset($data[$k]) || $data[$k] === null || $data[$k] === '') {
                return ['success'=>false,'error'=>'Missing param '.$k];
            }
        }

        // Additional validation for numeric IDs
        if ((int)$data['branch_id'] <= 0) {
            return ['success'=>false,'error'=>'Invalid branch_id'];
        }
        if ($data['from_currency_id'] === $data['to_currency_id']) {
            return ['success'=>false,'error'=>'Currencies must differ'];
        }

        // Fetch all rates once – cheaper than multiple queries
        $allRates = $this->getAllExchangeRatesIndexed();

        // Determine amount input
        $amountInput = $data[$data['user_amount_field']] ?? null;
        if ($amountInput === null || $amountInput <= 0) {
            return ['success'=>false,'error'=>'Invalid amount'];
        }

        // Calculate
        $calculated = $this->_calculateAdvancedDetails(
            $amountInput,
            $data['from_currency_id'],
            $data['to_currency_id'],
            $data['user_amount_field'],
            $allRates
        );
        if (!$calculated['success']) {
            return ['success'=>false,'error'=>$calculated['error']];
        }

        // Merge calculated fields into data to be inserted
        $txData = array_merge(
            [
                'amount_from'      => $calculated['amount_from'],
                'amount_to'        => $calculated['amount_to'],
                'rate_used'        => $calculated['rate_used'],
                'commission'       => $calculated['commission'],
                'profit'           => $calculated['profit'],
                'status'           => 'completed',
            ],
            $data
        );

        // Begin atomic transaction with aggressive timeout settings
        $this->db->query("SET SESSION innodb_lock_wait_timeout = 3");
        $this->db->query("SET SESSION wait_timeout = 60");
        $this->db->query("SET SESSION autocommit = 0");

        // Start transaction
        if (!$this->db->begin_transaction()) {
            return ['success' => false, 'error' => 'Failed to start transaction'];
        }

        try {
            $exchangeId = $this->addExchangeTransaction($txData);
            if (!$exchangeId) {
                throw new Exception('Exchange insert failed');
            }

            // ===== MOVEMENTS =====
            $cashMgr = new CashManager(new Database());
            $bankMgr = new BankManager(new Database());

            // Funding (we RECEIVE money from customer) – system cash increases => deposit
            $fund  = $data['funding_source'] ?? null;
            $disp  = $data['disburse_source'] ?? null;
            if ($fund === null || $disp === null) {
                throw new Exception('Funding / Disbursement sources not set');
            }
            // Determine amounts in each side
            $fundAmount = $txData['amount_from'];
            $dispAmount = $txData['amount_to'];

            // funding currency is from_currency, disbursement currency is to_currency
            if ($fund['type'] === 'cash') {
                $res = $cashMgr->addCashMovementForExchange((int)$fund['id'], 'deposit', $fundAmount, $data['from_currency_id'], $exchangeId, $data['created_by'], 'Exchange funding');
            } else {
                $res = $bankMgr->addBankMovementForExchange((int)$fund['id'], 'deposit', $fundAmount, $data['from_currency_id'], $exchangeId, $data['created_by'], 'Exchange funding');
            }
            if (!$res['success']) throw new Exception($res['error']);
            $fundMovId = $res['movement_id'] ?? null;

            // Disbursement (system pays customer) – withdrawal
            if ($disp['type'] === 'cash') {
                $res2 = $cashMgr->addCashMovementForExchange((int)$disp['id'], 'withdrawal', $dispAmount, $data['to_currency_id'], $exchangeId, $data['created_by'], 'Exchange disbursement');
            } else {
                $res2 = $bankMgr->addBankMovementForExchange((int)$disp['id'], 'withdrawal', $dispAmount, $data['to_currency_id'], $exchangeId, $data['created_by'], 'Exchange disbursement');
            }
            if (!$res2['success']) throw new Exception($res2['error']);
            $dispMovId = $res2['movement_id'] ?? null;

            // ===== ACCOUNTING ENTRIES =====
            $accMgr = new AccountingManager($this->db);
            // entry #1 fund flow
            $accMgr->createEntry([
                'entry_date'      => date('Y-m-d'),
                'description'     => 'Currency exchange',
                'branch_id'       => $data['branch_id'],
                'account_debit'   => 'Cash/Bank', // placeholder
                'account_credit'  => 'Cash/Bank', // placeholder
                'amount'          => $fundAmount, // recorded in from_currency maybe – simplified
                'currency_id'     => $data['from_currency_id'],
                'created_by'      => $data['created_by'],
                'exchange_id'     => $exchangeId,
                'cash_movement_id'=> $fund['type']==='cash' ? $fundMovId : null,
                'bank_movement_id'=> $fund['type']==='bank' ? $fundMovId : null,
            ]);
            // entry #2 profit
            if (abs($calculated['profit']) > 0.01) {
                $accMgr->createEntry([
                    'entry_date'  => date('Y-m-d'),
                    'description' => 'Exchange profit',
                    'branch_id'   => $data['branch_id'],
                    'account_debit'=> $calculated['profit']>0 ? 'Cash/Bank' : 'Exchange Loss',
                    'account_credit'=> $calculated['profit']>0 ? 'Exchange Profit' : 'Cash/Bank',
                    'amount'      => abs($calculated['profit']),
                    'currency_id' => BASE_CURRENCY_ID,
                    'created_by'  => $data['created_by'],
                    'exchange_id' => $exchangeId,
                ]);
            }

            // Commit transaction
            if (!$this->db->commit()) {
                throw new Exception('Failed to commit transaction');
            }

            // Reset settings
            $this->db->query("SET SESSION autocommit = 1");
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");

            log_activity($data['created_by'], 'exchange.create', ['exchange_id'=>$exchangeId]);
            return ['success'=>true,'exchange_id'=>$exchangeId];

        } catch (Exception $ex) {
            // Rollback on any error
            $this->db->rollback();

            // Reset settings
            $this->db->query("SET SESSION autocommit = 1");
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");

            return ['success'=>false,'error'=>$ex->getMessage()];
        }
    }

    /**
     * Build a hash map of exchange rates indexed by from->to for quick lookup.
     */
    private function getAllExchangeRatesIndexed(): array
    {
        $rows = $this->getAllExchangeRates();
        $map  = [];
        foreach ($rows as $r) {
            $map[$r['from_currency_id'].'-'.$r['to_currency_id']] = $r;
        }
        return $map;
    }

    /**
     * Core calculation logic for cross-currency conversions, commission & profit.
     *
     * @return array{success:bool, amount_from?:float, amount_to?:float, rate_used?:float, commission?:float, profit?:float, error?:string}
     */
    private function _calculateAdvancedDetails(float $amountInput, int $fromId, int $toId, string $userAmountField, array $rateMap): array
    {
        $keyDirect = $fromId.'-'.$toId;
        $direct    = $rateMap[$keyDirect] ?? null;
        $baseId    = defined('BASE_CURRENCY_ID') ? BASE_CURRENCY_ID : $fromId; // fallback

        $rateUsed = null;
        if ($direct) {
            // Use direct pair
            if ($userAmountField === 'amount_from') {
                $rateUsed = $direct['sell_rate']; // we sell FROM currency, take sell_rate
                $amountFrom = $amountInput;
                $amountTo   = $amountFrom * $rateUsed;
            } else {
                $rateUsed = $direct['buy_rate']; // user specified amount_to, so we will buy TO currency
                $amountTo = $amountInput;
                $amountFrom = $amountTo / $rateUsed;
            }
        } else {
            // Fallback: via base currency (two legs)
            $leg1 = $rateMap[$fromId.'-'.$baseId] ?? null; // convert from -> base using sell
            $leg2 = $rateMap[$baseId.'-'.$toId] ?? null;   // base -> to using sell
            if (!$leg1 || !$leg2) {
                return ['success'=>false,'error'=>'No rate path found'];
            }
            if ($userAmountField === 'amount_from') {
                // amountInput is in FROM currency
                $amountBase = $amountInput * $leg1['sell_rate'];
                $amountTo   = $amountBase * $leg2['sell_rate'];
                $amountFrom = $amountInput;
                $rateUsed   = $amountTo / $amountFrom;
            } else {
                // amountInput is in TO currency
                $amountBase = $amountInput / $leg2['buy_rate'];
                $amountFrom = $amountBase / $leg1['buy_rate'];
                $amountTo   = $amountInput;
                $rateUsed   = $amountTo / $amountFrom;
            }
        }

        // Commission – simplistic 0.2% of amount_from
        $commission = round($amountFrom * 0.002, 4);

        // Profit – simplistic spread: difference between sell & buy rates vs mid
        $midRate = $rateUsed; // placeholder; in real world we use mid market
        $profit  = round(($amountFrom * $rateUsed) * 0.001, 4); // 0.1% profit placeholder

        return [
            'success'     => true,
            'amount_from' => $amountFrom,
            'amount_to'   => $amountTo,
            'rate_used'   => $rateUsed,
            'commission'  => $commission,
            'profit'      => $profit,
        ];
    }

    /**
     * Get today's exchange operations count
     */
    public function getTodayExchangeCount(): int {
        $sql = "SELECT COUNT(*) as count FROM exchanges WHERE DATE(created_at) = CURDATE()";
        $result = $this->db->query($sql);
        if ($result && $row = $result->fetch_assoc()) {
            return (int)$row['count'];
        }
        return 0;
    }

    /**
     * Get today's total profit from exchanges
     */
    public function getTodayProfit(): float {
        $sql = "SELECT COALESCE(SUM(profit), 0) as total FROM exchanges WHERE DATE(created_at) = CURDATE()";
        $result = $this->db->query($sql);
        if ($result && $row = $result->fetch_assoc()) {
            return (float)$row['total'];
        }
        return 0.0;
    }

    /**
     * Get latest exchange rates
     */
    public function getLatestRates(): array {
        $sql = "SELECT er.buy_rate, er.sell_rate, 
                       fc.code as from_currency,
                       tc.code as to_currency
                FROM exchange_rates er
                JOIN currencies fc ON fc.id = er.from_currency_id
                JOIN currencies tc ON tc.id = er.to_currency_id
                WHERE er.id > 0
                ORDER BY er.updated_at DESC
                LIMIT 5";
        
        $result = $this->db->query($sql);
        $rates = [];
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $rates[] = [
                    'from_currency' => $row['from_currency'],
                    'to_currency' => $row['to_currency'],
                    'rate' => ($row['buy_rate'] + $row['sell_rate']) / 2 // متوسط السعر
                ];
            }
        }
        
        return $rates;
    }

    public function getLatestActivities(int $limit = 5) {
        // Use correct column names based on actual database schema
        $sql = "SELECT
                e.id,
                e.from_amount as amount_from,
                e.to_amount as amount_to,
                e.exchange_rate,
                e.created_at,
                COALESCE(fcb.currency_code, fba.currency_code, 'N/A') as from_currency,
                COALESCE(tcb.currency_code, tba.currency_code, 'N/A') as to_currency,
                COALESCE(c.full_name, 'N/A') as customer_name
                FROM exchanges e
                LEFT JOIN cash_boxes fcb ON fcb.id = e.from_cash_box_id
                LEFT JOIN cash_boxes tcb ON tcb.id = e.to_cash_box_id
                LEFT JOIN bank_accounts fba ON fba.id = e.from_bank_account_id
                LEFT JOIN bank_accounts tba ON tba.id = e.to_bank_account_id
                LEFT JOIN customers c ON c.id = e.customer_id
                WHERE e.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ORDER BY e.created_at DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        
        $stmt->bind_param('i', $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        $activities = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        
        return $activities;
    }

    public function getExchangeCountChange(): float {
        $sql = "SELECT 
                (SELECT COUNT(*) FROM exchanges 
                 WHERE DATE(created_at) = CURDATE()) as today,
                (SELECT COUNT(*) FROM exchanges 
                 WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) as yesterday";
        
        $result = $this->db->query($sql);
        if (!$result) return 0.0;
        
        $row = $result->fetch_assoc();
        if (!$row || $row['yesterday'] == 0) return 0.0;
        
        return (($row['today'] - $row['yesterday']) / $row['yesterday']) * 100;
    }

    public function getProfitChange(): float {
        $sql = "SELECT 
                (SELECT COALESCE(SUM(profit), 0) FROM exchanges 
                 WHERE DATE(created_at) = CURDATE()) as today,
                (SELECT COALESCE(SUM(profit), 0) FROM exchanges 
                 WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)) as yesterday";
        
        $result = $this->db->query($sql);
        if (!$result) return 0.0;
        
        $row = $result->fetch_assoc();
        if (!$row || $row['yesterday'] == 0) return 0.0;
        
        return (($row['today'] - $row['yesterday']) / $row['yesterday']) * 100;
    }

    public function getWeeklyOperations(): array {
        $sql = "SELECT 
                DATE(created_at) as date,
                COUNT(*) as count
                FROM exchanges 
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC";
        
        $result = $this->db->query($sql);
        if (!$result) return [];
        
        $data = [];
        while ($row = $result->fetch_assoc()) {
            $data[$row['date']] = (int)$row['count'];
        }
        
        // Fill in missing dates with 0
        $end = new DateTime();
        $start = new DateTime('-6 days');
        $interval = new DateInterval('P1D');
        $period = new DatePeriod($start, $interval, $end);
        
        $complete_data = [];
        foreach ($period as $date) {
            $date_str = $date->format('Y-m-d');
            $complete_data[$date_str] = $data[$date_str] ?? 0;
        }
        $complete_data[$end->format('Y-m-d')] = $data[$end->format('Y-m-d')] ?? 0;
        
        return array_values($complete_data);
    }
} 
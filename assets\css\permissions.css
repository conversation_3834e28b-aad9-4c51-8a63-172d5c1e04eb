/* تنسيقات خاصة بصفحة الصلاحيات */

/* بطاقات الإحصائيات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* تحسين الجدول */
#permissionsTable {
    font-size: 0.9rem;
}

#permissionsTable th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    border: none;
    padding: 12px 8px;
}

#permissionsTable td {
    vertical-align: middle;
    padding: 10px 8px;
    border-bottom: 1px solid #dee2e6;
}

#permissionsTable tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* تحسين الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.6em;
    border-radius: 6px;
}

/* تحسين الأكواد */
code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.85rem;
    border: 1px solid #e9ecef;
}

/* تحسين الأزرار */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* تحسين الفلاتر */
.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.form-select, .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus, .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تحسين الإحصائيات */
.card.bg-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #0aa2c0 100%) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%) !important;
}

/* تحسين الأيقونات */
.fas {
    margin-left: 5px;
}

/* تحسين Modal */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
    border-radius: 10px 10px 0 0;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

/* تحسين الجدول المتجاوب */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* تحسين عداد الصلاحيات */
#permissionCount {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    border-radius: 12px;
    background-color: #0d6efd;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* تحسين الشارات الملونة */
.badge.bg-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, #198754 0%, #146c43 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%) !important;
    color: #000 !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #0aa2c0 100%) !important;
    color: #000 !important;
}

.badge.bg-dark {
    background: linear-gradient(135deg, #212529 0%, #343a40 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
}

/* تحسين التخطيط العام */
.content {
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* تحسين العناوين */
h2, h5 {
    color: #495057;
    font-weight: 600;
}

/* تحسين الروابط والأزرار */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* تحسين الجدول للشاشات الصغيرة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.3em 0.5em;
    }
    
    code {
        font-size: 0.75rem;
        padding: 0.1rem 0.3rem;
    }
}

/* تحسين التمرير */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تأثيرات الحركة */
.card, .btn, .badge {
    transition: all 0.2s ease-in-out;
}

/* تحسين النصوص */
.text-dark {
    color: #495057 !important;
}

/* تحسين الحدود */
.border-start {
    border-left: 4px solid #0d6efd !important;
}

/* تحسين المسافات */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.me-2 {
    margin-left: 0.5rem !important;
}

.ms-2 {
    margin-right: 0.5rem !important;
}

-- =====================================================
-- نظام سجل المعاملات اليومية - Daily Transactions System
-- =====================================================

-- جدول الدول والعملات
CREATE TABLE IF NOT EXISTS countries (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL COMMENT 'اسم الدولة بالعربية',
    name_en VARCHAR(255) NOT NULL COMMENT 'اسم الدولة بالإنجليزية',
    currency_code VARCHAR(10) NOT NULL COMMENT 'رمز العملة (USD, SAR, EGP)',
    currency_name_ar VARCHAR(100) NOT NULL COMMENT 'اسم العملة بالعربية',
    currency_name_en VARCHAR(100) NOT NULL COMMENT 'اسم العملة بالإنجليزية',
    currency_symbol VARCHAR(10) COMMENT 'رمز العملة ($, ﷼, £)',
    is_active BOOLEAN DEFAULT 1 COMMENT 'حالة النشاط',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_country_name_ar (name_ar),
    INDEX idx_country_name_en (name_en),
    INDEX idx_currency_code (currency_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الدول والعملات';

-- جدول المعاملات اليومية الرئيسي
CREATE TABLE IF NOT EXISTS daily_transactions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    transaction_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم المعاملة',
    
    -- نوع العملية (الدولة والعملة)
    country_id INT UNSIGNED NOT NULL COMMENT 'معرف الدولة',
    
    -- المبالغ والحسابات
    base_amount DECIMAL(18,2) NOT NULL COMMENT 'مبلغ الحوالة الأساسي',
    customer_rate DECIMAL(18,6) NOT NULL COMMENT 'سعر القص للزبون',
    operation_type ENUM('multiply', 'divide') NOT NULL COMMENT 'نوع العملية الحسابية',
    calculated_amount DECIMAL(18,2) GENERATED ALWAYS AS (
        CASE 
            WHEN operation_type = 'multiply' THEN base_amount * customer_rate
            WHEN operation_type = 'divide' THEN base_amount / customer_rate
            ELSE 0
        END
    ) STORED COMMENT 'المبلغ الناتج (محسوب تلقائياً)',
    
    exchange_rate DECIMAL(18,6) NOT NULL COMMENT 'سعر الصرف',
    recipient_amount INT UNSIGNED GENERATED ALWAYS AS (
        FLOOR(calculated_amount * exchange_rate)
    ) STORED COMMENT 'المبلغ للمستلم (رقم صحيح)',
    
    -- نوع التسليم
    delivery_type ENUM('cash', 'bank', 'usdt') NOT NULL COMMENT 'نوع التسليم',
    transfer_amount DECIMAL(18,2) NULL COMMENT 'المبلغ المراد تحويله (للبنكي و USDT)',
    recipient_name VARCHAR(255) NULL COMMENT 'اسم المستلم (للبنكي و USDT)',
    
    -- ملاحظات
    notes TEXT NULL COMMENT 'ملاحظة',
    
    -- معلومات النظام
    branch_id INT UNSIGNED NULL COMMENT 'معرف الفرع',
    created_by INT UNSIGNED NULL COMMENT 'المستخدم الذي أنشأ المعاملة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    
    -- Foreign Keys
    CONSTRAINT fk_daily_trans_country FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT fk_daily_trans_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT fk_daily_trans_user FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
    
    -- Indexes
    INDEX idx_transaction_number (transaction_number),
    INDEX idx_created_at (created_at),
    INDEX idx_delivery_type (delivery_type),
    INDEX idx_country_id (country_id),
    INDEX idx_branch_id (branch_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المعاملات اليومية';

-- جدول تاريخ المعاملات (للتتبع والتدقيق)
CREATE TABLE IF NOT EXISTS daily_transaction_history (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    transaction_id INT UNSIGNED NOT NULL COMMENT 'معرف المعاملة',
    action_type ENUM('created', 'updated', 'deleted') NOT NULL COMMENT 'نوع العملية',
    old_values JSON NULL COMMENT 'القيم القديمة',
    new_values JSON NULL COMMENT 'القيم الجديدة',
    changed_by INT UNSIGNED NULL COMMENT 'المستخدم الذي قام بالتغيير',
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ التغيير',
    ip_address VARCHAR(45) NULL COMMENT 'عنوان IP',
    user_agent TEXT NULL COMMENT 'معلومات المتصفح',
    
    CONSTRAINT fk_dt_history_transaction FOREIGN KEY (transaction_id) REFERENCES daily_transactions(id) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT fk_dt_history_user FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
    
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_changed_at (changed_at),
    INDEX idx_action_type (action_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='تاريخ تغييرات المعاملات اليومية';

-- إدراج بيانات الدول والعملات الأساسية
INSERT IGNORE INTO countries (id, name_ar, name_en, currency_code, currency_name_ar, currency_name_en, currency_symbol) VALUES
(1, 'الولايات المتحدة الأمريكية', 'United States', 'USD', 'دولار أمريكي', 'US Dollar', '$'),
(2, 'المملكة العربية السعودية', 'Saudi Arabia', 'SAR', 'ريال سعودي', 'Saudi Riyal', '﷼'),
(3, 'جمهورية مصر العربية', 'Egypt', 'EGP', 'جنيه مصري', 'Egyptian Pound', '£'),
(4, 'الجمهورية اليمنية', 'Yemen', 'YER', 'ريال يمني', 'Yemeni Rial', '﷼'),
(5, 'دولة الإمارات العربية المتحدة', 'United Arab Emirates', 'AED', 'درهم إماراتي', 'UAE Dirham', 'د.إ'),
(6, 'دولة الكويت', 'Kuwait', 'KWD', 'دينار كويتي', 'Kuwaiti Dinar', 'د.ك'),
(7, 'مملكة البحرين', 'Bahrain', 'BHD', 'دينار بحريني', 'Bahraini Dinar', 'د.ب'),
(8, 'سلطنة عمان', 'Oman', 'OMR', 'ريال عماني', 'Omani Rial', 'ر.ع'),
(9, 'دولة قطر', 'Qatar', 'QAR', 'ريال قطري', 'Qatari Riyal', 'ر.ق'),
(10, 'الجمهورية اللبنانية', 'Lebanon', 'LBP', 'ليرة لبنانية', 'Lebanese Pound', 'ل.ل'),
(11, 'الجمهورية العربية السورية', 'Syria', 'SYP', 'ليرة سورية', 'Syrian Pound', 'ل.س'),
(12, 'المملكة الأردنية الهاشمية', 'Jordan', 'JOD', 'دينار أردني', 'Jordanian Dinar', 'د.أ'),
(13, 'جمهورية العراق', 'Iraq', 'IQD', 'دينار عراقي', 'Iraqi Dinar', 'د.ع'),
(14, 'المملكة المغربية', 'Morocco', 'MAD', 'درهم مغربي', 'Moroccan Dirham', 'د.م'),
(15, 'الجمهورية التونسية', 'Tunisia', 'TND', 'دينار تونسي', 'Tunisian Dinar', 'د.ت'),
(16, 'الجمهورية الجزائرية', 'Algeria', 'DZD', 'دينار جزائري', 'Algerian Dinar', 'د.ج'),
(17, 'جمهورية السودان', 'Sudan', 'SDG', 'جنيه سوداني', 'Sudanese Pound', 'ج.س'),
(18, 'المملكة المتحدة', 'United Kingdom', 'GBP', 'جنيه إسترليني', 'British Pound', '£'),
(19, 'الاتحاد الأوروبي', 'European Union', 'EUR', 'يورو', 'Euro', '€'),
(20, 'جمهورية تركيا', 'Turkey', 'TRY', 'ليرة تركية', 'Turkish Lira', '₺');

-- إضافة الصلاحيات الخاصة بنظام المعاملات اليومية
INSERT IGNORE INTO permissions (name, description, module) VALUES
('daily_transactions.view', 'عرض المعاملات اليومية', 'daily_transactions'),
('daily_transactions.create', 'إنشاء معاملة يومية جديدة', 'daily_transactions'),
('daily_transactions.edit', 'تعديل المعاملات اليومية', 'daily_transactions'),
('daily_transactions.delete', 'حذف المعاملات اليومية', 'daily_transactions'),
('daily_transactions.export', 'تصدير المعاملات اليومية', 'daily_transactions'),
('daily_transactions.reports', 'تقارير المعاملات اليومية', 'daily_transactions');

-- منح الصلاحيات للأدوار الموجودة
INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'System Admin' 
AND p.module = 'daily_transactions';

INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'Branch Manager' 
AND p.name IN ('daily_transactions.view', 'daily_transactions.create', 'daily_transactions.edit', 'daily_transactions.reports');

INSERT IGNORE INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'Cashier' 
AND p.name IN ('daily_transactions.view', 'daily_transactions.create');

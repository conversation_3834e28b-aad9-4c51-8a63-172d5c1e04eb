<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paypal_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('paypal.mark_received');

$paypalMgr = new PayPalManager(new Database());

$id = (int)($_GET['id'] ?? 0);
if (!$id) {
    set_flash('danger', 'معرف غير صحيح');
    redirect('paypal_incoming_transfers.php');
}

// Get the transfer
$transfer = $paypalMgr->getTransferById($id);
if (!$transfer) {
    set_flash('danger', 'الحوالة غير موجودة');
    redirect('paypal_incoming_transfers.php');
}

// Update status to received
$data = [
    'recipient_name' => $transfer['recipient_name'],
    'recipient_phone' => $transfer['recipient_phone'],
    'transaction_code' => $transfer['transaction_code'],
    'sender_name' => $transfer['sender_name'],
    'amount' => $transfer['amount'],
    'status' => 'مستلم'
];

if ($paypalMgr->updateTransfer($id, $data)) {
    // تسجيل عملية تأكيد استلام حوالة PayPal
    ActivityHelper::logStatusChange(
        'paypal_transfers',
        "حوالة PayPal #$id - " . $transfer['recipient_name'],
        $transfer['status'],
        'مستلم',
        $id
    );
    set_flash('success', 'تم تحديد الحوالة كمستلمة بنجاح');
} else {
    set_flash('danger', 'تعذر تحديث الحوالة');
}

redirect('paypal_incoming_transfers.php');
?> 
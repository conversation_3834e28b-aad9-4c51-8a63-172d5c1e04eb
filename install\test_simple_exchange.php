<?php
/**
 * Test SimpleExchangeManager functionality
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/simple_exchange_manager.php';

echo "<h2>اختبار مدير الصرافة المبسط</h2>\n";

try {
    echo "<h3>1. إنشاء مدير الصرافة</h3>\n";
    
    $db = new Database();
    $exManager = new SimpleExchangeManager($db);
    echo "<p style='color: green;'>✓ تم إنشاء SimpleExchangeManager بنجاح</p>\n";
    
    echo "<h3>2. فحص البيانات المطلوبة</h3>\n";
    
    // Check customers
    $conn = Database::getConnection();
    $result = $conn->query("SELECT id, full_name FROM customers WHERE status = 'active' LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $customer = $result->fetch_assoc();
        echo "<p style='color: green;'>✓ عميل متاح: {$customer['full_name']} (ID: {$customer['id']})</p>\n";
    } else {
        echo "<p style='color: red;'>✗ لا توجد عملاء نشطين</p>\n";
        exit;
    }
    
    // Check currencies
    $result = $conn->query("SELECT id, code, name FROM currencies WHERE is_active = 1 LIMIT 2");
    $currencies = [];
    if ($result && $result->num_rows >= 2) {
        while ($row = $result->fetch_assoc()) {
            $currencies[] = $row;
            echo "<p style='color: green;'>✓ عملة متاحة: {$row['name']} ({$row['code']}) - ID: {$row['id']}</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ تحتاج إلى عملتين نشطتين على الأقل</p>\n";
        exit;
    }
    
    // Check exchange rate
    $fromCurrency = $currencies[0]['id'];
    $toCurrency = $currencies[1]['id'];
    
    $rate = $exManager->getExchangeRateByPair($fromCurrency, $toCurrency);
    if ($rate) {
        echo "<p style='color: green;'>✓ سعر صرف متاح: {$currencies[0]['code']} → {$currencies[1]['code']} (معدل: {$rate['sell_rate']})</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ لا يوجد سعر صرف مباشر، سيتم إنشاء واحد للاختبار</p>\n";
        
        // Create a test exchange rate
        $testRate = [
            'from_currency_id' => $fromCurrency,
            'to_currency_id' => $toCurrency,
            'buy_rate' => 1.0,
            'sell_rate' => 1.1,
            'updated_by' => 1
        ];
        
        $rateId = $exManager->addExchangeRate($testRate);
        if ($rateId) {
            echo "<p style='color: green;'>✓ تم إنشاء سعر صرف تجريبي (ID: $rateId)</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في إنشاء سعر صرف تجريبي</p>\n";
            exit;
        }
    }
    
    // Check cash boxes
    $result = $conn->query("SELECT id, name, current_balance FROM cash_boxes WHERE status = 'active' LIMIT 2");
    $cashBoxes = [];
    if ($result && $result->num_rows >= 2) {
        while ($row = $result->fetch_assoc()) {
            $cashBoxes[] = $row;
            echo "<p style='color: green;'>✓ صندوق متاح: {$row['name']} (رصيد: {$row['current_balance']}) - ID: {$row['id']}</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ تحتاج إلى صندوقين نشطين على الأقل</p>\n";
        exit;
    }
    
    echo "<h3>3. اختبار عملية صرف تجريبية</h3>\n";
    
    // Prepare test exchange data
    $testExchangeData = [
        'customer_id' => $customer['id'],
        'from_currency_id' => $fromCurrency,
        'to_currency_id' => $toCurrency,
        'amount_from' => 100.0,
        'user_amount_field' => 'amount_from',
        'funding_source' => ['type' => 'cash', 'id' => $cashBoxes[0]['id']],
        'disburse_source' => ['type' => 'cash', 'id' => $cashBoxes[1]['id']],
        'notes' => 'اختبار عملية صرف',
        'branch_id' => 1,
        'created_by' => 1
    ];
    
    echo "<p><strong>بيانات الاختبار:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>العميل: {$customer['full_name']}</li>\n";
    echo "<li>من عملة: {$currencies[0]['code']} (مبلغ: 100)</li>\n";
    echo "<li>إلى عملة: {$currencies[1]['code']}</li>\n";
    echo "<li>صندوق التمويل: {$cashBoxes[0]['name']}</li>\n";
    echo "<li>صندوق الصرف: {$cashBoxes[1]['name']}</li>\n";
    echo "</ul>\n";
    
    // Test the exchange (dry run - don't actually execute)
    echo "<p style='color: blue;'>ℹ️ هذا اختبار تجريبي - لن يتم تنفيذ العملية فعلياً</p>\n";
    
    // Validate the data structure
    $requiredFields = ['customer_id','from_currency_id','to_currency_id','created_by','branch_id','user_amount_field'];
    $missingFields = [];
    
    foreach ($requiredFields as $field) {
        if (!isset($testExchangeData[$field]) || $testExchangeData[$field] === null || $testExchangeData[$field] === '') {
            $missingFields[] = $field;
        }
    }
    
    if (empty($missingFields)) {
        echo "<p style='color: green;'>✓ جميع الحقول المطلوبة متوفرة</p>\n";
    } else {
        echo "<p style='color: red;'>✗ حقول مفقودة: " . implode(', ', $missingFields) . "</p>\n";
    }
    
    if ($testExchangeData['from_currency_id'] === $testExchangeData['to_currency_id']) {
        echo "<p style='color: red;'>✗ العملات متشابهة</p>\n";
    } else {
        echo "<p style='color: green;'>✓ العملات مختلفة</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ اختبار مدير الصرافة المبسط مكتمل!</h3>\n";
    echo "<p><strong>النتيجة:</strong> النظام جاهز لتنفيذ عمليات الصرف</p>\n";
    echo "<p><a href='../dashboard/exchange.php' target='_blank' style='color: #007bff; font-weight: bold;'>اختبر عملية صرف حقيقية الآن</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الاختبار: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

<?php
require_once __DIR__ . '/../includes/database.php';

try {
    $db = new Database();
    $conn = $db::getConnection();
    
    // Read the SQL migration
    $sql = file_get_contents(__DIR__ . '/fix_withdrawal_status.sql');
    
    if ($conn->multi_query($sql)) {
        echo "تم تحديث جدول سحب المبالغ بنجاح لإضافة حالة 'مستلمة'\n";
        echo "الآن يمكن حفظ الحالة بشكل صحيح\n";
    } else {
        echo "خطأ في تحديث الجدول: " . $conn->error . "\n";
    }
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}
?> 
<?php
/**
 * Update CashManager to use currency_code instead of currency_id
 */

require_once __DIR__ . '/../config/config.php';

echo "<h2>تحديث CashManager لاستخدام رمز العملة</h2>\n";

try {
    $cashManagerFile = __DIR__ . '/../includes/cash_manager.php';
    
    if (!file_exists($cashManagerFile)) {
        throw new Exception("ملف CashManager غير موجود");
    }
    
    echo "<h3>1. قراءة الملف الحالي</h3>\n";
    
    $content = file_get_contents($cashManagerFile);
    if ($content === false) {
        throw new Exception("فشل في قراءة ملف CashManager");
    }
    
    echo "<p>✓ تم قراءة الملف بنجاح</p>\n";
    
    echo "<h3>2. تطبيق التحديثات</h3>\n";
    
    $updates = 0;
    
    // Update getAllCashBoxes method
    $oldPattern1 = 'SELECT cb.*, cur.code  AS currency_code,
                       cur.symbol      AS currency_symbol,
                       b.name          AS branch_name,
                       u.full_name     AS responsible_name
                FROM cash_boxes cb
                LEFT JOIN currencies cur ON cur.id = cb.currency_id';
    
    $newPattern1 = 'SELECT cb.*, cur.code AS currency_code,
                       cur.symbol AS currency_symbol,
                       b.name AS branch_name,
                       u.full_name AS responsible_name
                FROM cash_boxes cb
                LEFT JOIN currencies cur ON cur.code = cb.currency_code';
    
    if (strpos($content, $oldPattern1) !== false) {
        $content = str_replace($oldPattern1, $newPattern1, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث getAllCashBoxes</p>\n";
    }
    
    // Update getCashBoxById method
    $oldPattern2 = 'SELECT cb.*, curr.name as currency_name, curr.code as currency_code,
                       curr.symbol as currency_symbol, b.name as branch_name,
                       u.full_name as responsible_name
                FROM cash_boxes cb
                LEFT JOIN currencies curr ON curr.id = cb.currency_id';
    
    $newPattern2 = 'SELECT cb.*, curr.name as currency_name, curr.code as currency_code,
                       curr.symbol as currency_symbol, b.name as branch_name,
                       u.full_name as responsible_name
                FROM cash_boxes cb
                LEFT JOIN currencies curr ON curr.code = cb.currency_code';
    
    if (strpos($content, $oldPattern2) !== false) {
        $content = str_replace($oldPattern2, $newPattern2, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث getCashBoxById</p>\n";
    }
    
    // Update addCashBox method
    $oldPattern3 = 'INSERT INTO cash_boxes (name, currency_id, initial_balance, current_balance, branch_id, responsible_user_id, is_active, created_at, updated_at)
                VALUES (?,?,?,?,?,?,?, NOW(), NOW())';
    
    $newPattern3 = 'INSERT INTO cash_boxes (name, currency_code, initial_balance, current_balance, branch_id, responsible_user_id, is_active, created_at, updated_at)
                VALUES (?,?,?,?,?,?,?, NOW(), NOW())';
    
    if (strpos($content, $oldPattern3) !== false) {
        $content = str_replace($oldPattern3, $newPattern3, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث addCashBox</p>\n";
    }
    
    // Update parameter binding for addCashBox
    $oldPattern4 = '$stmt->bind_param(
            \'siddiis\',
            $data[\'name\'],
            $data[\'currency_id\'],';
    
    $newPattern4 = '$stmt->bind_param(
            \'ssddiis\',
            $data[\'name\'],
            $data[\'currency_code\'],';
    
    if (strpos($content, $oldPattern4) !== false) {
        $content = str_replace($oldPattern4, $newPattern4, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث bind_param في addCashBox</p>\n";
    }
    
    // Update getCashBoxMovements method
    $oldPattern5 = 'LEFT JOIN currencies curr ON curr.id = cb.currency_id';
    $newPattern5 = 'LEFT JOIN currencies curr ON curr.code = cb.currency_code';
    
    $content = str_replace($oldPattern5, $newPattern5, $content);
    $updates++;
    echo "<p style='color: green;'>✓ تم تحديث getCashBoxMovements</p>\n";
    
    // Update getCashMovementById method
    $oldPattern6 = 'LEFT JOIN currencies curr ON curr.id = cb.currency_id';
    // Already replaced above
    
    // Remove currency_id parameters from movement methods
    $oldPattern7 = 'public function addCashMovementForExchange(int $boxId, string $type, float $amount, int $currencyId, ?int $exchangeId, int $userId, string $description = \'\'): array';
    $newPattern7 = 'public function addCashMovementForExchange(int $boxId, string $type, float $amount, ?int $exchangeId, int $userId, string $description = \'\'): array';
    
    if (strpos($content, $oldPattern7) !== false) {
        $content = str_replace($oldPattern7, $newPattern7, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث addCashMovementForExchange signature</p>\n";
    }
    
    // Update movement insert statements to remove currency_id
    $oldPattern8 = 'INSERT INTO cash_movements (cash_box_id, movement_type, amount, currency_id, description, reference_number, exchange_id, user_id, created_at)
                                        VALUES (?,?,?,?,?,?,?, ?, NOW())';
    
    $newPattern8 = 'INSERT INTO cash_movements (cash_box_id, movement_type, amount, description, reference_number, exchange_id, user_id, created_at)
                                        VALUES (?,?,?,?,?,?, ?, NOW())';
    
    if (strpos($content, $oldPattern8) !== false) {
        $content = str_replace($oldPattern8, $newPattern8, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث movement insert statement</p>\n";
    }
    
    // Update bind_param for movement insert
    $oldPattern9 = '$stmt->bind_param(\'isdissii\', $boxId, $type, $amount, $currencyId, $description, $ref, $exchangeId, $userId);';
    $newPattern9 = '$stmt->bind_param(\'isdssi\', $boxId, $type, $amount, $description, $ref, $exchangeId, $userId);';
    
    if (strpos($content, $oldPattern9) !== false) {
        $content = str_replace($oldPattern9, $newPattern9, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث bind_param للحركات</p>\n";
    }
    
    // Update transfer method signature
    $oldPattern10 = 'public function addCashMovementForTransfer(int $boxId, string $type, float $amount, int $currencyId, ?int $transferId, int $userId, string $description = \'\'): array';
    $newPattern10 = 'public function addCashMovementForTransfer(int $boxId, string $type, float $amount, ?int $transferId, int $userId, string $description = \'\'): array';
    
    if (strpos($content, $oldPattern10) !== false) {
        $content = str_replace($oldPattern10, $newPattern10, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث addCashMovementForTransfer signature</p>\n";
    }
    
    // Update transfer insert statement
    $oldPattern11 = 'INSERT INTO cash_movements (cash_box_id, movement_type, amount, currency_id, description, reference_number, transfer_id, user_id, created_at) VALUES (?,?,?,?,?,?,?, ?, NOW())';
    $newPattern11 = 'INSERT INTO cash_movements (cash_box_id, movement_type, amount, description, reference_number, transfer_id, user_id, created_at) VALUES (?,?,?,?,?,?, ?, NOW())';
    
    if (strpos($content, $oldPattern11) !== false) {
        $content = str_replace($oldPattern11, $newPattern11, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث transfer insert statement</p>\n";
    }
    
    // Update transfer bind_param
    $oldPattern12 = '$stmt->bind_param(\'isdissii\',$boxId,$type,$amount,$currencyId,$description,$ref,$transferId,$userId);';
    $newPattern12 = '$stmt->bind_param(\'isdssi\',$boxId,$type,$amount,$description,$ref,$transferId,$userId);';
    
    if (strpos($content, $oldPattern12) !== false) {
        $content = str_replace($oldPattern12, $newPattern12, $content);
        $updates++;
        echo "<p style='color: green;'>✓ تم تحديث transfer bind_param</p>\n";
    }
    
    echo "<h3>3. حفظ الملف المحدث</h3>\n";
    
    if ($updates > 0) {
        // Create backup
        $backupFile = $cashManagerFile . '.backup.' . date('Y-m-d-H-i-s');
        if (copy($cashManagerFile, $backupFile)) {
            echo "<p style='color: green;'>✓ تم إنشاء نسخة احتياطية: " . basename($backupFile) . "</p>\n";
        }
        
        // Save updated content
        if (file_put_contents($cashManagerFile, $content) !== false) {
            echo "<p style='color: green;'>✓ تم حفظ الملف المحدث بنجاح</p>\n";
            echo "<p><strong>عدد التحديثات المطبقة:</strong> $updates</p>\n";
        } else {
            throw new Exception("فشل في حفظ الملف المحدث");
        }
    } else {
        echo "<p style='color: orange;'>⚠ لم يتم العثور على تحديثات مطلوبة</p>\n";
    }
    
    echo "<h3>4. تحديث ملف dashboard/cash.php</h3>\n";
    
    $dashboardFile = __DIR__ . '/../dashboard/cash.php';
    if (file_exists($dashboardFile)) {
        $dashboardContent = file_get_contents($dashboardFile);
        
        // Replace currency_id with currency_code in the dashboard
        $dashboardUpdates = 0;
        
        // Update form field names
        $dashboardContent = str_replace('name="currency_id"', 'name="currency_code"', $dashboardContent);
        $dashboardContent = str_replace('$_POST[\'currency_id\']', '$_POST[\'currency_code\']', $dashboardContent);
        $dashboardContent = str_replace('data-currency', 'data-currency-code', $dashboardContent);
        $dashboardContent = str_replace('currency_id', 'currency_code', $dashboardContent);
        
        $dashboardUpdates++;
        
        if (file_put_contents($dashboardFile, $dashboardContent)) {
            echo "<p style='color: green;'>✓ تم تحديث dashboard/cash.php</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في تحديث dashboard/cash.php</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم تحديث CashManager بنجاح!</h3>\n";
    echo "<p><strong>التحديثات المطبقة:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>تم تحديث جميع الاستعلامات لاستخدام currency_code</li>\n";
    echo "<li>تم إزالة معاملات currency_id من الدوال</li>\n";
    echo "<li>تم تحديث bind_param statements</li>\n";
    echo "<li>تم تحديث dashboard/cash.php</li>\n";
    echo "</ul>\n";
    
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>اختبار صفحة الصناديق</a></li>\n";
    echo "<li><a href='view_all_currencies.php' target='_blank'>عرض جميع العملات</a></li>\n";
    echo "<li><a href='manage_currencies.php' target='_blank'>إدارة العملات</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

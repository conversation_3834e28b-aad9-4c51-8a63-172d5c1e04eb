<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/user_manager.php';
require_once __DIR__ . '/../includes/image_helper.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$current_user = $auth->getCurrentUser();

$pageTitle = 'المستخدمون';

// Permission check
if (!$auth->hasPermission('users.view')) {
    include __DIR__ . '/../includes/header.php';
    echo '<main class="content p-4"><div class="alert alert-danger">تم رفض الوصول</div></main>';
    include __DIR__ . '/../includes/footer.php';
    exit;
}

$db          = new Database();
$userManager = new UserManager($db);

// Handle actions (delete, activate, deactivate)
$message = null;
if (isset($_GET['action'], $_GET['id'])) {
    $action  = $_GET['action'];
    $userId  = (int) $_GET['id'];
    $token   = $_GET['token'] ?? '';

    if (!verify_csrf_token($token)) {
        $message = ['type' => 'danger', 'text' => 'رمز CSRF غير صالح'];
    } else {
        switch ($action) {
            case 'delete':
                if ($auth->hasPermission('users.delete')) {
                    // Delete profile image if exists
                    $user = $userManager->getUserById($userId);
                    if ($user && !empty($user['profile_image'])) {
                        ImageHelper::deleteProfileImage($user['profile_image']);
                    }
                    
                    if ($userManager->deleteUser($userId)) {
                        // تسجيل عملية حذف المستخدم
                        ActivityHelper::logDelete(
                            'users',
                            $user['username'] ?? "مستخدم #$userId",
                            $user,
                            $userId
                        );
                        $message = ['type' => 'success', 'text' => 'تم حذف المستخدم بنجاح'];
                    } else {
                        $message = ['type' => 'danger', 'text' => 'فشل حذف المستخدم'];
                    }
                }
                break;
            case 'activate':
                if ($auth->hasPermission('users.edit')) {
                    if ($userManager->updateUserStatus($userId, 'active')) {
                        // تسجيل عملية تفعيل المستخدم
                        ActivityHelper::logStatusChange(
                            'users',
                            "مستخدم #$userId",
                            'inactive',
                            'active',
                            $userId
                        );
                        $message = ['type' => 'success', 'text' => 'تم تفعيل المستخدم'];
                    }
                }
                break;
            case 'deactivate':
                if ($auth->hasPermission('users.edit')) {
                    if ($userManager->updateUserStatus($userId, 'inactive')) {
                        // تسجيل عملية إلغاء تفعيل المستخدم
                        ActivityHelper::logStatusChange(
                            'users',
                            "مستخدم #$userId",
                            'active',
                            'inactive',
                            $userId
                        );
                        $message = ['type' => 'success', 'text' => 'تم إلغاء تفعيل المستخدم'];
                    }
                }
                break;
        }
    }
}

$users = $userManager->getAllUsers();

// تسجيل عملية عرض قائمة المستخدمين
ActivityHelper::logView('users', 'عرض قائمة المستخدمين');

$csrfToken = get_csrf_token();

include __DIR__ . '/../includes/header.php';
?>

<!-- Dashboard content starts here (main-content div is already opened in header.php) -->
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">المستخدمون</h1>
                <p class="text-muted mb-0">إدارة مستخدمي النظام</p>
            </div>
            <?php if ($auth->hasPermission('users.create')): ?>
            <a href="add_user.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة مستخدم جديد
            </a>
            <?php endif; ?>
        </div>
    </div>
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $message['text']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Users Table Card -->
    <div class="dashboard-card">
        <div class="table-responsive">
            <table class="table table-hover sortable-table" id="usersTable">
        <thead class="table-light">
            <tr>
                <th>#</th>
                <th>الصورة</th>
                <th>اسم المستخدم</th>
                <th>الاسم الكامل</th>
                <th>البريد الإلكتروني</th>
                <th>الهاتف</th>
                <th>الدور</th>
                <th>الفرع</th>
                <th>الحالة</th>
                <th>آخر تسجيل دخول</th>
                <th>تاريخ الإنشاء</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($users as $u): ?>
            <tr>
                <td><?php echo $u['id']; ?></td>
                <td>
                    <?php if (!empty($u['profile_image'])): ?>
                        <?php echo ImageHelper::getProfileImageHtmlWithClasses($u['profile_image'], $u['id'], 'profile-image-md', ''); ?>
                    <?php else: ?>
                        <?php echo ImageHelper::getDefaultAvatarHtml('default-avatar-md'); ?>
                    <?php endif; ?>
                </td>
                <td><?php echo htmlspecialchars($u['username']); ?></td>
                <td><?php echo htmlspecialchars($u['full_name']); ?></td>
                <td><?php echo htmlspecialchars($u['email']); ?></td>
                <td><?php echo htmlspecialchars($u['phone']); ?></td>
                <td><?php echo htmlspecialchars($u['role_name']); ?></td>
                <td><?php echo htmlspecialchars($u['branch_name']); ?></td>
                <td>
                    <?php 
                    $statusClass = '';
                    $statusText = '';
                    switch($u['status']) {
                        case 'active':
                            $statusClass = 'badge bg-success';
                            $statusText = 'نشط';
                            break;
                        case 'inactive':
                            $statusClass = 'badge bg-warning';
                            $statusText = 'غير نشط';
                            break;
                        case 'blocked':
                            $statusClass = 'badge bg-danger';
                            $statusText = 'محظور';
                            break;
                        default:
                            $statusClass = 'badge bg-secondary';
                            $statusText = $u['status'];
                    }
                    ?>
                    <span class="<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                </td>
                <td><?php echo htmlspecialchars($u['last_login']); ?></td>
                <td><?php echo htmlspecialchars($u['created_at']); ?></td>
                <td>
                    <?php if ($auth->hasPermission('users.edit')): ?>
                        <a href="edit_user.php?id=<?php echo $u['id']; ?>" class="btn btn-sm btn-primary">تعديل</a>
                    <?php endif; ?>
                    <?php if ($auth->hasPermission('users.delete')): ?>
                        <a href="?action=delete&id=<?php echo $u['id']; ?>&token=<?php echo $csrfToken; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف المستخدم؟');">حذف</a>
                    <?php endif; ?>
                    <?php if ($auth->hasPermission('users.edit')): ?>
                        <?php if ($u['status'] === 'active'): ?>
                            <a href="?action=deactivate&id=<?php echo $u['id']; ?>&token=<?php echo $csrfToken; ?>" class="btn btn-sm btn-warning">تعطيل</a>
                        <?php else: ?>
                            <a href="?action=activate&id=<?php echo $u['id']; ?>&token=<?php echo $csrfToken; ?>" class="btn btn-sm btn-success">تفعيل</a>
                        <?php endif; ?>
                    <?php endif; ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
            </table>
        </div> <!-- /.table-responsive -->
    </div> <!-- /.dashboard-card -->

</div> <!-- /.container-fluid -->

<?php include __DIR__ . '/../includes/footer.php'; ?>
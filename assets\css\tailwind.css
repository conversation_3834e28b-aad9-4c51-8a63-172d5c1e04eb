@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    direction: rtl;
  }
}

@layer components {
  /* Buttons */
  .btn-primary {
    @apply bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors duration-200;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200;
  }
  
  /* Forms */
  .form-input {
    @apply w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-group {
    @apply mb-4;
  }
  
  /* Cards */
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .card-header {
    @apply flex items-center justify-between mb-4 pb-4 border-b border-gray-200;
  }
  
  /* Dashboard Layout */
  .dashboard-wrapper {
    @apply flex min-h-screen bg-gray-100;
  }
  
  /* ===== تم تعليق أنماط الشريط الجانبي بسبب التوحيد في sidebar-unified.css =====
  .sidebar {
    @apply w-64 bg-white shadow-md fixed h-full overflow-y-auto;
  }
  .sidebar-link {
    @apply flex items-center gap-2 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200;
  }
  .sidebar-link.active {
    @apply bg-primary-50 text-primary-600 font-medium;
  }
  .main-content {
    @apply flex-1 mr-64 p-8;
  }
/* ===== نهاية التعليق ===== */
  
  /* Tables */
  .table-container {
    @apply overflow-x-auto bg-white rounded-lg shadow;
  }
  
  .data-table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header th {
    @apply px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }
  
  /* Stats Cards */
  .stats-card {
    @apply bg-white rounded-lg shadow-md p-6 flex items-center justify-between;
  }
  
  .stats-icon {
    @apply w-12 h-12 rounded-full flex items-center justify-center text-2xl;
  }
  
  /* Page Headers */
  .page-header {
    @apply mb-8 flex items-center justify-between;
  }
  
  .page-title {
    @apply text-2xl font-bold text-gray-800;
  }
  
  /* Filters */
  .filter-container {
    @apply flex gap-4 mb-6 flex-wrap;
  }
  
  .filter-item {
    @apply flex items-center gap-2;
  }
  
  /* Responsive Utilities */
  .responsive-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  /* Alert Messages */
  .alert {
    @apply p-4 mb-4 rounded-lg;
  }
  
  .alert-success {
    @apply bg-green-50 text-green-700;
  }
  
  .alert-error {
    @apply bg-red-50 text-red-700;
  }
  
  .alert-warning {
    @apply bg-yellow-50 text-yellow-700;
  }
  
  .alert-info {
    @apply bg-blue-50 text-blue-700;
  }
  
  /* Modal */
  .modal-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4;
  }
  
  .modal-content {
    @apply bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto;
  }
  
  .modal-header {
    @apply p-6 border-b border-gray-200;
  }
  
  .modal-body {
    @apply p-6;
  }
  
  .modal-footer {
    @apply p-6 border-t border-gray-200 flex justify-end gap-4;
  }
} 
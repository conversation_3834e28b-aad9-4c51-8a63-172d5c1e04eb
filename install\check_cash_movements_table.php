<?php
/**
 * Check cash_movements table structure
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>فحص هيكل جدول cash_movements</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص أعمدة الجدول</h3>\n";
    
    $result = $conn->query("DESCRIBE cash_movements");
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>\n";
        echo "</tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>\n";
            echo "<td>{$row['Field']}</td>\n";
            echo "<td>{$row['Type']}</td>\n";
            echo "<td>{$row['Null']}</td>\n";
            echo "<td>{$row['Key']}</td>\n";
            echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>\n";
            echo "<td>{$row['Extra']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>2. فحص الحقول المطلوبة للتحديث</h3>\n";
    
    $requiredFields = ['movement_type', 'amount', 'description', 'reference_number', 'updated_at'];
    
    foreach ($requiredFields as $field) {
        $result = $conn->query("SHOW COLUMNS FROM cash_movements LIKE '$field'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ عمود $field موجود</p>\n";
        } else {
            echo "<p style='color: red;'>✗ عمود $field مفقود</p>\n";
        }
    }
    
    echo "<h3>3. فحص البيانات الموجودة</h3>\n";
    
    $result = $conn->query("SELECT COUNT(*) as count FROM cash_movements");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<p><strong>عدد الحركات المالية:</strong> $count</p>\n";
        
        if ($count > 0) {
            $result = $conn->query("SELECT id, movement_type, amount, description, reference_number, created_at FROM cash_movements ORDER BY id DESC LIMIT 3");
            if ($result) {
                echo "<p><strong>آخر 3 حركات:</strong></p>\n";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
                echo "<tr style='background: #f8f9fa;'>\n";
                echo "<th>ID</th><th>النوع</th><th>المبلغ</th><th>الوصف</th><th>رقم المرجع</th><th>تاريخ الإنشاء</th>\n";
                echo "</tr>\n";
                
                while ($row = $result->fetch_assoc()) {
                    echo "<tr>\n";
                    echo "<td>{$row['id']}</td>\n";
                    echo "<td>{$row['movement_type']}</td>\n";
                    echo "<td>{$row['amount']}</td>\n";
                    echo "<td>" . htmlspecialchars($row['description']) . "</td>\n";
                    echo "<td>" . htmlspecialchars($row['reference_number']) . "</td>\n";
                    echo "<td>{$row['created_at']}</td>\n";
                    echo "</tr>\n";
                }
                echo "</table>\n";
            }
        }
    }
    
    echo "<h3>4. اختبار استعلام التحديث</h3>\n";
    
    // Test the update query structure
    $testSql = "UPDATE cash_movements SET 
                    movement_type = ?,
                    amount = ?,
                    description = ?,
                    reference_number = ?,
                    updated_at = NOW()
                WHERE id = ?";
    
    $stmt = $conn->prepare($testSql);
    if ($stmt) {
        echo "<p style='color: green;'>✓ استعلام التحديث صحيح</p>\n";
        $stmt->close();
    } else {
        echo "<p style='color: red;'>✗ خطأ في استعلام التحديث: " . $conn->error . "</p>\n";
    }
    
    echo "<h3>5. فحص صلاحيات قاعدة البيانات</h3>\n";
    
    // Check database privileges
    $result = $conn->query("SHOW GRANTS");
    if ($result) {
        echo "<p><strong>صلاحيات قاعدة البيانات:</strong></p>\n";
        echo "<ul>\n";
        while ($row = $result->fetch_array()) {
            echo "<li>" . htmlspecialchars($row[0]) . "</li>\n";
        }
        echo "</ul>\n";
    }
    
    echo "<h3>6. اختبار المعاملات</h3>\n";
    
    try {
        $conn->begin_transaction();
        echo "<p style='color: green;'>✓ بدء المعاملة نجح</p>\n";
        
        $conn->rollback();
        echo "<p style='color: green;'>✓ إلغاء المعاملة نجح</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في المعاملات: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى فحص جدول cash_movements</h3>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
</style>

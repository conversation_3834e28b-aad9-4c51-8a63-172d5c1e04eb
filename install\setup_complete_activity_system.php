<?php
/**
 * إعداد نظام تسجيل العمليات الشامل - التشغيل الكامل
 * Complete Activity Logging System Setup
 */

require_once __DIR__ . '/../config/database.php';

echo "<h1>إعداد نظام تسجيل العمليات الشامل</h1>\n";
echo "<p>جاري إعداد النظام الكامل لتسجيل العمليات...</p>\n";

try {
    // الاتصال بقاعدة البيانات
    $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($mysqli->connect_error) {
        throw new Exception('فشل الاتصال بقاعدة البيانات: ' . $mysqli->connect_error);
    }
    
    $mysqli->set_charset(DB_CHARSET);
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>\n";
    
    // الخطوة 1: إنشاء جدول تسجيل العمليات
    echo "<h2>الخطوة 1: إنشاء جدول تسجيل العمليات</h2>\n";
    
    $sqlFile1 = __DIR__ . '/create_system_activity_logs.sql';
    if (file_exists($sqlFile1)) {
        $sql1 = file_get_contents($sqlFile1);
        $queries1 = array_filter(array_map('trim', explode(';', $sql1)));
        
        foreach ($queries1 as $query) {
            if (empty($query) || strpos($query, '--') === 0) continue;
            
            if ($mysqli->query($query)) {
                echo "<p style='color: green;'>✅ " . substr($query, 0, 50) . "...</p>\n";
            } else {
                echo "<p style='color: red;'>❌ خطأ: " . $mysqli->error . "</p>\n";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠ ملف SQL للجدول غير موجود</p>\n";
    }
    
    // الخطوة 2: إضافة الصلاحيات
    echo "<h2>الخطوة 2: إضافة الصلاحيات</h2>\n";
    
    $sqlFile2 = __DIR__ . '/add_activity_logs_permissions.sql';
    if (file_exists($sqlFile2)) {
        $sql2 = file_get_contents($sqlFile2);
        $queries2 = array_filter(array_map('trim', explode(';', $sql2)));
        
        foreach ($queries2 as $query) {
            if (empty($query) || strpos($query, '--') === 0) continue;
            
            if ($mysqli->query($query)) {
                echo "<p style='color: green;'>✅ " . substr($query, 0, 50) . "...</p>\n";
            } else {
                echo "<p style='color: red;'>❌ خطأ: " . $mysqli->error . "</p>\n";
            }
        }
    } else {
        echo "<p style='color: orange;'>⚠ ملف SQL للصلاحيات غير موجود</p>\n";
    }
    
    // الخطوة 3: التحقق من النظام
    echo "<h2>الخطوة 3: التحقق من النظام</h2>\n";
    
    // التحقق من وجود الجدول
    $result = $mysqli->query("SHOW TABLES LIKE 'system_activity_logs'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ جدول system_activity_logs موجود</p>\n";
        
        // عد الأعمدة
        $structure = $mysqli->query("DESCRIBE system_activity_logs");
        $columnCount = $structure->num_rows;
        echo "<p>📊 عدد الأعمدة: $columnCount</p>\n";
        
        // عد الفهارس
        $indexes = $mysqli->query("SHOW INDEX FROM system_activity_logs");
        $indexCount = $indexes->num_rows;
        echo "<p>🔍 عدد الفهارس: $indexCount</p>\n";
        
    } else {
        echo "<p style='color: red;'>❌ جدول system_activity_logs غير موجود</p>\n";
    }
    
    // التحقق من الصلاحيات
    $permResult = $mysqli->query("SELECT COUNT(*) as count FROM permissions WHERE name LIKE 'system_activity_logs.%'");
    if ($permResult) {
        $permCount = $permResult->fetch_assoc()['count'];
        echo "<p style='color: green;'>✅ تم إضافة $permCount صلاحية لسجل العمليات</p>\n";
    }
    
    // الخطوة 4: اختبار النظام
    echo "<h2>الخطوة 4: اختبار النظام</h2>\n";
    
    // محاولة إدراج سجل تجريبي
    $testSql = "INSERT INTO system_activity_logs (
        username, action_type, module, operation, description,
        ip_address, status, created_at
    ) VALUES (
        'system', 'SYSTEM', 'installation', 'setup_complete', 
        'تم إعداد نظام تسجيل العمليات الشامل بنجاح',
        ?, 'SUCCESS', NOW()
    )";
    
    $testStmt = $mysqli->prepare($testSql);
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $testStmt->bind_param('s', $clientIP);
    
    if ($testStmt->execute()) {
        $testId = $testStmt->insert_id;
        echo "<p style='color: green;'>✅ تم إدراج سجل تجريبي بنجاح (ID: $testId)</p>\n";
        
        // قراءة السجل التجريبي
        $readTest = $mysqli->query("SELECT * FROM system_activity_logs WHERE id = $testId");
        if ($readTest && $readTest->num_rows > 0) {
            echo "<p style='color: green;'>✅ تم قراءة السجل التجريبي بنجاح</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في إدراج السجل التجريبي: " . $mysqli->error . "</p>\n";
    }
    
    // الخطوة 5: إحصائيات النظام
    echo "<h2>الخطوة 5: إحصائيات النظام</h2>\n";
    
    $stats = [];
    
    // عدد السجلات الإجمالي
    $totalResult = $mysqli->query("SELECT COUNT(*) as total FROM system_activity_logs");
    if ($totalResult) {
        $stats['total_logs'] = $totalResult->fetch_assoc()['total'];
    }
    
    // عدد المستخدمين النشطين
    $usersResult = $mysqli->query("SELECT COUNT(*) as total FROM users WHERE status = 'active'");
    if ($usersResult) {
        $stats['active_users'] = $usersResult->fetch_assoc()['total'];
    }
    
    // عدد الصلاحيات
    $permsResult = $mysqli->query("SELECT COUNT(*) as total FROM permissions");
    if ($permsResult) {
        $stats['total_permissions'] = $permsResult->fetch_assoc()['total'];
    }
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h3>📊 إحصائيات النظام</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>إجمالي سجلات العمليات:</strong> " . ($stats['total_logs'] ?? 0) . "</li>\n";
    echo "<li><strong>المستخدمين النشطين:</strong> " . ($stats['active_users'] ?? 0) . "</li>\n";
    echo "<li><strong>إجمالي الصلاحيات:</strong> " . ($stats['total_permissions'] ?? 0) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // النتيجة النهائية
    echo "<hr>\n";
    echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h2>🎉 تم إعداد النظام بنجاح!</h2>\n";
    echo "<p><strong>نظام تسجيل العمليات الشامل جاهز للاستخدام</strong></p>\n";
    echo "<h3>الخطوات التالية:</h3>\n";
    echo "<ol>\n";
    echo "<li>انتقل إلى <a href='../dashboard/system_activity_logs.php' target='_blank'>صفحة سجل العمليات</a></li>\n";
    echo "<li>تأكد من أن الأدمن يمكنه الوصول إلى الصفحة</li>\n";
    echo "<li>اختبر العمليات المختلفة في النظام</li>\n";
    echo "<li>راقب تسجيل العمليات في الوقت الفعلي</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
    echo "<h2>❌ خطأ في الإعداد</h2>\n";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>الرجاء مراجعة الإعدادات والمحاولة مرة أخرى</p>\n";
    echo "</div>\n";
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}

echo "<hr>\n";
echo "<p><a href='../dashboard/'>العودة إلى لوحة التحكم</a> | ";
echo "<a href='../dashboard/system_activity_logs.php'>عرض سجل العمليات</a></p>\n";
?>

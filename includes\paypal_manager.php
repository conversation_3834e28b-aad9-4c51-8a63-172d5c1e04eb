<?php
/**
 * PayPalManager – handles PayPal transfer registrations
 */
require_once __DIR__ . '/database.php';

class PayPalManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Check whether a transaction code already exists (to enforce uniqueness).
     */
    public function transactionCodeExists(string $code): bool
    {
        $stmt = $this->db->prepare('SELECT 1 FROM paypal_transfers WHERE transaction_code = ? LIMIT 1');
        if (!$stmt) return false; // If prepare fails treat as not exists to allow insert attempt (will fail if duplicate)
        $stmt->bind_param('s', $code);
        $stmt->execute();
        $res = $stmt->get_result();
        $exists = $res && $res->num_rows > 0;
        $stmt->close();
        return $exists;
    }

    /**
     * Add new PayPal transfer record.
     * Returns inserted ID or false.
     *
     * Expected keys in $data:
     *  - recipient_name
     *  - recipient_phone
     *  - transaction_code (MUST be unique)
     *  - sender_name
     *  - amount (float) – base currency
     *  - status ("مستلم" | "لم يستلم")
     */
    /**
     * Get all PayPal transfers (latest first)
     */
    /**
     * Get transfers with optional filters: recipient_name (like), status, date_from, date_to
     */
    public function getTransfers(array $filters = []): array
    {
        $sql = 'SELECT * FROM paypal_transfers WHERE 1=1';
        $params = [];
        $types  = '';

        if (!empty($filters['search'])) {
            $sql .= ' AND (recipient_name LIKE ? OR sender_name LIKE ? OR transaction_code LIKE ?)';
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
            $types   .= 'sss';
        }
        if (!empty($filters['status']) && in_array($filters['status'], ['مستلم','لم يستلم'], true)) {
            $sql .= ' AND status = ?';
            $params[] = $filters['status'];
            $types   .= 's';
        }
        if (!empty($filters['date_from'])) {
            $sql .= ' AND DATE(created_at) >= ?';
            $params[] = $filters['date_from'];
            $types   .= 's';
        }
        if (!empty($filters['date_to'])) {
            $sql .= ' AND DATE(created_at) <= ?';
            $params[] = $filters['date_to'];
            $types   .= 's';
        }

        $sql .= ' ORDER BY created_at DESC';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return [];
        if ($params) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $res  = $stmt->get_result();
        $rows = $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $rows;
    }

    public function getAllTransfers(): array
    {
        $sql = 'SELECT * FROM paypal_transfers ORDER BY created_at DESC';
        $res = $this->db->query($sql);
        return $res ? $res->fetch_all(MYSQLI_ASSOC) : [];
    }

    public function getTransferById(int $id): ?array
    {
        $stmt = $this->db->prepare('SELECT * FROM paypal_transfers WHERE id = ? LIMIT 1');
        if (!$stmt) return null;
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc() ?: null;
        $stmt->close();
        return $row;
    }

    public function updateTransfer(int $id, array $data): bool
    {
        $sql = 'UPDATE paypal_transfers SET recipient_name=?, recipient_phone=?, transaction_code=?, sender_name=?, amount=?, status=? WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $stmt->bind_param('ssssdsi',
            $data['recipient_name'],
            $data['recipient_phone'],
            $data['transaction_code'],
            $data['sender_name'],
            $data['amount'],
            $data['status'],
            $id
        );
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function deleteTransfer(int $id): bool
    {
        $stmt = $this->db->prepare('DELETE FROM paypal_transfers WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $id);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    public function addTransfer(array $data): int|false
    {
        $sql = 'INSERT INTO paypal_transfers (recipient_name, recipient_phone, transaction_code, sender_name, amount, status, created_at) VALUES (?,?,?,?,?,?, NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $stmt->bind_param(
            'ssssds',
            $data['recipient_name'],
            $data['recipient_phone'],
            $data['transaction_code'],
            $data['sender_name'],
            $data['amount'],
            $data['status']
        );

        if ($stmt->execute()) {
            $id = $stmt->insert_id;
            $stmt->close();
            return $id;
        }
        $stmt->close();
        return false;
    }

    /**
     * Get total amount of incoming transfers
     */
    public function getTotalIncomingAmount(): float
    {
        $sql = 'SELECT SUM(amount) as total FROM paypal_transfers';
        $res = $this->db->query($sql);
        if (!$res) return 0;
        
        $row = $res->fetch_assoc();
        return (float)($row['total'] ?? 0);
    }

    /**
     * Get total amount by status
     */
    public function getTotalAmountByStatus(string $status): float
    {
        $stmt = $this->db->prepare('SELECT SUM(amount) as total FROM paypal_transfers WHERE status = ?');
        if (!$stmt) return 0;
        
        $stmt->bind_param('s', $status);
        $stmt->execute();
        $res = $stmt->get_result();
        $row = $res->fetch_assoc();
        $stmt->close();
        return (float)($row['total'] ?? 0);
    }

    /**
     * Get transfer statistics
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_amount' => $this->getTotalIncomingAmount(),
            'received_amount' => $this->getTotalAmountByStatus('مستلم'),
            'pending_amount' => $this->getTotalAmountByStatus('لم يستلم'),
            'total_count' => 0,
            'received_count' => 0,
            'pending_count' => 0
        ];

        // Get counts
        $sql = 'SELECT status, COUNT(*) as count FROM paypal_transfers GROUP BY status';
        $res = $this->db->query($sql);
        if ($res) {
            while ($row = $res->fetch_assoc()) {
                $stats['total_count'] += $row['count'];
                if ($row['status'] === 'مستلم') {
                    $stats['received_count'] = $row['count'];
                } elseif ($row['status'] === 'لم يستلم') {
                    $stats['pending_count'] = $row['count'];
                }
            }
        }

        return $stats;
    }
}

/* ----------------------------------------------------------
SQL to create the required table (run once on your DB):

CREATE TABLE `paypal_transfers` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `recipient_name` VARCHAR(255) NOT NULL,
  `recipient_phone` VARCHAR(50) NOT NULL,
  `transaction_code` VARCHAR(255) NOT NULL UNIQUE,
  `sender_name` VARCHAR(255) NOT NULL,
  `amount` DECIMAL(10,2) NOT NULL,
  `status` ENUM('مستلم','لم يستلم') NOT NULL DEFAULT 'لم يستلم',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
----------------------------------------------------------- */

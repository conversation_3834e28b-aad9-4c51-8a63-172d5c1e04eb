/**
 * Unified Sidebar CSS - الحل الجذري والموحد
 * يلغي جميع ملفات CSS الأخرى للشريط الجانبي
 */

/* ===== إعادة تعيين وإلغاء التضارب ===== */
/* إلغاء جميع الأنماط المتضاربة */
.sidebar-container,
.sidebar-wrapper,
.old-sidebar,
.mobile-sidebar-toggle,
.old-sidebar-toggle {
    display: none !important;
    visibility: hidden !important;
}

/* ===== المتغيرات الأساسية ===== */
:root {
    --sidebar-width: 250px;
    --sidebar-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --sidebar-bg: linear-gradient(180deg, #2c3e50 0%, #1a252f 100%);
    --sidebar-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    --sidebar-z-index: 1000;
    --overlay-z-index: 999;
    --toggle-z-index: 1001;
}

/* ===== الشريط الجانبي الأساسي ===== */
#sidebar {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    width: var(--sidebar-width) !important;
    height: 100vh !important;
    background: var(--sidebar-bg) !important;
    color: #fff !important;
    z-index: var(--sidebar-z-index) !important;
    transition: var(--sidebar-transition) !important;
    box-shadow: var(--sidebar-shadow) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    direction: rtl !important;
    display: flex !important;
    flex-direction: column !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* فرض إظهار الشريط الجانبي على أجهزة سطح المكتب */
@media (min-width: 992px) {
    #sidebar {
        right: 0 !important;
        transform: translateX(0) !important;
        display: flex !important;
        visibility: visible !important;
    }
}

/* ===== حالات الشريط الجانبي ===== */
/* الحالة المرئية */
#sidebar.show,
#sidebar.active {
    right: 0;
    transform: translateX(0);
}

/* الحالة المخفية */
#sidebar.collapsed {
    right: calc(-1 * var(--sidebar-width));
    transform: translateX(100%);
}

/* ===== التصميم المتجاوب ===== */
/* أجهزة سطح المكتب */
@media (min-width: 992px) {
    #sidebar {
        right: 0;
        box-shadow: var(--sidebar-shadow);
    }
    
    #sidebar.collapsed {
        right: calc(-1 * var(--sidebar-width));
    }
    
    .main-content {
        margin-right: var(--sidebar-width);
        transition: margin-right 0.3s ease;
    }
    
    .sidebar-collapsed .main-content {
        margin-right: 0;
    }
}

/* الأجهزة المحمولة والأجهزة اللوحية */
@media (max-width: 991.98px) {
    #sidebar {
        right: calc(-1 * var(--sidebar-width));
        box-shadow: none;
    }
    
    #sidebar.show,
    #sidebar.active {
        right: 0;
        box-shadow: -5px 0 25px rgba(0, 0, 0, 0.3);
    }
    
    .main-content {
        margin-right: 0 !important;
        width: 100% !important;
    }
}

/* الشاشات الصغيرة جداً */
@media (max-width: 575.98px) {
    #sidebar {
        width: 100%;
        right: -100%;
    }
    
    #sidebar.show,
    #sidebar.active {
        right: 0;
    }
}

/* ===== زر التبديل ===== */
.sidebar-toggle-btn {
    position: fixed !important;
    top: 15px !important;
    right: 15px !important;
    z-index: var(--toggle-z-index) !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    border: none !important;
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    user-select: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

.sidebar-toggle-btn:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4) !important;
    background: linear-gradient(135deg, #0056b3, #004085) !important;
}

.sidebar-toggle-btn:active {
    transform: scale(0.95) !important;
}

.sidebar-toggle-btn i {
    font-size: 18px !important;
    transition: transform 0.3s ease !important;
}

/* إظهار زر التبديل على الأجهزة المحمولة */
@media (max-width: 991.98px) {
    .sidebar-toggle-btn {
        display: flex !important;
    }
}

/* ===== طبقة التغطية ===== */
.sidebar-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: var(--overlay-z-index) !important;
    display: none !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    cursor: pointer !important;
}

.sidebar-overlay.show {
    display: block !important;
    opacity: 1 !important;
}

/* ===== إزالة جميع التعريفات المتضاربة ===== */
/* سيتم إعادة تعريف كل شيء بشكل موحد أدناه */

/* ===== إصلاح التضارب مع ملفات CSS الأخرى ===== */
/* إخفاء أي أزرار تبديل أخرى */
.mobile-sidebar-toggle,
.old-sidebar-toggle {
    display: none !important;
}

/* إخفاء أي طبقات تغطية أخرى */
.mobile-overlay,
.old-sidebar-overlay {
    display: none !important;
}

/* ===== تحسينات الأداء ===== */
#sidebar * {
    box-sizing: border-box;
}

/* تحسين التمرير */
#sidebar::-webkit-scrollbar {
    width: 6px;
}

#sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

#sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

#sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* ===== منع التمرير في الخلفية على الأجهزة المحمولة ===== */
body.sidebar-open {
    overflow: hidden;
}

@media (min-width: 992px) {
    body.sidebar-open {
        overflow: auto;
    }
}

/* ===== نظام القوائم الموحد والجذري ===== */

/* إزالة جميع التعريفات المتضاربة */
#sidebar .sidebar-section,
#sidebar .sidebar-dropdown,
#sidebar .transfers-menu,
#sidebar .paypal-menu {
    margin: 0 !important;
    padding: 0 !important;
    background: none !important;
    border: none !important;
}

/* ===== الروابط الأساسية ===== */
#sidebar .sidebar-link {
    display: flex !important;
    align-items: center !important;
    padding: 0.75rem 1rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    border-radius: 0.375rem !important;
    margin: 0.25rem 0.5rem !important;
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
}

#sidebar .sidebar-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
    text-decoration: none !important;
    transform: translateX(-2px) !important;
}

#sidebar .sidebar-link.active {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    font-weight: 500 !important;
}

#sidebar .sidebar-link i {
    margin-left: 0.75rem !important;
    width: 20px !important;
    text-align: center !important;
    flex-shrink: 0 !important;
}

/* ===== القوائم القابلة للطي (Collapsible) ===== */
#sidebar .menu-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0.75rem 1rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    border-radius: 0.375rem !important;
    margin: 0.25rem 0.5rem !important;
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
    width: calc(100% - 1rem) !important;
}

#sidebar .menu-header:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
    text-decoration: none !important;
    transform: translateX(-2px) !important;
}

#sidebar .menu-header .menu-icon {
    margin-left: 0.75rem !important;
    width: 20px !important;
    text-align: center !important;
    flex-shrink: 0 !important;
}

#sidebar .menu-header .menu-text {
    flex-grow: 1 !important;
    text-align: right !important;
}

#sidebar .menu-header .menu-arrow {
    transition: transform 0.3s ease !important;
    font-size: 0.8rem !important;
    margin-right: 0.5rem !important;
}

#sidebar .menu-header.expanded .menu-arrow {
    transform: rotate(180deg) !important;
}

/* ===== القوائم الفرعية ===== */
#sidebar .menu-content {
    max-height: 0 !important;
    overflow: hidden !important;
    transition: max-height 0.3s ease, opacity 0.3s ease !important;
    opacity: 0 !important;
    background: rgba(0, 0, 0, 0.1) !important;
    border-radius: 0.375rem !important;
    margin: 0.25rem 0.5rem !important;
}

#sidebar .menu-content.expanded {
    max-height: 500px !important;
    opacity: 1 !important;
}

#sidebar .menu-item {
    display: flex !important;
    align-items: center !important;
    padding: 0.5rem 1rem 0.5rem 2rem !important;
    color: rgba(255, 255, 255, 0.7) !important;
    text-decoration: none !important;
    font-size: 0.9rem !important;
    transition: all 0.2s ease !important;
    border-radius: 0.25rem !important;
    margin: 0.15rem 0.5rem !important;
    background: transparent !important;
    border: none !important;
    cursor: pointer !important;
}

#sidebar .menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
    text-decoration: none !important;
    transform: translateX(-3px) !important;
}

#sidebar .menu-item.active {
    background-color: rgba(255, 255, 255, 0.15) !important;
    color: #fff !important;
    font-weight: 500 !important;
}

#sidebar .menu-item i {
    margin-left: 0.75rem !important;
    width: 20px !important;
    text-align: center !important;
    flex-shrink: 0 !important;
}

/* ===== أنماط خاصة للقوائم المختلفة ===== */

/* قائمة PayPal */
#sidebar .paypal-menu .menu-header .menu-icon {
    color: #0070ba !important;
    font-size: 1.1rem !important;
}

#sidebar .paypal-menu .menu-item::before {
    content: '' !important;
    width: 4px !important;
    height: 4px !important;
    background-color: #0070ba !important;
    border-radius: 50% !important;
    margin-right: 0.5rem !important;
    flex-shrink: 0 !important;
}

/* قائمة الحوالات */
#sidebar .transfers-menu .menu-item::before {
    content: '' !important;
    width: 4px !important;
    height: 4px !important;
    background-color: rgba(255, 255, 255, 0.5) !important;
    border-radius: 50% !important;
    margin-right: 0.5rem !important;
    flex-shrink: 0 !important;
}

/* ===== إزالة جميع الأنماط المتضاربة ===== */
#sidebar .sidebar-section-header,
#sidebar .sidebar-sub-links,
#sidebar .sidebar-sub-link,
#sidebar .sidebar-dropdown,
#sidebar .dropdown-toggle,
#sidebar .sidebar-submenu,
#sidebar .sidebar-sublink,
#sidebar .transfers-main-link,
#sidebar .transfers-submenu,
#sidebar .transfers-sublink,
#sidebar .transfers-icon-wrapper,
#sidebar .transfers-text,
#sidebar .transfers-chevron,
#sidebar .transfers-subicon-wrapper,
#sidebar .transfers-indicator,
#sidebar .paypal-main-link,
#sidebar .paypal-submenu,
#sidebar .paypal-sublink,
#sidebar .paypal-icon-wrapper,
#sidebar .paypal-text,
#sidebar .paypal-chevron,
#sidebar .paypal-subicon-wrapper,
#sidebar .paypal-indicator {
    /* إزالة جميع الأنماط القديمة */
    all: unset !important;
    /* إعادة تطبيق الأنماط الأساسية فقط */
    box-sizing: border-box !important;
}

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/reports_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';
require_once __DIR__ . '/../includes/branch_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth=new Auth();
$auth->requireLogin();
$auth->requirePermission('reports.view');

$curMgr=new CurrencyManager(new Database());
$branchMgr=new BranchManager(new Database());
$repMgr=new ReportsManager(new Database());

$filters=[
  'from_date'=>$_GET['from_date'] ?? '',
  'to_date'=>$_GET['to_date'] ?? '',
  'branch_id'=>isset($_GET['branch_id'])? (int)$_GET['branch_id']:null,
  'transfer_type'=>$_GET['transfer_type'] ?? '',
  'status'=>$_GET['status'] ?? '',
  'currency_id'=>isset($_GET['currency_id'])? (int)$_GET['currency_id']:null,
];
$data=$repMgr->getTransferSummaryReportData($filters);
$currencies=$curMgr->getAllCurrencies(true);
$branches=$branchMgr->getAllBranches();

// تسجيل عملية عرض تقرير ملخص الحوالات
ActivityHelper::logView('reports', 'عرض تقرير ملخص الحوالات', $filters);

$statuses=['معلقة','مرسلة','مكتملة','ملغاة','مستلمة'];
$pageTitle='تقرير ملخص الحوالات';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>
<div class="container p-4">
 <h3><i class="fas fa-money-bill-wave"></i> تقرير ملخص الحوالات</h3>
 <form method="get" class="row g-2 mb-3">
  <div class="col-md-2"><input type="date" name="from_date" value="<?php echo htmlspecialchars($filters['from_date']); ?>" class="form-control" placeholder="من"></div>
  <div class="col-md-2"><input type="date" name="to_date" value="<?php echo htmlspecialchars($filters['to_date']); ?>" class="form-control" placeholder="إلى"></div>
  <div class="col-md-2"><select name="branch_id" class="form-select"><option value="">كل الفروع</option><?php foreach($branches as $b){?><option value="<?php echo $b['id']; ?>" <?php if($filters['branch_id']==$b['id'])echo'selected';?>><?php echo htmlspecialchars($b['name']); ?></option><?php }?></select></div>
  <div class="col-md-2"><select name="transfer_type" class="form-select"><option value="">كل الأنواع</option><option value="صادرة" <?php if($filters['transfer_type']=='صادرة')echo'selected';?>>صادرة</option><option value="واردة" <?php if($filters['transfer_type']=='واردة')echo'selected';?>>واردة</option></select></div>
  <div class="col-md-2"><select name="status" class="form-select"><option value="">كل الحالات</option><?php foreach($statuses as $st){?><option value="<?php echo $st; ?>" <?php if($filters['status']==$st)echo'selected';?>><?php echo $st; ?></option><?php }?></select></div>
  <div class="col-md-2 d-grid"><button class="btn btn-primary"><i class="fas fa-search"></i> عرض</button></div>
 </form>
 <div class="table-responsive">
  <table class="table table-bordered table-sm align-middle">
   <thead class="table-light"><tr><th>الحالة</th><th>نوع</th><th>الفرع</th><th>عدد</th><th>إجمالي المرسل</th><th>إجمالي المستلم</th><th>الرسوم</th><th>الربح</th></tr></thead>
   <tbody>
   <?php $tot=['tx'=>0,'fee'=>0,'profit'=>0]; foreach($data as $row): ?>
     <tr>
      <td><?php echo htmlspecialchars($row['status']); ?></td>
      <td><?php echo htmlspecialchars($row['transfer_type']); ?></td>
      <td><?php echo htmlspecialchars($row['branch_name']); ?></td>
      <td><?php echo $row['total_tx']; $tot['tx']+=$row['total_tx']; ?></td>
      <td><?php echo number_format($row['total_sent'],2); ?></td>
      <td><?php echo number_format($row['total_received'],2); ?></td>
      <td><?php echo number_format($row['total_fees'],2); $tot['fee']+=$row['total_fees']; ?></td>
      <td><?php echo number_format($row['total_profit'],2); $tot['profit']+=$row['total_profit']; ?></td>
     </tr>
   <?php endforeach; if(!$data): ?><tr><td colspan="8" class="text-center">لا توجد بيانات</td></tr><?php endif; ?>
   </tbody>
   <tfoot class="table-light"><tr><th colspan="3">الإجمالي</th><th><?php echo $tot['tx']; ?></th><th colspan="2"></th><th><?php echo number_format($tot['fee'],2); ?></th><th><?php echo number_format($tot['profit'],2); ?></th></tr></tfoot>
  </table>
 </div>
</div>
<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
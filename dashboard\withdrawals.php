<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/withdrawal_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('withdrawals.view');

$withdrawalMgr = new WithdrawalManager(new Database());

// Build filters from GET
$filters = [
    'search' => $_GET['search'] ?? '',
    'status' => $_GET['status'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? ''
];

// Handle form submission for adding/editing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $csrf = $_POST['csrf_token'] ?? '';
        if (verify_csrf_token($csrf)) {
            if ($_POST['action'] === 'add') {
                $amount = (float)($_POST['amount'] ?? 0);
                $beneficiary = sanitize_input($_POST['beneficiary'] ?? '');
                $commission = (float)($_POST['commission'] ?? 0);
                $status = sanitize_input($_POST['status'] ?? 'معلق');
                $notes = sanitize_input($_POST['notes'] ?? '');
                
                // Calculate net amount
                $netAmount = $withdrawalMgr->calculateNetAmount($amount, $commission);
                
                $data = [
                    'amount' => $amount,
                    'beneficiary' => $beneficiary,
                    'commission' => $commission,
                    'net_amount' => $netAmount,
                    'status' => $status,
                    'created_by' => $auth->getCurrentUser()['id'],
                    'notes' => $notes
                ];
                
                if ($withdrawalMgr->addWithdrawal($data)) {
                    set_flash('success', 'تم إضافة سحب المبلغ بنجاح');
                } else {
                    set_flash('danger', 'تعذر إضافة سحب المبلغ');
                }
            } elseif ($_POST['action'] === 'update' && isset($_POST['id'])) {
                $id = (int)$_POST['id'];
                $amount = (float)($_POST['amount'] ?? 0);
                $beneficiary = sanitize_input($_POST['beneficiary'] ?? '');
                $commission = (float)($_POST['commission'] ?? 0);
                $status = sanitize_input($_POST['status'] ?? 'معلق');
                $notes = sanitize_input($_POST['notes'] ?? '');
                
                // Calculate net amount
                $netAmount = $withdrawalMgr->calculateNetAmount($amount, $commission);
                
                $data = [
                    'amount' => $amount,
                    'beneficiary' => $beneficiary,
                    'commission' => $commission,
                    'net_amount' => $netAmount,
                    'status' => $status,
                    'notes' => $notes
                ];
                
                if ($withdrawalMgr->updateWithdrawal($id, $data)) {
                    set_flash('success', 'تم تحديث سحب المبلغ بنجاح');
                } else {
                    set_flash('danger', 'تعذر تحديث سحب المبلغ');
                }
            } elseif ($_POST['action'] === 'delete' && isset($_POST['id'])) {
                $id = (int)$_POST['id'];
                if ($withdrawalMgr->deleteWithdrawal($id)) {
                    set_flash('success', 'تم حذف سحب المبلغ بنجاح');
                } else {
                    set_flash('danger', 'تعذر حذف سحب المبلغ');
                }
            } elseif ($_POST['action'] === 'mark_received' && isset($_POST['id'])) {
                $id = (int)$_POST['id'];
                if ($withdrawalMgr->markAsReceived($id)) {
                    set_flash('success', 'تم تأكيد استلام المبلغ بنجاح');
                } else {
                    set_flash('danger', 'تعذر تأكيد استلام المبلغ');
                }
            }
        }
        redirect('withdrawals.php');
    }
}

$withdrawals = $withdrawalMgr->getWithdrawals($filters);
$stats = $withdrawalMgr->getStatistics();

$pageTitle = 'سحب المبالغ';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mb-0"><i class="fas fa-money-bill-wave"></i> سحب المبالغ</h3>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWithdrawalModal">
            <i class="fas fa-plus"></i> إضافة سحب مبلغ
        </button>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المبالغ</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['total_amount'], 2); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">صافي الوصول</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['total_net_amount'], 2); ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">معلق</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['pending_amount'], 2); ?></h4>
                            <small><?php echo $stats['pending_count']; ?> عملية</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مكتمل</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['completed_amount'], 2); ?></h4>
                            <small><?php echo $stats['completed_count']; ?> عملية</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">مستلمة</h6>
                            <h4 class="mb-0"><?php echo number_format($stats['received_amount'] ?? 0, 2); ?></h4>
                            <small><?php echo $stats['received_count'] ?? 0; ?> عملية</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-handshake fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Form -->
    <form method="get" class="row g-2 mb-3">
        <div class="col-md-3">
            <input type="text" name="search" class="form-control" placeholder="بحث (مستفيد/ملاحظات)" value="<?php echo htmlspecialchars($filters['search']); ?>">
        </div>
        <div class="col-md-2">
            <select name="status" class="form-select">
                <option value="">-- الحالة --</option>
                <option value="معلق" <?php echo $filters['status']==='معلق'?'selected':''; ?>>معلق</option>
                <option value="مكتمل" <?php echo $filters['status']==='مكتمل'?'selected':''; ?>>مكتمل</option>
                <option value="مستلمة" <?php echo $filters['status']==='مستلمة'?'selected':''; ?>>مستلمة</option>
            </select>
        </div>
        <div class="col-md-2">
            <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($filters['date_from']); ?>">
        </div>
        <div class="col-md-2">
            <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($filters['date_to']); ?>">
        </div>
        <div class="col-md-3 d-flex">
            <button class="btn btn-secondary me-2" type="submit"><i class="fas fa-filter"></i> فلترة</button>
            <a href="withdrawals.php" class="btn btn-outline-secondary me-2">إعادة ضبط</a>
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-file-export"></i> تصدير
                </button>
                <ul class="dropdown-menu">
                    <li>
                        <a class="dropdown-item" href="export_withdrawals.php?format=excel<?php echo !empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : ''; ?>">
                            <i class="fas fa-file-excel me-1"></i> تصدير Excel
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="export_withdrawals.php?format=pdf<?php echo !empty($_SERVER['QUERY_STRING']) ? '&' . $_SERVER['QUERY_STRING'] : ''; ?>">
                            <i class="fas fa-file-pdf me-1"></i> تصدير PDF
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </form>

    <?php if (empty($withdrawals)): ?>
        <div class="alert alert-info">لا توجد عمليات سحب مبالغ مسجلة.</div>
    <?php else: ?>
    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead class="table-light">
                <tr>
                    <th>#</th>
                    <th>المبلغ</th>
                    <th>المستفيد</th>
                    <th>العمولة (%)</th>
                    <th>صافي الوصول</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                    <th>إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($withdrawals as $row): ?>
                    <tr>
                        <td><?php echo $row['id']; ?></td>
                        <td><?php echo number_format($row['amount'], 2); ?></td>
                        <td><?php echo htmlspecialchars($row['beneficiary']); ?></td>
                        <td><?php echo number_format($row['commission'], 2); ?>%</td>
                        <td><?php echo number_format($row['net_amount'], 2); ?></td>
                        <td>
                            <?php if ($row['status'] === 'مكتمل'): ?>
                                <span class="badge bg-success">مكتمل</span>
                            <?php elseif ($row['status'] === 'مستلمة'): ?>
                                <span class="badge bg-primary">مستلمة</span>
                            <?php else: ?>
                                <span class="badge bg-warning text-dark">معلق</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo date('Y-m-d H:i', strtotime($row['created_at'])); ?></td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewWithdrawal(<?php echo $row['id']; ?>)" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="editWithdrawal(<?php echo $row['id']; ?>)" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <?php if ($row['status'] === 'مكتمل'): ?>
                                <button class="btn btn-sm btn-success" onclick="markAsReceived(<?php echo $row['id']; ?>)" title="تأكيد الاستلام">
                                    <i class="fas fa-check"></i>
                                </button>
                            <?php endif; ?>
                            <button class="btn btn-sm btn-danger" onclick="deleteWithdrawal(<?php echo $row['id']; ?>)" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <?php 
        $totalAmount = 0;
        $totalNetAmount = 0;
        foreach ($withdrawals as $w) {
            $totalAmount += (float)$w['amount'];
            $totalNetAmount += (float)$w['net_amount'];
        }
    ?>
    <div class="mt-3">
        <div class="row">
            <div class="col-md-6">
                <strong>إجمالي المبلغ<?php echo $filters['search']||$filters['status']||$filters['date_from']||$filters['date_to']?' (حسب الفلتر)':''; ?>: 
                    <?php echo number_format($totalAmount, 2); ?>
                </strong>
            </div>
            <div class="col-md-6">
                <strong>إجمالي صافي الوصول: 
                    <?php echo number_format($totalNetAmount, 2); ?>
                </strong>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Add Withdrawal Modal -->
<div class="modal fade" id="addWithdrawalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة سحب مبلغ جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="mb-3">
                        <label class="form-label">المبلغ</label>
                        <input type="number" name="amount" class="form-control" step="0.01" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المستفيد (المرسل إليه)</label>
                        <input type="text" name="beneficiary" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">العمولة (%)</label>
                        <input type="number" name="commission" class="form-control" step="0.01" value="0" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select" required>
                            <option value="معلق">معلق</option>
                            <option value="مكتمل">مكتمل</option>
                            <option value="مستلمة">مستلمة</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Withdrawal Modal -->
<div class="modal fade" id="editWithdrawalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل سحب المبلغ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">
                    
                    <div class="mb-3">
                        <label class="form-label">المبلغ</label>
                        <input type="number" name="amount" id="edit_amount" class="form-control" step="0.01" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المستفيد (المرسل إليه)</label>
                        <input type="text" name="beneficiary" id="edit_beneficiary" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">العمولة (%)</label>
                        <input type="number" name="commission" id="edit_commission" class="form-control" step="0.01" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" id="edit_status" class="form-select" required>
                            <option value="معلق">معلق</option>
                            <option value="مكتمل">مكتمل</option>
                            <option value="مستلمة">مستلمة</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" id="edit_notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Withdrawal Modal -->
<div class="modal fade" id="viewWithdrawalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل سحب المبلغ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewWithdrawalContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
function editWithdrawal(id) {
    // Load withdrawal data via AJAX
    fetch('ajax/get_withdrawal.php?id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('edit_id').value = data.withdrawal.id;
                document.getElementById('edit_amount').value = data.withdrawal.amount;
                document.getElementById('edit_beneficiary').value = data.withdrawal.beneficiary;
                document.getElementById('edit_commission').value = data.withdrawal.commission;
                document.getElementById('edit_status').value = data.withdrawal.status;
                document.getElementById('edit_notes').value = data.withdrawal.notes || '';
                
                new bootstrap.Modal(document.getElementById('editWithdrawalModal')).show();
            } else {
                alert('خطأ في تحميل البيانات');
            }
        })
        .catch(error => {
            alert('خطأ في الاتصال بالخادم');
        });
}

function viewWithdrawal(id) {
    // Load withdrawal details via AJAX
    fetch('ajax/get_withdrawal.php?id=' + id)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const w = data.withdrawal;
                document.getElementById('viewWithdrawalContent').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <strong>المبلغ:</strong> ${parseFloat(w.amount).toLocaleString('ar-SA', {minimumFractionDigits: 2})}
                        </div>
                        <div class="col-md-6">
                            <strong>المستفيد:</strong> ${w.beneficiary}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>العمولة:</strong> ${parseFloat(w.commission).toFixed(2)}%
                        </div>
                        <div class="col-md-6">
                            <strong>صافي الوصول:</strong> ${parseFloat(w.net_amount).toLocaleString('ar-SA', {minimumFractionDigits: 2})}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <strong>الحالة:</strong> 
                            <span class="badge ${w.status === 'مكتمل' ? 'bg-success' : 'bg-warning text-dark'}">${w.status}</span>
                        </div>
                        <div class="col-md-6">
                            <strong>التاريخ:</strong> ${new Date(w.created_at).toLocaleString('ar-SA')}
                        </div>
                    </div>
                    ${w.notes ? `<div class="row mt-2"><div class="col-12"><strong>ملاحظات:</strong><br>${w.notes}</div></div>` : ''}
                `;
                
                new bootstrap.Modal(document.getElementById('viewWithdrawalModal')).show();
            } else {
                alert('خطأ في تحميل البيانات');
            }
        })
        .catch(error => {
            alert('خطأ في الاتصال بالخادم');
        });
}

function deleteWithdrawal(id) {
    if (confirm('هل أنت متأكد من حذف سحب المبلغ؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function markAsReceived(id) {
    if (confirm('هل تريد تأكيد استلام هذا المبلغ؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
            <input type="hidden" name="action" value="mark_received">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
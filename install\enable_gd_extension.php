<?php
/**
 * GD Extension Checker and Enabler for XAMPP
 * This script helps you check if GD extension is available and provides instructions to enable it
 */

echo "<h2>GD Extension Status Check</h2>";

// Check if GD is loaded
$gdLoaded = extension_loaded('gd');
$gdFunctions = function_exists('imagecreatefromjpeg');

echo "<h3>Current Status:</h3>";
echo "<ul>";
echo "<li>GD Extension Loaded: " . ($gdLoaded ? "<span style='color: green;'>✓ YES</span>" : "<span style='color: red;'>✗ NO</span>") . "</li>";
echo "<li>GD Functions Available: " . ($gdFunctions ? "<span style='color: green;'>✓ YES</span>" : "<span style='color: red;'>✗ NO</span>") . "</li>";
echo "</ul>";

if ($gdLoaded && $gdFunctions) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>✓ GD Extension is working properly!</strong><br>";
    echo "Image resizing functionality will work correctly.";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>⚠ GD Extension is not available!</strong><br>";
    echo "Image uploads will work, but resizing will be disabled.";
    echo "</div>";
    
    echo "<h3>How to Enable GD Extension in XAMPP:</h3>";
    echo "<ol>";
    echo "<li>Open your XAMPP Control Panel</li>";
    echo "<li>Click on 'Config' button next to Apache</li>";
    echo "<li>Select 'PHP (php.ini)'</li>";
    echo "<li>Find the line: <code>;extension=gd</code></li>";
    echo "<li>Remove the semicolon to make it: <code>extension=gd</code></li>";
    echo "<li>Save the file</li>";
    echo "<li>Restart Apache in XAMPP Control Panel</li>";
    echo "<li>Refresh this page to check if GD is now available</li>";
    echo "</ol>";
    
    echo "<h3>Alternative Method:</h3>";
    echo "<ol>";
    echo "<li>Navigate to: <code>C:\\xampp\\php\\php.ini</code></li>";
    echo "<li>Open the file in a text editor</li>";
    echo "<li>Find the line: <code>;extension=gd</code></li>";
    echo "<li>Remove the semicolon to make it: <code>extension=gd</code></li>";
    echo "<li>Save the file</li>";
    echo "<li>Restart Apache in XAMPP Control Panel</li>";
    echo "</ol>";
}

echo "<h3>PHP Info:</h3>";
echo "<p>Loaded Extensions:</p>";
echo "<ul>";
$extensions = get_loaded_extensions();
foreach ($extensions as $ext) {
    if (stripos($ext, 'gd') !== false) {
        echo "<li><strong>{$ext}</strong></li>";
    } else {
        echo "<li>{$ext}</li>";
    }
}
echo "</ul>";

echo "<h3>Test GD Functions:</h3>";
if ($gdLoaded && $gdFunctions) {
    echo "<p>Testing GD functions...</p>";
    
    // Create a test image
    $testImage = imagecreatetruecolor(100, 100);
    if ($testImage) {
        $red = imagecolorallocate($testImage, 255, 0, 0);
        imagefill($testImage, 0, 0, $red);
        
        $testFile = __DIR__ . '/../uploads/profiles/test_gd.png';
        $uploadDir = __DIR__ . '/../uploads/profiles/';
        
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        if (imagepng($testImage, $testFile)) {
            echo "<span style='color: green;'>✓ GD test successful! Test image created.</span><br>";
            echo "<img src='../uploads/profiles/test_gd.png' style='border: 1px solid #ccc; margin: 10px 0;'>";
            
            // Clean up test file
            unlink($testFile);
        } else {
            echo "<span style='color: red;'>✗ GD test failed!</span>";
        }
        
        imagedestroy($testImage);
    }
} else {
    echo "<p>GD functions not available for testing.</p>";
}

echo "<hr>";
echo "<p><a href='../dashboard/'>← Back to Dashboard</a></p>";
?> 
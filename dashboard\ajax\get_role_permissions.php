<?php
/**
 * جلب صلاحيات دور محدد عبر AJAX
 * Get Role Permissions via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/role_manager.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.permissions')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لإدارة صلاحيات الأدوار'
    ]);
    exit;
}

try {
    $roleId = (int)($_GET['role_id'] ?? 0);
    
    if (!$roleId) {
        echo json_encode([
            'success' => false,
            'message' => 'معرف الدور مطلوب'
        ]);
        exit;
    }
    
    $db = new Database();
    $roleMgr = new RoleManager($db);
    
    // التحقق من وجود الدور
    $role = $roleMgr->getRoleById($roleId);
    if (!$role) {
        echo json_encode([
            'success' => false,
            'message' => 'الدور غير موجود'
        ]);
        exit;
    }
    
    $permissions = $roleMgr->getRolePermissions($roleId);
    
    echo json_encode([
        'success' => true,
        'permissions' => $permissions
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

<?php
/**
 * Simplified Exchange Manager - No locks, minimal transactions
 */

require_once __DIR__ . '/database.php';
require_once __DIR__ . '/exchange_manager.php';

class SimpleExchangeManager extends ExchangeManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        parent::__construct($database);
        $this->db = $database::getConnection();
    }

    /**
     * Process exchange without complex locking mechanisms
     */
    public function processExchangeSimple(array $data): array
    {
        // Basic validation
        foreach (['customer_id','created_by','branch_id','funding_source','disburse_source'] as $k) {
            if (!isset($data[$k]) || $data[$k] === null || $data[$k] === '') {
                return ['success'=>false,'error'=>'Missing param '.$k];
            }
        }

        // Validate funding and disbursement sources
        if (!isset($data['funding_source']['type']) || !isset($data['funding_source']['id'])) {
            return ['success'=>false,'error'=>'Invalid funding source'];
        }
        if (!isset($data['disburse_source']['type']) || !isset($data['disburse_source']['id'])) {
            return ['success'=>false,'error'=>'Invalid disburse source'];
        }

        // Use provided amounts directly (no need for stored exchange rates)
        $amountFrom = floatval($data['amount_from'] ?? 0);
        $amountTo = floatval($data['amount_to'] ?? 0);

        // Validate amounts
        if ($amountFrom <= 0 || $amountTo <= 0) {
            return ['success'=>false,'error'=>'Both amounts must be greater than zero'];
        }

        // Calculate exchange rate from provided amounts
        $calculatedRate = $amountTo / $amountFrom;

        // Simple commission calculation
        $commission = round($amountFrom * 0.002, 4);
        $profit = round($amountFrom * 0.001, 4);

        // Prepare transaction data
        $txData = [
            'customer_id' => $data['customer_id'],
            'amount_from' => $amountFrom,
            'amount_to' => $amountTo,
            'rate_used' => $calculatedRate,
            'commission' => $commission,
            'profit' => $profit,
            'status' => 'completed',
            'notes' => $data['notes'] ?? '',
            'branch_id' => $data['branch_id'],
            'created_by' => $data['created_by']
        ];

        // Use simple transaction without locks
        try {
            // Set minimal timeout
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 1");
            $this->db->query("SET SESSION autocommit = 0");

            // Insert exchange record using correct database schema
            $exchangeId = $this->addSimpleExchangeTransaction($txData, $data);
            if (!$exchangeId) {
                throw new Exception('Failed to create exchange record');
            }

            // Update cash/bank balances directly without locks
            $fundSrc = $data['funding_source'] ?? null;
            $dispSrc = $data['disburse_source'] ?? null;

            if (!$fundSrc || !$dispSrc) {
                throw new Exception('Funding/disbursement sources required');
            }

            // Update funding source (we receive money)
            if ($fundSrc['type'] === 'cash') {
                $this->updateCashBoxBalance($fundSrc['id'], $amountFrom, 'deposit');
                $fundCurrencyId = $this->getCashBoxCurrencyId($fundSrc['id']);
                $this->insertCashMovement($fundSrc['id'], 'deposit', $amountFrom, $fundCurrencyId, $exchangeId, $data['created_by']);
            } else {
                $this->updateBankAccountBalance($fundSrc['id'], $amountFrom, 'deposit');
                $fundCurrencyId = $this->getBankAccountCurrencyId($fundSrc['id']);
                $this->insertBankMovement($fundSrc['id'], 'deposit', $amountFrom, $fundCurrencyId, $exchangeId, $data['created_by']);
            }

            // Update disbursement source (we pay money)
            if ($dispSrc['type'] === 'cash') {
                $this->updateCashBoxBalance($dispSrc['id'], $amountTo, 'withdrawal');
                $dispCurrencyId = $this->getCashBoxCurrencyId($dispSrc['id']);
                $this->insertCashMovement($dispSrc['id'], 'withdrawal', $amountTo, $dispCurrencyId, $exchangeId, $data['created_by']);
            } else {
                $this->updateBankAccountBalance($dispSrc['id'], $amountTo, 'withdrawal');
                $dispCurrencyId = $this->getBankAccountCurrencyId($dispSrc['id']);
                $this->insertBankMovement($dispSrc['id'], 'withdrawal', $amountTo, $dispCurrencyId, $exchangeId, $data['created_by']);
            }

            // Commit
            $this->db->commit();
            $this->db->query("SET SESSION autocommit = 1");
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");

            return ['success' => true, 'exchange_id' => $exchangeId];

        } catch (Exception $e) {
            $this->db->rollback();
            $this->db->query("SET SESSION autocommit = 1");
            $this->db->query("SET SESSION innodb_lock_wait_timeout = 10");
            
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function updateCashBoxBalance(int $boxId, float $amount, string $type): void
    {
        $operator = $type === 'deposit' ? '+' : '-';
        $sql = "UPDATE cash_boxes SET current_balance = current_balance $operator ?, updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) throw new Exception('Failed to prepare cash box update');
        $stmt->bind_param('di', $amount, $boxId);
        if (!$stmt->execute()) throw new Exception('Failed to update cash box balance');
        $stmt->close();
    }

    private function updateBankAccountBalance(int $accountId, float $amount, string $type): void
    {
        $operator = $type === 'deposit' ? '+' : '-';
        $sql = "UPDATE bank_accounts SET current_balance = current_balance $operator ?, updated_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) throw new Exception('Failed to prepare bank account update');
        $stmt->bind_param('di', $amount, $accountId);
        if (!$stmt->execute()) throw new Exception('Failed to update bank account balance');
        $stmt->close();
    }

    private function insertCashMovement(int $boxId, string $type, float $amount, int $currencyId, int $exchangeId, int $userId): void
    {
        $sql = "INSERT INTO cash_movements (cash_box_id, movement_type, amount, currency_id, description, exchange_id, user_id, created_at) 
                VALUES (?, ?, ?, ?, 'Exchange operation', ?, ?, NOW())";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) throw new Exception('Failed to prepare cash movement');
        $stmt->bind_param('isdiii', $boxId, $type, $amount, $currencyId, $exchangeId, $userId);
        if (!$stmt->execute()) throw new Exception('Failed to insert cash movement');
        $stmt->close();
    }

    private function insertBankMovement(int $accountId, string $type, float $amount, int $currencyId, int $exchangeId, int $userId): void
    {
        $sql = "INSERT INTO bank_movements (bank_account_id, movement_type, amount, currency_id, description, exchange_id, user_id, created_at)
                VALUES (?, ?, ?, ?, 'Exchange operation', ?, ?, NOW())";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) throw new Exception('Failed to prepare bank movement');
        $stmt->bind_param('isdiii', $accountId, $type, $amount, $currencyId, $exchangeId, $userId);
        if (!$stmt->execute()) throw new Exception('Failed to insert bank movement');
        $stmt->close();
    }

    private function addSimpleExchangeTransaction(array $txData, array $originalData): int
    {
        // Map funding and disbursement sources to cash box/bank account IDs
        $fundingSource = $originalData['funding_source'];
        $disburseSource = $originalData['disburse_source'];

        // Build SQL dynamically based on source types
        $columns = ['customer_id', 'from_amount', 'to_amount', 'exchange_rate', 'commission', 'profit', 'status', 'notes', 'branch_id', 'created_by'];
        $values = ['?', '?', '?', '?', '?', '?', '?', '?', '?', '?'];
        $params = [
            $txData['customer_id'],
            $txData['amount_from'],
            $txData['amount_to'],
            $txData['rate_used'],
            $txData['commission'],
            $txData['profit'],
            $txData['status'],
            $txData['notes'],
            $txData['branch_id'],
            $txData['created_by']
        ];
        $types = 'iddddssii';

        // Add funding source
        if ($fundingSource['type'] === 'cash') {
            $columns[] = 'from_cash_box_id';
            $values[] = '?';
            $params[] = intval($fundingSource['id']);
            $types .= 'i';
        } else {
            $columns[] = 'from_bank_account_id';
            $values[] = '?';
            $params[] = intval($fundingSource['id']);
            $types .= 'i';
        }

        // Add disbursement source
        if ($disburseSource['type'] === 'cash') {
            $columns[] = 'to_cash_box_id';
            $values[] = '?';
            $params[] = intval($disburseSource['id']);
            $types .= 'i';
        } else {
            $columns[] = 'to_bank_account_id';
            $values[] = '?';
            $params[] = intval($disburseSource['id']);
            $types .= 'i';
        }

        // Add created_at column with NOW() as default
        $columns[] = 'created_at';
        $values[] = 'NOW()';

        $sql = 'INSERT INTO exchanges (' . implode(', ', $columns) . ') VALUES (' . implode(', ', $values) . ')';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 0;

        $stmt->bind_param($types, ...$params);

        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }

        $stmt->close();
        return 0;
    }

    private function getCashBoxCurrencyId(int $cashBoxId): int
    {
        $sql = "SELECT currency_id FROM cash_boxes WHERE id = ? LIMIT 1";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 1; // Default currency ID
        $stmt->bind_param('i', $cashBoxId);
        $stmt->execute();
        $stmt->bind_result($currencyId);
        $stmt->fetch();
        $stmt->close();
        return $currencyId ?: 1; // Default to currency ID 1 if not found
    }

    private function getBankAccountCurrencyId(int $accountId): int
    {
        $sql = "SELECT currency_id FROM bank_accounts WHERE id = ? LIMIT 1";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return 1; // Default currency ID
        $stmt->bind_param('i', $accountId);
        $stmt->execute();
        $stmt->bind_result($currencyId);
        $stmt->fetch();
        $stmt->close();
        return $currencyId ?: 1; // Default to currency ID 1 if not found
    }

    /**
     * Override parent processExchange method to work with our simplified approach
     */
    public function processExchange(array $data): array
    {
        return $this->processExchangeSimple($data);
    }
}

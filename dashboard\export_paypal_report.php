<?php
/**
 * تصدير تقارير PayPal
 * Export PayPal Reports
 */

require_once '../includes/auth.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    header('Location: ../login.php');
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('paypal.export') && !$auth->hasPermission('paypal.reports')) {
    header('Location: ../dashboard/index.php?error=access_denied');
    exit;
}

$db = new Database();
$conn = Database::getConnection();

// معالجة الفلاتر
$dateFrom = $_GET['date_from'] ?? date('Y-m-01');
$dateTo = $_GET['date_to'] ?? date('Y-m-d');
$status = $_GET['status'] ?? '';
$transactionCode = $_GET['transaction_code'] ?? '';
$exportFormat = $_GET['export'] ?? 'excel';

// بناء شروط الاستعلام
$whereConditions = ["DATE(created_at) BETWEEN ? AND ?"];
$params = [$dateFrom, $dateTo];
$types = 'ss';

if (!empty($status)) {
    $whereConditions[] = "status = ?";
    $params[] = $status;
    $types .= 's';
}

if (!empty($transactionCode)) {
    $whereConditions[] = "transaction_code LIKE ?";
    $params[] = '%' . $transactionCode . '%';
    $types .= 's';
}

$whereClause = implode(' AND ', $whereConditions);

// جلب البيانات
$query = "
    SELECT
        id,
        transaction_code,
        sender_name,
        recipient_name,
        recipient_phone,
        amount,
        status,
        created_at
    FROM paypal_transfers
    WHERE $whereClause
    ORDER BY created_at DESC
";

$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$transfers = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// الإحصائيات
$statsQuery = "
    SELECT
        COUNT(*) as total_transfers,
        SUM(CASE WHEN status = 'مستلم' THEN amount ELSE 0 END) as completed_amount,
        SUM(CASE WHEN status = 'لم يستلم' THEN amount ELSE 0 END) as pending_amount,
        0 as cancelled_amount,
        COUNT(CASE WHEN status = 'مستلم' THEN 1 END) as completed_count,
        COUNT(CASE WHEN status = 'لم يستلم' THEN 1 END) as pending_count,
        0 as cancelled_count
    FROM paypal_transfers
    WHERE $whereClause
";

$stmt = $conn->prepare($statsQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();
$stats = $result->fetch_assoc();
$stmt->close();

if ($exportFormat === 'excel') {
    // تصدير Excel
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="paypal_report_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    echo "\xEF\xBB\xBF"; // UTF-8 BOM
    
    echo "<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel'>";
    echo "<head><meta charset='UTF-8'></head>";
    echo "<body>";
    
    // عنوان التقرير
    echo "<h2>تقرير حوالات PayPal</h2>";
    echo "<p>الفترة: من " . $dateFrom . " إلى " . $dateTo . "</p>";
    echo "<p>تاريخ التصدير: " . date('Y-m-d H:i:s') . "</p>";
    echo "<br>";
    
    // الإحصائيات
    echo "<h3>الإحصائيات العامة</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><td><b>إجمالي الحوالات</b></td><td>" . $stats['total_transfers'] . "</td></tr>";
    echo "<tr><td><b>المبلغ المكتمل</b></td><td>$" . number_format($stats['completed_amount'], 2) . "</td></tr>";
    echo "<tr><td><b>المبلغ قيد الانتظار</b></td><td>$" . number_format($stats['pending_amount'], 2) . "</td></tr>";
    echo "<tr><td><b>المبلغ الملغي</b></td><td>$" . number_format($stats['cancelled_amount'], 2) . "</td></tr>";
    echo "<tr><td><b>الحوالات المكتملة</b></td><td>" . $stats['completed_count'] . "</td></tr>";
    echo "<tr><td><b>الحوالات قيد الانتظار</b></td><td>" . $stats['pending_count'] . "</td></tr>";
    echo "<tr><td><b>الحوالات الملغية</b></td><td>" . $stats['cancelled_count'] . "</td></tr>";
    echo "</table>";
    echo "<br><br>";
    
    // جدول البيانات
    echo "<h3>تفاصيل الحوالات</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr>";
    echo "<th>رمز المعاملة</th>";
    echo "<th>اسم المرسل</th>";
    echo "<th>اسم المستقبل</th>";
    echo "<th>هاتف المستقبل</th>";
    echo "<th>المبلغ</th>";
    echo "<th>الحالة</th>";
    echo "<th>تاريخ الإنشاء</th>";
    echo "</tr>";
    
    foreach ($transfers as $transfer) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($transfer['transaction_code']) . "</td>";
        echo "<td>" . htmlspecialchars($transfer['sender_name']) . "</td>";
        echo "<td>" . htmlspecialchars($transfer['recipient_name']) . "</td>";
        echo "<td>" . htmlspecialchars($transfer['recipient_phone']) . "</td>";
        echo "<td>$" . number_format($transfer['amount'], 2) . "</td>";
        echo "<td>" . htmlspecialchars($transfer['status']) . "</td>";
        echo "<td>" . $transfer['created_at'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</body></html>";
    
} elseif ($exportFormat === 'pdf') {
    // تصدير PDF (يتطلب مكتبة PDF)
    header('Content-Type: text/html; charset=utf-8');
    
    echo "<!DOCTYPE html>";
    echo "<html dir='rtl' lang='ar'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<title>تقرير حوالات PayPal</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; margin: 20px; }";
    echo "table { width: 100%; border-collapse: collapse; margin: 20px 0; }";
    echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
    echo "th { background-color: #f2f2f2; font-weight: bold; }";
    echo "h1, h2, h3 { color: #333; }";
    echo ".stats-table { width: 50%; }";
    echo ".header { text-align: center; margin-bottom: 30px; }";
    echo ".footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }";
    echo "@media print { body { margin: 0; } }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    
    // رأس التقرير
    echo "<div class='header'>";
    echo "<h1>تقرير حوالات PayPal</h1>";
    echo "<p>الفترة: من " . $dateFrom . " إلى " . $dateTo . "</p>";
    echo "<p>تاريخ التصدير: " . date('Y-m-d H:i:s') . "</p>";
    echo "</div>";
    
    // الإحصائيات
    echo "<h2>الإحصائيات العامة</h2>";
    echo "<table class='stats-table'>";
    echo "<tr><td><strong>إجمالي الحوالات</strong></td><td>" . $stats['total_transfers'] . "</td></tr>";
    echo "<tr><td><strong>المبلغ المكتمل</strong></td><td>$" . number_format($stats['completed_amount'], 2) . "</td></tr>";
    echo "<tr><td><strong>المبلغ قيد الانتظار</strong></td><td>$" . number_format($stats['pending_amount'], 2) . "</td></tr>";
    echo "<tr><td><strong>المبلغ الملغي</strong></td><td>$" . number_format($stats['cancelled_amount'], 2) . "</td></tr>";
    echo "<tr><td><strong>الحوالات المكتملة</strong></td><td>" . $stats['completed_count'] . "</td></tr>";
    echo "<tr><td><strong>الحوالات قيد الانتظار</strong></td><td>" . $stats['pending_count'] . "</td></tr>";
    echo "<tr><td><strong>الحوالات الملغية</strong></td><td>" . $stats['cancelled_count'] . "</td></tr>";
    echo "</table>";
    
    // جدول البيانات
    echo "<h2>تفاصيل الحوالات</h2>";
    echo "<table>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>رمز المعاملة</th>";
    echo "<th>المرسل</th>";
    echo "<th>المستقبل</th>";
    echo "<th>المبلغ</th>";
    echo "<th>الحالة</th>";
    echo "<th>التاريخ</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($transfers as $transfer) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($transfer['transaction_code']) . "</td>";
        echo "<td>" . htmlspecialchars($transfer['sender_name']) . "</td>";
        echo "<td>" . htmlspecialchars($transfer['recipient_name']) . "<br><small>" . htmlspecialchars($transfer['recipient_phone']) . "</small></td>";
        echo "<td>$" . number_format($transfer['amount'], 2) . "</td>";
        echo "<td>" . htmlspecialchars($transfer['status']) . "</td>";
        echo "<td>" . date('Y-m-d', strtotime($transfer['created_at'])) . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    
    // تذييل التقرير
    echo "<div class='footer'>";
    echo "<p>تم إنشاء هذا التقرير بواسطة نظام TrustPlus</p>";
    echo "<p>© " . date('Y') . " جميع الحقوق محفوظة</p>";
    echo "</div>";
    
    echo "<script>";
    echo "window.onload = function() { window.print(); };";
    echo "</script>";
    
    echo "</body>";
    echo "</html>";
    
} else {
    // تصدير CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="paypal_report_' . date('Y-m-d') . '.csv"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // رؤوس الأعمدة
    fputcsv($output, [
        'رمز المعاملة',
        'اسم المرسل',
        'اسم المستقبل',
        'هاتف المستقبل',
        'المبلغ',
        'الحالة',
        'تاريخ الإنشاء'
    ]);
    
    // البيانات
    foreach ($transfers as $transfer) {
        fputcsv($output, [
            $transfer['transaction_code'],
            $transfer['sender_name'],
            $transfer['recipient_name'],
            $transfer['recipient_phone'],
            '$' . number_format($transfer['amount'], 2),
            $transfer['status'],
            $transfer['created_at']
        ]);
    }
    
    fclose($output);
}
?>

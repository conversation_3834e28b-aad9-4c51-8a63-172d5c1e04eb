<?php
/**
 * AccountingManager
 *
 * Handles creation of accounting_entries rows that link to business events
 * (cash / bank movements, transfers, exchanges, etc.). All calls are expected
 * to be executed INSIDE an existing database transaction managed by the
 * caller (e.g. ExchangeManager, CashManager). The passed `$mysqli` connection
 * must therefore already be open and (optionally) have autocommit disabled.
 *
 * IMPORTANT: This manager does NOT start or commit/rollback transactions –
 *            that is the responsibility of the higher-level service that is
 *            orchestrating the complete workflow so that all inserts either
 *            succeed or fail together.
 */
require_once __DIR__ . '/database.php';

class AccountingManager
{
    /** @var mysqli */
    private $db;

    public function __construct(mysqli $mysqli)
    {
        $this->db = $mysqli;
    }

    /**
     * Create a single accounting entry.
     *
     * @param array $data Expected keys:
     *  - entry_date      (string Y-m-d)  – default TODAY
     *  - description     (string|null)
     *  - branch_id       (int)
     *  - account_debit   (string)
     *  - account_credit  (string)
     *  - amount          (float)
     *  - currency_id     (int)
     *  - created_by      (int|null)
     *  - transfer_id     (int|null)
     *  - exchange_id     (int|null)
     *  - cash_movement_id(int|null)
     *  - bank_movement_id(int|null)
     *
     * @return int|false New entry ID or false on failure.
     */
    public function createEntry(array $data)
    {
        $sql = 'INSERT INTO accounting_entries (entry_date, description, branch_id, account_debit, account_credit, amount, currency_id, created_by, transfer_id, exchange_id, cash_movement_id, bank_movement_id, created_at)
                VALUES (?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())';

        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $entryDate  = $data['entry_date'] ?? date('Y-m-d');
        $desc       = $data['description'] ?? null;
        $branchId   = $data['branch_id'];
        $acctDr     = $data['account_debit'];
        $acctCr     = $data['account_credit'];
        $amount     = $data['amount'];
        $currencyId = $data['currency_id'];
        $createdBy  = $data['created_by'] ?? null;
        $transferId = $data['transfer_id'] ?? null;
        $exchangeId = $data['exchange_id'] ?? null;
        $cashMovId  = $data['cash_movement_id'] ?? null;
        $bankMovId  = $data['bank_movement_id'] ?? null;

        $stmt->bind_param(
            'ssissdiiiiii',
            $entryDate,
            $desc,
            $branchId,
            $acctDr,
            $acctCr,
            $amount,
            $currencyId,
            $createdBy,
            $transferId,
            $exchangeId,
            $cashMovId,
            $bankMovId
        );

        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }
} 
<?php
/**
 * Debug Sidebar Profile Image
 * This script helps debug the sidebar profile image display
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/image_helper.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1'>";
echo "<title>تصحيح الشريط الجانبي</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css'>";
echo "<link rel='stylesheet' href='../assets/css/dashboard-improvements.css'>";
echo "<link rel='stylesheet' href='../assets/css/profile-images.css'>";
echo "<style>";
echo ".debug-sidebar { background: #2c3e50; color: white; padding: 20px; width: 300px; min-height: 400px; }";
echo ".debug-info { background: #f8f9fa; padding: 20px; margin: 20px; border-radius: 8px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container-fluid'>";
echo "<h2 class='p-4'>تصحيح صورة الشريط الجانبي</h2>";

echo "<div class='d-flex'>";
echo "<div class='debug-sidebar'>";

// Test sidebar header
echo "<h4 class='text-center mb-4'>Trust Plus</h4>";
echo "<hr class='border-light'>";

// Test profile section
echo "<div class='p-4 border-bottom border-light'>";
echo "<div class='d-flex align-items-center'>";

// Test with profile image
echo "<div class='profile-avatar me-3'>";
echo "<img src='../assets/images/default-avatar.png' alt='صورة المستخدم'>";
echo "</div>";

echo "<div class='flex-grow-1 text-white'>";
echo "<p class='mb-1 fw-medium'>مدير النظام</p>";
echo "<p class='mb-0 small opacity-75'>Administrator</p>";
echo "</div>";

echo "</div>";
echo "</div>";

// Test without profile image
echo "<div class='p-4 border-bottom border-light mt-3'>";
echo "<div class='d-flex align-items-center'>";

echo "<div class='profile-avatar me-3'>";
echo "<div class='default-avatar'>";
echo "<i class='fas fa-user'></i>";
echo "</div>";
echo "</div>";

echo "<div class='flex-grow-1 text-white'>";
echo "<p class='mb-1 fw-medium'>مستخدم بدون صورة</p>";
echo "<p class='mb-0 small opacity-75'>User without image</p>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>"; // End sidebar

echo "<div class='flex-grow-1'>";
echo "<div class='debug-info'>";

echo "<h3>معلومات التصحيح:</h3>";

// Check CSS files
$cssFiles = [
    '../assets/css/dashboard-improvements.css',
    '../assets/css/profile-images.css'
];

echo "<h4>ملفات CSS:</h4>";
echo "<ul>";
foreach ($cssFiles as $file) {
    $exists = file_exists(__DIR__ . '/' . $file);
    $size = $exists ? filesize(__DIR__ . '/' . $file) : 0;
    echo "<li>" . basename($file) . ": " . ($exists ? "✅ موجود ({$size} bytes)" : "❌ غير موجود") . "</li>";
}
echo "</ul>";

// Check image files
$imageFiles = [
    '../assets/images/default-avatar.png'
];

echo "<h4>ملفات الصور:</h4>";
echo "<ul>";
foreach ($imageFiles as $file) {
    $exists = file_exists(__DIR__ . '/' . $file);
    $size = $exists ? filesize(__DIR__ . '/' . $file) : 0;
    echo "<li>" . basename($file) . ": " . ($exists ? "✅ موجود ({$size} bytes)" : "❌ غير موجود") . "</li>";
}
echo "</ul>";

// Check CSS content
echo "<h4>محتوى CSS للشريط الجانبي:</h4>";
$dashboardCss = file_get_contents(__DIR__ . '/../assets/css/dashboard-improvements.css');
if (strpos($dashboardCss, '.sidebar .profile-avatar') !== false) {
    echo "<p>✅ CSS للشريط الجانبي موجود</p>";
} else {
    echo "<p>❌ CSS للشريط الجانبي غير موجود</p>";
}

// Check BASE_URL
echo "<h4>إعدادات النظام:</h4>";
echo "<ul>";
echo "<li>BASE_URL: " . (defined('BASE_URL') ? BASE_URL : 'غير محدد') . "</li>";
echo "<li>Current Directory: " . __DIR__ . "</li>";
echo "<li>CSS Path: " . __DIR__ . '/../assets/css/dashboard-improvements.css' . "</li>";
echo "</ul>";

echo "</div>"; // End debug-info
echo "</div>"; // End flex-grow-1

echo "</div>"; // End d-flex

echo "</div>"; // End container-fluid

echo "</body>";
echo "</html>";
?> 
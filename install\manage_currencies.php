<?php
/**
 * Manage currencies - activate, deactivate, delete
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إدارة العملات</h2>\n";

try {
    $conn = Database::getConnection();
    $message = '';
    $messageType = '';
    
    // Handle form submissions
    if ($_POST) {
        if (isset($_POST['action']) && isset($_POST['currency_code'])) {
            $action = $_POST['action'];
            $currencyCode = $_POST['currency_code'];
            
            switch ($action) {
                case 'activate':
                    $sql = "UPDATE currencies SET is_active = 1, updated_at = NOW() WHERE code = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('s', $currencyCode);
                    if ($stmt->execute()) {
                        $message = "تم تفعيل العملة $currencyCode بنجاح";
                        $messageType = 'success';
                    } else {
                        $message = "فشل في تفعيل العملة $currencyCode";
                        $messageType = 'error';
                    }
                    break;
                    
                case 'deactivate':
                    // Check if currency is used in cash boxes
                    $checkSql = "SELECT COUNT(*) as count FROM cash_boxes WHERE currency_code = ?";
                    $checkStmt = $conn->prepare($checkSql);
                    $checkStmt->bind_param('s', $currencyCode);
                    $checkStmt->execute();
                    $result = $checkStmt->get_result();
                    $count = $result->fetch_assoc()['count'];
                    
                    if ($count > 0) {
                        $message = "لا يمكن إلغاء تفعيل العملة $currencyCode لأنها مستخدمة في $count صندوق";
                        $messageType = 'warning';
                    } else {
                        $sql = "UPDATE currencies SET is_active = 0, updated_at = NOW() WHERE code = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param('s', $currencyCode);
                        if ($stmt->execute()) {
                            $message = "تم إلغاء تفعيل العملة $currencyCode بنجاح";
                            $messageType = 'success';
                        } else {
                            $message = "فشل في إلغاء تفعيل العملة $currencyCode";
                            $messageType = 'error';
                        }
                    }
                    break;
                    
                case 'delete':
                    // Check if currency is used in cash boxes
                    $checkSql = "SELECT COUNT(*) as count FROM cash_boxes WHERE currency_code = ?";
                    $checkStmt = $conn->prepare($checkSql);
                    $checkStmt->bind_param('s', $currencyCode);
                    $checkStmt->execute();
                    $result = $checkStmt->get_result();
                    $count = $result->fetch_assoc()['count'];
                    
                    if ($count > 0) {
                        $message = "لا يمكن حذف العملة $currencyCode لأنها مستخدمة في $count صندوق";
                        $messageType = 'warning';
                    } else {
                        $sql = "DELETE FROM currencies WHERE code = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param('s', $currencyCode);
                        if ($stmt->execute()) {
                            $message = "تم حذف العملة $currencyCode بنجاح";
                            $messageType = 'success';
                        } else {
                            $message = "فشل في حذف العملة $currencyCode";
                            $messageType = 'error';
                        }
                    }
                    break;
            }
        }
        
        if (isset($_POST['bulk_action']) && isset($_POST['selected_currencies'])) {
            $bulkAction = $_POST['bulk_action'];
            $selectedCurrencies = $_POST['selected_currencies'];
            $processedCount = 0;
            
            foreach ($selectedCurrencies as $currencyCode) {
                switch ($bulkAction) {
                    case 'activate':
                        $sql = "UPDATE currencies SET is_active = 1, updated_at = NOW() WHERE code = ?";
                        break;
                    case 'deactivate':
                        $sql = "UPDATE currencies SET is_active = 0, updated_at = NOW() WHERE code = ?";
                        break;
                }
                
                if (isset($sql)) {
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('s', $currencyCode);
                    if ($stmt->execute()) {
                        $processedCount++;
                    }
                }
            }
            
            $message = "تم تطبيق العملية على $processedCount عملة من أصل " . count($selectedCurrencies);
            $messageType = 'success';
        }
    }
    
    // Display message
    if ($message) {
        $alertClass = '';
        switch ($messageType) {
            case 'success': $alertClass = 'alert-success'; break;
            case 'error': $alertClass = 'alert-danger'; break;
            case 'warning': $alertClass = 'alert-warning'; break;
        }
        echo "<div class='alert $alertClass' style='padding: 10px; margin: 10px 0; border-radius: 5px; background: " . 
             ($messageType === 'success' ? '#d4edda' : ($messageType === 'error' ? '#f8d7da' : '#fff3cd')) . 
             "; border: 1px solid " . 
             ($messageType === 'success' ? '#c3e6cb' : ($messageType === 'error' ? '#f5c6cb' : '#ffeaa7')) . 
             ";'>$message</div>\n";
    }
    
    // Get all currencies with usage information
    $result = $conn->query("
        SELECT c.*, 
               COALESCE(cb_count.count, 0) as cash_boxes_count,
               COALESCE(ex_count.count, 0) as exchanges_count
        FROM currencies c
        LEFT JOIN (
            SELECT currency_code, COUNT(*) as count 
            FROM cash_boxes 
            GROUP BY currency_code
        ) cb_count ON c.code = cb_count.currency_code
        LEFT JOIN (
            SELECT from_currency, COUNT(*) as count 
            FROM exchanges 
            GROUP BY from_currency
        ) ex_count ON c.code = ex_count.from_currency
        ORDER BY c.is_active DESC, c.name
    ");
    
    if (!$result) {
        throw new Exception("فشل في جلب العملات: " . $conn->error);
    }
    
    echo "<form method='post' id='currencyForm'>\n";
    echo "<div style='margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;'>\n";
    echo "<h3>عمليات مجمعة</h3>\n";
    echo "<div style='display: flex; gap: 10px; align-items: center; flex-wrap: wrap;'>\n";
    echo "<select name='bulk_action' style='padding: 5px;'>\n";
    echo "<option value=''>اختر العملية</option>\n";
    echo "<option value='activate'>تفعيل المحدد</option>\n";
    echo "<option value='deactivate'>إلغاء تفعيل المحدد</option>\n";
    echo "</select>\n";
    echo "<button type='submit' onclick='return confirmBulkAction()' style='padding: 5px 15px; background: #007bff; color: white; border: none; border-radius: 3px;'>تطبيق</button>\n";
    echo "<button type='button' onclick='selectAll()' style='padding: 5px 15px; background: #28a745; color: white; border: none; border-radius: 3px;'>تحديد الكل</button>\n";
    echo "<button type='button' onclick='selectNone()' style='padding: 5px 15px; background: #6c757d; color: white; border: none; border-radius: 3px;'>إلغاء التحديد</button>\n";
    echo "</div>\n";
    echo "</div>\n";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>\n";
    echo "<tr style='background: #e9ecef;'>\n";
    echo "<th style='padding: 8px;'><input type='checkbox' id='selectAllCheckbox' onchange='toggleAll()'></th>\n";
    echo "<th style='padding: 8px;'>الرمز</th>\n";
    echo "<th style='padding: 8px;'>اسم العملة</th>\n";
    echo "<th style='padding: 8px;'>الرمز المختصر</th>\n";
    echo "<th style='padding: 8px;'>البلد</th>\n";
    echo "<th style='padding: 8px;'>الحالة</th>\n";
    echo "<th style='padding: 8px;'>الاستخدام</th>\n";
    echo "<th style='padding: 8px;'>العمليات</th>\n";
    echo "</tr>\n";
    
    while ($row = $result->fetch_assoc()) {
        $statusText = $row['is_active'] ? 'نشط' : 'غير نشط';
        $statusColor = $row['is_active'] ? 'green' : 'red';
        $usageText = '';
        $canDelete = true;
        
        if ($row['cash_boxes_count'] > 0) {
            $usageText .= $row['cash_boxes_count'] . ' صندوق';
            $canDelete = false;
        }
        if ($row['exchanges_count'] > 0) {
            if ($usageText) $usageText .= ', ';
            $usageText .= $row['exchanges_count'] . ' صرافة';
            $canDelete = false;
        }
        if (!$usageText) {
            $usageText = 'غير مستخدم';
        }
        
        echo "<tr>\n";
        echo "<td style='padding: 8px; text-align: center;'><input type='checkbox' name='selected_currencies[]' value='{$row['code']}' class='currency-checkbox'></td>\n";
        echo "<td style='padding: 8px; font-weight: bold; font-family: monospace;'>{$row['code']}</td>\n";
        echo "<td style='padding: 8px;'>{$row['name']}</td>\n";
        echo "<td style='padding: 8px; text-align: center; font-weight: bold;'>{$row['symbol']}</td>\n";
        echo "<td style='padding: 8px;'>{$row['country']}</td>\n";
        echo "<td style='padding: 8px; color: $statusColor; font-weight: bold;'>$statusText</td>\n";
        echo "<td style='padding: 8px; font-size: 12px;'>$usageText</td>\n";
        echo "<td style='padding: 8px;'>\n";
        
        if ($row['is_active']) {
            if ($canDelete) {
                echo "<button type='submit' name='action' value='deactivate' onclick='return confirm(\"هل تريد إلغاء تفعيل العملة {$row['code']}؟\")' style='padding: 2px 8px; background: #ffc107; color: black; border: none; border-radius: 3px; margin: 1px;'>إلغاء تفعيل</button>\n";
            } else {
                echo "<span style='color: #6c757d; font-size: 12px;'>مستخدم</span>\n";
            }
        } else {
            echo "<button type='submit' name='action' value='activate' onclick='return confirm(\"هل تريد تفعيل العملة {$row['code']}؟\")' style='padding: 2px 8px; background: #28a745; color: white; border: none; border-radius: 3px; margin: 1px;'>تفعيل</button>\n";
        }
        
        if ($canDelete) {
            echo "<button type='submit' name='action' value='delete' onclick='return confirm(\"هل تريد حذف العملة {$row['code']} نهائياً؟ هذا الإجراء لا يمكن التراجع عنه!\")' style='padding: 2px 8px; background: #dc3545; color: white; border: none; border-radius: 3px; margin: 1px;'>حذف</button>\n";
        }
        
        echo "<input type='hidden' name='currency_code' value='{$row['code']}'>\n";
        echo "</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    echo "</form>\n";
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #e3f2fd; border-radius: 5px;'>\n";
    echo "<h3>ملاحظات مهمة</h3>\n";
    echo "<ul>\n";
    echo "<li>لا يمكن حذف أو إلغاء تفعيل العملات المستخدمة في الصناديق أو عمليات الصرافة</li>\n";
    echo "<li>العملات غير النشطة لن تظهر في قوائم إنشاء الصناديق الجديدة</li>\n";
    echo "<li>يمكن إعادة تفعيل العملات المعطلة في أي وقت</li>\n";
    echo "<li>العملات الرقمية معطلة افتراضياً ويمكن تفعيلها عند الحاجة</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='margin: 20px 0;'>\n";
    echo "<a href='view_all_currencies.php' style='padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>عرض جميع العملات</a>\n";
    echo "<a href='insert_all_currencies.php' style='padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;'>إعادة إدخال العملات</a>\n";
    echo "<a href='../dashboard/cash.php' style='padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;'>العودة للصناديق</a>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
}
?>

<script>
function toggleAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.currency-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.currency-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    document.getElementById('selectAllCheckbox').checked = true;
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.currency-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAllCheckbox').checked = false;
}

function confirmBulkAction() {
    const selectedCount = document.querySelectorAll('.currency-checkbox:checked').length;
    if (selectedCount === 0) {
        alert('يرجى تحديد عملة واحدة على الأقل');
        return false;
    }
    
    const action = document.querySelector('select[name="bulk_action"]').value;
    if (!action) {
        alert('يرجى اختيار العملية المطلوبة');
        return false;
    }
    
    return confirm(`هل تريد تطبيق العملية على ${selectedCount} عملة؟`);
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
h2, h3 { color: #333; }
table { background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
th, td { padding: 8px; text-align: right; border: 1px solid #dee2e6; }
th { background: #e9ecef; font-weight: bold; }
tr:nth-child(even) { background: #f8f9fa; }
tr:hover { background: #e3f2fd; }
button { cursor: pointer; }
button:hover { opacity: 0.8; }
a { text-decoration: none; }
a:hover { opacity: 0.8; }
</style>

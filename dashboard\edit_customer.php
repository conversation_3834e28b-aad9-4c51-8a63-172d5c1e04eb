<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('customers.edit');

$db              = new Database();
$customerManager = new CustomerManager($db);
$current_user    = $auth->getCurrentUser();

$customerId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
if ($customerId <= 0) {
    echo '<div class="alert alert-danger m-4">معرّف العميل غير صالح</div>';
    exit;
}

$customer = $customerManager->getCustomerById($customerId);
if (!$customer) {
    echo '<div class="alert alert-danger m-4">العميل غير موجود</div>';
    exit;
}

$errors = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز CSRF غير صالح.';
    }

    // Gather & sanitize inputs
    $full_name  = sanitize_input($_POST['full_name'] ?? '');
    $id_number  = sanitize_input($_POST['id_number'] ?? '');
    $id_type    = sanitize_input($_POST['id_type'] ?? '');
    $nationality = sanitize_input($_POST['nationality'] ?? '');
    $birth_date  = sanitize_input($_POST['birth_date'] ?? '');
    $phone       = sanitize_input($_POST['phone'] ?? '');
    $email       = sanitize_input($_POST['email'] ?? '');
    $address     = sanitize_input($_POST['address'] ?? '');
    $occupation  = sanitize_input($_POST['occupation'] ?? '');
    $risk_level  = sanitize_input($_POST['risk_level'] ?? 'low');
    $status      = sanitize_input($_POST['status'] ?? 'active');
    $kyc_status  = sanitize_input($_POST['kyc_status'] ?? 'pending');

    // Validation
    if ($full_name === '') $errors[] = 'الاسم مطلوب';
    $validIdTypes = ['national_id','passport','residence'];
    if (!in_array($id_type, $validIdTypes, true)) $errors[] = 'نوع الهوية غير صالح';
    $validRisk = ['low','medium','high'];
    if (!in_array($risk_level, $validRisk, true)) $errors[] = 'مستوى المخاطر غير صالح';
    $validStatus = ['active','inactive','blocked'];
    if (!in_array($status, $validStatus, true)) $errors[] = 'الحالة غير صالحة';
    $validKyc = ['pending','under_review','approved','rejected'];
    if (!in_array($kyc_status, $validKyc, true)) $errors[] = 'حالة KYC غير صالحة';

    if ($id_number && $customerManager->idNumberExists($id_number, $customerId)) {
        $errors[] = 'رقم الهوية مستخدم مسبقاً';
    }

    if (empty($errors)) {
        $updateData = [
            'full_name'  => $full_name,
            'id_number'  => $id_number ?: null,
            'id_type'    => $id_type,
            'nationality'=> $nationality,
            'birth_date' => $birth_date ?: null,
            'phone'      => $phone,
            'email'      => $email,
            'address'    => $address,
            'occupation' => $occupation,
            'risk_level' => $risk_level,
            'status'     => $status,
            'kyc_status' => $kyc_status,
        ];

        $oldData = $customer;
        $ok = $customerManager->updateCustomer($customerId, $updateData);
        if ($ok) {
            // تسجيل عملية تعديل العميل
            ActivityHelper::logUpdate(
                'customers',
                $customer['full_name'],
                $oldData,
                $updateData,
                $customerId
            );

            header('Location: customer_details.php?id=' . $customerId . '&msg=updated');
            exit;
        } else {
            // تسجيل فشل التعديل
            ActivityHelper::logError(
                'customers',
                'edit_customer',
                'فشل في تعديل بيانات العميل: ' . $customer['full_name'],
                [
                    'customer_id' => $customerId,
                    'attempted_data' => $updateData
                ]
            );
            $errors[] = 'حدث خطأ أثناء التحديث';
        }
    }
} else {
    // Prefill
    $_POST = $customer;
}

if (
    $_SERVER['REQUEST_METHOD'] === 'POST' &&
    isset($_POST['upload_document']) &&
    verify_csrf_token($_POST['csrf_token'] ?? '')
) {
    // معالجة رفع مستند جديد
    $docErrors = [];
    $docType = sanitize_input($_POST['document_type'] ?? '');
    $allowedTypes = [
        'id_card','passport','driving_license','utility_bill','bank_statement','other'
    ];
    if (!in_array($docType, $allowedTypes, true)) {
        $docErrors[] = 'نوع المستند غير صالح';
    }

    if (!isset($_FILES['document_file']) || $_FILES['document_file']['error'] !== UPLOAD_ERR_OK) {
        $docErrors[] = 'لم يتم اختيار ملف أو حدث خطأ في الرفع';
    }

    if (!$docErrors) {
        $file      = $_FILES['document_file'];
        $maxSizeMB = 10;
        if ($file['size'] > ($maxSizeMB * 1024 * 1024)) {
            $docErrors[] = 'حجم الملف يتجاوز ' . $maxSizeMB . 'MB';
        }
        $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowedExt = ['pdf','jpg','jpeg','png'];
        if (!in_array($ext, $allowedExt, true)) {
            $docErrors[] = 'نوع الملف غير مدعوم';
        }
    }

    if (!$docErrors) {
        $customerDir = __DIR__ . '/../uploads/documents/' . $customerId;
        if (!is_dir($customerDir)) {
            mkdir($customerDir, 0755, true);
        }
        $safeName = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/','', $file['name']);
        $destPath = $customerDir . '/' . $safeName;
        if (move_uploaded_file($file['tmp_name'], $destPath)) {
            // سجل في قاعدة البيانات
            $relPath = 'uploads/documents/' . $customerId . '/' . $safeName;
            $stmt = $db::getConnection()->prepare('INSERT INTO documents (customer_id, type, file_path, status, uploaded_at) VALUES (?,?,?,\'pending\', NOW())');
            if ($stmt) {
                $stmt->bind_param('iss', $customerId, $docType, $relPath);
                $stmt->execute();
                $stmt->close();
                $successUpload = true;
            } else {
                $docErrors[] = 'خطأ في حفظ المستند في قاعدة البيانات';
            }
        } else {
            $docErrors[] = 'فشل نقل الملف إلى الخادم';
        }
    }
}

$pageTitle = 'تعديل عميل';
include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>
<main class="content p-4">
    <h2 class="mb-4">تعديل بيانات عميل</h2>
    <?php if ($errors): ?>
        <div class="alert alert-danger"><ul class="mb-0">
            <?php foreach ($errors as $e): ?><li><?php echo $e; ?></li><?php endforeach; ?>
        </ul></div>
    <?php endif; ?>

    <form method="post" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">

        <div class="mb-3">
            <label class="form-label">الاسم الكامل</label>
            <input type="text" name="full_name" class="form-control" value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label">رقم الهوية</label>
                <input type="text" name="id_number" class="form-control" value="<?php echo htmlspecialchars($_POST['id_number'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">نوع الهوية</label>
                <select name="id_type" class="form-select" required>
                    <?php $sel = $_POST['id_type'] ?? ''; ?>
                    <option value="national_id" <?php echo ($sel==='national_id')?'selected':''; ?>>بطاقة هوية</option>
                    <option value="passport" <?php echo ($sel==='passport')?'selected':''; ?>>جواز سفر</option>
                    <option value="residence" <?php echo ($sel==='residence')?'selected':''; ?>>إقامة</option>
                </select>
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">الجنسية</label>
                <input type="text" name="nationality" class="form-control" value="<?php echo htmlspecialchars($_POST['nationality'] ?? ''); ?>">
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label">تاريخ الميلاد</label>
                <input type="date" name="birth_date" class="form-control" value="<?php echo htmlspecialchars($_POST['birth_date'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">الهاتف</label>
                <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
            </div>
            <div class="col-md-4 mb-3">
                <label class="form-label">البريد الإلكتروني</label>
                <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">العنوان</label>
            <textarea name="address" class="form-control" rows="2"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
        </div>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">المهنة</label>
                <input type="text" name="occupation" class="form-control" value="<?php echo htmlspecialchars($_POST['occupation'] ?? ''); ?>">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">مستوى المخاطر</label>
                <?php $selRisk = $_POST['risk_level'] ?? 'low'; ?>
                <select name="risk_level" class="form-select">
                    <option value="low" <?php echo ($selRisk==='low')?'selected':''; ?>>منخفض</option>
                    <option value="medium" <?php echo ($selRisk==='medium')?'selected':''; ?>>متوسط</option>
                    <option value="high" <?php echo ($selRisk==='high')?'selected':''; ?>>مرتفع</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">الحالة</label>
                <?php $selStatus = $_POST['status'] ?? 'active'; ?>
                <select name="status" class="form-select">
                    <option value="active" <?php echo ($selStatus==='active')?'selected':''; ?>>نشط</option>
                    <option value="inactive" <?php echo ($selStatus==='inactive')?'selected':''; ?>>غير نشط</option>
                    <option value="blocked" <?php echo ($selStatus==='blocked')?'selected':''; ?>>محظور</option>
                </select>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">حالة KYC</label>
            <?php $selKyc = $_POST['kyc_status'] ?? 'pending'; ?>
            <select name="kyc_status" class="form-select">
                <option value="pending" <?php echo ($selKyc==='pending')?'selected':''; ?>>قيد الانتظار</option>
                <option value="under_review" <?php echo ($selKyc==='under_review')?'selected':''; ?>>تحت المراجعة</option>
                <option value="approved" <?php echo ($selKyc==='approved')?'selected':''; ?>>مقبول</option>
                <option value="rejected" <?php echo ($selKyc==='rejected')?'selected':''; ?>>مرفوض</option>
            </select>
        </div>

        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
        <a href="customer_details.php?id=<?php echo $customerId; ?>" class="btn btn-secondary">إلغاء</a>
    </form>

    <!-- رسائل رفع المستند -->
    <?php if (isset($successUpload) && $successUpload): ?>
        <div class="alert alert-success mt-4">تم رفع المستند بنجاح.</div>
    <?php elseif (!empty($docErrors)): ?>
        <div class="alert alert-danger mt-4"><ul class="mb-0">
            <?php foreach ($docErrors as $e): ?><li><?php echo $e; ?></li><?php endforeach; ?>
        </ul></div>
    <?php endif; ?>

    <!-- نموذج رفع مستند جديد -->
    <div class="card mt-4">
        <div class="card-header">رفع مستند جديد</div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">نوع المستند</label>
                        <select name="document_type" class="form-select" required>
                            <option value="">-- اختر --</option>
                            <option value="id_card">بطاقة هوية</option>
                            <option value="passport">جواز سفر</option>
                            <option value="driving_license">رخصة قيادة</option>
                            <option value="utility_bill">فاتورة خدمات</option>
                            <option value="bank_statement">كشف حساب بنكي</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الملف</label>
                        <input type="file" name="document_file" class="form-control" accept=".pdf,.jpg,.jpeg,.png" required>
                        <div class="form-text">PDF, JPG, PNG (حد أقصى 10MB)</div>
                    </div>
                </div>
                <button type="submit" name="upload_document" class="btn btn-success"><i class="fas fa-upload me-1"></i>رفع</button>
            </form>
        </div>
    </div>
</main>
<?php include __DIR__ . '/../includes/footer.php'; ?> 
<?php
/**
 * اختبار الميزات الجديدة
 * Test New Features
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الميزات الجديدة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .feature-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-rocket text-success"></i>
            اختبار الميزات الجديدة
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>الميزات الجديدة المضافة</h5>
            <ul class="mb-0">
                <li><strong>استعادة المعاملات المحذوفة:</strong> إمكانية إرجاع المعاملات من سلة المحذوفات</li>
                <li><strong>الحذف النهائي:</strong> حذف المعاملات نهائياً من قاعدة البيانات</li>
                <li><strong>تقارير شاملة:</strong> إحصائيات ورسوم بيانية تفاعلية</li>
                <li><strong>تصدير متقدم:</strong> تصدير إلى Excel, PDF, CSV</li>
            </ul>
        </div>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                echo "<div class='test-result test-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
            
            // اختبار الملفات الجديدة
            showInfo("<strong>اختبار الملفات الجديدة:</strong>");
            
            $new_files = [
                'dashboard/daily_transactions_reports.php' => 'صفحة التقارير',
                'dashboard/export_daily_transactions.php' => 'صفحة التصدير',
                'dashboard/deleted_daily_transactions.php' => 'صفحة المعاملات المحذوفة (محدثة)'
            ];
            
            foreach ($new_files as $file => $description) {
                $file_path = __DIR__ . '/../' . $file;
                $exists = file_exists($file_path);
                showResult($description, $exists, $exists ? "الملف موجود" : "الملف غير موجود");
            }
            
            // اختبار وجود معاملات محذوفة
            showInfo("<strong>اختبار المعاملات المحذوفة:</strong>");
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM deleted_daily_transactions");
                $deleted_count = $stmt->fetch()['count'];
                showResult("وجود معاملات محذوفة", true, "يوجد {$deleted_count} معاملة محذوفة");
                
                if ($deleted_count > 0) {
                    // اختبار إمكانية الاستعادة
                    $stmt = $pdo->query("SELECT id, transaction_number FROM deleted_daily_transactions LIMIT 1");
                    $deleted_transaction = $stmt->fetch();
                    
                    if ($deleted_transaction) {
                        showInfo("معاملة متاحة للاختبار: #{$deleted_transaction['transaction_number']} (ID: {$deleted_transaction['id']})");
                    }
                }
            } catch (Exception $e) {
                showResult("فحص المعاملات المحذوفة", false, $e->getMessage());
            }
            
            // اختبار إحصائيات التقارير
            showInfo("<strong>اختبار إحصائيات التقارير:</strong>");
            
            try {
                // إحصائيات عامة
                $stmt = $pdo->query("SELECT COUNT(*) as total, SUM(base_amount) as total_amount FROM daily_transactions");
                $stats = $stmt->fetch();
                showResult("الإحصائيات العامة", true, "إجمالي المعاملات: {$stats['total']}, إجمالي المبلغ: " . number_format($stats['total_amount'], 2));
                
                // إحصائيات نوع التسليم
                $stmt = $pdo->query("SELECT delivery_type, COUNT(*) as count FROM daily_transactions GROUP BY delivery_type");
                $delivery_stats = $stmt->fetchAll();
                
                if (!empty($delivery_stats)) {
                    $delivery_summary = [];
                    foreach ($delivery_stats as $stat) {
                        $delivery_summary[] = $stat['delivery_type'] . ': ' . $stat['count'];
                    }
                    showResult("إحصائيات نوع التسليم", true, implode(', ', $delivery_summary));
                } else {
                    showResult("إحصائيات نوع التسليم", false, "لا توجد بيانات");
                }
                
            } catch (Exception $e) {
                showResult("إحصائيات التقارير", false, $e->getMessage());
            }
            
            // اختبار صلاحيات التقارير
            showInfo("<strong>اختبار الصلاحيات:</strong>");
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM permissions WHERE name = 'daily_transactions.reports'");
                $reports_permission = $stmt->fetch()['count'];
                showResult("صلاحية التقارير", $reports_permission > 0, $reports_permission > 0 ? "الصلاحية موجودة" : "الصلاحية غير موجودة");
                
            } catch (Exception $e) {
                showResult("فحص الصلاحيات", false, $e->getMessage());
            }
            
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 90) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> جميع الميزات الجديدة تعمل بشكل مثالي";
            echo "</div>";
        } elseif ($success_rate >= 70) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> معظم الميزات تعمل";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج إصلاح!</strong> هناك مشاكل في الميزات الجديدة";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <!-- بطاقات الميزات الجديدة -->
        <div class="row mt-4">
            <div class="col-md-6 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-trash-restore me-2"></i>
                            إدارة المعاملات المحذوفة
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>استعادة أو حذف نهائي للمعاملات المحذوفة</p>
                        <a href="../dashboard/deleted_daily_transactions.php" class="btn btn-warning">
                            <i class="fas fa-trash-alt me-1"></i>
                            المعاملات المحذوفة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير والإحصائيات
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>تقارير شاملة مع رسوم بيانية تفاعلية</p>
                        <a href="../dashboard/daily_transactions_reports.php" class="btn btn-primary">
                            <i class="fas fa-chart-line me-1"></i>
                            عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="../dashboard/daily_transactions.php" class="btn btn-success btn-lg">
                <i class="fas fa-list me-2"></i>
                قائمة المعاملات
            </a>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-info btn-lg ms-2">
                <i class="fas fa-plus me-2"></i>
                إضافة معاملة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

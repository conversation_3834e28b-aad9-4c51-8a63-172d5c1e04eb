<?php
/**
 * Update Operation Amounts
 * This script updates existing operation amounts to reflect the new calculation method
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>تحديث مبالغ العمليات</h2>";

try {
    $db = Database::getConnection();
    
    // Start transaction
    $db->begin_transaction();
    
    echo "<p>جاري تحديث مبالغ العمليات...</p>";
    
    // Read SQL file
    $sql = file_get_contents(__DIR__ . '/update_operation_amounts.sql');
    
    // Execute multi-query SQL
    if ($db->multi_query($sql)) {
        do {
            // Store first result set
            if ($result = $db->store_result()) {
                $result->free();
            }
            // Check for more results
        } while ($db->more_results() && $db->next_result());
        
        echo "<p style='color: green;'>✓ تم تحديث مبالغ العمليات بنجاح</p>";
    } else {
        throw new Exception("فشل في تحديث مبالغ العمليات: " . $db->error);
    }
    
    // Commit transaction
    $db->commit();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ تم تحديث مبالغ العمليات بنجاح</h3>";
    echo "<p style='color: #155724; margin: 0;'>تم تحديث مبالغ العمليات لتعكس طريقة الحساب الجديدة.</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ ملاحظات مهمة:</h4>";
    echo "<ul style='color: #856404; margin: 0;'>";
    echo "<li>تم تحديث مبالغ العمليات لتكون (المبلغ الأساسي - رسوم القص)</li>";
    echo "<li>العمليات التي ليس لها أسعار قص مطابقة تم تحديثها لتكون المبلغ الأساسي نفسه</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/offices.php' style='color: #007bff; text-decoration: none;'>→ الذهاب إلى صفحة المكاتب</a></p>";
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($db)) {
        $db->rollback();
    }
    
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في تحديث مبالغ العمليات</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
}
?> 
/*
 * إصلاحات شاملة للشريط الجانبي
 * Comprehensive sidebar fixes
 */

/* إصلاح التخطيط الأساسي */
#sidebar {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    width: 250px !important;
    height: 100vh !important;
    z-index: 1000 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    background: linear-gradient(180deg, #2c3e50 0%, #1a252f 100%) !important;
    color: #fff !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
}

/* إصلاح مشاكل النصوص */
#sidebar * {
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* إصلاح مشاكل التمرير */
#sidebar::-webkit-scrollbar {
    width: 6px;
}

#sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

#sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

#sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* إصلاح مشاكل الروابط */
.sidebar-link,
.sidebar-sub-link {
    display: flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.sidebar-link span,
.sidebar-sub-link span {
    flex: 1 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

.sidebar-link i,
.sidebar-sub-link i {
    flex-shrink: 0 !important;
    width: 1.5rem !important;
    text-align: center !important;
}

/* إصلاح مشاكل القوائم المنسدلة */
.sidebar-section {
    overflow: visible !important;
    position: relative !important;
}

.sidebar-sub-links {
    overflow: hidden !important;
    transition: max-height 0.3s ease, opacity 0.3s ease !important;
}

.sidebar-sub-links.collapsed {
    max-height: 0 !important;
    opacity: 0 !important;
}

/* إصلاح مشاكل PayPal */
.paypal-menu {
    overflow: visible !important;
    position: relative !important;
    z-index: 10 !important;
}

.paypal-submenu {
    overflow: visible !important;
    position: relative !important;
    z-index: 9 !important;
}

.paypal-submenu.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.paypal-submenu.collapse:not(.show) {
    display: none !important;
}

/* إصلاح مشاكل الأيقونات */
.paypal-icon-wrapper,
.paypal-subicon-wrapper {
    flex-shrink: 0 !important;
    overflow: hidden !important;
}

/* إصلاح مشاكل النصوص في PayPal */
.paypal-text,
.paypal-sublink span {
    flex: 1 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* إصلاح مشاكل الحدود والخطوط */
.paypal-menu,
.paypal-submenu,
.sidebar-section,
.sidebar-link,
.sidebar-sub-link {
    border: none !important;
    outline: none !important;
}

/* إصلاح مشاكل التفاعل */
.sidebar-section-header.collapsible {
    cursor: pointer !important;
    user-select: none !important;
}

.sidebar-section-header .toggle-icon {
    transition: transform 0.3s ease !important;
}

.sidebar-section-header.collapsed .toggle-icon {
    transform: rotate(-90deg) !important;
}

/* إصلاح مشاكل الاستجابة */
@media (max-width: 992px) {
    #sidebar {
        right: -250px !important;
        transition: right 0.3s ease !important;
    }
    
    #sidebar.active {
        right: 0 !important;
    }
}

/* إصلاح مشاكل المحتوى الرئيسي */
.main-content {
    margin-right: 250px !important;
    transition: margin-right 0.3s ease !important;
    min-height: 100vh !important;
    width: calc(100% - 250px) !important;
    position: relative !important;
}

/* إصلاح خاص لصفحة transfers */
body:has(.main-content) {
    margin-right: 0 !important;
}

@media (max-width: 992px) {
    .main-content {
        margin-right: 0 !important;
        width: 100% !important;
    }
}

/* إصلاح مشاكل الحاوي */
.container {
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

/* إصلاح مشاكل الفيض */
html, body {
    overflow-x: hidden !important;
}

/* إصلاح مشاكل الطباعة */
@media print {
    #sidebar {
        display: none !important;
    }
    
    body {
        margin-right: 0 !important;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
}

/* إصلاح مشاكل التركيز */
.sidebar-link:focus,
.sidebar-sub-link:focus,
.sidebar-section-header:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5) !important;
    outline-offset: 2px !important;
}

/* إصلاح مشاكل الحركة */
.sidebar-link,
.sidebar-sub-link,
.sidebar-section-header {
    transition: all 0.2s ease !important;
}

/* إصلاح مشاكل الألوان */
.sidebar-link:hover,
.sidebar-link.active,
.sidebar-sub-link:hover,
.sidebar-sub-link.active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #fff !important;
}

.sidebar-link.active,
.sidebar-sub-link.active {
    background-color: rgba(255, 255, 255, 0.15) !important;
    font-weight: 500 !important;
}

/* إصلاح مشاكل الخروج */
.sidebar .text-danger {
    color: #ff6b6b !important;
}

.sidebar .text-danger:hover {
    color: #ff8e8e !important;
    background-color: rgba(255, 107, 107, 0.1) !important;
}

/* إصلاحات إضافية للتأكد من عمل الشريط الجانبي */
.sidebar-dropdown .dropdown-toggle::after {
    display: none !important;
}

.sidebar-submenu.show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-height: none !important;
}

.sidebar-submenu.collapse:not(.show) {
    display: none !important;
    max-height: 0 !important;
    opacity: 0 !important;
}

/* إصلاح مشاكل Bootstrap */
.collapse.show {
    display: block !important;
}

.collapse:not(.show) {
    display: none !important;
}

/* إصلاح مشاكل التداخل مع Bootstrap */
#sidebar .collapse {
    transition: none !important;
}

#sidebar .collapsing {
    transition: none !important;
}

/* إصلاح مشاكل الأزرار */
.sidebar-toggle-btn {
    position: fixed !important;
    top: 10px !important;
    right: 10px !important;
    z-index: 1001 !important;
    display: none !important;
}

@media (max-width: 992px) {
    .sidebar-toggle-btn {
        display: block !important;
    }
}

/* إصلاح مشاكل النصوص العربية */
#sidebar * {
    direction: rtl !important;
    text-align: right !important;
}

/* إصلاح مشاكل الخطوط */
#sidebar {
    font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif !important;
}

/* إصلاح مشاكل التمرير النهائي */
#sidebar {
    scrollbar-width: thin !important;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1) !important;
}

/* إصلاح شامل للشريط الجانبي في جميع الصفحات */
#sidebar {
    visibility: visible !important;
    opacity: 1 !important;
    transform: translateX(0) !important;
}

/* إصلاح مشاكل التداخل مع المحتوى */
.page-wrapper {
    position: relative !important;
    width: 100% !important;
    min-height: 100vh !important;
}

/* إصلاح مشاكل الفيض العام */
html {
    overflow-x: hidden !important;
}

body {
    overflow-x: hidden !important;
    position: relative !important;
}

/* إصلاح مشاكل الحاوي */
.container {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* إصلاح مشاكل الجداول */
.table-responsive {
    overflow-x: auto !important;
    max-width: 100% !important;
}

/* إصلاح مشاكل النوافذ المنبثقة */
.modal {
    z-index: 1050 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* إصلاح مشاكل القوائم المنسدلة */
.dropdown-menu {
    z-index: 1020 !important;
}

/* إصلاح مشاكل التحميل */
.loading-overlay {
    z-index: 1030 !important;
}

/* إصلاح نهائي للتأكد من عمل الشريط الجانبي */
@media (min-width: 993px) {
    #sidebar {
        right: 0 !important;
        transform: translateX(0) !important;
    }

    .main-content {
        margin-right: 250px !important;
        width: calc(100% - 250px) !important;
    }
}

@media (max-width: 992px) {
    #sidebar {
        right: -250px !important;
        transform: translateX(0) !important;
    }

    #sidebar.active {
        right: 0 !important;
    }

    .main-content {
        margin-right: 0 !important;
        width: 100% !important;
    }
}

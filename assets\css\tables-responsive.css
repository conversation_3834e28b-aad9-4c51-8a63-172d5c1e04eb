/*
 * تحسينات الجداول المتجاوبة
 * Responsive Tables Enhancements
 */

/* ===== BASE TABLE STYLES ===== */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-color);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-light);
    text-align: right;
}

.table th {
    font-weight: 600;
    background-color: var(--bg-light);
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

/* ===== RESPONSIVE TABLE WRAPPER ===== */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table-responsive > .table {
    margin-bottom: 0;
    background-color: white;
}

/* ===== TABLET RESPONSIVE (768px - 991px) ===== */
@media (max-width: 991.98px) and (min-width: 768px) {
    .table th,
    .table td {
        padding: 0.6rem 0.5rem;
        font-size: 0.9rem;
    }
    
    .table th {
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    /* إخفاء بعض الأعمدة غير المهمة */
    .table .d-tablet-none {
        display: none;
    }
}

/* ===== MOBILE RESPONSIVE (max-width: 767px) ===== */
@media (max-width: 767.98px) {
    .table-responsive {
        border: none;
        box-shadow: none;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.875rem;
        white-space: nowrap;
    }
    
    .table th {
        font-size: 0.75rem;
        padding: 0.75rem 0.25rem;
    }
    
    /* تحسين التمرير الأفقي */
    .table-responsive::-webkit-scrollbar {
        height: 6px;
    }
    
    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
    
    /* إخفاء الأعمدة غير المهمة */
    .table .d-mobile-none {
        display: none;
    }
}

/* ===== VERY SMALL SCREENS (max-width: 575px) ===== */
@media (max-width: 575.98px) {
    /* إخفاء الجدول التقليدي */
    .table-mobile-stack {
        display: none !important;
    }
    
    /* إظهار عرض البطاقات */
    .table-mobile-cards {
        display: block !important;
    }
    
    /* تنسيق البطاقات */
    .mobile-card {
        background: white;
        border: 1px solid var(--border-light);
        border-radius: 10px;
        margin-bottom: 1rem;
        padding: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .mobile-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    }
    
    .mobile-card-header {
        font-weight: bold;
        color: var(--primary);
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--primary);
        font-size: 1.1rem;
    }
    
    .mobile-card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--bg-light);
    }
    
    .mobile-card-row:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }
    
    .mobile-card-label {
        font-weight: 500;
        color: var(--text-muted);
        font-size: 0.875rem;
        flex: 0 0 40%;
    }
    
    .mobile-card-value {
        font-weight: 500;
        text-align: left;
        flex: 1;
        margin-right: 0.5rem;
    }
    
    /* تحسين الشارات في البطاقات */
    .mobile-card-value .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* تحسين الأزرار في البطاقات */
    .mobile-card-value .btn-group {
        flex-direction: row;
        gap: 0.25rem;
    }
    
    .mobile-card-value .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* ===== TABLE ENHANCEMENTS ===== */
/* تحسين ألوان الصفوف */
.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover > tbody > tr:hover > td {
    background-color: rgba(var(--primary-rgb), 0.05);
    transition: background-color 0.3s ease;
}

/* تحسين الحدود */
.table-bordered {
    border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--border-light);
}

/* تحسين الجداول الداكنة */
.table-dark {
    background-color: var(--dark);
    color: white;
}

.table-dark th,
.table-dark td {
    border-color: rgba(255, 255, 255, 0.1);
}

/* ===== TABLE ACTIONS ===== */
.table-actions {
    white-space: nowrap;
    width: 1%;
}

.table-actions .btn-group {
    gap: 0.25rem;
}

@media (max-width: 767.98px) {
    .table-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .table-actions .btn i {
        font-size: 0.875rem;
    }
}

/* ===== TABLE SORTING ===== */
.table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.table th.sortable:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
}

.table th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    left: 0.5rem;
    opacity: 0.5;
    font-size: 0.75rem;
}

.table th.sortable.asc::after {
    content: '\f0de';
    opacity: 1;
    color: var(--primary);
}

.table th.sortable.desc::after {
    content: '\f0dd';
    opacity: 1;
    color: var(--primary);
}

/* ===== TABLE LOADING STATE ===== */
.table-loading {
    position: relative;
    overflow: hidden;
}

.table-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(var(--primary-rgb), 0.1),
        transparent
    );
    animation: tableLoading 1.5s infinite;
    z-index: 1;
}

@keyframes tableLoading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* ===== TABLE EMPTY STATE ===== */
.table-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.table-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.table-empty h5 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.table-empty p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

/* ===== TABLE PAGINATION ===== */
.table-pagination {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-top: 1rem;
    padding: 1rem 0;
    border-top: 1px solid var(--border-light);
}

.table-pagination-info {
    color: var(--text-muted);
    font-size: 0.875rem;
}

@media (max-width: 575.98px) {
    .table-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .table-responsive {
        overflow: visible !important;
    }
    
    .table {
        border-collapse: collapse !important;
        font-size: 0.8rem !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
        padding: 0.25rem !important;
    }
    
    .table-actions,
    .d-print-none {
        display: none !important;
    }
    
    .mobile-card {
        break-inside: avoid;
        border: 1px solid #000 !important;
        margin-bottom: 0.5rem !important;
    }
}

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/office_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('offices.operations.create');

$officeMgr = new OfficeManager(new Database());
$errors = [];

// Get office ID
$officeId = (int)($_GET['office_id'] ?? 0);
if (!$officeId) {
    set_flash('danger', 'معرف المكتب غير صحيح');
    redirect('offices.php');
}

// Get office details
$office = $officeMgr->getOfficeById($officeId);
if (!$office) {
    set_flash('danger', 'المكتب غير موجود');
    redirect('offices.php');
}

// Get exchange rates for this office
$exchangeRates = $officeMgr->getOfficeExchangeRates($officeId);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf = $_POST['csrf_token'] ?? '';
    if (!verify_csrf_token($csrf)) {
        $errors[] = 'CSRF غير صالح';
    } else {
        // Validate and sanitize input
        $exchangeRateId = (int)($_POST['exchange_rate_id'] ?? 0);
        $baseAmount = (float)($_POST['base_amount'] ?? 0);
        $trackingNumber = trim($_POST['tracking_number'] ?? '');
        $isCredit = (isset($_POST['is_credit']) && $_POST['is_credit'] == '1') ? 1 : 0;
        $operationType = $_POST['operation_type'] ?? 'multiply';

        // Validation
        if ($exchangeRateId <= 0) {
            $errors[] = 'يرجى اختيار نوع العملية';
        }

        if ($baseAmount <= 0) {
            $errors[] = 'المبلغ يجب أن يكون أكبر من صفر';
        }

        if (empty($errors)) {
            // Get the selected exchange rate
            $selectedRate = null;
            foreach ($exchangeRates as $rate) {
                if ($rate['id'] == $exchangeRateId) {
                    $selectedRate = $rate;
                    break;
                }
            }

            if (!$selectedRate) {
                $errors[] = 'سعر القص غير موجود';
            } else {
                // Calculate the amount based on operation type
                if ($operationType === 'multiply') {
                    // ضرب: أسعار القص × المبلغ الأساسي
                    $finalAmount = $selectedRate['exchange_rate_percentage'] * $baseAmount;
                } else {
                    // قسمة: المبلغ الأساسي ÷ سعر القص
                    if ($selectedRate['exchange_rate_percentage'] != 0) {
                        $finalAmount = $baseAmount / $selectedRate['exchange_rate_percentage'];
                    } else {
                        $errors[] = 'لا يمكن القسمة على صفر - سعر القص يجب أن يكون أكبر من صفر';
                        $finalAmount = 0;
                    }
                }

                if (empty($errors)) {
                    $operationData = [
                        'office_id' => $officeId,
                        'operation_name' => $selectedRate['operation_name'],
                        'tracking_number' => $trackingNumber,
                        'base_amount' => $baseAmount,
                        'amount' => $finalAmount,
                        'operation_type' => $operationType,
                        'is_credit' => $isCredit
                    ];
                }

                $newOperationId = $officeMgr->addOperation($operationData);
                if ($newOperationId) {
                    log_activity($auth->getCurrentUser()['id'], 'office.operation.create', [
                        'operation_id' => $newOperationId,
                        'office_id' => $officeId
                    ]);
                    set_flash('success', 'تم إضافة العملية بنجاح');
                    redirect("office_details.php?id=$officeId");
                } else {
                    $errors[] = 'حدث خطأ أثناء حفظ العملية';
                }
            }
        }
    }
}

$csrf = get_csrf_token();
$pageTitle = 'إضافة عملية جديدة - ' . $office['office_name'];
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container p-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-plus"></i> إضافة عملية جديدة
                    </h4>
                    <small class="text-muted">المكتب: <?php echo htmlspecialchars($office['office_name']); ?></small>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($exchangeRates)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            لا توجد أسعار قص مضافة لهذا المكتب. 
                            <a href="add_exchange_rate.php?office_id=<?php echo $officeId; ?>" class="alert-link">
                                أضف سعر قص جديد أولاً
                            </a>
                        </div>
                    <?php else: ?>
                        <form method="post" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="exchange_rate_id" class="form-label">نوع العملية <span class="text-danger">*</span></label>
                                    <select class="form-select" id="exchange_rate_id" name="exchange_rate_id" required>
                                        <option value="">-- اختر نوع العملية --</option>
                                        <?php foreach($exchangeRates as $rate): ?>
                                            <?php if($rate['is_active']): ?>
                                                <option value="<?php echo $rate['id']; ?>"
                                                    data-rate="<?php echo $rate['exchange_rate_percentage']; ?>">
                                                    <?php echo htmlspecialchars($rate['operation_name']); ?>
                                                    (<?php echo $rate['exchange_rate_percentage']; ?>)
                                                </option>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">اختر نوع العملية من أسعار القص</div>
                                    <div class="invalid-feedback">
                                        يرجى اختيار نوع العملية
                                    </div>
                                </div>
                                
                                <div class="col-12 col-sm-6 col-md-6 col-lg-4 col-xl-6 mb-3">
                                    <label for="base_amount" class="form-label">المبلغ الأساسي <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control form-control-responsive" id="base_amount"
                                               name="base_amount"
                                               value="<?php echo htmlspecialchars($_POST['base_amount'] ?? ''); ?>"
                                               step="0.01" min="0.01" required>
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <div class="form-text">أدخل المبلغ الأساسي بالدولار</div>
                                    <div class="invalid-feedback">
                                        المبلغ مطلوب ويجب أن يكون أكبر من صفر
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="operation_type" class="form-label">نوع العملية الحسابية <span class="text-danger">*</span></label>
                                    <select class="form-select" id="operation_type" name="operation_type" required>
                                        <option value="multiply" <?php echo (!isset($_POST['operation_type']) || $_POST['operation_type'] === 'multiply') ? 'selected' : ''; ?>>
                                            ضرب (أسعار القص × المبلغ الأساسي)
                                        </option>
                                        <option value="divide" <?php echo (isset($_POST['operation_type']) && $_POST['operation_type'] === 'divide') ? 'selected' : ''; ?>>
                                            قسمة (المبلغ الأساسي ÷ سعر القص)
                                        </option>
                                    </select>
                                    <div class="form-text">اختر نوع العملية الحسابية</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="tracking_number" class="form-label">رقم التتبع</label>
                                    <input type="text" class="form-control" id="tracking_number" name="tracking_number" value="<?php echo htmlspecialchars($_POST['tracking_number'] ?? ''); ?>" maxlength="255">
                                    <div class="form-text">يمكن تركه فارغًا إذا لم يتوفر</div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label d-block">نوع العملية <span class="text-danger">*</span></label>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="is_credit" id="is_credit_yes" value="1" 
                                           <?php echo (!isset($_POST['is_credit']) || $_POST['is_credit']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_credit_yes">
                                        <span class="badge bg-success">لنا</span>
                                        <small class="text-muted me-2">(زيادة في الحساب الخاص بي)</small>
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="is_credit" id="is_credit_no" value="0"
                                           <?php echo (isset($_POST['is_credit']) && !$_POST['is_credit']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_credit_no">
                                        <span class="badge bg-danger">لكم</span>
                                        <small class="text-muted me-2">(نقصان في الحساب)</small>
                                    </label>
                                </div>
                            </div>

                            <!-- Preview Section -->
                            <div class="alert alert-info mb-4">
                                <h6><i class="fas fa-info-circle"></i> معاينة العملية</h6>
                                <div id="operation-preview">
                                    <p class="mb-1">العملية: <strong id="preview-operation">-</strong></p>
                                    <p class="mb-1">المبلغ الأساسي: <strong id="preview-base-amount">0.00</strong> $</p>
                                    <p class="mb-1">سعر القص: <strong id="preview-rate">0.00</strong></p>
                                    <p class="mb-1">العملية الحسابية: <strong id="preview-fee">-</strong></p>
                                    <p class="mb-1">النتيجة النهائية: <strong id="preview-final-amount">0.00</strong> $</p>
                                    <p class="mb-0">تصنيف العملية: <strong id="preview-type">لنا (زيادة في الحساب)</strong></p>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="office_details.php?id=<?php echo $office['id']; ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right"></i> رجوع
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ العملية
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
        
        // Live preview
        const rateSelect = document.getElementById('exchange_rate_id');
        const baseAmountInput = document.getElementById('base_amount');
        const operationTypeSelect = document.getElementById('operation_type');
        const previewOperation = document.getElementById('preview-operation');
        const previewBaseAmount = document.getElementById('preview-base-amount');
        const previewRate = document.getElementById('preview-rate');
        const previewFee = document.getElementById('preview-fee');
        const previewFinalAmount = document.getElementById('preview-final-amount');
        const previewType = document.getElementById('preview-type');
        const creditRadio = document.getElementById('is_credit_yes');
        const debitRadio = document.getElementById('is_credit_no');
        
        function updatePreview() {
            const baseAmount = parseFloat(baseAmountInput.value) || 0;
            const operationType = operationTypeSelect.value;
            previewBaseAmount.textContent = baseAmount.toFixed(2);

            let rate = 0;
            let operationName = '-';

            if (rateSelect.selectedIndex > 0) {
                const selectedOption = rateSelect.options[rateSelect.selectedIndex];
                rate = parseFloat(selectedOption.dataset.rate) || 0;
                operationName = selectedOption.text;
            }

            previewOperation.textContent = operationName;
            previewRate.textContent = rate.toFixed(2);

            // Calculate final amount based on operation type
            let finalAmount = 0;
            let operationText = '';

            if (operationType === 'multiply') {
                // ضرب: أسعار القص × المبلغ الأساسي
                finalAmount = rate * baseAmount;
                operationText = `${rate.toFixed(2)} × ${baseAmount.toFixed(2)} = ${finalAmount.toFixed(2)}`;
            } else {
                // قسمة: المبلغ الأساسي ÷ سعر القص
                if (rate !== 0) {
                    finalAmount = baseAmount / rate;
                    operationText = `${baseAmount.toFixed(2)} ÷ ${rate.toFixed(2)} = ${finalAmount.toFixed(2)}`;
                } else {
                    finalAmount = 0;
                    operationText = 'لا يمكن القسمة على صفر - سعر القص يجب أن يكون أكبر من صفر';
                }
            }

            previewFee.textContent = operationText;
            previewFinalAmount.textContent = finalAmount.toFixed(2);

            if (creditRadio.checked) {
                previewType.textContent = 'لنا (زيادة في الحساب)';
                previewType.className = 'text-success';
            } else {
                previewType.textContent = 'لكم (نقصان في الحساب)';
                previewType.className = 'text-danger';
            }
        }
        
        rateSelect.addEventListener('change', updatePreview);
        baseAmountInput.addEventListener('input', updatePreview);
        operationTypeSelect.addEventListener('change', updatePreview);
        creditRadio.addEventListener('change', updatePreview);
        debitRadio.addEventListener('change', updatePreview);
        
        // Initialize preview
        updatePreview();
    });
})();
</script>

<style>
/* Responsive styles for base_amount field */
.form-control-responsive {
    transition: all 0.3s ease;
}

/* Extra small devices (phones, less than 576px) */
@media (max-width: 575.98px) {
    .form-control-responsive {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem 0.5rem;
    }

    .input-group .form-control-responsive {
        border-radius: 0.375rem 0 0 0.375rem;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .form-control-responsive {
        font-size: 15px;
        padding: 0.625rem 0.75rem;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .form-control-responsive {
        font-size: 14px;
        padding: 0.5rem 0.75rem;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .form-control-responsive {
        font-size: 14px;
        padding: 0.5rem 1rem;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .form-control-responsive {
        font-size: 16px;
        padding: 0.625rem 1rem;
    }
}

/* Focus states for better accessibility */
.form-control-responsive:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
}

/* RTL support for Arabic text */
.form-control-responsive {
    direction: ltr; /* Keep numbers left-to-right */
    text-align: left;
}
</style>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
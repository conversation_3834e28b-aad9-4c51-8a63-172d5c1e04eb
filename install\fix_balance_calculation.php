<?php
/**
 * Fix balance calculation in cash movements
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إصلاح حساب الرصيد في الحركات المالية</h2>\n";

try {
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص الحركات الحالية</h3>\n";
    
    // Get movements with current balance calculation
    $result = $conn->query("
        SELECT cm.id, cm.cash_box_id, cm.movement_type, cm.amount, cm.created_at,
               cb.name as cash_box_name,
               (SELECT cb.initial_balance FROM cash_boxes cb WHERE cb.id = cm.cash_box_id) as initial_balance
        FROM cash_movements cm
        LEFT JOIN cash_boxes cb ON cb.id = cm.cash_box_id
        ORDER BY cm.cash_box_id, cm.created_at, cm.id
        LIMIT 10
    ");
    
    if ($result) {
        echo "<p><strong>عينة من الحركات:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID</th><th>الصندوق</th><th>النوع</th><th>المبلغ</th><th>التاريخ</th><th>الرصيد الابتدائي</th>\n";
        echo "</tr>\n";
        
        $movements = [];
        while ($row = $result->fetch_assoc()) {
            $movements[] = $row;
            echo "<tr>\n";
            echo "<td>{$row['id']}</td>\n";
            echo "<td>{$row['cash_box_name']}</td>\n";
            echo "<td>{$row['movement_type']}</td>\n";
            echo "<td>{$row['amount']}</td>\n";
            echo "<td>{$row['created_at']}</td>\n";
            echo "<td>{$row['initial_balance']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>2. حساب الرصيد الصحيح لكل حركة</h3>\n";
    
    // Calculate correct balance for each movement
    if (!empty($movements)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID الحركة</th><th>النوع</th><th>المبلغ</th><th>الرصيد المحسوب</th><th>الحالة</th>\n";
        echo "</tr>\n";
        
        foreach ($movements as $movement) {
            // Calculate balance up to this movement
            $balanceQuery = "
                SELECT 
                    (SELECT initial_balance FROM cash_boxes WHERE id = ?) +
                    COALESCE(SUM(CASE 
                        WHEN movement_type = 'deposit' THEN amount 
                        ELSE -amount 
                    END), 0) as calculated_balance
                FROM cash_movements 
                WHERE cash_box_id = ? 
                AND (created_at < ? OR (created_at = ? AND id <= ?))
            ";
            
            $stmt = $conn->prepare($balanceQuery);
            $stmt->bind_param('iissi', 
                $movement['cash_box_id'], 
                $movement['cash_box_id'], 
                $movement['created_at'], 
                $movement['created_at'], 
                $movement['id']
            );
            $stmt->execute();
            $balanceResult = $stmt->get_result();
            $calculatedBalance = $balanceResult->fetch_assoc()['calculated_balance'];
            $stmt->close();
            
            echo "<tr>\n";
            echo "<td>{$movement['id']}</td>\n";
            echo "<td>{$movement['movement_type']}</td>\n";
            echo "<td>{$movement['amount']}</td>\n";
            echo "<td>" . number_format($calculatedBalance, 2) . "</td>\n";
            echo "<td style='color: green;'>✓ محسوب</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>3. تحديث دالة getCashBoxMovements</h3>\n";
    
    // Test the improved query
    $improvedQuery = "
        SELECT cm.*, u.full_name AS user_name, cb.name as cash_box_name,
               curr.code as currency_code, curr.symbol as currency_symbol,
               cm.movement_type as type, cm.exchange_id, cm.transfer_id,
               (
                   SELECT 
                       cb.initial_balance + 
                       COALESCE(SUM(CASE 
                           WHEN cm2.movement_type = 'deposit' THEN cm2.amount 
                           ELSE -cm2.amount 
                       END), 0)
                   FROM cash_movements cm2 
                   WHERE cm2.cash_box_id = cm.cash_box_id 
                   AND (cm2.created_at < cm.created_at OR (cm2.created_at = cm.created_at AND cm2.id <= cm.id))
               ) as balance_after
        FROM cash_movements cm
        LEFT JOIN users u ON u.id = cm.user_id
        LEFT JOIN cash_boxes cb ON cb.id = cm.cash_box_id
        LEFT JOIN currencies curr ON curr.id = cb.currency_id
        WHERE cm.cash_box_id = ?
        ORDER BY cm.created_at DESC, cm.id DESC
        LIMIT 5
    ";
    
    echo "<p><strong>الاستعلام المحسن:</strong></p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-size: 12px;'>";
    echo htmlspecialchars($improvedQuery);
    echo "</pre>\n";
    
    // Test with a cash box
    $cashBoxResult = $conn->query("SELECT id FROM cash_boxes ORDER BY id DESC LIMIT 1");
    if ($cashBoxResult && $cashBoxResult->num_rows > 0) {
        $cashBoxId = $cashBoxResult->fetch_assoc()['id'];
        
        echo "<h3>4. اختبار الاستعلام المحسن</h3>\n";
        
        $stmt = $conn->prepare($improvedQuery);
        if ($stmt) {
            $stmt->bind_param('i', $cashBoxId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            echo "<p><strong>نتائج الاستعلام المحسن للصندوق $cashBoxId:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>ID</th><th>النوع</th><th>المبلغ</th><th>الوصف</th><th>المرجع</th><th>المستخدم</th><th>الرصيد بعد</th>\n";
            echo "</tr>\n";
            
            while ($row = $result->fetch_assoc()) {
                echo "<tr>\n";
                echo "<td>{$row['id']}</td>\n";
                echo "<td>{$row['type']}</td>\n";
                echo "<td>" . number_format($row['amount'], 2) . "</td>\n";
                echo "<td>" . htmlspecialchars($row['description'] ?: '-') . "</td>\n";
                echo "<td>" . htmlspecialchars($row['reference_number'] ?: '-') . "</td>\n";
                echo "<td>" . htmlspecialchars($row['user_name'] ?: 'غير محدد') . "</td>\n";
                echo "<td>" . number_format($row['balance_after'], 2) . "</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
            
            $stmt->close();
        } else {
            echo "<p style='color: red;'>✗ فشل في تحضير الاستعلام المحسن</p>\n";
        }
    }
    
    echo "<h3>5. إنشاء ملف إصلاح CashManager</h3>\n";
    
    $fixCode = '
// إصلاح دالة getCashBoxMovements في CashManager
public function getCashBoxMovements(int $boxId, array $filters = [], int $limit = 0, int $offset = 0): array
{
    $sql = \'SELECT cm.*, u.full_name AS user_name, cb.name as cash_box_name,
                   curr.code as currency_code, curr.symbol as currency_symbol,
                   cm.movement_type as type, cm.exchange_id, cm.transfer_id,
                   (
                       SELECT 
                           cb.initial_balance + 
                           COALESCE(SUM(CASE 
                               WHEN cm2.movement_type = "deposit" THEN cm2.amount 
                               ELSE -cm2.amount 
                           END), 0)
                       FROM cash_movements cm2 
                       WHERE cm2.cash_box_id = cm.cash_box_id 
                       AND (cm2.created_at < cm.created_at OR (cm2.created_at = cm.created_at AND cm2.id <= cm.id))
                   ) as balance_after
            FROM cash_movements cm
            LEFT JOIN users u ON u.id = cm.user_id
            LEFT JOIN cash_boxes cb ON cb.id = cm.cash_box_id
            LEFT JOIN currencies curr ON curr.id = cb.currency_id
            WHERE cm.cash_box_id = ?\';
    // ... باقي الكود
}
';
    
    echo "<p><strong>الكود المطلوب للإصلاح:</strong></p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-size: 12px;'>";
    echo htmlspecialchars($fixCode);
    echo "</pre>\n";
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم تشخيص وإعداد إصلاح حساب الرصيد</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>تطبيق الإصلاح على ملف CashManager</li>\n";
    echo "<li>اختبار عرض الحركات مرة أخرى</li>\n";
    echo "<li>التحقق من صحة حساب الأرصدة</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

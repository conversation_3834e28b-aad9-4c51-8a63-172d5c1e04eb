<?php
/**
 * Fix customers table status column issue
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = Database::getConnection();
    
    echo "<h2>إصلاح مشكلة عمود status في جدول customers</h2>\n";
    
    // Check if status column exists
    $result = $db->query("SHOW COLUMNS FROM customers LIKE 'status'");
    
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✓ عمود status موجود في جدول customers</p>\n";
        
        // Check current values
        $result = $db->query("SELECT DISTINCT status FROM customers");
        if ($result) {
            echo "<p><strong>القيم الحالية لعمود status:</strong></p>\n";
            while ($row = $result->fetch_assoc()) {
                echo "<p>- " . ($row['status'] ?? 'NULL') . "</p>\n";
            }
        }
        
        // Update NULL values to 'active'
        $updateResult = $db->query("UPDATE customers SET status = 'active' WHERE status IS NULL OR status = ''");
        if ($updateResult) {
            echo "<p style='color: green;'>✓ تم تحديث القيم الفارغة إلى 'active'</p>\n";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠ عمود status غير موجود، سيتم إضافته</p>\n";
        
        // Add status column
        $addColumnQuery = "ALTER TABLE customers ADD COLUMN status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' AFTER phone";
        if ($db->query($addColumnQuery)) {
            echo "<p style='color: green;'>✓ تم إضافة عمود status بنجاح</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في إضافة عمود status: " . $db->error . "</p>\n";
        }
    }
    
    // Verify the fix
    $result = $db->query("SELECT COUNT(*) as count FROM customers WHERE status = 'active'");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<p style='color: blue;'>ℹ️ عدد العملاء النشطين: $count</p>\n";
    }
    
    echo "<p style='color: green; font-weight: bold;'>✅ تم إصلاح مشكلة عمود status</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2 { color: #333; }
p { margin: 5px 0; }
</style>

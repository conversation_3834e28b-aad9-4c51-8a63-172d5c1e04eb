<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/user_manager.php';
require_once __DIR__ . '/../includes/role_manager.php';
require_once __DIR__ . '/../includes/branch_manager.php';
require_once __DIR__ . '/../includes/image_helper.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('users.edit');

$current_user = $auth->getCurrentUser();

$db           = new Database();
$userManager  = new UserManager($db);
$roleManager  = new RoleManager($db);
$branchMgr    = new BranchManager($db);

$user_id = isset($_GET['id']) ? (int) $_GET['id'] : 0;
if ($user_id <= 0) {
    echo '<div class="alert alert-danger m-4">معرّف المستخدم غير صالح</div>';
    exit;
}

$existingUser = $userManager->getUserById($user_id);
if (!$existingUser) {
    echo '<div class="alert alert-danger m-4">المستخدم غير موجود</div>';
    exit;
}

$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF check
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $errors[] = 'رمز CSRF غير صالح.';
    }

    // Sanitize inputs
    $username   = sanitize_input($_POST['username'] ?? '');
    $full_name  = sanitize_input($_POST['full_name'] ?? '');
    $email      = sanitize_input($_POST['email'] ?? '');
    $phone      = sanitize_input($_POST['phone'] ?? '');
    $password   = $_POST['password'] ?? '';
    $password2  = $_POST['confirm_password'] ?? '';
    $role_id    = (int) ($_POST['role_id'] ?? 0);
    $branch_id  = (int) ($_POST['branch_id'] ?? 0);
    $status     = sanitize_input($_POST['status'] ?? 'active');

    // Validation
    if (empty($username))   $errors[] = 'اسم المستخدم مطلوب';
    if ($password !== '') {
        if ($password !== $password2) $errors[] = 'كلمتا المرور غير متطابقتين';
    }
    if ($role_id <= 0)      $errors[] = 'الرجاء اختيار دور';
    if ($branch_id <= 0)    $errors[] = 'الرجاء اختيار فرع';

    if ($userManager->userExists($username, $user_id)) {
        $errors[] = 'اسم المستخدم مستخدم مسبقاً';
    }

    if (empty($errors)) {
        $updateData = [
            'username'   => $username,
            'full_name'  => $full_name,
            'email'      => $email,
            'phone'      => $phone,
            'role_id'    => $role_id,
            'branch_id'  => $branch_id,
            'status'     => $status,
        ];
        if ($password !== '') {
            $updateData['password'] = $password; // hashing handled in manager
        }

        // Handle profile image upload
        if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
            $imagePath = ImageHelper::uploadProfileImage($_FILES['profile_image'], $user_id);
            if ($imagePath) {
                // Delete old image if exists
                if (!empty($existingUser['profile_image'])) {
                    ImageHelper::deleteProfileImage($existingUser['profile_image']);
                }
                $updateData['profile_image'] = $imagePath;
            }
        }

        // Handle image deletion
        if (isset($_POST['delete_image']) && $_POST['delete_image'] === '1') {
            if (!empty($existingUser['profile_image'])) {
                ImageHelper::deleteProfileImage($existingUser['profile_image']);
                $updateData['profile_image'] = null;
            }
        }

        $oldData = $existingUser; // before update
        $ok = $userManager->updateUser($user_id, $updateData);
        if ($ok) {
            // تسجيل عملية تعديل المستخدم
            ActivityHelper::logUpdate(
                'users',
                $existingUser['username'] . ' (' . $existingUser['full_name'] . ')',
                $oldData,
                $updateData,
                $user_id
            );
            header('Location: users.php?msg=updated');
            exit;
        } else {
            $errors[] = 'حدث خطأ أثناء تحديث المستخدم';
        }
    }
} else {
    // Pre-fill form with existing data
    $_POST = $existingUser;
}

$roles    = $roleManager->getAllRoles();
$branches = $branchMgr->getAllBranches();

$pageTitle = 'تعديل مستخدم';
include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <h2 class="mb-4">تعديل مستخدم</h2>

    <?php if ($errors): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $e): ?>
                    <li><?php echo $e; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <form method="post" enctype="multipart/form-data" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
        <input type="hidden" name="id" value="<?php echo $user_id; ?>">

        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <input type="text" name="username" class="form-control" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                </div>

                <div class="mb-3">
                    <label class="form-label">الاسم الكامل</label>
                    <input type="text" name="full_name" class="form-control" value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>">
                </div>

                <div class="mb-3">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                </div>

                <div class="mb-3">
                    <label class="form-label">الهاتف</label>
                    <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">كلمة المرور الجديدة (اتركها فارغة للإبقاء عليها)</label>
                        <input type="password" name="password" class="form-control">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">تأكيد كلمة المرور</label>
                        <input type="password" name="confirm_password" class="form-control">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الدور</label>
                        <select name="role_id" class="form-select" required>
                            <option value="">-- اختر --</option>
                            <?php foreach ($roles as $r): ?>
                                <option value="<?php echo $r['id']; ?>" <?php echo (($_POST['role_id'] ?? $existingUser['role_id']) == $r['id']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($r['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الفرع</label>
                        <select name="branch_id" class="form-select" required>
                            <option value="">-- اختر --</option>
                            <?php foreach ($branches as $b): ?>
                                <option value="<?php echo $b['id']; ?>" <?php echo (($_POST['branch_id'] ?? $existingUser['branch_id']) == $b['id']) ? 'selected' : ''; ?>><?php echo htmlspecialchars($b['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <?php $selectedStatus = $_POST['status'] ?? $existingUser['status']; ?>
                        <option value="active" <?php echo ($selectedStatus === 'active') ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo ($selectedStatus === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                        <option value="blocked" <?php echo ($selectedStatus === 'blocked') ? 'selected' : ''; ?>>محظور</option>
                    </select>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">صورة الملف الشخصي</h5>
                    </div>
                    <div class="card-body">
                        <!-- Current Image -->
                        <?php if (!empty($existingUser['profile_image'])): ?>
                            <div class="text-center mb-3">
                                <?php echo ImageHelper::getProfileImageHtmlWithClasses($existingUser['profile_image'], $user_id, 'profile-image-xl', ''); ?>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-danger" onclick="deleteImage()">حذف الصورة</button>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label class="form-label">اختر صورة جديدة</label>
                            <input type="file" name="profile_image" class="form-control" accept="image/*">
                            <div class="form-text">الأنواع المسموحة: JPG, PNG, GIF. الحد الأقصى: 5MB</div>
                        </div>
                        
                        <div id="image-preview" class="text-center" style="display: none;">
                            <img id="preview-img" src="" alt="معاينة الصورة" class="profile-image profile-image-xl">
                        </div>

                        <input type="hidden" name="delete_image" id="delete_image" value="0">
                    </div>
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-primary">تحديث</button>
        <a href="users.php" class="btn btn-secondary">إلغاء</a>
    </form>
</main>

<script>
// Image preview functionality
document.querySelector('input[name="profile_image"]').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});

// Delete image functionality
function deleteImage() {
    if (confirm('هل أنت متأكد من حذف الصورة؟')) {
        document.getElementById('delete_image').value = '1';
        // Hide current image
        const currentImage = document.querySelector('.card-body img');
        if (currentImage) {
            currentImage.style.display = 'none';
        }
    }
}
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?> 
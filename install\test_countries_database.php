<?php
/**
 * اختبار جدول الدول في قاعدة البيانات
 * Test Countries Database Table
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار جدول الدول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .table-responsive { max-height: 400px; overflow-y: auto; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-database text-primary"></i>
            اختبار جدول الدول في قاعدة البيانات
        </h1>
        
        <?php
        try {
            $pdo = PDODatabase::getConnection();
            echo '<div class="alert alert-success">';
            echo '<i class="fas fa-check-circle me-2"></i>';
            echo '<strong>✅ تم الاتصال بقاعدة البيانات بنجاح</strong>';
            echo '</div>';
            
            // فحص وجود جدول countries
            $stmt = $pdo->query("SHOW TABLES LIKE 'countries'");
            $table_exists = $stmt->rowCount() > 0;
            
            if ($table_exists) {
                echo '<div class="alert alert-success">';
                echo '<i class="fas fa-table me-2"></i>';
                echo '<strong>✅ جدول countries موجود</strong>';
                echo '</div>';
                
                // فحص بنية الجدول
                echo '<div class="card mb-4">';
                echo '<div class="card-header bg-info text-white">';
                echo '<h5 class="mb-0"><i class="fas fa-columns me-2"></i>بنية جدول countries</h5>';
                echo '</div>';
                echo '<div class="card-body">';
                
                $stmt = $pdo->query("DESCRIBE countries");
                $columns = $stmt->fetchAll();
                
                echo '<div class="table-responsive">';
                echo '<table class="table table-striped">';
                echo '<thead class="table-dark">';
                echo '<tr><th>اسم العمود</th><th>نوع البيانات</th><th>يمكن أن يكون فارغ</th><th>المفتاح</th><th>القيمة الافتراضية</th></tr>';
                echo '</thead>';
                echo '<tbody>';
                
                foreach ($columns as $column) {
                    echo '<tr>';
                    echo '<td><code>' . htmlspecialchars($column['Field']) . '</code></td>';
                    echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
                    echo '<td>' . ($column['Null'] == 'YES' ? 'نعم' : 'لا') . '</td>';
                    echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                    echo '</tr>';
                }
                
                echo '</tbody>';
                echo '</table>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                
                // فحص البيانات الموجودة
                $stmt = $pdo->query("SELECT COUNT(*) as total_count FROM countries");
                $total_countries = $stmt->fetch()['total_count'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as active_count FROM countries WHERE is_active = 1");
                $active_countries = $stmt->fetch()['active_count'];
                
                echo '<div class="row mb-4">';
                echo '<div class="col-md-6">';
                echo '<div class="card border-primary">';
                echo '<div class="card-body text-center">';
                echo '<h3 class="text-primary">' . $total_countries . '</h3>';
                echo '<p class="mb-0">إجمالي الدول</p>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                
                echo '<div class="col-md-6">';
                echo '<div class="card border-success">';
                echo '<div class="card-body text-center">';
                echo '<h3 class="text-success">' . $active_countries . '</h3>';
                echo '<p class="mb-0">الدول النشطة</p>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
                
                if ($active_countries > 0) {
                    // عرض الدول النشطة
                    echo '<div class="card mb-4">';
                    echo '<div class="card-header bg-success text-white">';
                    echo '<h5 class="mb-0"><i class="fas fa-globe me-2"></i>الدول النشطة المتاحة للبحث</h5>';
                    echo '</div>';
                    echo '<div class="card-body">';
                    
                    $stmt = $pdo->query("SELECT id, name_ar, currency_code, currency_symbol FROM countries WHERE is_active = 1 ORDER BY name_ar");
                    $countries = $stmt->fetchAll();
                    
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-hover">';
                    echo '<thead class="table-success">';
                    echo '<tr><th>ID</th><th>اسم الدولة</th><th>رمز العملة</th><th>رمز العملة</th><th>نص البحث</th></tr>';
                    echo '</thead>';
                    echo '<tbody>';
                    
                    foreach ($countries as $country) {
                        $searchText = $country['name_ar'] . ' ' . $country['currency_code'] . ' ' . $country['currency_symbol'];
                        echo '<tr>';
                        echo '<td><span class="badge bg-primary">' . $country['id'] . '</span></td>';
                        echo '<td><strong>' . htmlspecialchars($country['name_ar']) . '</strong></td>';
                        echo '<td><code>' . htmlspecialchars($country['currency_code']) . '</code></td>';
                        echo '<td>' . htmlspecialchars($country['currency_symbol']) . '</td>';
                        echo '<td><small class="text-muted">' . htmlspecialchars($searchText) . '</small></td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody>';
                    echo '</table>';
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';
                    
                    // اختبار الاستعلام المستخدم في النظام
                    echo '<div class="card mb-4">';
                    echo '<div class="card-header bg-warning text-dark">';
                    echo '<h5 class="mb-0"><i class="fas fa-code me-2"></i>اختبار الاستعلام المستخدم في النظام</h5>';
                    echo '</div>';
                    echo '<div class="card-body">';
                    
                    echo '<div class="code-block mb-3">';
                    echo '<strong>الاستعلام المستخدم:</strong><br>';
                    echo '<code>SELECT id, name_ar, currency_code, currency_symbol FROM countries WHERE is_active = 1 ORDER BY name_ar</code>';
                    echo '</div>';
                    
                    echo '<div class="alert alert-success">';
                    echo '<i class="fas fa-check-circle me-2"></i>';
                    echo '<strong>✅ الاستعلام يعمل بشكل صحيح ويجلب ' . count($countries) . ' دولة نشطة</strong>';
                    echo '</div>';
                    
                    // عرض عينة من البيانات كما تظهر في JavaScript
                    echo '<h6>عينة من البيانات كما تظهر في JavaScript:</h6>';
                    echo '<div class="code-block">';
                    echo '<code>';
                    echo 'const countriesData = [<br>';
                    $sample_countries = array_slice($countries, 0, 3);
                    foreach ($sample_countries as $index => $country) {
                        echo '&nbsp;&nbsp;{<br>';
                        echo '&nbsp;&nbsp;&nbsp;&nbsp;id: ' . $country['id'] . ',<br>';
                        echo '&nbsp;&nbsp;&nbsp;&nbsp;name_ar: "' . htmlspecialchars($country['name_ar']) . '",<br>';
                        echo '&nbsp;&nbsp;&nbsp;&nbsp;currency_code: "' . htmlspecialchars($country['currency_code']) . '",<br>';
                        echo '&nbsp;&nbsp;&nbsp;&nbsp;currency_symbol: "' . htmlspecialchars($country['currency_symbol']) . '",<br>';
                        echo '&nbsp;&nbsp;&nbsp;&nbsp;searchText: "' . htmlspecialchars($searchText) . '"<br>';
                        echo '&nbsp;&nbsp;}' . ($index < count($sample_countries) - 1 ? ',' : '') . '<br>';
                    }
                    if (count($countries) > 3) {
                        echo '&nbsp;&nbsp;// ... و ' . (count($countries) - 3) . ' دولة أخرى<br>';
                    }
                    echo '];';
                    echo '</code>';
                    echo '</div>';
                    
                    echo '</div>';
                    echo '</div>';
                    
                } else {
                    echo '<div class="alert alert-warning">';
                    echo '<i class="fas fa-exclamation-triangle me-2"></i>';
                    echo '<strong>⚠️ لا توجد دول نشطة في الجدول</strong><br>';
                    echo 'تحتاج إلى إضافة دول وتفعيلها بتعيين <code>is_active = 1</code>';
                    echo '</div>';
                }
                
            } else {
                echo '<div class="alert alert-danger">';
                echo '<i class="fas fa-times-circle me-2"></i>';
                echo '<strong>❌ جدول countries غير موجود</strong><br>';
                echo 'تحتاج إلى إنشاء الجدول أولاً';
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<i class="fas fa-times-circle me-2"></i>';
            echo '<strong>❌ خطأ في الاتصال بقاعدة البيانات:</strong><br>';
            echo htmlspecialchars($e->getMessage());
            echo '</div>';
        }
        ?>

        <!-- إرشادات إضافة البيانات -->
        <div class="card mt-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-plus me-2"></i>إرشادات إضافة الدول</h5>
            </div>
            <div class="card-body">
                <p>إذا كان الجدول فارغاً أو تريد إضافة دول جديدة، يمكنك استخدام الاستعلام التالي:</p>
                
                <div class="code-block">
                    <strong>مثال لإضافة دول:</strong><br>
                    <code>
                    INSERT INTO countries (name_ar, currency_code, currency_symbol, is_active) VALUES<br>
                    ('مصر', 'EGP', 'ج.م', 1),<br>
                    ('السعودية', 'SAR', 'ر.س', 1),<br>
                    ('الإمارات', 'AED', 'د.إ', 1),<br>
                    ('الكويت', 'KWD', 'د.ك', 1),<br>
                    ('قطر', 'QAR', 'ر.ق', 1),<br>
                    ('البحرين', 'BHD', 'د.ب', 1),<br>
                    ('عمان', 'OMR', 'ر.ع', 1),<br>
                    ('الأردن', 'JOD', 'د.أ', 1),<br>
                    ('لبنان', 'LBP', 'ل.ل', 1),<br>
                    ('العراق', 'IQD', 'د.ع', 1);
                    </code>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                    <ul class="mb-0">
                        <li><strong>is_active:</strong> يجب أن يكون 1 للدول النشطة</li>
                        <li><strong>name_ar:</strong> اسم الدولة بالعربية</li>
                        <li><strong>currency_code:</strong> رمز العملة (مثل USD, EUR)</li>
                        <li><strong>currency_symbol:</strong> رمز العملة (مثل $, €)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="text-center mt-4">
            <h5 class="mb-3">🧪 اختبر النظام الآن</h5>
            <a href="../dashboard/add_daily_transaction.php" class="btn btn-success btn-lg" target="_blank">
                <i class="fas fa-plus me-2"></i>
                اختبار البحث التفاعلي
            </a>
            <a href="create_countries_table.php" class="btn btn-warning btn-lg ms-2" target="_blank">
                <i class="fas fa-database me-2"></i>
                إنشاء جدول الدول
            </a>
        </div>

        <!-- معلومات إضافية -->
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-lightbulb me-2"></i>كيف يعمل النظام:</h6>
            <ol>
                <li><strong>جلب البيانات:</strong> يتم جلب الدول من جدول <code>countries</code> عند تحميل الصفحة</li>
                <li><strong>تحويل إلى JavaScript:</strong> يتم تحويل البيانات إلى مصفوفة JavaScript</li>
                <li><strong>البحث التفاعلي:</strong> يتم البحث في البيانات محلياً بدون استعلامات إضافية</li>
                <li><strong>الاختيار:</strong> عند الاختيار يتم حفظ ID الدولة في حقل مخفي</li>
                <li><strong>الحفظ:</strong> يتم حفظ ID الدولة مع بيانات المعاملة</li>
            </ol>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

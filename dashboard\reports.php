<?php
/**
 * Reports Dashboard
 * لوحة التقارير الرئيسية
 */
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/reports_manager.php';
require_once __DIR__ . '/../includes/exchange_manager.php';
require_once __DIR__ . '/../includes/transfer_manager.php';
require_once __DIR__ . '/../includes/enhanced_transfer_manager.php';
require_once __DIR__ . '/../includes/office_manager.php';
require_once __DIR__ . '/../includes/currency_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

// التحقق من الصلاحيات
$auth = new Auth();
if (!$auth->checkSession()) {
    redirect('auth/login.php');
}

// التحقق من صلاحيات عرض التقارير
if (!$auth->hasPermission('reports.view')) {
    set_flash('danger', 'ليس لديك الإذن المطلوب لعرض التقارير');
    redirect('dashboard/index.php');
}

// إنشاء مديري البيانات
$db = new Database();
$reportsManager = new ReportsManager($db);
$exchangeManager = new ExchangeManager($db);
$transferManager = new EnhancedTransferManager($db);
$officeManager = new OfficeManager($db);
$currencyManager = new CurrencyManager($db);

// الحصول على بيانات الإحصائيات
$todayExchangeCount = $exchangeManager->getTodayExchangeCount();
$todayProfit = $exchangeManager->getTodayProfit();
$exchangeCountChange = $exchangeManager->getExchangeCountChange();
$profitChange = $exchangeManager->getProfitChange();
$weeklyOperations = $exchangeManager->getWeeklyOperations();

// بيانات الحوالات
$todayTransferCount = $transferManager->getTodayTransferCount();
$transferCountChange = $transferManager->getTransferCountChange();
$weeklyTransfers = $transferManager->getWeeklyOperations();

// بيانات المكاتب
$offices = $officeManager->getAllOffices(['is_active' => 1]);
$officeCount = count($offices);

// بيانات العملات
$currencies = $currencyManager->getAllCurrencies(true);

// تسجيل عملية عرض مركز التقارير
ActivityHelper::logView('reports', 'عرض مركز التقارير والإحصائيات');

// الحصول على بيانات الرسوم البيانية
// بيانات الصرافة حسب العملة
$exchangeByPairData = $reportsManager->getExchangeProfitReportData([
    'from_date' => date('Y-m-d', strtotime('-30 days')),
    'to_date' => date('Y-m-d')
]);

// بيانات الحوالات حسب النوع والحالة
$transferSummaryData = $reportsManager->getTransferSummaryReportData([
    'from_date' => date('Y-m-d', strtotime('-30 days')),
    'to_date' => date('Y-m-d')
]);

// تجهيز بيانات الرسوم البيانية
$exchangeLabels = [];
$exchangeData = [];
$exchangeProfitData = [];

// التأكد من أن البيانات الأسبوعية هي مصفوفة قبل المعالجة
if (is_array($weeklyOperations)) {
    foreach ($weeklyOperations as $day) {
        if (is_array($day) && isset($day['day_name'])) {
            $exchangeLabels[] = $day['day_name'];
            $exchangeData[] = isset($day['count']) ? $day['count'] : 0;
            $exchangeProfitData[] = isset($day['profit']) ? $day['profit'] : 0;
        }
    }
}

// تجهيز بيانات الحوالات
$transferLabels = [];
$transferData = [];
// التأكد من أن البيانات الأسبوعية للحوالات هي مصفوفة قبل المعالجة
if (is_array($weeklyTransfers)) {
    foreach ($weeklyTransfers as $day) {
        if (is_array($day) && isset($day['day_name'])) {
            $transferLabels[] = $day['day_name'];
            $transferData[] = isset($day['count']) ? $day['count'] : 0;
        }
    }
}

// تجهيز بيانات الحوالات حسب النوع
$transferTypeData = [
    'صادرة' => 0,
    'واردة' => 0
];

$transferStatusData = [
    'معلقة' => 0,
    'مكتملة' => 0,
    'مرسلة' => 0,
    'مستلمة' => 0,
    'ملغاة' => 0
];

// التأكد من أن بيانات ملخص الحوالات هي مصفوفة قبل المعالجة
if (is_array($transferSummaryData)) {
    foreach ($transferSummaryData as $row) {
        if (!is_array($row)) {
            continue;
        }
        
        if (isset($row['transfer_type']) && isset($row['total_tx'])) {
            if (isset($transferTypeData[$row['transfer_type']])) {
                $transferTypeData[$row['transfer_type']] += $row['total_tx'];
            }
        }
        
        if (isset($row['status']) && isset($row['total_tx'])) {
            if (isset($transferStatusData[$row['status']])) {
                $transferStatusData[$row['status']] += $row['total_tx'];
            }
        }
    }
}

// تجهيز بيانات أزواج العملات
$currencyPairData = [];
$currencyProfitData = [];

// التأكد من أن بيانات أزواج العملات هي مصفوفة قبل المعالجة
if (is_array($exchangeByPairData)) {
    foreach ($exchangeByPairData as $row) {
        if (!is_array($row)) {
            continue;
        }
        
        if (isset($row['pair'])) {
            if (!isset($currencyPairData[$row['pair']])) {
                $currencyPairData[$row['pair']] = 0;
                $currencyProfitData[$row['pair']] = 0;
            }
            
            if (isset($row['total_tx'])) {
                $currencyPairData[$row['pair']] += $row['total_tx'];
            }
            
            if (isset($row['total_profit'])) {
                $currencyProfitData[$row['pair']] += $row['total_profit'];
            }
        }
    }
}

// قائمة التقارير المتاحة
$reports = [
    ['type' => 'exchange_profit', 'name' => 'تقرير أرباح الصرافة', 'description' => 'إجمالي أرباح العُملات على مدى فترة محددة', 'permission' => 'reports.financial.view', 'icon' => 'fa-coins', 'file' => 'report_exchange_profit.php', 'color' => 'primary'],
    ['type' => 'transfer_summary', 'name' => 'تقرير ملخص الحوالات', 'description' => 'ملخص العمليات، الرسوم والأرباح', 'permission' => 'reports.view', 'icon' => 'fa-money-bill-wave', 'file' => 'report_transfer_summary.php', 'color' => 'success'],
    ['type' => 'offices', 'name' => 'تقرير المكاتب', 'description' => 'إحصائيات وأرصدة المكاتب', 'permission' => 'reports.view', 'icon' => 'fa-building', 'file' => 'report_offices.php', 'color' => 'info'],
    ['type' => 'daily_summary', 'name' => 'التقرير اليومي', 'description' => 'ملخص العمليات اليومية والأرباح', 'permission' => 'reports.view', 'icon' => 'fa-calendar-day', 'file' => 'report_daily_summary.php', 'color' => 'warning'],
];

$pageTitle = 'مركز التقارير';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3><i class="fas fa-chart-line"></i> مركز التقارير والإحصائيات</h3>
        <div>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>
    
    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">عمليات الصرافة اليوم</h6>
                            <h4 class="mb-0"><?php echo number_format($todayExchangeCount); ?></h4>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-coins text-primary fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge <?php echo $exchangeCountChange >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                            <i class="fas <?php echo $exchangeCountChange >= 0 ? 'fa-arrow-up' : 'fa-arrow-down'; ?>"></i>
                            <?php echo number_format(abs($exchangeCountChange), 1); ?>%
                        </span>
                        <span class="text-muted ms-2">مقارنة بالأمس</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">أرباح الصرافة اليوم</h6>
                            <h4 class="mb-0"><?php echo number_format($todayProfit, 2); ?></h4>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-dollar-sign text-success fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge <?php echo $profitChange >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                            <i class="fas <?php echo $profitChange >= 0 ? 'fa-arrow-up' : 'fa-arrow-down'; ?>"></i>
                            <?php echo number_format(abs($profitChange), 1); ?>%
                        </span>
                        <span class="text-muted ms-2">مقارنة بالأمس</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">حوالات اليوم</h6>
                            <h4 class="mb-0"><?php echo number_format($todayTransferCount); ?></h4>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="fas fa-paper-plane text-info fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="badge <?php echo $transferCountChange >= 0 ? 'bg-success' : 'bg-danger'; ?>">
                            <i class="fas <?php echo $transferCountChange >= 0 ? 'fa-arrow-up' : 'fa-arrow-down'; ?>"></i>
                            <?php echo number_format(abs($transferCountChange), 1); ?>%
                        </span>
                        <span class="text-muted ms-2">مقارنة بالأمس</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">المكاتب النشطة</h6>
                            <h4 class="mb-0"><?php echo number_format($officeCount); ?></h4>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-building text-warning fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <span class="text-muted">إجمالي المكاتب النشطة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">تحليل العمليات الأسبوعية</h5>
                </div>
                <div class="card-body">
                    <canvas id="weeklyOperationsChart" height="250"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">توزيع الحوالات حسب النوع</h5>
                </div>
                <div class="card-body">
                    <canvas id="transferTypePieChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">توزيع الحوالات حسب الحالة</h5>
                </div>
                <div class="card-body">
                    <canvas id="transferStatusChart" height="250"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">أزواج العملات الأكثر تداولاً</h5>
                </div>
                <div class="card-body">
                    <canvas id="currencyPairsChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- قائمة التقارير المتاحة -->
    <h4 class="mb-3"><i class="fas fa-file-alt"></i> التقارير المتاحة</h4>
    <div class="row mb-4">
    <?php foreach($reports as $rep): ?>
        <?php if($auth->hasPermission($rep['permission'])): ?>
            <div class="col-md-3 mb-3">
                <div class="card h-100 shadow-sm border-<?php echo $rep['color']; ?>">
                <div class="card-body d-flex flex-column">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3 bg-<?php echo $rep['color']; ?> bg-opacity-10 p-3 rounded">
                                <i class="fas <?php echo $rep['icon']; ?> text-<?php echo $rep['color']; ?>"></i>
                            </div>
                            <h5 class="mb-0"><?php echo htmlspecialchars($rep['name']); ?></h5>
                        </div>
                    <p class="flex-grow-1 small text-muted"><?php echo htmlspecialchars($rep['description']); ?></p>
                        <a href="<?php echo $rep['file']; ?>" class="btn btn-outline-<?php echo $rep['color']; ?> mt-auto">
                            <i class="fas fa-chart-bar"></i> عرض التقرير
                        </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    <?php endforeach; ?>
  </div>
    
    <!-- آخر العمليات -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخر عمليات الصرافة</h5>
                    <a href="exchange.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>العملات</th>
                                    <th>المبلغ</th>
                                    <th>الربح</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $latestExchanges = $exchangeManager->getLatestActivities(5);
                                if (empty($latestExchanges)): 
                                ?>
                                <tr>
                                    <td colspan="4" class="text-center py-3 text-muted">لا توجد عمليات حديثة</td>
                                </tr>
                                <?php else: foreach($latestExchanges as $ex): ?>
                                <tr>
                                    <td><?php echo date('Y-m-d', strtotime($ex['created_at'])); ?></td>
                                    <td><?php echo htmlspecialchars($ex['from_currency_code'] . ' → ' . $ex['to_currency_code']); ?></td>
                                    <td><?php echo number_format($ex['amount_from'], 2) . ' ' . htmlspecialchars($ex['from_currency_code']); ?></td>
                                    <td class="text-success"><?php echo number_format($ex['profit'], 2); ?></td>
                                </tr>
                                <?php endforeach; endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">آخر الحوالات</h5>
                    <a href="all_transfers.php" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الحوالة</th>
                                    <th>المستفيد</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $latestTransfers = $transferManager->getRecentTransfers(5);
                                if (empty($latestTransfers)): 
                                ?>
                                <tr>
                                    <td colspan="4" class="text-center py-3 text-muted">لا توجد حوالات حديثة</td>
                                </tr>
                                <?php else: foreach($latestTransfers as $transfer): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($transfer['transaction_number']); ?></td>
                                    <td><?php echo htmlspecialchars($transfer['beneficiary_name']); ?></td>
                                    <td><?php echo number_format($transfer['sending_amount'], 2); ?></td>
                                    <td>
                                        <?php
                                        $statusColors = [
                                            'معلقة' => 'warning',
                                            'مكتملة' => 'success',
                                            'مرسلة' => 'info',
                                            'مستلمة' => 'primary',
                                            'ملغاة' => 'danger'
                                        ];
                                        $statusColor = $statusColors[$transfer['status']] ?? 'secondary';
                                        ?>
                                        <span class="badge bg-<?php echo $statusColor; ?>">
                                            <?php echo htmlspecialchars($transfer['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
  </div>
</div>

<!-- إضافة مكتبة Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الألوان
    const primaryColor = '#4e73df';
    const successColor = '#1cc88a';
    const infoColor = '#36b9cc';
    const warningColor = '#f6c23e';
    const dangerColor = '#e74a3b';
    const secondaryColor = '#858796';
    
    // رسم بياني للعمليات الأسبوعية
    const weeklyCtx = document.getElementById('weeklyOperationsChart').getContext('2d');
    new Chart(weeklyCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($exchangeLabels); ?>,
            datasets: [
                {
                    label: 'عمليات الصرافة',
                    data: <?php echo json_encode($exchangeData); ?>,
                    borderColor: primaryColor,
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'الحوالات',
                    data: <?php echo json_encode($transferData); ?>,
                    borderColor: infoColor,
                    backgroundColor: 'rgba(54, 185, 204, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    align: 'end',
                    labels: {
                        boxWidth: 15,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
    
    // رسم بياني لتوزيع الحوالات حسب النوع
    const transferTypeCtx = document.getElementById('transferTypePieChart').getContext('2d');
    new Chart(transferTypeCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(<?php echo json_encode($transferTypeData); ?>),
            datasets: [{
                data: Object.values(<?php echo json_encode($transferTypeData); ?>),
                backgroundColor: [primaryColor, infoColor],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 15,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                }
            },
            cutout: '65%'
        }
    });
    
    // رسم بياني لتوزيع الحوالات حسب الحالة
    const transferStatusCtx = document.getElementById('transferStatusChart').getContext('2d');
    new Chart(transferStatusCtx, {
        type: 'bar',
        data: {
            labels: Object.keys(<?php echo json_encode($transferStatusData); ?>),
            datasets: [{
                label: 'عدد الحوالات',
                data: Object.values(<?php echo json_encode($transferStatusData); ?>),
                backgroundColor: [warningColor, successColor, infoColor, primaryColor, dangerColor],
                borderWidth: 0,
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
    
    // رسم بياني لأزواج العملات
    const currencyPairsCtx = document.getElementById('currencyPairsChart').getContext('2d');
    new Chart(currencyPairsCtx, {
        type: 'bar',
        data: {
            labels: Object.keys(<?php echo json_encode($currencyPairData); ?>),
            datasets: [{
                label: 'عدد العمليات',
                data: Object.values(<?php echo json_encode($currencyPairData); ?>),
                backgroundColor: 'rgba(78, 115, 223, 0.7)',
                borderWidth: 0,
                borderRadius: 4,
                order: 2
            },
            {
                label: 'الربح',
                data: Object.values(<?php echo json_encode($currencyProfitData); ?>),
                type: 'line',
                borderColor: successColor,
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                borderWidth: 2,
                pointBackgroundColor: successColor,
                pointRadius: 4,
                fill: false,
                tension: 0.4,
                order: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    align: 'end',
                    labels: {
                        boxWidth: 15,
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
});
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?> 
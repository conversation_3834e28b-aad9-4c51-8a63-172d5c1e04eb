<?php
/**
 * اختبار التقارير والتصدير
 * Test Reports and Export
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التقارير والتصدير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .feature-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-chart-line text-primary"></i>
            اختبار التقارير والتصدير
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>الميزات المحدثة</h5>
            <ul class="mb-0">
                <li><strong>إصلاح التقارير:</strong> تم إصلاح مشاكل الاستعلامات في صفحة التقارير</li>
                <li><strong>تصدير متعدد:</strong> قائمة منسدلة للتصدير في الصفحة الرئيسية</li>
                <li><strong>PDF محسن:</strong> تصدير PDF مع تنسيق أفضل وإمكانية الطباعة</li>
                <li><strong>إحصائيات شاملة:</strong> رسوم بيانية وإحصائيات مفصلة</li>
            </ul>
        </div>

        <?php
        $tests_passed = 0;
        $tests_failed = 0;

        function showResult($test_name, $success, $message = '') {
            global $tests_passed, $tests_failed;
            
            if ($success) {
                $tests_passed++;
                echo "<div class='test-result test-success'>";
                echo "<i class='fas fa-check-circle me-2'></i>";
                echo "<strong>✓ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            } else {
                $tests_failed++;
                echo "<div class='test-result test-error'>";
                echo "<i class='fas fa-times-circle me-2'></i>";
                echo "<strong>✗ {$test_name}</strong>";
                if ($message) echo "<br><small>{$message}</small>";
                echo "</div>";
            }
        }

        function showInfo($message) {
            echo "<div class='test-result test-info'>";
            echo "<i class='fas fa-info-circle me-2'></i>";
            echo $message;
            echo "</div>";
        }

        try {
            $pdo = PDODatabase::getConnection();
            showResult("اتصال قاعدة البيانات", true, "تم الاتصال بنجاح");
            
            // اختبار وجود البيانات للتقارير
            showInfo("<strong>اختبار البيانات الأساسية:</strong>");
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM daily_transactions");
                $total_transactions = $stmt->fetch()['count'];
                showResult("وجود معاملات للتقارير", $total_transactions > 0, "يوجد {$total_transactions} معاملة");
                
                if ($total_transactions > 0) {
                    // اختبار الإحصائيات العامة
                    $stmt = $pdo->query("SELECT 
                        COUNT(*) as total_transactions,
                        SUM(base_amount) as total_base_amount,
                        SUM(recipient_amount) as total_recipient_amount,
                        AVG(base_amount) as avg_base_amount
                    FROM daily_transactions");
                    $stats = $stmt->fetch();
                    
                    showResult("الإحصائيات العامة", true, 
                        "إجمالي: {$stats['total_transactions']} | " .
                        "مجموع المبلغ: " . number_format($stats['total_base_amount'], 2) . " | " .
                        "متوسط المبلغ: " . number_format($stats['avg_base_amount'], 2)
                    );
                    
                    // اختبار إحصائيات نوع التسليم
                    $stmt = $pdo->query("SELECT delivery_type, COUNT(*) as count FROM daily_transactions GROUP BY delivery_type");
                    $delivery_stats = $stmt->fetchAll();
                    
                    if (!empty($delivery_stats)) {
                        $delivery_summary = [];
                        foreach ($delivery_stats as $stat) {
                            $delivery_names = ['cash' => 'كاش', 'bank' => 'بنكي', 'usdt' => 'USDT'];
                            $type_name = $delivery_names[$stat['delivery_type']] ?? $stat['delivery_type'];
                            $delivery_summary[] = $type_name . ': ' . $stat['count'];
                        }
                        showResult("إحصائيات نوع التسليم", true, implode(' | ', $delivery_summary));
                    }
                    
                    // اختبار إحصائيات الدول
                    $stmt = $pdo->query("SELECT c.name_ar, COUNT(*) as count 
                                       FROM daily_transactions dt 
                                       LEFT JOIN countries c ON dt.country_id = c.id 
                                       GROUP BY dt.country_id, c.name_ar 
                                       ORDER BY count DESC LIMIT 5");
                    $country_stats = $stmt->fetchAll();
                    
                    if (!empty($country_stats)) {
                        $country_summary = [];
                        foreach ($country_stats as $stat) {
                            $country_summary[] = $stat['name_ar'] . ': ' . $stat['count'];
                        }
                        showResult("إحصائيات الدول", true, implode(' | ', $country_summary));
                    }
                }
            } catch (Exception $e) {
                showResult("فحص البيانات", false, $e->getMessage());
            }
            
            // اختبار ملفات التقارير والتصدير
            showInfo("<strong>اختبار ملفات النظام:</strong>");
            
            $files_to_check = [
                'dashboard/daily_transactions_reports.php' => 'صفحة التقارير',
                'dashboard/export_daily_transactions.php' => 'صفحة التصدير',
                'dashboard/daily_transactions.php' => 'الصفحة الرئيسية (محدثة)'
            ];
            
            foreach ($files_to_check as $file => $description) {
                $file_path = __DIR__ . '/../' . $file;
                $exists = file_exists($file_path);
                
                if ($exists) {
                    // فحص محتوى الملف للتأكد من التحديثات
                    $content = file_get_contents($file_path);
                    
                    if ($file == 'dashboard/daily_transactions.php') {
                        $has_dropdown = strpos($content, 'dropdown-toggle') !== false;
                        showResult($description, $has_dropdown, $has_dropdown ? "يحتوي على قائمة التصدير المنسدلة" : "لا يحتوي على قائمة التصدير");
                    } elseif ($file == 'dashboard/daily_transactions_reports.php') {
                        $has_charts = strpos($content, 'Chart.js') !== false;
                        showResult($description, $has_charts, $has_charts ? "يحتوي على مكتبة الرسوم البيانية" : "لا يحتوي على الرسوم البيانية");
                    } else {
                        showResult($description, true, "الملف موجود");
                    }
                } else {
                    showResult($description, false, "الملف غير موجود");
                }
            }
            
            // اختبار روابط التصدير
            showInfo("<strong>اختبار روابط التصدير:</strong>");
            
            $export_formats = ['excel', 'pdf', 'csv'];
            foreach ($export_formats as $format) {
                $export_url = "export_daily_transactions.php?format={$format}";
                showResult("رابط تصدير {$format}", true, $export_url);
            }
            
        } catch (Exception $e) {
            showResult("اتصال قاعدة البيانات", false, $e->getMessage());
        }

        // عرض النتائج النهائية
        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-success text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_passed}</h3>";
        echo "<p>اختبارات نجحت</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card bg-danger text-white'>";
        echo "<div class='card-body text-center'>";
        echo "<h3>{$tests_failed}</h3>";
        echo "<p>اختبارات فشلت</p>";
        echo "</div></div></div>";
        echo "</div>";

        $total_tests = $tests_passed + $tests_failed;
        $success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;
        
        echo "<div class='mt-3 text-center'>";
        echo "<h4>معدل النجاح: {$success_rate}%</h4>";
        
        if ($success_rate >= 90) {
            echo "<div class='alert alert-success'>";
            echo "<i class='fas fa-trophy me-2'></i>";
            echo "<strong>ممتاز!</strong> التقارير والتصدير يعملان بشكل مثالي";
            echo "</div>";
        } elseif ($success_rate >= 70) {
            echo "<div class='alert alert-warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>جيد!</strong> معظم الميزات تعمل";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<i class='fas fa-times-circle me-2'></i>";
            echo "<strong>يحتاج إصلاح!</strong> هناك مشاكل في النظام";
            echo "</div>";
        }
        echo "</div>";
        ?>

        <!-- بطاقات الاختبار -->
        <div class="row mt-4">
            <div class="col-md-4 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            صفحة التقارير
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>تقارير شاملة مع رسوم بيانية</p>
                        <a href="../dashboard/daily_transactions_reports.php" class="btn btn-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            اختبار التقارير
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-download me-2"></i>
                            التصدير المباشر
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>تصدير مباشر بصيغ متعددة</p>
                        <div class="btn-group-vertical w-100">
                            <a href="../dashboard/export_daily_transactions.php?format=excel" class="btn btn-success btn-sm mb-1" target="_blank">
                                <i class="fas fa-file-excel me-1"></i>
                                Excel
                            </a>
                            <a href="../dashboard/export_daily_transactions.php?format=pdf" class="btn btn-danger btn-sm mb-1" target="_blank">
                                <i class="fas fa-file-pdf me-1"></i>
                                PDF
                            </a>
                            <a href="../dashboard/export_daily_transactions.php?format=csv" class="btn btn-info btn-sm" target="_blank">
                                <i class="fas fa-file-csv me-1"></i>
                                CSV
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-3">
                <div class="card feature-card">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            الصفحة الرئيسية
                        </h6>
                    </div>
                    <div class="card-body">
                        <p>قائمة المعاملات مع التصدير</p>
                        <a href="../dashboard/daily_transactions.php" class="btn btn-warning" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i>
                            اختبار القائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="../dashboard/daily_transactions.php" class="btn btn-primary btn-lg">
                <i class="fas fa-list me-2"></i>
                قائمة المعاملات
            </a>
            <a href="../dashboard/daily_transactions_reports.php" class="btn btn-success btn-lg ms-2">
                <i class="fas fa-chart-line me-2"></i>
                التقارير
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
/**
 * <PERSON><PERSON><PERSON> to restart MySQL service and clear locks
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إعادة تشغيل MySQL وإصلاح الأقفال</h2>\n";

try {
    $db = Database::getConnection();
    
    echo "<h3>1. إنهاء العمليات المعلقة</h3>\n";
    
    // Show current processes
    $result = $db->query("SHOW PROCESSLIST");
    if ($result) {
        $processes = $result->fetch_all(MYSQLI_ASSOC);
        $killedCount = 0;
        
        foreach ($processes as $process) {
            // Kill long-running or sleeping processes
            if (($process['Command'] === 'Sleep' && $process['Time'] > 60) || 
                ($process['Command'] === 'Query' && $process['Time'] > 30)) {
                
                $killQuery = "KILL " . $process['Id'];
                try {
                    if ($db->query($killQuery)) {
                        echo "<p style='color: orange;'>تم إنهاء العملية: ID {$process['Id']} - {$process['Command']} ({$process['Time']}s)</p>\n";
                        $killedCount++;
                    }
                } catch (Exception $e) {
                    // Process might have already ended
                }
            }
        }
        
        if ($killedCount === 0) {
            echo "<p style='color: green;'>لا توجد عمليات معلقة</p>\n";
        } else {
            echo "<p style='color: blue;'>تم إنهاء $killedCount عملية معلقة</p>\n";
        }
    }
    
    echo "<h3>2. إلغاء جميع الأقفال</h3>\n";
    
    // Unlock all tables
    $db->query("UNLOCK TABLES");
    echo "<p style='color: green;'>تم إلغاء جميع أقفال الجداول</p>\n";
    
    // Reset transaction isolation level
    $db->query("SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED");
    echo "<p style='color: green;'>تم إعادة تعيين مستوى عزل المعاملات</p>\n";
    
    // Set lock wait timeout
    $db->query("SET SESSION innodb_lock_wait_timeout = 10");
    echo "<p style='color: green;'>تم تعيين مهلة انتظار الأقفال إلى 10 ثواني</p>\n";
    
    // Reset autocommit
    $db->query("SET autocommit = 1");
    echo "<p style='color: green;'>تم تفعيل الحفظ التلقائي</p>\n";
    
    echo "<h3>3. تحسين إعدادات MySQL</h3>\n";
    
    // Optimize settings for better performance
    $optimizations = [
        "SET GLOBAL innodb_lock_wait_timeout = 10",
        "SET GLOBAL wait_timeout = 300",
        "SET GLOBAL interactive_timeout = 300",
        "SET GLOBAL max_connections = 200"
    ];
    
    foreach ($optimizations as $query) {
        try {
            $db->query($query);
            echo "<p style='color: green;'>✓ " . htmlspecialchars($query) . "</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ " . htmlspecialchars($query) . " - " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<h3>4. فحص حالة النظام</h3>\n";
    
    // Check system status
    $statusQueries = [
        "SHOW STATUS LIKE 'Threads_connected'",
        "SHOW STATUS LIKE 'Threads_running'", 
        "SHOW STATUS LIKE 'Innodb_row_lock_waits'",
        "SHOW STATUS LIKE 'Innodb_row_lock_time_avg'"
    ];
    
    foreach ($statusQueries as $query) {
        try {
            $result = $db->query($query);
            if ($result) {
                $row = $result->fetch_assoc();
                echo "<p><strong>{$row['Variable_name']}:</strong> {$row['Value']}</p>\n";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>خطأ في فحص: $query</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح مشاكل MySQL بنجاح!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>جرب إنشاء عملية صرف جديدة</li>\n";
    echo "<li>إذا استمرت المشكلة، قم بإعادة تشغيل XAMPP</li>\n";
    echo "<li>تأكد من عدم وجود تطبيقات أخرى تستخدم قاعدة البيانات</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>\n";
    echo "<p><strong>الحلول البديلة:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>أعد تشغيل XAMPP Control Panel</li>\n";
    echo "<li>أوقف MySQL ثم شغله مرة أخرى</li>\n";
    echo "<li>تحقق من ملفات سجل MySQL للأخطاء</li>\n";
    echo "</ul>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
</style>

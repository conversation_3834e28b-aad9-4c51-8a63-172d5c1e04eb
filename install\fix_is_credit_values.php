<?php
/**
 * Fix is_credit Values
 * This script ensures that all is_credit values in the office_operations table are either 0 or 1
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>تصحيح قيم is_credit في جدول عمليات المكاتب</h2>";

try {
    $db = Database::getConnection();
    
    // Start transaction
    $db->begin_transaction();
    
    echo "<p>جاري تصحيح قيم is_credit...</p>";
    
    // Update any non-standard values to ensure they are either 0 or 1
    $sql = "UPDATE office_operations SET is_credit = 1 WHERE is_credit != 0 AND is_credit != 1";
    $db->query($sql);
    
    // Convert is_credit to TINYINT(1) to ensure it's stored as a boolean
    $sql = "ALTER TABLE office_operations MODIFY is_credit TINYINT(1) NOT NULL COMMENT 'TRUE = لنا (credit), FALSE = لكم (debit)'";
    $db->query($sql);
    
    // Commit transaction
    $db->commit();
    
    echo "<p style='color: green;'>✓ تم تصحيح قيم is_credit بنجاح</p>";
    
    // Now show the updated values
    $sql = "SELECT id, operation_name, is_credit FROM office_operations ORDER BY id";
    $result = $db->query($sql);
    
    if ($result) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>العملية</th><th>is_credit</th><th>النوع</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['operation_name']) . "</td>";
            echo "<td>" . $row['is_credit'] . " (" . gettype($row['is_credit']) . ")</td>";
            echo "<td>" . ($row['is_credit'] == 1 ? 'لنا' : 'لكم') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        $result->free();
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ تم تصحيح قيم is_credit بنجاح</h3>";
    echo "<p style='color: #155724; margin: 0;'>تم تصحيح قيم is_credit في جدول عمليات المكاتب.</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/offices.php' style='color: #007bff; text-decoration: none;'>→ الذهاب إلى صفحة المكاتب</a></p>";
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($db)) {
        $db->rollback();
    }
    
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في تصحيح قيم is_credit</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
}
?> 
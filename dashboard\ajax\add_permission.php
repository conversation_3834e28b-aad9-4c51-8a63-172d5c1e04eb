<?php
/**
 * إضافة صلاحية جديدة عبر AJAX
 * Add New Permission via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/permission_manager.php';
require_once __DIR__ . '/../../includes/activity_helper.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('permissions.create')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لإضافة صلاحيات جديدة'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'طريقة طلب غير صحيحة'
    ]);
    exit;
}

try {
    // استلام البيانات
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $module = trim($_POST['module'] ?? '');
    
    // التحقق من صحة البيانات
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم الصلاحية مطلوب';
    } elseif (!preg_match('/^[a-zA-Z0-9_.]+$/', $name)) {
        $errors[] = 'اسم الصلاحية يجب أن يحتوي على أحرف وأرقام ونقاط وشرطات سفلية فقط';
    }
    
    if (empty($description)) {
        $errors[] = 'وصف الصلاحية مطلوب';
    }
    
    if (empty($module)) {
        $errors[] = 'الوحدة مطلوبة';
    }
    
    // التحقق من عدم تكرار اسم الصلاحية
    $db = new Database();
    $permManager = new PermissionManager($db);
    
    if ($permManager->getPermissionByName($name)) {
        $errors[] = 'اسم الصلاحية موجود بالفعل';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'أخطاء في البيانات',
            'errors' => $errors
        ]);
        exit;
    }
    
    // إضافة الصلاحية
    $data = [
        'name' => $name,
        'description' => $description,
        'module' => $module
    ];
    
    $newId = $permManager->addPermission($data);
    
    if ($newId) {
        // تسجيل العملية
        ActivityHelper::logCreate(
            'permissions',
            $name,
            $data,
            $newId
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة الصلاحية بنجاح',
            'permission' => [
                'id' => $newId,
                'name' => $name,
                'description' => $description,
                'module' => $module
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'فشل في إضافة الصلاحية'
        ]);
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    ActivityHelper::logError(
        'permissions',
        'add_permission',
        'فشل في إضافة صلاحية جديدة: ' . $e->getMessage(),
        [
            'attempted_data' => $_POST,
            'error' => $e->getMessage()
        ]
    );
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/cash_manager.php';
require_once __DIR__ . '/../includes/bank_manager.php';
require_once __DIR__ . '/../includes/branch_manager.php';
require_once __DIR__ . '/../includes/user_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('cash.view');

$currentUser = $auth->getCurrentUser();
$userId      = $currentUser['id'];

// Instantiate managers
$cashManager  = new CashManager(new Database());
$bankManager  = new BankManager(new Database());
$branchMgr    = new BranchManager(new Database());
$userMgr      = new UserManager(new Database());

// Flash messages are now handled in includes/functions.php

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $token  = $_POST['csrf_token'] ?? '';
    $activeTab = $_POST['active_tab'] ?? 'cash';

    if (!verify_csrf_token($token)) {
        set_flash('danger', 'رمز CSRF غير صالح');
        redirect('cash.php?tab=' . $activeTab);
    }

    // Permission map
    $permEdit      = 'cash.edit';
    $permMovements = 'cash.movements';

    switch ($action) {
        case 'add_cash_box':
            if (!$auth->hasPermission($permEdit)) {
                set_flash('danger', 'ليس لديك الإذن المطلوب');
                redirect('cash.php');
            }
            $data = [
                'name'                => sanitize_input($_POST['name'] ?? ''),
                'currency_code'       => sanitize_input($_POST['currency_code'] ?? ''),
                'initial_balance'     => (float)($_POST['initial_balance'] ?? 0),
                'branch_id'           => (int)($_POST['branch_id'] ?? 0),
                'responsible_user_id' => (int)($_POST['responsible_user_id'] ?? 0),
                'is_active'           => isset($_POST['is_active']) ? 1 : 0,
            ];
            $newId = $cashManager->addCashBox($data);
            if ($newId) {
                log_activity($userId, 'cash.add_box', ['box_id' => $newId], 'cash');
                set_flash('success', 'تم إضافة الصندوق بنجاح');
            } else {
                set_flash('danger', 'فشل إضافة الصندوق');
            }
            redirect('cash.php?tab=cash');
            break;

        case 'deposit_cash':
        case 'withdraw_cash':
            if (!$auth->hasPermission($permMovements)) {
                set_flash('danger', 'ليس لديك الإذن المطلوب');
                redirect('cash.php');
            }
            $type   = $action === 'deposit_cash' ? 'deposit' : 'withdrawal';
            $boxId  = (int)($_POST['cash_box_id'] ?? 0);
            $amount = (float)($_POST['amount'] ?? 0);
            $desc   = sanitize_input($_POST['description'] ?? '');
            $ref    = sanitize_input($_POST['reference_number'] ?? '');
            $res    = $cashManager->addCashMovement($boxId, $type, $amount, $desc, $ref, $userId);
            if ($res['success']) {
                log_activity($userId, 'cash.' . $type, ['movement_id' => $res['movement_id']], 'cash', $boxId);
                set_flash('success', 'تمت العملية بنجاح');
            } else {
                set_flash('danger', $res['error'] ?? 'خطأ غير متوقع');
            }
            redirect('cash.php?tab=cash');
            break;

        // Cash Box Actions
        case 'add_cash_box':
            if ($auth->hasPermission('cash.edit')) {
                $data = [
                    'name' => sanitize_input($_POST['name'] ?? ''),
                    'currency_code' => sanitize_input($_POST['currency_code'] ?? ''),
                    'initial_balance' => (float)($_POST['initial_balance'] ?? 0),
                    'branch_id' => (int)($_POST['branch_id'] ?? 0),
                    'responsible_user_id' => (int)($_POST['responsible_user_id'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                $res = $cashManager->addCashBox($data);
                if ($res['success']) {
                    // تسجيل عملية إضافة صندوق نقدي
                    ActivityHelper::logCreate(
                        'cash_boxes',
                        $data['name'],
                        $data,
                        $res['id']
                    );
                    set_flash('success', 'تم إضافة الصندوق بنجاح');
                } else {
                    // تسجيل فشل إضافة الصندوق
                    ActivityHelper::logError(
                        'cash_boxes',
                        'add_cash_box',
                        'فشل في إضافة صندوق نقدي: ' . $data['name'],
                        ['attempted_data' => $data, 'error' => $res['error'] ?? 'خطأ غير محدد']
                    );
                    set_flash('danger', $res['error'] ?? 'خطأ في إضافة الصندوق');
                }
            }
            redirect('cash.php?tab=cash');
            break;

        case 'edit_cash_box':
            if ($auth->hasPermission('cash.edit')) {
                $cashBoxId = (int)($_POST['cash_box_id'] ?? 0);
                $data = [
                    'name' => sanitize_input($_POST['name'] ?? ''),
                    'currency_code' => sanitize_input($_POST['currency_code'] ?? ''),
                    'branch_id' => (int)($_POST['branch_id'] ?? 0),
                    'responsible_user_id' => (int)($_POST['responsible_user_id'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                $success = $cashManager->updateCashBox($cashBoxId, $data);
                if ($success) {
                    // تسجيل عملية تعديل صندوق نقدي
                    ActivityHelper::logUpdate(
                        'cash_boxes',
                        $data['name'],
                        [],
                        $data,
                        $cashBoxId
                    );
                    set_flash('success', 'تم تحديث الصندوق بنجاح');
                } else {
                    // تسجيل فشل التعديل
                    ActivityHelper::logError(
                        'cash_boxes',
                        'edit_cash_box',
                        'فشل في تعديل صندوق نقدي #' . $cashBoxId,
                        ['box_id' => $cashBoxId, 'attempted_data' => $data]
                    );
                    set_flash('danger', 'خطأ في تحديث الصندوق');
                }
            }
            redirect('cash.php?tab=cash');
            break;

        case 'toggle_cash_status':
            if ($auth->hasPermission('cash.edit')) {
                $cashBoxId = (int)($_POST['cash_box_id'] ?? 0);
                $res = $cashManager->toggleCashBoxStatus($cashBoxId);
                if ($res['success']) {
                    log_activity($userId, 'cash_box.toggle_status', ['box_id' => $cashBoxId]);
                    set_flash('success', $res['message'] ?? 'تم تغيير حالة الصندوق بنجاح');
                } else {
                    set_flash('danger', $res['error'] ?? 'خطأ في تغيير حالة الصندوق');
                }
            }
            redirect('cash.php?tab=cash');
            break;
            
        case 'delete_cash_box':
            if (!$auth->hasPermission('cash.edit')) {
                set_flash('danger', 'ليس لديك الإذن المطلوب لحذف الصندوق');
                redirect('cash.php?tab=cash');
            }
            
            $cashBoxId = (int)($_POST['cash_box_id'] ?? 0);
            
            // تحقق من وجود حركات مرتبطة بالصندوق
            $movements = $cashManager->getCashBoxMovements($cashBoxId);
            if (!empty($movements)) {
                set_flash('danger', 'لا يمكن حذف الصندوق لأنه يحتوي على حركات مالية. قم بإلغاء تفعيله بدلاً من ذلك.');
                redirect('cash.php?tab=cash');
            }
            
            // حذف الصندوق
            $success = $cashManager->deleteCashBox($cashBoxId);
            if ($success) {
                // تسجيل عملية حذف صندوق نقدي
                ActivityHelper::logDelete(
                    'cash_boxes',
                    "صندوق نقدي #$cashBoxId",
                    [],
                    $cashBoxId
                );
                set_flash('success', 'تم حذف الصندوق بنجاح');
            } else {
                set_flash('danger', 'فشل في حذف الصندوق');
            }
            redirect('cash.php?tab=cash');
            break;

        // Bank Account Actions
        case 'add_bank_account':
            if ($auth->hasPermission('cash.edit')) {
                $data = [
                    'account_name' => sanitize_input($_POST['account_name'] ?? ''),
                    'account_number' => sanitize_input($_POST['account_number'] ?? ''),
                    'bank_name' => sanitize_input($_POST['bank_name'] ?? ''),
                    'currency_code' => sanitize_input($_POST['currency_code'] ?? ''),
                    'initial_balance' => (float)($_POST['initial_balance'] ?? 0),
                    'branch_id' => (int)($_POST['branch_id'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                $res = $bankManager->addBankAccount($data);
                if ($res) {
                    // تسجيل عملية إضافة حساب بنكي
                    ActivityHelper::logCreate(
                        'bank_accounts',
                        $data['account_name'],
                        $data,
                        $res
                    );
                    set_flash('success', 'تم إضافة الحساب البنكي بنجاح');
                } else {
                    // تسجيل فشل إضافة الحساب
                    ActivityHelper::logError(
                        'bank_accounts',
                        'add_bank_account',
                        'فشل في إضافة حساب بنكي: ' . $data['account_name'],
                        ['attempted_data' => $data]
                    );
                    set_flash('danger', 'خطأ في إضافة الحساب البنكي');
                }
            }
            redirect('cash.php?tab=bank');
            break;

        case 'deposit_bank':
            if ($auth->hasPermission('cash.movements')) {
                $bankAccountId = (int)($_POST['bank_account_id'] ?? 0);
                $amount = (float)($_POST['amount'] ?? 0);
                $description = sanitize_input($_POST['description'] ?? '');
                $referenceNumber = sanitize_input($_POST['reference_number'] ?? '');

                if ($amount > 0) {
                    $res = $bankManager->addBankMovement($bankAccountId, 'deposit', $amount, $description, $referenceNumber, $userId);
                    if ($res['success']) {
                        log_activity($userId, 'bank_account.deposit', [
                            'account_id' => $bankAccountId,
                            'amount' => $amount,
                            'description' => $description,
                            'reference' => $referenceNumber
                        ]);
                        set_flash('success', 'تم إيداع المبلغ بنجاح');
                    } else {
                        set_flash('danger', $res['error'] ?? 'خطأ في عملية الإيداع');
                    }
                } else {
                    set_flash('danger', 'المبلغ يجب أن يكون أكبر من صفر');
                }
            }
            redirect('cash.php?tab=bank');
            break;

        case 'withdraw_bank':
            if ($auth->hasPermission('cash.movements')) {
                $bankAccountId = (int)($_POST['bank_account_id'] ?? 0);
                $amount = (float)($_POST['amount'] ?? 0);
                $description = sanitize_input($_POST['description'] ?? '');
                $referenceNumber = sanitize_input($_POST['reference_number'] ?? '');

                if ($amount > 0) {
                    $res = $bankManager->addBankMovement($bankAccountId, 'withdrawal', $amount, $description, $referenceNumber, $userId);
                    if ($res['success']) {
                        log_activity($userId, 'bank_account.withdrawal', [
                            'account_id' => $bankAccountId,
                            'amount' => $amount,
                            'description' => $description,
                            'reference' => $referenceNumber
                        ]);
                        set_flash('success', 'تم سحب المبلغ بنجاح');
                    } else {
                        set_flash('danger', $res['error'] ?? 'خطأ في عملية السحب');
                    }
                } else {
                    set_flash('danger', 'المبلغ يجب أن يكون أكبر من صفر');
                }
            }
            redirect('cash.php?tab=bank');
            break;

        case 'edit_bank_account':
            if ($auth->hasPermission('cash.edit')) {
                $bankAccountId = (int)($_POST['bank_account_id'] ?? 0);
                $data = [
                    'account_name' => sanitize_input($_POST['account_name'] ?? ''),
                    'account_number' => sanitize_input($_POST['account_number'] ?? ''),
                    'bank_name' => sanitize_input($_POST['bank_name'] ?? ''),
                    'currency_code' => sanitize_input($_POST['currency_code'] ?? ''),
                    'branch_id' => (int)($_POST['branch_id'] ?? 0),
                    'is_active' => isset($_POST['is_active']) ? 1 : 0
                ];

                $res = $bankManager->updateBankAccount($bankAccountId, $data);
                if ($res['success']) {
                    log_activity($userId, 'bank_account.update', ['account_id' => $bankAccountId, 'data' => $data]);
                    set_flash('success', 'تم تحديث الحساب البنكي بنجاح');
                } else {
                    set_flash('danger', $res['error'] ?? 'خطأ في تحديث الحساب البنكي');
                }
            }
            redirect('cash.php?tab=bank');
            break;

        case 'toggle_bank_status':
            if ($auth->hasPermission('cash.edit')) {
                $bankAccountId = (int)($_POST['bank_account_id'] ?? 0);
                $res = $bankManager->toggleBankAccountStatus($bankAccountId);
                if ($res['success']) {
                    log_activity($userId, 'bank_account.toggle_status', ['account_id' => $bankAccountId]);
                    set_flash('success', 'تم تغيير حالة الحساب بنجاح');
                } else {
                    set_flash('danger', $res['error'] ?? 'خطأ في تغيير حالة الحساب');
                }
            }
            redirect('cash.php?tab=bank');
            break;
    }
}

// GET – initial data fetch
$activeTab   = $_GET['tab'] ?? 'cash';
$branchId    = null; // Could be set based on user role/branch.
$cashBoxes   = $cashManager->getAllCashBoxes($branchId);
$bankAccounts= $bankManager->getAllBankAccounts($branchId);
$branches    = $branchMgr->getAllBranches();
$users       = $userMgr->getAllUsers();
// Retrieve currencies directly
$conn = Database::getConnection();
$currencies = [];
$resCur = $conn->query('SELECT code, name, symbol FROM currencies WHERE is_active = 1 ORDER BY name');
if ($resCur) {
    $currencies = $resCur->fetch_all(MYSQLI_ASSOC);
    $resCur->free();
}
$csrf = get_csrf_token();

// ------------------ UI ------------------
$pageTitle = 'إدارة الصناديق والحسابات البنكية';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>
<style>
/* Custom cash/ bank tables styling */
.table-custom thead th {
    height: 42px;
    background: linear-gradient(45deg, #3f51b5, #2196f3);
    color: #fff;
    vertical-align: middle;
}
.table-custom td, .table-custom th { padding: .45rem .5rem; }
.table-custom tbody tr:hover {
    background-color: #f1f5ff;
}
.status-badge {
    font-size: 0.85rem;
    padding: 0.35em 0.6em;
    border-radius: 0.5rem;
}
.status-badge.active {
    background-color: #28a745;
    color: #fff;
}
.status-badge.inactive {
    background-color: #dc3545;
    color: #fff;
}
.action-buttons { display:flex; flex-direction:row; gap:4px; }
.action-buttons .btn{ margin:0 !important; }
.action-buttons .btn i {
    font-size: 0.9rem;
}
</style>
<div class="container-fluid p-4">
    <h3 class="mb-4"><i class="fas fa-university"></i> إدارة الصناديق والحسابات البنكية</h3>

    <?php foreach (get_flash() as $type => $msg): ?>
        <div class="alert alert-<?php echo $type; ?> alert-dismissible fade show" role="alert">
            <?php echo $msg; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endforeach; ?>

    <!-- Nav tabs -->
    <ul class="nav nav-tabs" id="cashBankTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link <?php echo $activeTab === 'cash' ? 'active' : ''; ?>" id="cash-tab" data-bs-toggle="tab" data-bs-target="#cashPane" type="button" role="tab">الصناديق</button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link <?php echo $activeTab === 'bank' ? 'active' : ''; ?>" id="bank-tab" data-bs-toggle="tab" data-bs-target="#bankPane" type="button" role="tab">الحسابات البنكية</button>
        </li>
    </ul>

    <!-- Tab panes -->
    <div class="tab-content border border-top-0 p-3" id="cashBankTabsContent">
        <!-- Cash boxes pane -->
        <div class="tab-pane fade <?php echo $activeTab === 'cash' ? 'show active' : ''; ?>" id="cashPane" role="tabpanel">
            <div class="d-flex justify-content-between mb-3">
                <h5 class="m-0">قائمة الصناديق</h5>
                <?php if ($auth->hasPermission('cash.edit')): ?>
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCashBoxModal"><i class="fas fa-plus"></i> إضافة صندوق</button>
                <?php endif; ?>
            </div>
            <div class="table-responsive">
                <table class="table table-hover table-bordered align-middle small">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>العملة</th>
                            <th>الرصيد الحالي</th>
                            <th>الفرع</th>
                            <th>المسؤول</th>
                            <th>الحالة</th>
                            <th class="text-center">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($cashBoxes as $idx => $box): ?>
                        <tr>
                            <td><?php echo $idx + 1; ?></td>
                            <td><?php echo htmlspecialchars($box['name']); ?></td>
                            <td><?php echo htmlspecialchars($box['currency_symbol'] ?: $box['currency_code']); ?></td>
                            <td>
                                <span class="cash-balance <?php
                                    $balance = (float)$box['current_balance'];
                                    echo $balance > 0 ? 'positive' : ($balance < 0 ? 'negative' : 'zero');
                                ?>">
                                    <?php echo number_format($balance, 2); ?>
                                    <small class="text-muted ms-1"><?php echo htmlspecialchars($box['currency_symbol'] ?: $box['currency_code']); ?></small>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($box['branch_name']); ?></td>
                            <td><?php echo htmlspecialchars($box['responsible_name']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $box['is_active'] ? 'active' : 'inactive'; ?>">
                                    <i class="fas fa-<?php echo $box['is_active'] ? 'check-circle' : 'times-circle'; ?> me-1"></i>
                                    <?php echo $box['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="action-buttons">
                                    <?php if ($auth->hasPermission('cash.movements')): ?>
                                        <button class="btn btn-sm btn-success me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#depositModal"
                                                data-id="<?php echo $box['id']; ?>"
                                                data-bs-toggle="tooltip"
                                                title="إيداع">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#withdrawModal"
                                                data-id="<?php echo $box['id']; ?>"
                                                data-bs-toggle="tooltip"
                                                title="سحب">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    <?php endif; ?>

                                    <?php if ($auth->hasPermission('cash.view')): ?>
                                        <button class="btn btn-sm btn-info me-1"
                                                onclick="viewCashBoxHistory(<?php echo $box['id']; ?>)"
                                                data-bs-toggle="tooltip"
                                                title="عرض الحركات">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    <?php endif; ?>

                                    <?php if ($auth->hasPermission('cash.edit')): ?>
                                        <button class="btn btn-sm btn-primary me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editCashBoxModal"
                                                data-id="<?php echo $box['id']; ?>"
                                                data-name="<?php echo htmlspecialchars($box['name']); ?>"
                                                data-currency-code="<?php echo $box['currency_code']; ?>"
                                                data-branch="<?php echo $box['branch_id']; ?>"
                                                data-responsible="<?php echo $box['responsible_user_id']; ?>"
                                                data-active="<?php echo $box['is_active']; ?>"
                                                data-bs-toggle="tooltip"
                                                title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <form method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من تغيير حالة هذا الصندوق؟')">
                                            <input type="hidden" name="action" value="toggle_cash_status">
                                            <input type="hidden" name="cash_box_id" value="<?php echo $box['id']; ?>">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                                            <input type="hidden" name="active_tab" value="cash">
                                            <button type="submit"
                                                    class="btn btn-sm <?php echo $box['is_active'] ? 'btn-secondary' : 'btn-success'; ?>"
                                                    data-bs-toggle="tooltip"
                                                    title="<?php echo $box['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                <i class="fas fa-<?php echo $box['is_active'] ? 'ban' : 'check'; ?>"></i>
                                            </button>
                                        </form>
                                        
                                        <form method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الصندوق؟ لا يمكن التراجع عن هذه العملية.')">
                                            <input type="hidden" name="action" value="delete_cash_box">
                                            <input type="hidden" name="cash_box_id" value="<?php echo $box['id']; ?>">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                                            <input type="hidden" name="active_tab" value="cash">
                                            <button type="submit"
                                                    class="btn btn-sm btn-danger"
                                                    data-bs-toggle="tooltip"
                                                    title="حذف الصندوق"
                                                    <?php echo !empty($box['current_balance']) || $box['current_balance'] != 0 ? 'disabled' : ''; ?>>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Bank accounts pane (simplified, similar to cash boxes) -->
        <div class="tab-pane fade <?php echo $activeTab === 'bank' ? 'show active' : ''; ?>" id="bankPane" role="tabpanel">
            <div class="d-flex justify-content-between mb-3">
                <h5 class="m-0">قائمة الحسابات البنكية</h5>
                <?php if ($auth->hasPermission('cash.edit')): ?>
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addBankAccountModal"><i class="fas fa-plus"></i> إضافة حساب</button>
                <?php endif; ?>
            </div>
            <div class="table-responsive">
                <table class="table table-hover table-bordered align-middle small">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>البنك</th>
                            <th>العملة</th>
                            <th>الرصيد الحالي</th>
                            <th>الفرع</th>
                            <th>الحالة</th>
                            <th class="text-center">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($bankAccounts as $idx => $acc): ?>
                        <tr>
                            <td><?php echo $idx + 1; ?></td>
                            <td><?php echo htmlspecialchars($acc['account_name']); ?></td>
                            <td><?php echo htmlspecialchars($acc['bank_name']); ?></td>
                            <td><?php echo htmlspecialchars($acc['currency_symbol'] ?: $acc['currency_code']); ?></td>
                            <td>
                                <span class="cash-balance <?php
                                    $balance = (float)$acc['current_balance'];
                                    echo $balance > 0 ? 'positive' : ($balance < 0 ? 'negative' : 'zero');
                                ?>">
                                    <?php echo number_format($balance, 2); ?>
                                    <small class="text-muted ms-1"><?php echo htmlspecialchars($acc['currency_symbol'] ?: $acc['currency_code']); ?></small>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($acc['branch_name']); ?></td>
                            <td>
                                <span class="status-badge <?php echo $acc['is_active'] ? 'active' : 'inactive'; ?>">
                                    <i class="fas fa-<?php echo $acc['is_active'] ? 'check-circle' : 'times-circle'; ?> me-1"></i>
                                    <?php echo $acc['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="action-buttons">
                                    <?php if ($auth->hasPermission('cash.movements')): ?>
                                        <button class="btn btn-sm btn-success me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#depositBankModal"
                                                data-id="<?php echo $acc['id']; ?>"
                                                data-bs-toggle="tooltip"
                                                title="إيداع">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#withdrawBankModal"
                                                data-id="<?php echo $acc['id']; ?>"
                                                data-bs-toggle="tooltip"
                                                title="سحب">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    <?php endif; ?>

                                    <?php if ($auth->hasPermission('cash.view')): ?>
                                        <button class="btn btn-sm btn-info me-1"
                                                onclick="viewBankAccountHistory(<?php echo $acc['id']; ?>)"
                                                data-bs-toggle="tooltip"
                                                title="عرض الحركات">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    <?php endif; ?>

                                    <?php if ($auth->hasPermission('cash.edit')): ?>
                                        <button class="btn btn-sm btn-primary me-1"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editBankAccountModal"
                                                data-id="<?php echo $acc['id']; ?>"
                                                data-name="<?php echo htmlspecialchars($acc['account_name']); ?>"
                                                data-number="<?php echo htmlspecialchars($acc['account_number']); ?>"
                                                data-bank="<?php echo htmlspecialchars($acc['bank_name']); ?>"
                                                data-currency-code="<?php echo $acc['currency_code']; ?>"
                                                data-branch="<?php echo $acc['branch_id']; ?>"
                                                data-active="<?php echo $acc['is_active']; ?>"
                                                data-bs-toggle="tooltip"
                                                title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <form method="post" class="d-inline" onsubmit="return confirm('هل أنت متأكد من تغيير حالة هذا الحساب؟')">
                                            <input type="hidden" name="action" value="toggle_bank_status">
                                            <input type="hidden" name="bank_account_id" value="<?php echo $acc['id']; ?>">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                                            <input type="hidden" name="active_tab" value="bank">
                                            <button type="submit"
                                                    class="btn btn-sm <?php echo $acc['is_active'] ? 'btn-secondary' : 'btn-success'; ?>"
                                                    data-bs-toggle="tooltip"
                                                    title="<?php echo $acc['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                <i class="fas fa-<?php echo $acc['is_active'] ? 'ban' : 'check'; ?>"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- ----------------- Modals (simplified) ----------------- -->
<!-- Add Cash Box Modal -->
<div class="modal fade" id="addCashBoxModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة صندوق</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="add_cash_box">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="cash">
                <div class="mb-3">
                    <label class="form-label">الاسم</label>
                    <input type="text" name="name" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">العملة</label>
                    <select name="currency_code" class="form-select" required>
                        <?php foreach ($currencies as $cur): ?>
                            <option value="<?php echo $cur['code']; ?>"><?php echo htmlspecialchars($cur['code'] . ' - ' . $cur['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">الرصيد الابتدائي</label>
                    <input type="number" step="0.01" name="initial_balance" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">الفرع</label>
                    <select name="branch_id" class="form-select" required>
                        <?php foreach ($branches as $br): ?>
                            <option value="<?php echo $br['id']; ?>"><?php echo htmlspecialchars($br['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">المسؤول</label>
                    <select name="responsible_user_id" class="form-select" required>
                        <?php foreach ($users as $u): ?>
                            <option value="<?php echo $u['id']; ?>"><?php echo htmlspecialchars($u['full_name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="is_active" id="isActiveCash" checked>
                    <label class="form-check-label" for="isActiveCash">نشط</label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">حفظ</button>
            </div>
        </form>
    </div>
</div>

<!-- Deposit Modal for Cash -->
<div class="modal fade" id="depositModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إيداع نقدي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="deposit_cash">
                <input type="hidden" name="cash_box_id" id="depositCashBoxId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="cash">
                <div class="mb-3">
                    <label class="form-label">المبلغ</label>
                    <input type="number" step="0.01" name="amount" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea name="description" class="form-control"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">رقم المرجع</label>
                    <input type="text" name="reference_number" class="form-control">
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">تنفيذ</button>
            </div>
        </form>
    </div>
</div>

<!-- Withdrawal Modal for Cash (similar structure) -->
<div class="modal fade" id="withdrawModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">سحب نقدي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="withdraw_cash">
                <input type="hidden" name="cash_box_id" id="withdrawCashBoxId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="cash">
                <div class="mb-3">
                    <label class="form-label">المبلغ</label>
                    <input type="number" step="0.01" name="amount" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea name="description" class="form-control"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">رقم المرجع</label>
                    <input type="text" name="reference_number" class="form-control">
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">تنفيذ</button>
            </div>
        </form>
    </div>
</div>

<!-- ================= Bank Account Modals ================= -->

<!-- Add Bank Account Modal -->
<div class="modal fade" id="addBankAccountModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-university me-2"></i>
                    إضافة حساب بنكي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="add_bank_account">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="bank">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                        <input type="text" name="account_name" class="form-control" required
                               placeholder="مثال: حساب جاري - الراجحي">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم الحساب <span class="text-danger">*</span></label>
                        <input type="text" name="account_number" class="form-control" required
                               placeholder="رقم الحساب البنكي">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم البنك <span class="text-danger">*</span></label>
                        <input type="text" name="bank_name" class="form-control" required
                               placeholder="مثال: بنك الراجحي">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">العملة <span class="text-danger">*</span></label>
                        <select name="currency_code" class="form-select" required>
                            <option value="">اختر العملة</option>
                            <?php foreach ($currencies as $cur): ?>
                                <option value="<?php echo $cur['code']; ?>">
                                    <?php echo htmlspecialchars($cur['code'] . ' - ' . $cur['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الرصيد الابتدائي</label>
                        <input type="number" step="0.01" name="initial_balance" class="form-control"
                               value="0" placeholder="0.00">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الفرع <span class="text-danger">*</span></label>
                        <select name="branch_id" class="form-select" required>
                            <option value="">اختر الفرع</option>
                            <?php foreach ($branches as $br): ?>
                                <option value="<?php echo $br['id']; ?>">
                                    <?php echo htmlspecialchars($br['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="is_active" id="isActiveBankAdd" checked>
                    <label class="form-check-label" for="isActiveBankAdd">
                        <i class="fas fa-check-circle me-1"></i>
                        حساب نشط
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ الحساب
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Deposit Bank Modal -->
<div class="modal fade" id="depositBankModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2 text-success"></i>
                    إيداع في الحساب البنكي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="deposit_bank">
                <input type="hidden" name="bank_account_id" id="depositBankAccountId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="bank">

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم إضافة المبلغ إلى رصيد الحساب البنكي
                </div>

                <div class="mb-3">
                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                        <input type="number" step="0.01" name="amount" class="form-control" required
                               placeholder="أدخل المبلغ" min="0.01">
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea name="description" class="form-control" rows="3"
                              placeholder="وصف العملية (اختياري)"></textarea>
                </div>

                <div class="mb-3">
                    <label class="form-label">رقم المرجع</label>
                    <input type="text" name="reference_number" class="form-control"
                           placeholder="رقم الإيصال أو المرجع (اختياري)">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>
                    تنفيذ الإيداع
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Withdraw Bank Modal -->
<div class="modal fade" id="withdrawBankModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-minus-circle me-2 text-warning"></i>
                    سحب من الحساب البنكي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="withdraw_bank">
                <input type="hidden" name="bank_account_id" id="withdrawBankAccountId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="bank">

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم خصم المبلغ من رصيد الحساب البنكي
                </div>

                <div class="mb-3">
                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-dollar-sign"></i>
                        </span>
                        <input type="number" step="0.01" name="amount" class="form-control" required
                               placeholder="أدخل المبلغ" min="0.01">
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea name="description" class="form-control" rows="3"
                              placeholder="وصف العملية (اختياري)"></textarea>
                </div>

                <div class="mb-3">
                    <label class="form-label">رقم المرجع</label>
                    <input type="text" name="reference_number" class="form-control"
                           placeholder="رقم الإيصال أو المرجع (اختياري)">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-warning">
                    <i class="fas fa-minus me-1"></i>
                    تنفيذ السحب
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Bank Account Modal -->
<div class="modal fade" id="editBankAccountModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الحساب البنكي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="edit_bank_account">
                <input type="hidden" name="bank_account_id" id="editBankAccountId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="bank">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                        <input type="text" name="account_name" id="editAccountName" class="form-control" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">رقم الحساب <span class="text-danger">*</span></label>
                        <input type="text" name="account_number" id="editAccountNumber" class="form-control" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم البنك <span class="text-danger">*</span></label>
                        <input type="text" name="bank_name" id="editBankName" class="form-control" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">العملة <span class="text-danger">*</span></label>
                        <select name="currency_code" id="editCurrencyCode" class="form-select" required>
                            <?php foreach ($currencies as $cur): ?>
                                <option value="<?php echo $cur['code']; ?>">
                                    <?php echo htmlspecialchars($cur['code'] . ' - ' . $cur['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">الفرع <span class="text-danger">*</span></label>
                    <select name="branch_id" id="editBranchId" class="form-select" required>
                        <?php foreach ($branches as $br): ?>
                            <option value="<?php echo $br['id']; ?>">
                                <?php echo htmlspecialchars($br['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="is_active" id="editIsActive">
                    <label class="form-check-label" for="editIsActive">
                        <i class="fas fa-check-circle me-1"></i>
                        حساب نشط
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ التعديلات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- ================= Cash Box Modals ================= -->

<!-- Add Cash Box Modal -->
<div class="modal fade" id="addCashBoxModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cash-register me-2"></i>
                    إضافة صندوق نقدي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="add_cash_box">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="cash">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الصندوق <span class="text-danger">*</span></label>
                        <input type="text" name="name" class="form-control" required
                               placeholder="مثال: صندوق الدولار الأمريكي">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">العملة <span class="text-danger">*</span></label>
                        <select name="currency_code" class="form-select" required>
                            <option value="">اختر العملة</option>
                            <?php foreach ($currencies as $cur): ?>
                                <option value="<?php echo $cur['code']; ?>">
                                    <?php echo htmlspecialchars($cur['code'] . ' - ' . $cur['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الرصيد الابتدائي</label>
                        <input type="number" step="0.01" name="initial_balance" class="form-control"
                               value="0" placeholder="0.00">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الفرع <span class="text-danger">*</span></label>
                        <select name="branch_id" class="form-select" required>
                            <option value="">اختر الفرع</option>
                            <?php foreach ($branches as $br): ?>
                                <option value="<?php echo $br['id']; ?>">
                                    <?php echo htmlspecialchars($br['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">المسؤول <span class="text-danger">*</span></label>
                    <select name="responsible_user_id" class="form-select" required>
                        <option value="">اختر المسؤول</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?php echo $user['id']; ?>">
                                <?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="is_active" id="isActiveCashAdd" checked>
                    <label class="form-check-label" for="isActiveCashAdd">
                        <i class="fas fa-check-circle me-1"></i>
                        صندوق نشط
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ الصندوق
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Cash Box Modal -->
<div class="modal fade" id="editCashBoxModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form method="post" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الصندوق النقدي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" name="action" value="edit_cash_box">
                <input type="hidden" name="cash_box_id" id="editCashBoxId">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">
                <input type="hidden" name="active_tab" value="cash">

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">اسم الصندوق <span class="text-danger">*</span></label>
                        <input type="text" name="name" id="editCashBoxName" class="form-control" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">العملة <span class="text-danger">*</span></label>
                        <select name="currency_code" id="editCashBoxCurrency" class="form-select" required>
                            <?php foreach ($currencies as $cur): ?>
                                <option value="<?php echo $cur['code']; ?>">
                                    <?php echo htmlspecialchars($cur['code'] . ' - ' . $cur['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">الفرع <span class="text-danger">*</span></label>
                        <select name="branch_id" id="editCashBoxBranch" class="form-select" required>
                            <?php foreach ($branches as $br): ?>
                                <option value="<?php echo $br['id']; ?>">
                                    <?php echo htmlspecialchars($br['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">المسؤول <span class="text-danger">*</span></label>
                        <select name="responsible_user_id" id="editCashBoxResponsible" class="form-select" required>
                            <?php foreach ($users as $user): ?>
                                <option value="<?php echo $user['id']; ?>">
                                    <?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="is_active" id="editCashBoxActive">
                    <label class="form-check-label" for="editCashBoxActive">
                        <i class="fas fa-check-circle me-1"></i>
                        صندوق نشط
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ التعديلات
                </button>
            </div>
        </form>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Pass cash box ID to deposit / withdraw modals
var depositModalEl = document.getElementById('depositModal');
if (depositModalEl) {
    depositModalEl.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        document.getElementById('depositCashBoxId').value = button.getAttribute('data-id');
    });
}
var withdrawModalEl = document.getElementById('withdrawModal');
if (withdrawModalEl) {
    withdrawModalEl.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        document.getElementById('withdrawCashBoxId').value = button.getAttribute('data-id');
    });
}

// ================= Bank Account Modals JavaScript =================

// Deposit Bank Modal
var depositBankModalEl = document.getElementById('depositBankModal');
if (depositBankModalEl) {
    depositBankModalEl.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        document.getElementById('depositBankAccountId').value = button.getAttribute('data-id');

        // Clear form
        var form = depositBankModalEl.querySelector('form');
        form.querySelector('input[name="amount"]').value = '';
        form.querySelector('textarea[name="description"]').value = '';
        form.querySelector('input[name="reference_number"]').value = '';
    });
}

// Withdraw Bank Modal
var withdrawBankModalEl = document.getElementById('withdrawBankModal');
if (withdrawBankModalEl) {
    withdrawBankModalEl.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;
        document.getElementById('withdrawBankAccountId').value = button.getAttribute('data-id');

        // Clear form
        var form = withdrawBankModalEl.querySelector('form');
        form.querySelector('input[name="amount"]').value = '';
        form.querySelector('textarea[name="description"]').value = '';
        form.querySelector('input[name="reference_number"]').value = '';
    });
}

// Edit Bank Account Modal
var editBankModalEl = document.getElementById('editBankAccountModal');
if (editBankModalEl) {
    editBankModalEl.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;

        // Set account ID
        document.getElementById('editBankAccountId').value = button.getAttribute('data-id');

        // Populate form fields
        document.getElementById('editAccountName').value = button.getAttribute('data-name') || '';
        document.getElementById('editAccountNumber').value = button.getAttribute('data-number') || '';
        document.getElementById('editBankName').value = button.getAttribute('data-bank') || '';
        document.getElementById('editCurrencyCode').value = button.getAttribute('data-currency-code') || '';
        document.getElementById('editBranchId').value = button.getAttribute('data-branch') || '';
        document.getElementById('editIsActive').checked = button.getAttribute('data-active') == '1';
    });
}

// View Bank Account History Function
function viewBankAccountHistory(accountId) {
    // Create a modal or redirect to a history page
    var url = 'bank_account_history.php?id=' + accountId;

    // Option 1: Open in new window/tab
    window.open(url, '_blank');

    // Option 2: Redirect in same window
    // window.location.href = url;

    // Option 3: Load content in a modal (requires AJAX)
    /*
    fetch(url)
        .then(response => response.text())
        .then(html => {
            // Show in a modal
            showHistoryModal(html);
        })
        .catch(error => {
            console.error('Error loading history:', error);
            alert('خطأ في تحميل تاريخ الحساب');
        });
    */
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    // Add validation to amount inputs
    var amountInputs = document.querySelectorAll('input[name="amount"]');
    amountInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            var value = parseFloat(this.value);
            if (value <= 0) {
                this.setCustomValidity('المبلغ يجب أن يكون أكبر من صفر');
            } else {
                this.setCustomValidity('');
            }
        });
    });

    // Add confirmation for withdrawal operations
    var withdrawForms = document.querySelectorAll('form[action*="withdraw"]');
    withdrawForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            var amount = form.querySelector('input[name="amount"]').value;
            if (!confirm('هل أنت متأكد من سحب مبلغ ' + amount + '؟')) {
                e.preventDefault();
            }
        });
    });
});

// ================= Cash Box Modals JavaScript =================

// Edit Cash Box Modal
var editCashBoxModalEl = document.getElementById('editCashBoxModal');
if (editCashBoxModalEl) {
    editCashBoxModalEl.addEventListener('show.bs.modal', function (event) {
        var button = event.relatedTarget;

        // Set cash box ID
        document.getElementById('editCashBoxId').value = button.getAttribute('data-id');

        // Populate form fields
        document.getElementById('editCashBoxName').value = button.getAttribute('data-name') || '';
        document.getElementById('editCashBoxCurrency').value = button.getAttribute('data-currency') || '';
        document.getElementById('editCashBoxBranch').value = button.getAttribute('data-branch') || '';
        document.getElementById('editCashBoxResponsible').value = button.getAttribute('data-responsible') || '';
        document.getElementById('editCashBoxActive').checked = button.getAttribute('data-active') == '1';
    });
}

// View Cash Box History Function
function viewCashBoxHistory(boxId) {
    // Create a modal or redirect to a history page
    var url = 'cash_box_history.php?id=' + boxId;

    // Option 1: Open in new window/tab
    window.open(url, '_blank');

    // Option 2: Redirect in same window
    // window.location.href = url;

    // Option 3: Load content in a modal (requires AJAX)
    /*
    fetch(url)
        .then(response => response.text())
        .then(html => {
            // Show in a modal
            showHistoryModal(html);
        })
        .catch(error => {
            console.error('Error loading history:', error);
            alert('خطأ في تحميل تاريخ الصندوق');
        });
    */
}

// Auto-focus on amount field when modals open
document.addEventListener('DOMContentLoaded', function() {
    var modals = ['depositModal', 'withdrawModal', 'depositBankModal', 'withdrawBankModal'];
    modals.forEach(function(modalId) {
        var modal = document.getElementById(modalId);
        if (modal) {
            modal.addEventListener('shown.bs.modal', function() {
                var amountInput = modal.querySelector('input[name="amount"]');
                if (amountInput) {
                    amountInput.focus();
                }
            });
        }
    });
});

// ================= Additional Enhancements =================

// Format numbers in real-time
document.addEventListener('DOMContentLoaded', function() {
    var numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            if (this.value && !isNaN(this.value)) {
                var value = parseFloat(this.value);
                this.value = value.toFixed(2);
            }
        });
    });
});

// Add loading state to form submissions
document.addEventListener('DOMContentLoaded', function() {
    var forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function() {
            var submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                var originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري المعالجة...';

                // Re-enable after 5 seconds as fallback
                setTimeout(function() {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 5000);
            }
        });
    });
});

// Confirm dangerous actions
document.addEventListener('DOMContentLoaded', function() {
    var dangerousForms = document.querySelectorAll('form[action*="toggle"], form[action*="delete"]');
    dangerousForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            var action = form.querySelector('input[name="action"]').value;
            var confirmMessage = 'هل أنت متأكد من تنفيذ هذا الإجراء؟';

            if (action.includes('toggle')) {
                confirmMessage = 'هل أنت متأكد من تغيير حالة هذا العنصر؟';
            } else if (action.includes('delete')) {
                confirmMessage = 'هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.';
            }

            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });
    });
});

// Auto-save form data to localStorage (for recovery)
document.addEventListener('DOMContentLoaded', function() {
    var forms = document.querySelectorAll('form[method="post"]');
    forms.forEach(function(form) {
        var formId = form.id || 'form_' + Math.random().toString(36).substr(2, 9);

        // Load saved data
        var savedData = localStorage.getItem('form_data_' + formId);
        if (savedData) {
            try {
                var data = JSON.parse(savedData);
                Object.keys(data).forEach(function(key) {
                    var input = form.querySelector('[name="' + key + '"]');
                    if (input && input.type !== 'hidden' && input.name !== 'csrf_token') {
                        if (input.type === 'checkbox') {
                            input.checked = data[key];
                        } else {
                            input.value = data[key];
                        }
                    }
                });
            } catch (e) {
                console.log('Error loading saved form data:', e);
            }
        }

        // Save data on input
        var inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(function(input) {
            if (input.type !== 'hidden' && input.name !== 'csrf_token') {
                input.addEventListener('input', function() {
                    var formData = {};
                    inputs.forEach(function(inp) {
                        if (inp.type !== 'hidden' && inp.name !== 'csrf_token') {
                            if (inp.type === 'checkbox') {
                                formData[inp.name] = inp.checked;
                            } else {
                                formData[inp.name] = inp.value;
                            }
                        }
                    });
                    localStorage.setItem('form_data_' + formId, JSON.stringify(formData));
                });
            }
        });

        // Clear saved data on successful submit
        form.addEventListener('submit', function() {
            localStorage.removeItem('form_data_' + formId);
        });
    });
});
</script>
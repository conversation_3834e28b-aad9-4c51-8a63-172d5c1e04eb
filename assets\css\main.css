/*
 * Trust Plus Financial System - Main CSS
 * Global styles, typography, color palette variables, and basic RTL rules
 */

/* ===== CSS ROOT VARIABLES - COLOR PALETTE ===== */
:root {
  /* Primary Colors */
  --primary: #0d6efd;
  --primary-rgb: 13, 110, 253;
  --primary-gradient: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);

  /* Secondary Colors */
  --secondary: #6c757d;
  --secondary-rgb: 108, 117, 125;
  --secondary-gradient: linear-gradient(135deg, #6c757d 0%, #495057 100%);

  /* Success Colors */
  --success: #198754;
  --success-rgb: 25, 135, 84;
  --success-gradient: linear-gradient(135deg, #198754 0%, #146c43 100%);

  /* Danger Colors */
  --danger: #dc3545;
  --danger-rgb: 220, 53, 69;
  --danger-gradient: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);

  /* Warning Colors */
  --warning: #ffc107;
  --warning-rgb: 255, 193, 7;
  --warning-gradient: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);

  /* Info Colors */
  --info: #0dcaf0;
  --info-rgb: 13, 202, 240;
  --info-gradient: linear-gradient(135deg, #0dcaf0 0%, #0aa2c0 100%);

  /* Light/Dark Colors */
  --light: #f8f9fa;
  --light-rgb: 248, 249, 250;
  --dark: #212529;
  --dark-rgb: 33, 37, 41;

  /* Text Colors */
  --text-color: #212529;
  --text-muted: #6c757d;
  --text-light: #ffffff;

  /* Background Colors */
  --bg-color: #ffffff;
  --bg-light: #f8f9fa;
  --bg-dark: #212529;

  /* Border Colors */
  --border-color: #dee2e6;
  --border-light: #e9ecef;
  --border-dark: #495057;
}

/* ===== GLOBAL BODY STYLES ===== */
html {
  direction: rtl; /* RTL support for Arabic */
  text-align: right; /* Default text alignment for RTL */
}

body {
  font-family: 'Arial', 'Segoe UI', 'Tahoma', sans-serif;
  background-color: var(--bg-light);
  color: var(--text-color);
  direction: rtl;
  text-align: right;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  position: relative;
}

/* تحسينات الاستجابة العامة */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* تطبيق margin-right فقط للصفحات التي تستخدم main-content */
.main-content {
  margin-right: 250px;
  transition: margin-right 0.3s ease;
  min-height: 100vh;
  width: calc(100% - 250px);
  position: relative;
}

@media (max-width: 992px) {
  .main-content {
    margin-right: 0;
    width: 100%;
  }
}

/* ===== BASIC LINK STYLES ===== */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary);
  opacity: 0.8;
}

a:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* ===== HEADING STYLES ===== */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-color);
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* ===== GLOBAL RTL OVERRIDES ===== */
.float-start { float: right !important; }
.float-end { float: left !important; }
.text-start { text-align: right !important; }
.text-end { text-align: left !important; }

/* RTL margin and padding utilities */
.me-auto { margin-left: auto !important; }
.ms-auto { margin-right: auto !important; }

/* ===== GRADIENT BACKGROUND UTILITIES ===== */
.bg-primary-gradient { background: var(--primary-gradient); }
.bg-secondary-gradient { background: var(--secondary-gradient); }
.bg-success-gradient { background: var(--success-gradient); }
.bg-danger-gradient { background: var(--danger-gradient); }
.bg-warning-gradient { background: var(--warning-gradient); }
.bg-info-gradient { background: var(--info-gradient); }

/* ===== UTILITY CLASSES ===== */
.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--secondary) !important; }
.text-success { color: var(--success) !important; }
.text-danger { color: var(--danger) !important; }
.text-warning { color: var(--warning) !important; }
.text-info { color: var(--info) !important; }
.text-light { color: var(--light) !important; }
.text-dark { color: var(--dark) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--secondary) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-info { background-color: var(--info) !important; }
.bg-light { background-color: var(--light) !important; }
.bg-dark { background-color: var(--dark) !important; }

/* ===== BORDER UTILITIES ===== */
.border-primary { border-color: var(--primary) !important; }
.border-secondary { border-color: var(--secondary) !important; }
.border-success { border-color: var(--success) !important; }
.border-danger { border-color: var(--danger) !important; }
.border-warning { border-color: var(--warning) !important; }
.border-info { border-color: var(--info) !important; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 767.98px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
}

@media (min-width: 768px) {
  .d-md-none { display: block !important; }
  .d-md-block { display: none !important; }
}

/* ===== ACTION BUTTONS ENHANCEMENTS ===== */
.action-buttons {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.action-buttons .btn {
  transition: all 0.3s ease;
  border-radius: 6px;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1;
  min-width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-buttons .btn i {
  font-size: 0.875rem;
}

/* Specific button colors for actions */
.action-buttons .btn-success:hover {
  background-color: var(--success);
  border-color: var(--success);
}

.action-buttons .btn-warning:hover {
  background-color: var(--warning);
  border-color: var(--warning);
}

.action-buttons .btn-info:hover {
  background-color: var(--info);
  border-color: var(--info);
}

.action-buttons .btn-primary:hover {
  background-color: var(--primary);
  border-color: var(--primary);
}

.action-buttons .btn-secondary:hover {
  background-color: var(--secondary);
  border-color: var(--secondary);
}

/* ===== TABLE ENHANCEMENTS ===== */
.table-hover tbody tr:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
}

.table th {
  font-weight: 600;
  background-color: var(--bg-light);
  border-bottom: 2px solid var(--border-color);
}

/* ===== MODAL ENHANCEMENTS ===== */
.modal-header {
  background: var(--primary-gradient);
  color: white;
  border-bottom: none;
}

.modal-header .btn-close {
  filter: invert(1);
}

.modal-title i {
  opacity: 0.9;
}

/* ===== ALERT ENHANCEMENTS ===== */
.alert {
  border: none;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert i {
  margin-left: 8px;
}

/* ===== CASH MANAGEMENT SPECIFIC STYLES ===== */
.cash-balance {
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.cash-balance.positive {
  color: var(--success);
}

.cash-balance.negative {
  color: var(--danger);
}

.cash-balance.zero {
  color: var(--text-muted);
}

.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(var(--success-rgb), 0.1);
  color: var(--success);
  border: 1px solid rgba(var(--success-rgb), 0.3);
}

.status-badge.inactive {
  background-color: rgba(var(--secondary-rgb), 0.1);
  color: var(--secondary);
  border: 1px solid rgba(var(--secondary-rgb), 0.3);
}

/* ===== TAB ENHANCEMENTS ===== */
.nav-tabs .nav-link {
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.tab-content {
  background-color: var(--bg-color);
  border-radius: 0 8px 8px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* ===== FORM ENHANCEMENTS FOR MODALS ===== */
.modal-body .form-label {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.modal-body .form-control:focus,
.modal-body .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.modal-body .input-group-text {
  background-color: var(--bg-light);
  border-color: var(--border-color);
  color: var(--text-muted);
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 0.125rem;
  }

  .action-buttons .btn {
    width: 100%;
    justify-content: flex-start;
    padding: 0.25rem 0.5rem;
  }

  .action-buttons .btn i {
    margin-left: 8px;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .modal-dialog {
    margin: 0.5rem;
  }
}

/* ===== HISTORY PAGES SPECIFIC STYLES ===== */
.page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
  border-radius: 12px;
}

.page-header h1 {
  color: white;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
}

.dashboard-card {
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.dashboard-card:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.dashboard-card .card-header {
  background: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
  border-radius: 12px 12px 0 0;
  padding: 1rem 1.5rem;
}

.dashboard-card .card-body {
  padding: 1.5rem;
}

.dashboard-card .card-title {
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 0;
}

/* Movement table enhancements */
.table-hover tbody tr:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
  transform: scale(1.01);
  transition: all 0.2s ease;
}

.movement-row {
  border-right: 3px solid transparent;
  transition: all 0.3s ease;
}

.movement-row.deposit {
  border-right-color: var(--success);
}

.movement-row.withdrawal {
  border-right-color: var(--warning);
}

/* Summary cards */
.summary-card {
  background: linear-gradient(135deg, var(--bg-color) 0%, var(--bg-light) 100%);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0.5rem 0;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filter section */
.filter-section {
  background: var(--bg-light);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.filter-section .form-label {
  font-weight: 500;
  color: var(--text-color);
}

/* Pagination improvements */
.pagination .page-link {
  border-radius: 6px;
  margin: 0 2px;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
  transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

/* Export buttons */
.export-buttons .btn {
  margin: 0 0.25rem;
  border-radius: 6px;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.export-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Badge improvements */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 6px;
  font-weight: 500;
}

.badge i {
  margin-left: 0.25rem;
}

/* Code styling */
code {
  background-color: var(--bg-light);
  color: var(--primary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  border: 1px solid var(--border-color);
}

/* Empty state styling */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-muted);
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h5 {
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* ===== TRANSFERS TABLE ENHANCEMENTS ===== */
.table-responsive {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: white;
  margin-bottom: 2rem;
}

.transfers-table {
  font-size: 0.875rem;
  margin-bottom: 0;
  background: white;
}

.transfers-table th {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  font-weight: 600;
  text-align: center;
  vertical-align: middle;
  border: none;
  padding: 1rem 0.75rem;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  position: relative;
}

.transfers-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%);
}

.transfers-table td {
  vertical-align: middle;
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  transition: all 0.3s ease;
}

.transfers-table tbody tr {
  transition: all 0.3s ease;
}

.transfers-table tbody tr:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.transfers-table tbody tr:nth-child(even) {
  background-color: #fafbfc;
}

.transfers-table tbody tr:nth-child(even):hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Transfer status badges */
.status-badge {
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.status-badge i {
  font-size: 0.8rem;
}

/* Enhanced badge colors */
.badge.bg-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border-color: #059669;
}

.badge.bg-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
  border-color: #d97706;
  color: white !important;
}

.badge.bg-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  border-color: #dc2626;
}

.badge.bg-info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
  border-color: #0891b2;
}

.badge.bg-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  border-color: #2563eb;
}

.badge.bg-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
  border-color: #4b5563;
}

/* Action buttons group */
.btn-group-vertical .btn {
  border-radius: 8px;
  margin-bottom: 4px;
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  transition: all 0.3s ease;
  font-weight: 500;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.btn-group-vertical .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-group-vertical .btn:hover::before {
  left: 100%;
}

.btn-group-vertical .btn:hover {
  transform: translateX(-3px) translateY(-1px);
  box-shadow: 4px 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-group-vertical .btn i {
  font-size: 0.8rem;
  margin-right: 0.25rem;
}

/* Enhanced button colors */
.btn-outline-primary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-color: #3b82f6;
  color: #3b82f6;
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #2563eb;
  color: white;
}

.btn-outline-success {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: #10b981;
  color: #10b981;
}

.btn-outline-success:hover {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #059669;
  color: white;
}

.btn-outline-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #06b6d4;
  color: #06b6d4;
}

.btn-outline-info:hover {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  border-color: #0891b2;
  color: white;
}

/* Transfer amount styling */
.transfer-amount {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 700;
  font-size: 1rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.transfer-fee {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  font-size: 0.9rem;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Transaction number styling */
.transaction-number {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Transfer type badges */
.transfer-type-badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Date and time styling */
.transfer-date {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.transfer-time {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  background: #f3f4f6;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  display: inline-block;
  margin-top: 0.125rem;
}

/* User info styling */
.user-info {
  font-weight: 500;
  color: #374151;
}

.user-contact {
  font-size: 0.75rem;
  color: #6b7280;
  background: #f9fafb;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  display: inline-block;
}

/* Cash balance styling */
.cash-balance {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 700;
  font-size: 0.95rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
}

.cash-balance.positive {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border: 1px solid #10b981;
}

.cash-balance.negative {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 1px solid #ef4444;
}

.cash-balance.zero {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  border: 1px solid #9ca3af;
}

/* Edit movement form styling */
.edit-movement-form .input-group {
  transition: all 0.3s ease;
}

.edit-movement-form .input-group.border-success {
  border: 2px solid #10b981 !important;
  border-radius: 8px;
}

.edit-movement-form .input-group.border-warning {
  border: 2px solid #f59e0b !important;
  border-radius: 8px;
}

.edit-movement-form .input-group-text {
  background: #f8fafc;
  border: none;
  font-weight: 600;
}

.edit-movement-form .form-control:focus,
.edit-movement-form .form-select:focus {
  border-color: inherit;
  box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.15);
}

/* Movement info card */
.movement-info-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
}

.movement-info-card .table td {
  border: none;
  padding: 0.5rem 0;
}

.movement-info-card .table td:first-child {
  color: #64748b;
  font-size: 0.875rem;
}

.movement-info-card .table td:last-child {
  font-weight: 500;
  color: #1e293b;
}

/* Modal enhancements for transfer operations */
.transfer-modal .modal-header {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  padding: 1.5rem;
}

.transfer-modal .modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.transfer-modal .modal-title {
  font-weight: 600;
  font-size: 1.25rem;
}

.transfer-modal .modal-title i {
  opacity: 0.9;
  margin-right: 0.5rem;
}

.transfer-modal .btn-close {
  filter: invert(1);
  opacity: 0.8;
}

.transfer-modal .btn-close:hover {
  opacity: 1;
}

.transfer-modal .modal-body {
  padding: 1.5rem;
}

.transfer-modal .form-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.transfer-modal .form-select,
.transfer-modal .form-control {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

.transfer-modal .form-select:focus,
.transfer-modal .form-control:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.15);
  transform: translateY(-1px);
}

.transfer-modal .modal-footer {
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.transfer-modal .btn {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.transfer-modal .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Status update specific styling */
.status-update-form .alert {
  border-radius: 8px;
  border: none;
  font-size: 0.875rem;
}

.status-update-form .alert i {
  margin-right: 8px;
}

/* Delivery status specific styling */
.delivery-update-form .form-select option {
  padding: 0.5rem;
}

/* Transfer details modal */
.transfer-details-table {
  font-size: 0.875rem;
}

.transfer-details-table td {
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.transfer-details-table .fw-medium {
  color: var(--text-color);
  min-width: 120px;
}

/* Responsive improvements for transfers */
@media (max-width: 1200px) {
  .transfers-table th,
  .transfers-table td {
    padding: 0.75rem 0.5rem;
  }

  .btn-group-vertical .btn {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }
}

@media (max-width: 768px) {
  .transfers-table {
    font-size: 0.75rem;
  }

  .transfers-table th,
  .transfers-table td {
    padding: 0.5rem 0.25rem;
  }

  .btn-group-vertical .btn {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
    margin-bottom: 2px;
  }

  .status-badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
  }

  .transfer-amount {
    font-size: 0.8rem;
  }

  .transaction-number {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .user-info {
    font-size: 0.8rem;
  }

  .user-contact {
    font-size: 0.65rem;
  }

  .transfer-date {
    font-size: 0.75rem;
  }

  .transfer-time {
    font-size: 0.65rem;
  }
}

@media (max-width: 576px) {
  .transfers-table th,
  .transfers-table td {
    padding: 0.375rem 0.125rem;
  }

  .btn-group-vertical .btn {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }

  .status-badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Auto-refresh indicator */
.auto-refresh-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--success);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  z-index: 1050;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.auto-refresh-indicator.show {
  opacity: 1;
}

.auto-refresh-indicator i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



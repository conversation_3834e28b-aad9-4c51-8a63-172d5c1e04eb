<?php
/**
 * AJAX endpoint to get bank account movement details
 */

// Required includes
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/bank_manager.php';

// Set JSON header
header('Content-Type: application/json');

// Initialize classes
$auth = new Auth();
$db = new Database();
$bankManager = new BankManager($db);

try {
    // Authentication check
    $auth->requireLogin();
    $auth->requirePermission('cash.view');
    
    // Get movement ID
    $movementId = (int)($_GET['id'] ?? 0);
    
    if ($movementId <= 0) {
        throw new Exception('معرف الحركة غير صالح');
    }
    
    // Get movement details
    $movement = $bankManager->getBankMovementById($movementId);
    
    if (!$movement) {
        throw new Exception('الحركة غير موجودة');
    }
    
    // Get bank account details
    $bankAccount = $bankManager->getBankAccountById($movement['bank_account_id']);
    
    if (!$bankAccount) {
        throw new Exception('الحساب البنكي غير موجود');
    }
    
    // Build HTML content
    $html = '
    <div class="row">
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-university me-2"></i>
                معلومات الحركة البنكية
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">رقم الحركة:</td>
                    <td><code>' . htmlspecialchars($movement['id']) . '</code></td>
                </tr>
                <tr>
                    <td class="fw-medium">نوع الحركة:</td>
                    <td>';
    
    if ($movement['type'] === 'deposit') {
        $html .= '<span class="badge bg-success"><i class="fas fa-arrow-down me-1"></i> إيداع بنكي</span>';
    } else {
        $html .= '<span class="badge bg-warning"><i class="fas fa-arrow-up me-1"></i> سحب بنكي</span>';
    }
    
    $html .= '</td>
                </tr>
                <tr>
                    <td class="fw-medium">المبلغ:</td>
                    <td class="fw-bold ' . ($movement['type'] === 'deposit' ? 'text-success' : 'text-warning') . '">
                        ' . ($movement['type'] === 'deposit' ? '+' : '-') . number_format($movement['amount'], 2) . ' 
                        ' . htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']) . '
                    </td>
                </tr>
                <tr>
                    <td class="fw-medium">الرصيد قبل الحركة:</td>
                    <td>' . number_format($movement['balance_before'], 2) . ' ' . htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">الرصيد بعد الحركة:</td>
                    <td class="fw-bold">' . number_format($movement['balance_after'], 2) . ' ' . htmlspecialchars($bankAccount['currency_symbol'] ?: $bankAccount['currency_code']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">التاريخ والوقت:</td>
                    <td>' . date('Y-m-d H:i:s', strtotime($movement['created_at'])) . '</td>
                </tr>
            </table>
        </div>
        
        <div class="col-md-6">
            <h6 class="text-primary mb-3">
                <i class="fas fa-building me-2"></i>
                معلومات الحساب البنكي
            </h6>
            
            <table class="table table-sm">
                <tr>
                    <td class="fw-medium">اسم الحساب:</td>
                    <td>' . htmlspecialchars($bankAccount['account_name']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">رقم الحساب:</td>
                    <td><code>' . htmlspecialchars($bankAccount['account_number']) . '</code></td>
                </tr>
                <tr>
                    <td class="fw-medium">البنك:</td>
                    <td>' . htmlspecialchars($bankAccount['bank_name']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">العملة:</td>
                    <td>' . htmlspecialchars($bankAccount['currency_name'] . ' (' . $bankAccount['currency_code'] . ')') . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">الفرع:</td>
                    <td>' . htmlspecialchars($bankAccount['branch_name']) . '</td>
                </tr>
                <tr>
                    <td class="fw-medium">المستخدم المنفذ:</td>
                    <td>' . htmlspecialchars($movement['user_name'] ?: 'غير محدد') . '</td>
                </tr>
            </table>
        </div>
    </div>';
    
    // Add reference and receipt information
    if (!empty($movement['reference_number']) || !empty($movement['receipt_number'])) {
        $html .= '
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-receipt me-2"></i>
                    معلومات المراجع والإيصالات
                </h6>
                <div class="row">';
        
        if (!empty($movement['reference_number'])) {
            $html .= '
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title">رقم المرجع</h6>
                                <code class="fs-5">' . htmlspecialchars($movement['reference_number']) . '</code>
                            </div>
                        </div>
                    </div>';
        }
        
        if (!empty($movement['receipt_number'])) {
            $html .= '
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h6 class="card-title">رقم الإيصال</h6>
                                <code class="fs-5 text-info">' . htmlspecialchars($movement['receipt_number']) . '</code>
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-info" onclick="printReceipt(' . $movement['id'] . ')">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة الإيصال
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>';
        }
        
        $html .= '
                </div>
            </div>
        </div>';
    }
    
    // Add description if available
    if (!empty($movement['description'])) {
        $html .= '
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-file-alt me-2"></i>
                    وصف الحركة
                </h6>
                <div class="p-3 bg-light rounded">
                    ' . nl2br(htmlspecialchars($movement['description'])) . '
                </div>
            </div>
        </div>';
    }
    
    // Add processing information if available
    if (isset($movement['processing_date']) && $movement['processing_date']) {
        $html .= '
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-cogs me-2"></i>
                    معلومات المعالجة
                </h6>
                <div class="alert alert-info">
                    <div class="row">
                        <div class="col-md-6">
                            <small><strong>تاريخ المعالجة:</strong> ' . date('Y-m-d H:i:s', strtotime($movement['processing_date'])) . '</small>
                        </div>
                        <div class="col-md-6">
                            <small><strong>حالة المعالجة:</strong> 
                                <span class="badge bg-success">مكتملة</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    // Add audit trail if available
    if (isset($movement['created_at']) && isset($movement['updated_at']) && $movement['created_at'] !== $movement['updated_at']) {
        $html .= '
        <div class="row mt-4">
            <div class="col-12">
                <h6 class="text-primary mb-3">
                    <i class="fas fa-history me-2"></i>
                    سجل التعديلات
                </h6>
                <div class="alert alert-secondary">
                    <small>
                        <strong>تاريخ الإنشاء:</strong> ' . date('Y-m-d H:i:s', strtotime($movement['created_at'])) . '<br>
                        <strong>آخر تعديل:</strong> ' . date('Y-m-d H:i:s', strtotime($movement['updated_at'])) . '
                    </small>
                </div>
            </div>
        </div>';
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

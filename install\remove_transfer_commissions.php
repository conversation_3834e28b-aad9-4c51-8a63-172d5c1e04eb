<?php
/**
 * Remove Transfer Commission System
 * This script completely removes the transfer commission system from the database
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>حذف نظام عمولات التحويل</h2>";

try {
    $db = Database::getConnection();
    
    // Start transaction
    $db->begin_transaction();
    
    echo "<p>جاري حذف جدول عمولات التحويل...</p>";
    
    // Drop transfer_commissions table
    $sql = "DROP TABLE IF EXISTS transfer_commissions";
    if ($db->query($sql)) {
        echo "<p style='color: green;'>✓ تم حذف جدول transfer_commissions بنجاح</p>";
    } else {
        throw new Exception("فشل في حذف جدول transfer_commissions: " . $db->error);
    }
    
    echo "<p>جاري حذف صلاحيات عمولات التحويل...</p>";
    
    // Remove commission-related permissions from role_permissions
    $sql = "DELETE FROM role_permissions WHERE permission_id IN (
                SELECT id FROM permissions WHERE name LIKE 'transfers.commissions%'
            )";
    if ($db->query($sql)) {
        echo "<p style='color: green;'>✓ تم حذف صلاحيات عمولات التحويل من جدول role_permissions</p>";
    } else {
        throw new Exception("فشل في حذف صلاحيات عمولات التحويل: " . $db->error);
    }
    
    // Remove commission-related permissions
    $sql = "DELETE FROM permissions WHERE name LIKE 'transfers.commissions%'";
    if ($db->query($sql)) {
        echo "<p style='color: green;'>✓ تم حذف صلاحيات عمولات التحويل من جدول permissions</p>";
    } else {
        throw new Exception("فشل في حذف صلاحيات عمولات التحويل: " . $db->error);
    }
    
    // Clean up any orphaned role_permissions
    $sql = "DELETE rp FROM role_permissions rp 
            LEFT JOIN permissions p ON rp.permission_id = p.id 
            WHERE p.id IS NULL";
    if ($db->query($sql)) {
        echo "<p style='color: green;'>✓ تم تنظيف role_permissions من الصلاحيات المفقودة</p>";
    }
    
    // Commit transaction
    $db->commit();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ تم حذف نظام عمولات التحويل بنجاح</h3>";
    echo "<p style='color: #155724; margin: 0;'>تم حذف جميع الملفات والجداول والصلاحيات المرتبطة بنظام عمولات التحويل.</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ ملاحظات مهمة:</h4>";
    echo "<ul style='color: #856404; margin: 0;'>";
    echo "<li>تم حذف جميع ملفات عمولات التحويل من النظام</li>";
    echo "<li>تم إزالة الروابط من القائمة الجانبية</li>";
    echo "<li>تم حذف جميع الصلاحيات المرتبطة</li>";
    echo "<li>الحوالات الجديدة لن تحتوي على عمولات تلقائية</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($db)) {
        $db->rollback();
    }
    
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في حذف نظام عمولات التحويل</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
}
?> 
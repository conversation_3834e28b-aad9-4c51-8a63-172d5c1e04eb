<?php
/**
 * جلب بيانات دور محدد عبر AJAX
 * Get Role Data via AJAX
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/role_manager.php';

// إعداد الاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// التحقق من المصادقة
$auth = new Auth();
if (!$auth->checkSession()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول'
    ]);
    exit;
}

// التحقق من الصلاحية
if (!$auth->hasPermission('roles.view')) {
    echo json_encode([
        'success' => false,
        'message' => 'ليس لديك الإذن لعرض الأدوار'
    ]);
    exit;
}

try {
    $id = (int)($_GET['id'] ?? 0);
    
    if (!$id) {
        echo json_encode([
            'success' => false,
            'message' => 'معرف الدور مطلوب'
        ]);
        exit;
    }
    
    $db = new Database();
    $roleMgr = new RoleManager($db);
    
    $role = $roleMgr->getRoleById($id);
    
    if (!$role) {
        echo json_encode([
            'success' => false,
            'message' => 'الدور غير موجود'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'role' => $role
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في النظام: ' . $e->getMessage()
    ]);
}
?>

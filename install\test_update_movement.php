<?php
/**
 * Test updateCashMovement function specifically
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h2>اختبار دالة updateCashMovement</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. البحث عن حركة مالية للاختبار</h3>\n";
    
    // Find a movement to test with
    $result = $conn->query("SELECT * FROM cash_movements ORDER BY id DESC LIMIT 1");
    
    if (!$result || $result->num_rows === 0) {
        echo "<p style='color: red;'>✗ لا توجد حركات مالية للاختبار</p>\n";
        echo "<p>يرجى إنشاء حركة مالية أولاً من <a href='create_test_movement.php' target='_blank'>هنا</a></p>\n";
        exit;
    }
    
    $movement = $result->fetch_assoc();
    echo "<p style='color: green;'>✓ تم العثور على حركة للاختبار:</p>\n";
    echo "<ul>\n";
    echo "<li>رقم الحركة: {$movement['id']}</li>\n";
    echo "<li>النوع: {$movement['movement_type']}</li>\n";
    echo "<li>المبلغ الحالي: {$movement['amount']}</li>\n";
    echo "<li>الوصف: {$movement['description']}</li>\n";
    echo "</ul>\n";
    
    echo "<h3>2. اختبار دالة updateCashMovement مباشرة</h3>\n";
    
    // Get current cash box balance
    $cashBoxResult = $conn->query("SELECT current_balance FROM cash_boxes WHERE id = {$movement['cash_box_id']}");
    $currentBalance = $cashBoxResult->fetch_assoc()['current_balance'];
    echo "<p><strong>رصيد الصندوق الحالي:</strong> $currentBalance</p>\n";
    
    // Prepare test data
    $newAmount = $movement['amount'] + 25.00; // Add 25 to current amount
    $testData = [
        'movement_type' => $movement['movement_type'],
        'amount' => $newAmount,
        'description' => 'تحديث تجريبي - ' . date('Y-m-d H:i:s'),
        'reference_number' => 'TEST_UPDATE_' . time()
    ];
    
    // Calculate balance change
    $oldEffect = ($movement['movement_type'] === 'deposit') ? $movement['amount'] : -$movement['amount'];
    $newEffect = ($testData['movement_type'] === 'deposit') ? $testData['amount'] : -$testData['amount'];
    $balanceChange = $newEffect - $oldEffect;
    
    echo "<p><strong>بيانات التحديث:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>المبلغ الجديد: {$testData['amount']}</li>\n";
    echo "<li>الوصف الجديد: {$testData['description']}</li>\n";
    echo "<li>رقم المرجع الجديد: {$testData['reference_number']}</li>\n";
    echo "<li>تغيير الرصيد: $balanceChange</li>\n";
    echo "</ul>\n";
    
    echo "<h3>3. تنفيذ التحديث</h3>\n";
    
    $updateResult = $cashManager->updateCashMovement($movement['id'], $testData, $balanceChange);
    
    if ($updateResult) {
        echo "<p style='color: green; font-weight: bold;'>✅ تم تحديث الحركة بنجاح!</p>\n";
        
        // Verify the update
        $updatedMovement = $cashManager->getCashMovementById($movement['id']);
        if ($updatedMovement) {
            echo "<p><strong>البيانات بعد التحديث:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>المبلغ: {$updatedMovement['amount']}</li>\n";
            echo "<li>الوصف: {$updatedMovement['description']}</li>\n";
            echo "<li>رقم المرجع: {$updatedMovement['reference_number']}</li>\n";
            echo "</ul>\n";
        }
        
        // Check cash box balance
        $newBalanceResult = $conn->query("SELECT current_balance FROM cash_boxes WHERE id = {$movement['cash_box_id']}");
        $newBalance = $newBalanceResult->fetch_assoc()['current_balance'];
        echo "<p><strong>رصيد الصندوق الجديد:</strong> $newBalance</p>\n";
        echo "<p><strong>التغيير المتوقع:</strong> " . ($currentBalance + $balanceChange) . "</p>\n";
        
        if (abs($newBalance - ($currentBalance + $balanceChange)) < 0.01) {
            echo "<p style='color: green;'>✓ تم تحديث رصيد الصندوق بشكل صحيح</p>\n";
        } else {
            echo "<p style='color: red;'>✗ خطأ في تحديث رصيد الصندوق</p>\n";
        }
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ فشل في تحديث الحركة</p>\n";
        
        // Check error logs
        echo "<p><strong>تحقق من سجل الأخطاء:</strong></p>\n";
        $errorLogPath = ini_get('error_log');
        if ($errorLogPath && file_exists($errorLogPath)) {
            $lastLines = array_slice(file($errorLogPath), -10);
            echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-size: 12px;'>";
            echo htmlspecialchars(implode('', $lastLines));
            echo "</pre>\n";
        }
    }
    
    echo "<h3>4. اختبار صفحة التعديل</h3>\n";
    
    $editUrl = "../dashboard/edit_movement.php?id=" . $movement['id'];
    echo "<p><a href='$editUrl' target='_blank' style='color: #007bff; font-weight: bold;'>🔗 اختبر صفحة التعديل الآن</a></p>\n";
    
    echo "<h3>5. اختبار الاتصال بقاعدة البيانات</h3>\n";
    
    // Test database connection
    if ($conn->ping()) {
        echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات نشط</p>\n";
    } else {
        echo "<p style='color: red;'>✗ مشكلة في الاتصال بقاعدة البيانات</p>\n";
    }
    
    // Test transaction support
    $conn->begin_transaction();
    $testQuery = $conn->query("SELECT 1");
    if ($testQuery) {
        $conn->commit();
        echo "<p style='color: green;'>✓ دعم المعاملات يعمل</p>\n";
    } else {
        $conn->rollback();
        echo "<p style='color: red;'>✗ مشكلة في دعم المعاملات</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى اختبار دالة updateCashMovement</h3>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الاختبار: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

<?php
/**
 * Comprehensive fix for all database lock issues
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

try {
    $db = Database::getConnection();
    
    echo "<h2>إصلاح شامل لمشاكل أقفال قاعدة البيانات</h2>\n";
    
    echo "<h3>1. إنهاء جميع العمليات المعلقة</h3>\n";

    // First, kill ALL connections except our own
    $result = $db->query("SHOW PROCESSLIST");
    if ($result) {
        $processes = $result->fetch_all(MYSQLI_ASSOC);
        $killedCount = 0;

        foreach ($processes as $process) {
            // Skip our own connection and system processes
            if ($process['Id'] == $db->thread_id || $process['User'] === 'system user') {
                continue;
            }

            // Kill ALL other connections to ensure clean state
            try {
                $db->query("KILL CONNECTION " . $process['Id']);
                echo "<p style='color: orange;'>تم إنهاء الاتصال: ID {$process['Id']} - {$process['Command']} - {$process['User']}</p>\n";
                $killedCount++;
            } catch (Exception $e) {
                // Connection might have already ended
                echo "<p style='color: gray;'>لا يمكن إنهاء: ID {$process['Id']} - {$e->getMessage()}</p>\n";
            }
        }

        echo "<p style='color: blue;'>تم إنهاء $killedCount اتصال</p>\n";

        // Wait a moment for connections to close
        sleep(2);
    }
    
    echo "<h3>2. إلغاء جميع الأقفال وإعادة تعيين النظام</h3>\n";

    // Force unlock all tables
    $db->query("UNLOCK TABLES");
    echo "<p style='color: green;'>✓ تم إلغاء جميع أقفال الجداول</p>\n";

    // Flush everything
    $flushCommands = [
        "FLUSH TABLES",
        "FLUSH TABLES WITH READ LOCK",
        "UNLOCK TABLES",
        "FLUSH STATUS",
        "FLUSH QUERY CACHE"
    ];

    foreach ($flushCommands as $cmd) {
        try {
            $db->query($cmd);
            echo "<p style='color: green;'>✓ $cmd</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ $cmd - {$e->getMessage()}</p>\n";
        }
    }

    // Reset InnoDB
    try {
        $db->query("SET GLOBAL innodb_fast_shutdown = 0");
        echo "<p style='color: green;'>✓ تم إعداد InnoDB للإغلاق السريع</p>\n";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ إعداد InnoDB - {$e->getMessage()}</p>\n";
    }
    
    echo "<h3>3. إعادة تعيين إعدادات MySQL</h3>\n";
    
    $settings = [
        "SET GLOBAL innodb_lock_wait_timeout = 5",
        "SET GLOBAL wait_timeout = 120", 
        "SET GLOBAL interactive_timeout = 120",
        "SET GLOBAL max_connections = 100",
        "SET GLOBAL innodb_deadlock_detect = ON",
        "SET GLOBAL transaction_isolation = 'READ-COMMITTED'"
    ];
    
    foreach ($settings as $setting) {
        try {
            $db->query($setting);
            echo "<p style='color: green;'>✓ " . htmlspecialchars($setting) . "</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ " . htmlspecialchars($setting) . " - " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<h3>4. إعادة تشغيل جلسة العمل</h3>\n";
    
    // Reset current session
    $sessionSettings = [
        "SET SESSION innodb_lock_wait_timeout = 5",
        "SET SESSION wait_timeout = 120",
        "SET SESSION autocommit = 1",
        "SET SESSION transaction_isolation = 'READ-COMMITTED'"
    ];
    
    foreach ($sessionSettings as $setting) {
        try {
            $db->query($setting);
            echo "<p style='color: green;'>✓ " . htmlspecialchars($setting) . "</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ " . htmlspecialchars($setting) . " - " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<h3>5. فحص حالة النظام</h3>\n";
    
    // Check system status
    $statusQueries = [
        "SELECT COUNT(*) as active_connections FROM INFORMATION_SCHEMA.PROCESSLIST WHERE COMMAND != 'Sleep'",
        "SHOW STATUS LIKE 'Threads_connected'",
        "SHOW STATUS LIKE 'Innodb_row_lock_waits'",
        "SHOW STATUS LIKE 'Innodb_deadlocks'"
    ];
    
    foreach ($statusQueries as $query) {
        try {
            $result = $db->query($query);
            if ($result) {
                $row = $result->fetch_assoc();
                if (isset($row['active_connections'])) {
                    echo "<p><strong>الاتصالات النشطة:</strong> {$row['active_connections']}</p>\n";
                } elseif (isset($row['Variable_name'])) {
                    echo "<p><strong>{$row['Variable_name']}:</strong> {$row['Value']}</p>\n";
                }
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>خطأ في فحص: " . htmlspecialchars($query) . "</p>\n";
        }
    }
    
    echo "<h3>6. اختبار العمليات الأساسية</h3>\n";
    
    // Test basic operations
    try {
        // Test transaction
        $db->autocommit(false);
        $db->query("SELECT 1");
        $db->commit();
        $db->autocommit(true);
        echo "<p style='color: green;'>✓ اختبار المعاملات نجح</p>\n";
        
        // Test table access
        $result = $db->query("SELECT COUNT(*) as count FROM customers LIMIT 1");
        if ($result) {
            echo "<p style='color: green;'>✓ اختبار الوصول للجداول نجح</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ فشل في الاختبار: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم إصلاح جميع مشاكل الأقفال!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='fix_customers_status.php' target='_blank'>إصلاح مشكلة عمود status</a></li>\n";
    echo "<li><a href='../dashboard/exchange.php' target='_blank'>اختبار صفحة الصرافة</a></li>\n";
    echo "<li>إذا استمرت المشكلة، أعد تشغيل XAMPP</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الإصلاح: " . $e->getMessage() . "</p>\n";
    echo "<p><strong>جرب الحلول التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>أعد تشغيل خدمة MySQL في XAMPP</li>\n";
    echo "<li>أعد تشغيل XAMPP بالكامل</li>\n";
    echo "<li>تحقق من ملفات سجل MySQL</li>\n";
    echo "</ul>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

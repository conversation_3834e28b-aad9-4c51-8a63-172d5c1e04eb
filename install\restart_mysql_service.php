<?php
/**
 * Restart MySQL service completely
 */

echo "<h2>إعادة تشغيل خدمة MySQL</h2>\n";

// Check if we're on Windows (XAMPP)
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    echo "<h3>إعادة تشغيل MySQL في XAMPP</h3>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h4>خطوات إعادة تشغيل MySQL يدوياً:</h4>\n";
    echo "<ol>\n";
    echo "<li><strong>افتح XAMPP Control Panel</strong></li>\n";
    echo "<li><strong>اضغط على 'Stop' بجانب MySQL</strong></li>\n";
    echo "<li><strong>انتظر حتى يتوقف تماماً</strong></li>\n";
    echo "<li><strong>اضغط على 'Start' لإعادة تشغيله</strong></li>\n";
    echo "<li><strong>تأكد من أن الحالة أصبحت 'Running'</strong></li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
    echo "<h4>أو استخدم سطر الأوامر:</h4>\n";
    echo "<div style='background: #000; color: #0f0; padding: 10px; font-family: monospace; margin: 10px 0;'>\n";
    echo "net stop mysql<br>\n";
    echo "net start mysql\n";
    echo "</div>\n";
    
    // Try to execute the commands
    echo "<h4>محاولة إعادة التشغيل تلقائياً:</h4>\n";
    
    $output = [];
    $return_var = 0;
    
    // Try to stop MySQL
    exec('net stop mysql 2>&1', $output, $return_var);
    if ($return_var === 0) {
        echo "<p style='color: green;'>✓ تم إيقاف MySQL بنجاح</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ لم يتم إيقاف MySQL تلقائياً: " . implode(' ', $output) . "</p>\n";
    }
    
    // Wait a moment
    sleep(3);
    
    // Try to start MySQL
    $output = [];
    exec('net start mysql 2>&1', $output, $return_var);
    if ($return_var === 0) {
        echo "<p style='color: green;'>✓ تم تشغيل MySQL بنجاح</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ لم يتم تشغيل MySQL تلقائياً: " . implode(' ', $output) . "</p>\n";
    }
    
} else {
    // Linux/Unix systems
    echo "<h3>إعادة تشغيل MySQL في Linux</h3>\n";
    
    $commands = [
        'sudo systemctl restart mysql',
        'sudo service mysql restart',
        'sudo /etc/init.d/mysql restart'
    ];
    
    foreach ($commands as $cmd) {
        echo "<div style='background: #000; color: #0f0; padding: 10px; font-family: monospace; margin: 5px 0;'>\n";
        echo htmlspecialchars($cmd) . "\n";
        echo "</div>\n";
    }
}

// Test database connection after restart
echo "<h3>اختبار الاتصال بقاعدة البيانات</h3>\n";

try {
    // Wait a moment for MySQL to fully start
    sleep(5);
    
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../includes/database.php';
    
    $db = Database::getConnection();
    
    if ($db) {
        echo "<p style='color: green; font-weight: bold;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>\n";
        
        // Test a simple query
        $result = $db->query("SELECT 1 as test");
        if ($result) {
            echo "<p style='color: green;'>✓ اختبار الاستعلام نجح</p>\n";
        }
        
        // Check current settings
        $result = $db->query("SELECT @@innodb_lock_wait_timeout as timeout");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p><strong>Lock wait timeout:</strong> {$row['timeout']} seconds</p>\n";
        }
        
        $result = $db->query("SHOW STATUS LIKE 'Threads_connected'");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p><strong>Connected threads:</strong> {$row['Value']}</p>\n";
        }
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ فشل في الاتصال بقاعدة البيانات</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>\n";
    echo "<p><strong>يرجى إعادة تشغيل MySQL يدوياً من XAMPP Control Panel</strong></p>\n";
}

echo "<hr>\n";
echo "<h3>الخطوات التالية:</h3>\n";
echo "<ul>\n";
echo "<li><a href='fix_all_locks.php' target='_blank'>تشغيل إصلاح الأقفال</a></li>\n";
echo "<li><a href='../dashboard/exchange.php' target='_blank'>اختبار صفحة الصرافة</a></li>\n";
echo "<li>إذا استمرت المشكلة، أعد تشغيل الكمبيوتر</li>\n";
echo "</ul>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul, ol { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

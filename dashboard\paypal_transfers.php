<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paypal_manager.php';
require_once __DIR__ . '/../includes/withdrawal_manager.php';
require_once __DIR__ . '/../includes/database.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('paypal.dashboard');

// Initialize managers
$db = new Database();
$paypalManager = new PayPalManager($db);
$withdrawalManager = new WithdrawalManager($db);

// Get statistics
$paypalStats = $paypalManager->getStatistics();
$withdrawalStats = $withdrawalManager->getStatistics();

// Calculate totals
$totalIncomingTransfers = $paypalStats['total_amount'];
$totalWithdrawals = $withdrawalStats['total_amount'];
$netPaypalBalance = $totalIncomingTransfers - $totalWithdrawals;

$pageTitle = 'حوالات PayPal';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-5">
        <h2 class="mb-0"><i class="fab fa-paypal"></i> حوالات PayPal</h2>
    </div>

    <!-- PayPal Balance Summary -->
    <div class="row mb-5">
        <div class="col-md-4">
            <div class="card summary-card incoming-card">
                <div class="card-body text-center">
                    <div class="icon-container mb-3">
                        <i class="fas fa-download"></i>
                    </div>
                    <h5 class="card-title mb-2">إجمالي الحوالات الواردة</h5>
                    <h3 class="amount-value">$<?php echo number_format($totalIncomingTransfers, 2); ?></h3>
                    <div class="stats-mini">
                        <span class="badge bg-success"><?php echo $paypalStats['received_count']; ?> مستلمة</span>
                        <span class="badge bg-warning text-dark"><?php echo $paypalStats['pending_count']; ?> معلقة</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card summary-card withdrawal-card">
                <div class="card-body text-center">
                    <div class="icon-container mb-3">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h5 class="card-title mb-2">إجمالي سحب المبالغ</h5>
                    <h3 class="amount-value">$<?php echo number_format($totalWithdrawals, 2); ?></h3>
                    <div class="stats-mini">
                        <span class="badge bg-primary"><?php echo $withdrawalStats['received_count'] ?? 0; ?> مستلمة</span>
                        <span class="badge bg-warning text-dark"><?php echo $withdrawalStats['pending_count']; ?> معلقة</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card summary-card balance-card <?php echo $netPaypalBalance >= 0 ? 'positive' : 'negative'; ?>">
                <div class="card-body text-center">
                    <div class="icon-container mb-3">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <h5 class="card-title mb-2">صافي الرصيد المتبقي</h5>
                    <h3 class="amount-value">$<?php echo number_format($netPaypalBalance, 2); ?></h3>
                    <div class="balance-status">
                        <span class="status-badge <?php echo $netPaypalBalance >= 0 ? 'positive' : 'negative'; ?>">
                            <i class="fas fa-<?php echo $netPaypalBalance >= 0 ? 'arrow-up' : 'arrow-down'; ?>"></i>
                            <?php echo $netPaypalBalance >= 0 ? 'رصيد إيجابي' : 'رصيد سالب'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Navigation Cards -->
    <div class="row justify-content-center">
        <div class="col-md-5">
            <a href="paypal_incoming_transfers.php" class="text-decoration-none">
                <div class="card bg-gradient-primary text-white h-100 shadow-lg hover-card">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-download fa-5x"></i>
                        </div>
                        <h3 class="card-title mb-3">حوالات PayPal الواردة</h3>
                        <p class="card-text mb-4 fs-5">إدارة الحوالات الواردة من PayPal</p>
                        <div class="btn btn-light btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            عرض الحوالات الواردة
                        </div>
                    </div>
                </div>
            </a>
        </div>
        <div class="col-md-5">
            <a href="withdrawals.php" class="text-decoration-none">
                <div class="card bg-gradient-success text-white h-100 shadow-lg hover-card">
                    <div class="card-body text-center p-5">
                        <div class="mb-4">
                            <i class="fas fa-money-bill-wave fa-5x"></i>
                        </div>
                        <h3 class="card-title mb-3">سحب المبالغ</h3>
                        <p class="card-text mb-4 fs-5">إدارة عمليات سحب المبالغ</p>
                        <div class="btn btn-light btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            عرض سحب المبالغ
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- Detailed Statistics Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات مفصلة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- PayPal Incoming Transfers Stats -->
                        <div class="col-md-6">
                            <div class="stats-section incoming-stats">
                                <div class="section-header">
                                    <div class="header-icon">
                                        <i class="fas fa-download"></i>
                                    </div>
                                    <h6 class="section-title">إحصائيات الحوالات الواردة</h6>
                                </div>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-icon total-icon">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">إجمالي المبالغ</div>
                                            <div class="stat-value">$<?php echo number_format($paypalStats['total_amount'], 2); ?></div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon received-icon">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">مستلمة</div>
                                            <div class="stat-value">$<?php echo number_format($paypalStats['received_amount'], 2); ?></div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon pending-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">معلقة</div>
                                            <div class="stat-value">$<?php echo number_format($paypalStats['pending_amount'], 2); ?></div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon count-icon">
                                            <i class="fas fa-list"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">عدد الحوالات</div>
                                            <div class="stat-value"><?php echo number_format($paypalStats['total_count']); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Withdrawals Stats -->
                        <div class="col-md-6">
                            <div class="stats-section withdrawal-stats">
                                <div class="section-header">
                                    <div class="header-icon">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </div>
                                    <h6 class="section-title">إحصائيات سحب المبالغ</h6>
                                </div>
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-icon total-icon">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">إجمالي المبالغ</div>
                                            <div class="stat-value">$<?php echo number_format($withdrawalStats['total_amount'], 2); ?></div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon received-icon">
                                            <i class="fas fa-handshake"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">مستلمة</div>
                                            <div class="stat-value">$<?php echo number_format($withdrawalStats['received_amount'] ?? 0, 2); ?></div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon pending-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">معلقة</div>
                                            <div class="stat-value">$<?php echo number_format($withdrawalStats['pending_amount'], 2); ?></div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon count-icon">
                                            <i class="fas fa-list"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-label">عدد العمليات</div>
                                            <div class="stat-value"><?php echo number_format($withdrawalStats['total_count']); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Net Balance Summary -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="balance-summary rounded p-4">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h6 class="mb-3">
                                            <i class="fas fa-calculator me-2"></i>ملخص الرصيد الصافي
                                        </h6>
                                        <div class="d-flex align-items-center calculation">
                                            <div class="me-4">
                                                <small class="text-muted d-block">إجمالي الحوالات الواردة</small>
                                                <strong class="text-primary result">$<?php echo number_format($totalIncomingTransfers, 2); ?></strong>
                                            </div>
                                            <div class="me-4">
                                                <i class="fas fa-minus text-muted"></i>
                                            </div>
                                            <div class="me-4">
                                                <small class="text-muted d-block">إجمالي سحب المبالغ</small>
                                                <strong class="text-success result">$<?php echo number_format($totalWithdrawals, 2); ?></strong>
                                            </div>
                                            <div class="me-4">
                                                <i class="fas fa-equals text-muted"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">صافي الرصيد المتبقي</small>
                                                <strong class="<?php echo $netPaypalBalance >= 0 ? 'text-info' : 'text-danger'; ?> result">
                                                    $<?php echo number_format($netPaypalBalance, 2); ?>
                                                </strong>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <div class="me-3">
                                                <i class="fas fa-wallet fa-2x <?php echo $netPaypalBalance >= 0 ? 'text-info' : 'text-danger'; ?>"></i>
                                            </div>
                                            <div>
                                                <small class="text-muted d-block">الحالة</small>
                                                <strong class="<?php echo $netPaypalBalance >= 0 ? 'text-info' : 'text-danger'; ?>">
                                                    <?php echo $netPaypalBalance >= 0 ? 'رصيد إيجابي' : 'رصيد سالب'; ?>
                                                </strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }
    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    }
    .hover-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .hover-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
    }
    .card {
        border: none;
        border-radius: 20px;
    }
    .card-body {
        border-radius: 20px;
    }
    
    /* Summary cards styling */
    .summary-card {
        transition: all 0.3s ease;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
    }
    .summary-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }
    .summary-card .card-body {
        padding: 2rem;
        position: relative;
    }
    
    /* Incoming card styling */
    .incoming-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .incoming-card .icon-container {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
    }
    .incoming-card .icon-container i {
        font-size: 2.5rem;
        color: white;
    }
    
    /* Withdrawal card styling */
    .withdrawal-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    .withdrawal-card .icon-container {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
    }
    .withdrawal-card .icon-container i {
        font-size: 2.5rem;
        color: white;
    }
    
    /* Balance card styling */
    .balance-card.positive {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }
    .balance-card.negative {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
    }
    .balance-card .icon-container {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
    }
    .balance-card .icon-container i {
        font-size: 2.5rem;
        color: white;
    }
    
    .amount-value {
        font-size: 2.2rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    
    .stats-mini {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .balance-status {
        margin-top: 1rem;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: bold;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .status-badge.positive {
        background: rgba(255,255,255,0.3);
        color: white;
    }
    
    .status-badge.negative {
        background: rgba(255,255,255,0.3);
        color: white;
    }
    
    /* Detailed stats styling */
    .stats-section {
        padding: 1.5rem;
        border-radius: 15px;
        background: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        margin-bottom: 1rem;
    }
    
    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }
    
    .header-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        font-size: 1.5rem;
    }
    
    .incoming-stats .header-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .withdrawal-stats .header-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }
    
    .section-title {
        font-size: 1.1rem;
        font-weight: bold;
        margin: 0;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .stat-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .stat-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        font-size: 1.2rem;
        color: white;
    }
    
    .total-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .received-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .pending-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .count-icon {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333 !important;
    }
    
    .stat-content {
        flex: 1;
    }
    
    .stat-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    .stat-value {
        font-size: 1.1rem;
        font-weight: bold;
        color: #333;
    }
    
    .balance-summary {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 15px;
    }
    
    .balance-summary .calculation {
        font-size: 0.9rem;
    }
    
    .balance-summary .result {
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    /* Responsive improvements */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .balance-summary .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
        }
        
        .balance-summary .d-flex > div {
            margin-bottom: 1rem;
        }
        
        .balance-summary .text-end {
            text-align: left !important;
        }
        
        .amount-value {
            font-size: 1.8rem;
        }
        
        .icon-container {
            width: 60px !important;
            height: 60px !important;
        }
        
        .icon-container i {
            font-size: 2rem !important;
        }
    }
    </style>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

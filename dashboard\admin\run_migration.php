<?php
/**
 * Admin page to run database migrations
 * This page allows administrators to update the database schema
 */

// Required includes
require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';
require_once __DIR__ . '/../../includes/functions.php';

// Initialize auth
$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('system.admin'); // Only admins can run migrations

// Set page title
$page_title = 'تحديث قاعدة البيانات';

// Include header
include __DIR__ . '/../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-database me-2"></i>
                    تحديث قاعدة البيانات
                </h1>
                <p class="text-muted mb-0">إدارة وتحديث schema قاعدة البيانات</p>
            </div>
            <div>
                <a href="../" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- Migration Cards -->
    <div class="row">
        <!-- Transfer Delivery Status Migration -->
        <div class="col-md-6 mb-4">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-truck me-2"></i>
                        إضافة حالة التسليم للحوالات
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">
                        يضيف هذا التحديث الأعمدة المطلوبة لتتبع حالة تسليم الحوالات:
                    </p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-plus text-success me-2"></i> عمود <code>delivery_status</code></li>
                        <li><i class="fas fa-plus text-success me-2"></i> عمود <code>delivery_notes</code></li>
                        <li><i class="fas fa-edit text-info me-2"></i> تحديث قائمة حالات الحوالة</li>
                        <li><i class="fas fa-database text-primary me-2"></i> إضافة فهارس للأداء</li>
                    </ul>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التحديث.
                    </div>
                    
                    <div class="d-grid">
                        <a href="../../install/run_migration.php" class="btn btn-primary" target="_blank">
                            <i class="fas fa-play me-2"></i>
                            تشغيل التحديث
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Status Check -->
        <div class="col-md-6 mb-4">
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        فحص حالة قاعدة البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $db = Database::getConnection();
                        
                        // Check if delivery_status column exists
                        $checkQuery = "SHOW COLUMNS FROM transfers LIKE 'delivery_status'";
                        $result = $db->query($checkQuery);
                        $deliveryStatusExists = $result && $result->num_rows > 0;
                        
                        // Check if delivery_notes column exists
                        $checkQuery2 = "SHOW COLUMNS FROM transfers LIKE 'delivery_notes'";
                        $result2 = $db->query($checkQuery2);
                        $deliveryNotesExists = $result2 && $result2->num_rows > 0;
                        
                        // Check current status enum values
                        $checkQuery3 = "SHOW COLUMNS FROM transfers LIKE 'status'";
                        $result3 = $db->query($checkQuery3);
                        $statusColumn = $result3 ? $result3->fetch_assoc() : null;
                        $hasNewStatuses = $statusColumn && strpos($statusColumn['Type'], 'مقبولة') !== false;
                        
                        echo "<h6 class='text-primary mb-3'>حالة الأعمدة:</h6>";
                        echo "<ul class='list-unstyled'>";
                        
                        if ($deliveryStatusExists) {
                            echo "<li><i class='fas fa-check text-success me-2'></i> عمود <code>delivery_status</code> موجود</li>";
                        } else {
                            echo "<li><i class='fas fa-times text-danger me-2'></i> عمود <code>delivery_status</code> غير موجود</li>";
                        }
                        
                        if ($deliveryNotesExists) {
                            echo "<li><i class='fas fa-check text-success me-2'></i> عمود <code>delivery_notes</code> موجود</li>";
                        } else {
                            echo "<li><i class='fas fa-times text-danger me-2'></i> عمود <code>delivery_notes</code> غير موجود</li>";
                        }
                        
                        if ($hasNewStatuses) {
                            echo "<li><i class='fas fa-check text-success me-2'></i> حالات الحوالة محدثة</li>";
                        } else {
                            echo "<li><i class='fas fa-times text-danger me-2'></i> حالات الحوالة تحتاج تحديث</li>";
                        }
                        
                        echo "</ul>";
                        
                        if ($deliveryStatusExists && $deliveryNotesExists && $hasNewStatuses) {
                            echo "<div class='alert alert-success'>
                                    <i class='fas fa-check-circle me-2'></i>
                                    قاعدة البيانات محدثة ومجهزة لاستخدام نظام حالة التسليم.
                                  </div>";
                        } else {
                            echo "<div class='alert alert-warning'>
                                    <i class='fas fa-exclamation-triangle me-2'></i>
                                    قاعدة البيانات تحتاج تحديث لدعم نظام حالة التسليم.
                                  </div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>
                                <i class='fas fa-exclamation-circle me-2'></i>
                                خطأ في فحص قاعدة البيانات: " . htmlspecialchars($e->getMessage()) . "
                              </div>";
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="dashboard-card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-info-circle me-2"></i>
                تعليمات التحديث
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">قبل التحديث:</h6>
                    <ol>
                        <li>تأكد من عمل نسخة احتياطية كاملة من قاعدة البيانات</li>
                        <li>تأكد من عدم وجود مستخدمين يعملون على النظام</li>
                        <li>تحقق من مساحة القرص الصلب المتاحة</li>
                        <li>تأكد من صلاحيات قاعدة البيانات</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">بعد التحديث:</h6>
                    <ol>
                        <li>تحقق من نجاح التحديث في صفحة النتائج</li>
                        <li>اختبر وظائف الحوالات الجديدة</li>
                        <li>تحقق من عمل تحديث حالة التسليم</li>
                        <li>راجع سجلات النظام للتأكد من عدم وجود أخطاء</li>
                    </ol>
                </div>
            </div>
            
            <div class="alert alert-info mt-3">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>نصيحة:</strong> يمكنك تشغيل التحديث عدة مرات بأمان. النظام سيتحقق من وجود التحديثات ولن يطبقها مرة أخرى إذا كانت موجودة.
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/../../includes/footer.php'; ?>

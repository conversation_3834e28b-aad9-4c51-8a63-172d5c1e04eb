<?php
/**
 * <PERSON>tManager – central helper to record actions into audit_logs (JSON capable).
 * All inserts use prepared statements for security. Provide static helper for convenience.
 */
require_once __DIR__ . '/database.php';

class AuditManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        $this->db = $database::getConnection();
    }

    /**
     * Log an action to audit_logs table.
     *
     * @param int|null       $userId      User responsible (nullable for system actions)
     * @param string         $action      Simple action code e.g. "transfer.create"
     * @param string|null    $module      Logical module (users, transfers, etc.)
     * @param int|null       $recordId    Affected record ID
     * @param mixed|null     $old         Previous value (array|object|string|null)
     * @param mixed|null     $new         New value (array|object|string|null)
     * @param string|null    $ip          Source IP (default resolved from $_SERVER)
     * @param string|null    $agent       User-Agent (default resolved from $_SERVER)
     * @return bool
     */
    public function logAction($userId, string $action, ?string $module = null, ?int $recordId = null, $old = null, $new = null, ?string $ip = null, ?string $agent = null): bool
    {
        $ip    = $ip    ?? ($_SERVER['REMOTE_ADDR']     ?? null);
        $agent = $agent ?? ($_SERVER['HTTP_USER_AGENT'] ?? null);

        // Convert arrays/objects to JSON – leave strings untouched
        $oldJson = is_array($old) || is_object($old) ? json_encode($old, JSON_UNESCAPED_UNICODE) : $old;
        $newJson = is_array($new) || is_object($new) ? json_encode($new, JSON_UNESCAPED_UNICODE) : $new;

        $stmt = $this->db->prepare('INSERT INTO audit_logs (user_id, action, module, record_id, old_value, new_value, ip_address, user_agent, created_at)
                                     VALUES (?,?,?,?,?,?,?,?, NOW())');
        if (!$stmt) return false;
        // user_id can be null
        if ($userId !== null) {
            $stmt->bind_param('isisssss', $userId, $action, $module, $recordId, $oldJson, $newJson, $ip, $agent);
        } else {
            // Need to bind NULL – workaround by using null and binding with i for nullable int
            $nullInt = null;
            $stmt->bind_param('isisssss', $nullInt, $action, $module, $recordId, $oldJson, $newJson, $ip, $agent);
        }
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /** Static one-liner helper */
    public static function quickLog($userId, string $action, ?string $module = null, ?int $recordId = null, $old = null, $new = null): void
    {
        $mgr = new self(new Database());
        $mgr->logAction($userId, $action, $module, $recordId, $old, $new);
    }
} 
<?php
require_once __DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php';
require_once __DIR__ . '/../includes/pdo_database.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    die('معرف المعاملة غير صحيح');
}
$transaction_id = (int)$_GET['id'];

$pdo = PDODatabase::getConnection();
$stmt = $pdo->prepare("
    SELECT dt.*, 
           c.name_ar as country_name_ar, 
           c.name_en as country_name_en, 
           c.currency_code, 
           c.currency_symbol,
           b.name as branch_name,
           u_created.full_name as created_by_name,
           u_created.username as created_by_username,
           u_updated.full_name as updated_by_name,
           u_updated.username as updated_by_username
    FROM daily_transactions dt
    LEFT JOIN countries c ON dt.country_id = c.id
    LEFT JOIN branches b ON dt.branch_id = b.id
    LEFT JOIN users u_created ON dt.created_by = u_created.id
    LEFT JOIN users u_updated ON dt.updated_by = u_updated.id
    WHERE dt.id = ?
");
$stmt->execute([$transaction_id]);
$transaction = $stmt->fetch();
if (!$transaction) {
    die('المعاملة غير موجودة');
}

// إعداد PDF
$pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
$pdf->SetCreator('TrustPlus');
$pdf->SetAuthor('TrustPlus');
$pdf->SetTitle('تقرير معاملة يومية');
$pdf->SetSubject('تقرير معاملة يومية');
$pdf->SetMargins(15, 20, 15);
$pdf->SetAutoPageBreak(true, 20);
$pdf->AddPage();
$pdf->SetFont('aealarabiya', '', 16);

$html = '<h2 style="text-align:center;">تقرير معاملة يومية</h2>';
$html .= '<hr>';
$html .= '<table border="0" cellpadding="6" style="font-size:14px; width:100%; direction:rtl;">';
$html .= '<tr><td><b>رقم المعاملة:</b></td><td>' . htmlspecialchars($transaction['transaction_number']) . '</td></tr>';
$html .= '<tr><td><b>الدولة:</b></td><td>' . htmlspecialchars($transaction['country_name_ar']) . ' (' . htmlspecialchars($transaction['currency_code']) . ')</td></tr>';
$html .= '<tr><td><b>المبلغ الأساسي:</b></td><td>' . number_format($transaction['base_amount'], 2) . '</td></tr>';
$html .= '<tr><td><b>سعر القص للزبون:</b></td><td>' . number_format($transaction['customer_rate'], 6) . '</td></tr>';
$html .= '<tr><td><b>سعر القص للمكتب:</b></td><td>' . number_format($transaction['office_rate'], 6) . '</td></tr>';
$html .= '<tr><td><b>نوع العملية:</b></td><td>' . ($transaction['operation_type'] == 'multiply' ? 'ضرب' : 'قسمة') . '</td></tr>';
$html .= '<tr><td><b>المبلغ الناتج للزبون:</b></td><td>' . number_format($transaction['calculated_amount'], 2) . '</td></tr>';
$html .= '<tr><td><b>المبلغ الناتج للمكتب:</b></td><td>' . number_format($transaction['office_amount'], 2) . '</td></tr>';
$html .= '<tr><td><b>سعر الصرف:</b></td><td>' . number_format($transaction['exchange_rate'], 6) . '</td></tr>';
$html .= '<tr><td><b>المبلغ للمستلم:</b></td><td>' . number_format($transaction['recipient_amount']) . '</td></tr>';
$html .= '<tr><td><b>نوع التسليم:</b></td><td>' . htmlspecialchars($transaction['delivery_type']) . '</td></tr>';
$html .= '<tr><td><b>حالة التسليم:</b></td><td>' . htmlspecialchars($transaction['delivery_status']) . '</td></tr>';
$html .= '<tr><td><b>اسم المستلم:</b></td><td>' . htmlspecialchars($transaction['recipient_name']) . '</td></tr>';
$html .= '<tr><td><b>الفرع:</b></td><td>' . htmlspecialchars($transaction['branch_name']) . '</td></tr>';
$html .= '<tr><td><b>أنشئت بواسطة:</b></td><td>' . htmlspecialchars($transaction['created_by_name']) . '</td></tr>';
$html .= '<tr><td><b>تاريخ الإنشاء:</b></td><td>' . htmlspecialchars($transaction['created_at']) . '</td></tr>';
$html .= '<tr><td><b>ملاحظات:</b></td><td>' . nl2br(htmlspecialchars($transaction['notes'])) . '</td></tr>';
$html .= '</table>';

$pdf->writeHTML($html, true, false, true, false, '');
$pdf->Output('daily_transaction_' . $transaction['transaction_number'] . '.pdf', 'I'); 
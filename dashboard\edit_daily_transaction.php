<?php
/**
 * صفحة تعديل المعاملة اليومية
 * Edit Daily Transaction Page
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/pdo_database.php';

// إنشاء كائن المصادقة والتحقق من تسجيل الدخول
$auth = new Auth();
$auth->requireLogin();

// الحصول على بيانات المستخدم الحالي
$current_user = $auth->getCurrentUser();

// التحقق من الصلاحيات
if (!$auth->hasPermission('daily_transactions.edit')) {
    header('Location: ' . BASE_URL . 'dashboard/index.php?error=no_permission');
    exit;
}

// التحقق من وجود معرف المعاملة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: ' . BASE_URL . 'dashboard/daily_transactions.php?error=invalid_id');
    exit;
}

$transaction_id = (int)$_GET['id'];
$pageTitle = 'تعديل المعاملة اليومية';
$success_message = '';
$error_message = '';

// جلب بيانات المعاملة الحالية
try {
    $stmt = $pdo->prepare("SELECT * FROM daily_transactions WHERE id = ?");
    $stmt->execute([$transaction_id]);
    $transaction = $stmt->fetch();
    
    if (!$transaction) {
        header('Location: ' . BASE_URL . 'dashboard/daily_transactions.php?error=transaction_not_found');
        exit;
    }
} catch (Exception $e) {
    $error_message = "خطأ في جلب بيانات المعاملة: " . $e->getMessage();
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $country_id = (int)$_POST['country_id'];
        $base_amount = (float)$_POST['base_amount'];
        $customer_rate = (float)$_POST['customer_rate'];
        $operation_type = $_POST['operation_type'];
        $exchange_rate = (float)$_POST['exchange_rate'];
        $delivery_type = $_POST['delivery_type'];
        $transfer_amount = !empty($_POST['transfer_amount']) ? (float)$_POST['transfer_amount'] : null;
        $recipient_name = !empty($_POST['recipient_name']) ? trim($_POST['recipient_name']) : null;
        $notes = !empty($_POST['notes']) ? trim($_POST['notes']) : null;
        $office_rate = (float)$_POST['office_rate'];
        $delivery_status = isset($_POST['delivery_status']) ? $_POST['delivery_status'] : 'معلق';
        $updated_by = (int)$current_user['id'];

        // التحقق من صحة البيانات
        $validation_errors = [];

        // التحقق من وجود الدولة
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM countries WHERE id = ? AND is_active = 1");
        $stmt->execute([$country_id]);
        if ($stmt->fetchColumn() == 0) {
            $validation_errors[] = "الدولة المحددة غير موجودة أو غير نشطة";
        }

        // التحقق من صحة المبالغ
        if ($base_amount <= 0) {
            $validation_errors[] = "مبلغ الحوالة الأساسي يجب أن يكون أكبر من صفر";
        }

        if ($customer_rate <= 0) {
            $validation_errors[] = "سعر القص للزبون يجب أن يكون أكبر من صفر";
        }

        if ($exchange_rate <= 0) {
            $validation_errors[] = "سعر الصرف يجب أن يكون أكبر من صفر";
        }

        if ($office_rate <= 0) {
            $validation_errors[] = "سعر القص للمكتب يجب أن يكون أكبر من صفر";
        }

        // التحقق من البيانات المطلوبة للتحويل البنكي أو USDT
        if ($delivery_type == 'bank' || $delivery_type == 'usdt') {
            if (empty($transfer_amount) || $transfer_amount <= 0) {
                $validation_errors[] = "المبلغ المراد تحويله مطلوب للتحويل البنكي أو USDT";
            }

            if (empty($recipient_name)) {
                $validation_errors[] = "اسم المستلم مطلوب للتحويل البنكي أو USDT";
            }
        }

        if (!empty($validation_errors)) {
            $error_message = implode('<br>', $validation_errors);
        } else {
            // حساب المبلغ الناتج
            if ($operation_type == 'multiply') {
                $calculated_amount = $base_amount * $customer_rate;
            } else {
                $calculated_amount = $base_amount / $customer_rate;
            }

            // حساب المبلغ للمستلم
            $recipient_amount = floor($calculated_amount * $exchange_rate);

            // حساب المبلغ الناتج للمكتب
            if ($operation_type == 'multiply') {
                $office_amount = $base_amount * $office_rate;
            } else {
                $office_amount = $base_amount / $office_rate;
            }

            // بدء المعاملة
            $pdo->beginTransaction();

            try {
                // حفظ القيم القديمة للتاريخ
                $stmt = $pdo->prepare("SELECT * FROM daily_transactions WHERE id = ?");
                $stmt->execute([$transaction_id]);
                $old_transaction = $stmt->fetch();

                // تحديث المعاملة
                $update_sql = "UPDATE daily_transactions SET
                    country_id = ?,
                    base_amount = ?,
                    customer_rate = ?,
                    office_rate = ?,
                    operation_type = ?,
                    calculated_amount = ?,
                    office_amount = ?,
                    exchange_rate = ?,
                    recipient_amount = ?,
                    delivery_type = ?,
                    delivery_status = ?,
                    transfer_amount = ?,
                    recipient_name = ?,
                    notes = ?,
                    updated_by = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";

                $stmt = $pdo->prepare($update_sql);
                $stmt->execute([
                    $country_id, $base_amount, $customer_rate, $office_rate, $operation_type,
                    $calculated_amount, $office_amount, $exchange_rate, $recipient_amount,
                    $delivery_type, $delivery_status, $transfer_amount, $recipient_name, $notes,
                    $updated_by, $transaction_id
                ]);

                // إضافة سجل في تاريخ المعاملات
                $history_sql = "INSERT INTO daily_transaction_history
                    (transaction_id, action_type, old_values, new_values, changed_by, changed_at)
                    VALUES (?, 'updated', ?, ?, ?, CURRENT_TIMESTAMP)";

                $old_values = json_encode([
                    'country_id' => $old_transaction['country_id'],
                    'base_amount' => $old_transaction['base_amount'],
                    'customer_rate' => $old_transaction['customer_rate'],
                    'operation_type' => $old_transaction['operation_type'],
                    'exchange_rate' => $old_transaction['exchange_rate'],
                    'delivery_type' => $old_transaction['delivery_type'],
                    'transfer_amount' => $old_transaction['transfer_amount'],
                    'recipient_name' => $old_transaction['recipient_name'],
                    'notes' => $old_transaction['notes']
                ]);

                $new_values = json_encode([
                    'country_id' => $country_id,
                    'base_amount' => $base_amount,
                    'customer_rate' => $customer_rate,
                    'operation_type' => $operation_type,
                    'exchange_rate' => $exchange_rate,
                    'delivery_type' => $delivery_type,
                    'transfer_amount' => $transfer_amount,
                    'recipient_name' => $recipient_name,
                    'notes' => $notes
                ]);

                $stmt = $pdo->prepare($history_sql);
                $stmt->execute([$transaction_id, $old_values, $new_values, $updated_by]);

                // تأكيد المعاملة
                $pdo->commit();

                $success_message = "تم تحديث المعاملة بنجاح";

                // إعادة جلب البيانات المحدثة
                $stmt = $pdo->prepare("SELECT * FROM daily_transactions WHERE id = ?");
                $stmt->execute([$transaction_id]);
                $transaction = $stmt->fetch();

            } catch (Exception $e) {
                $pdo->rollback();
                throw $e;
            }
        }

    } catch (Exception $e) {
        $error_message = "حدث خطأ أثناء تحديث المعاملة: " . $e->getMessage();
    }
}

// جلب قائمة الدول النشطة
try {
    $countries_stmt = $pdo->query("SELECT id, name_ar, currency_code, currency_symbol FROM countries WHERE is_active = 1 ORDER BY name_ar");
    $countries = $countries_stmt->fetchAll();
} catch (Exception $e) {
    $countries = [];
    $error_message = "خطأ في جلب قائمة الدول: " . $e->getMessage();
}

require_once __DIR__ . '/../includes/header.php';
?>

<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-auto p-0">
                <?php require_once __DIR__ . '/../includes/sidebar.php'; ?>
            </div>

            <!-- Main Content -->
            <div class="col">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h3 mb-0">
                            <i class="fas fa-edit text-warning me-2"></i>
                            تعديل المعاملة اليومية
                            <span class="text-muted">#<?php echo htmlspecialchars($transaction['transaction_number']); ?></span>
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/">الرئيسية</a></li>
                                <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php">المعاملات اليومية</a></li>
                                <li class="breadcrumb-item active">تعديل المعاملة</li>
                            </ol>
                        </nav>
                    </div>

                    <!-- رسائل النجاح والخطأ -->
                    <?php if ($success_message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- معلومات المعاملة -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات المعاملة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>رقم المعاملة:</strong><br>
                                    <span class="text-primary"><?php echo htmlspecialchars($transaction['transaction_number']); ?></span>
                                </div>
                                <div class="col-md-3">
                                    <strong>تاريخ الإنشاء:</strong><br>
                                    <?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>آخر تحديث:</strong><br>
                                    <?php echo $transaction['updated_at'] ? date('Y-m-d H:i', strtotime($transaction['updated_at'])) : 'لم يتم التحديث'; ?>
                                </div>
                                <div class="col-md-3">
                                    <strong>الحالة:</strong><br>
                                    <span class="badge bg-warning">قيد التعديل</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج تعديل المعاملة -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>
                                تعديل بيانات المعاملة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="editTransactionForm" class="needs-validation" novalidate>
                                <div class="row">
                                    <!-- نوع العملية (الدولة والعملة) -->
                                    <div class="col-md-6 mb-3">
                                        <label for="country_search" class="form-label required">
                                            <i class="fas fa-globe me-1"></i>
                                            نوع العملية (الدولة والعملة)
                                        </label>
                                        <div class="position-relative">
                                            <input type="text" class="form-control" id="country_search" placeholder="ابحث عن الدولة أو العملة..." autocomplete="off" value="<?php
                                                if (isset($_POST['country_id']) && $_POST['country_id']) {
                                                    $selected = array_filter($countries, function($c) { return $c['id'] == $_POST['country_id']; });
                                                    if ($selected) echo htmlspecialchars(reset($selected)['name_ar']);
                                                } elseif (!empty($transaction['country_id'])) {
                                                    $selected = array_filter($countries, function($c) use ($transaction) { return $c['id'] == $transaction['country_id']; });
                                                    if ($selected) echo htmlspecialchars(reset($selected)['name_ar']);
                                                }
                                            ?>">
                                            <input type="hidden" id="country_id" name="country_id" value="<?php echo isset($_POST['country_id']) ? htmlspecialchars($_POST['country_id']) : htmlspecialchars($transaction['country_id']); ?>">
                                            <div class="invalid-feedback">يرجى اختيار الدولة والعملة</div>
                                            <div id="country_results" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none;"></div>
                                        </div>
                                        <div id="selected_country" class="mt-2" style="display: none;">
                                            <div class="alert alert-info py-2 mb-0">
                                                <i class="fas fa-check-circle me-2"></i>
                                                <span id="selected_country_text"></span>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearCountrySelection()">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- مبلغ الحوالة الأساسي -->
                                    <div class="col-md-6 mb-3">
                                        <label for="base_amount" class="form-label required">
                                            <i class="fas fa-money-bill me-1"></i>
                                            مبلغ الحوالة الأساسي
                                        </label>
                                        <input type="number" class="form-control" id="base_amount" name="base_amount" step="0.01" min="0.01" required value="<?php echo isset($_POST['base_amount']) ? htmlspecialchars($_POST['base_amount']) : htmlspecialchars($transaction['base_amount']); ?>">
                                        <div class="invalid-feedback">يرجى إدخال مبلغ صحيح أكبر من صفر</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <!-- سعر القص للمكتب -->
                                    <div class="col-md-4 mb-3">
                                        <label for="office_rate" class="form-label required">
                                            <i class="fas fa-percent me-1"></i>
                                            سعر القص للمكتب
                                        </label>
                                        <input type="number" class="form-control" id="office_rate" name="office_rate" step="0.000001" min="0.000001" required value="<?php echo isset($_POST['office_rate']) ? htmlspecialchars($_POST['office_rate']) : htmlspecialchars($transaction['office_rate']); ?>">
                                        <div class="invalid-feedback">يرجى إدخال سعر صحيح أكبر من صفر</div>
                                    </div>
                                    <!-- سعر القص للزبون -->
                                    <div class="col-md-4 mb-3">
                                        <label for="customer_rate" class="form-label required">
                                            <i class="fas fa-percentage me-1"></i>
                                            سعر القص للزبون
                                        </label>
                                        <input type="number" class="form-control" id="customer_rate" name="customer_rate" step="0.000001" min="0.000001" required value="<?php echo isset($_POST['customer_rate']) ? htmlspecialchars($_POST['customer_rate']) : htmlspecialchars($transaction['customer_rate']); ?>">
                                        <div class="invalid-feedback">يرجى إدخال سعر صحيح أكبر من صفر</div>
                                    </div>
                                    <!-- نوع العملية الحسابية -->
                                    <div class="col-md-4 mb-3">
                                        <label for="operation_type" class="form-label required">
                                            <i class="fas fa-calculator me-1"></i>
                                            نوع العملية الحسابية
                                        </label>
                                        <select class="form-select" id="operation_type" name="operation_type" required>
                                            <option value="">اختر نوع العملية</option>
                                            <option value="multiply" <?php echo (isset($_POST['operation_type']) ? $_POST['operation_type'] : $transaction['operation_type']) == 'multiply' ? 'selected' : ''; ?>>ضرب (×)</option>
                                            <option value="divide" <?php echo (isset($_POST['operation_type']) ? $_POST['operation_type'] : $transaction['operation_type']) == 'divide' ? 'selected' : ''; ?>>قسمة (÷)</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار نوع العملية</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <!-- المبلغ الناتج للزبون (محسوب تلقائياً) -->
                                    <div class="col-md-4 mb-3">
                                        <label for="calculated_amount" class="form-label">
                                            <i class="fas fa-equals me-1"></i>
                                            المبلغ الناتج للزبون
                                        </label>
                                        <input type="text" class="form-control bg-light" id="calculated_amount" readonly value="<?php echo number_format($transaction['calculated_amount'], 2); ?>" placeholder="سيتم الحساب تلقائياً">
                                        <small class="text-muted">يتم حسابه تلقائياً بناءً على العملية المختارة</small>
                                    </div>
                                    <!-- المبلغ الناتج للمكتب (محسوب تلقائياً) -->
                                    <div class="col-md-4 mb-3">
                                        <label for="office_amount" class="form-label">
                                            <i class="fas fa-equals me-1"></i>
                                            المبلغ الناتج للمكتب
                                        </label>
                                        <input type="text" class="form-control bg-light" id="office_amount" readonly value="<?php echo number_format($transaction['office_amount'], 2); ?>" placeholder="سيتم الحساب تلقائياً">
                                        <small class="text-muted">يتم حسابه تلقائياً بناءً على العملية المختارة</small>
                                    </div>
                                    <!-- سعر الصرف -->
                                    <div class="col-md-4 mb-3">
                                        <label for="exchange_rate" class="form-label required">
                                            <i class="fas fa-exchange-alt me-1"></i>
                                            سعر الصرف
                                        </label>
                                        <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" step="0.000001" min="0.000001" required value="<?php echo isset($_POST['exchange_rate']) ? htmlspecialchars($_POST['exchange_rate']) : htmlspecialchars($transaction['exchange_rate']); ?>">
                                        <div class="invalid-feedback">يرجى إدخال سعر صرف صحيح أكبر من صفر</div>
                                    </div>
                                </div>
                                <div class="row">
                                    <!-- المبلغ للمستلم (محسوب تلقائياً) -->
                                    <div class="col-md-6 mb-3">
                                        <label for="recipient_amount" class="form-label">
                                            <i class="fas fa-hand-holding-usd me-1"></i>
                                            المبلغ للمستلم (رقم صحيح)
                                        </label>
                                        <input type="text" class="form-control bg-light" id="recipient_amount" readonly value="<?php echo number_format($transaction['recipient_amount']); ?>" placeholder="سيتم الحساب تلقائياً">
                                        <small class="text-muted">يتم حسابه تلقائياً كرقم صحيح</small>
                                    </div>
                                    <!-- نوع التسليم -->
                                    <div class="col-md-6 mb-3">
                                        <label for="delivery_type" class="form-label required">
                                            <i class="fas fa-truck me-1"></i>
                                            نوع التسليم
                                        </label>
                                        <select class="form-select" id="delivery_type" name="delivery_type" required>
                                            <option value="">اختر نوع التسليم</option>
                                            <option value="cash" <?php echo (isset($_POST['delivery_type']) ? $_POST['delivery_type'] : $transaction['delivery_type']) == 'cash' ? 'selected' : ''; ?>>كاش (Cash)</option>
                                            <option value="bank" <?php echo (isset($_POST['delivery_type']) ? $_POST['delivery_type'] : $transaction['delivery_type']) == 'bank' ? 'selected' : ''; ?>>بنكي (Bank Transfer)</option>
                                            <option value="usdt" <?php echo (isset($_POST['delivery_type']) ? $_POST['delivery_type'] : $transaction['delivery_type']) == 'usdt' ? 'selected' : ''; ?>>USDT (عملة رقمية)</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار نوع التسليم</div>
                                    </div>
                                </div>
                                <!-- حالة التسليم -->
                                <div class="mb-3">
                                    <label for="delivery_status" class="form-label required">
                                        <i class="fas fa-check-double me-1"></i>
                                        حالة التسليم
                                    </label>
                                    <select class="form-select" id="delivery_status" name="delivery_status" required>
                                        <option value="">اختر حالة التسليم</option>
                                        <option value="مستلم" <?php echo (isset($_POST['delivery_status']) ? $_POST['delivery_status'] : $transaction['delivery_status']) == 'مستلم' ? 'selected' : ''; ?>>مستلم</option>
                                        <option value="معلق" <?php echo (isset($_POST['delivery_status']) ? $_POST['delivery_status'] : $transaction['delivery_status']) == 'معلق' ? 'selected' : ''; ?>>معلق</option>
                                    </select>
                                    <div class="invalid-feedback">يرجى اختيار حالة التسليم</div>
                                </div>
                                <div class="row">
                                    <!-- حقل اسم المستلم (بحث تلقائي من العملاء) -->
                                    <div class="col-md-6 mb-3 position-relative" id="recipient_name_field">
                                        <label for="recipient_name" class="form-label required">
                                            <i class="fas fa-user me-1"></i>
                                            اسم المستلم
                                        </label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="recipient_name_search" name="recipient_name_search" maxlength="255" placeholder="ابحث عن اسم المستلم..." value="<?php echo isset($_POST['recipient_name']) ? htmlspecialchars($_POST['recipient_name']) : htmlspecialchars($transaction['recipient_name']); ?>">
                                            <input type="hidden" id="recipient_name" name="recipient_name" value="<?php echo isset($_POST['recipient_name']) ? htmlspecialchars($_POST['recipient_name']) : htmlspecialchars($transaction['recipient_name']); ?>">
                                            <button type="button" class="btn btn-outline-success" title="إضافة عميل جديد" onclick="window.open('customers.php?add=1', '_blank')">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                        <div id="recipient_results" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto; display: none;"></div>
                                        <div class="invalid-feedback">يرجى اختيار اسم المستلم من القائمة أو إضافته</div>
                                    </div>
                                    <!-- حقل المبلغ المراد تحويله (للبنكي و USDT فقط) -->
                                    <div class="col-md-6 mb-3" id="transfer_amount_field" style="display: <?php echo ($transaction['delivery_type'] == 'bank' || $transaction['delivery_type'] == 'usdt') ? 'block' : 'none'; ?>;">
                                        <label for="transfer_amount" class="form-label required">
                                            <i class="fas fa-money-check me-1"></i>
                                            المبلغ المراد تحويله
                                        </label>
                                        <input type="number" class="form-control" id="transfer_amount" name="transfer_amount" step="0.01" min="0.01" value="<?php echo isset($_POST['transfer_amount']) ? htmlspecialchars($_POST['transfer_amount']) : htmlspecialchars($transaction['transfer_amount']); ?>" placeholder="أدخل المبلغ المراد تحويله">
                                        <div class="invalid-feedback">يرجى إدخال مبلغ صحيح للتحويل</div>
                                    </div>
                                </div>
                                <!-- ملاحظة -->
                                <div class="mb-4">
                                    <label for="notes" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>
                                        ملاحظة
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : htmlspecialchars($transaction['notes']); ?></textarea>
                                </div>
                                <!-- أزرار التحكم -->
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save me-2"></i>
                                            حفظ التعديلات
                                        </button>
                                        <button type="reset" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                                            <i class="fas fa-undo me-2"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <a href="<?php echo BASE_URL; ?>dashboard/daily_transactions.php" class="btn btn-outline-secondary btn-lg">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        العودة للقائمة
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSS إضافي للحقول الإضافية -->
    <style>
        #additional_fields {
            display: none !important; /* إخفاء افتراضي قوي */
            transition: all 0.3s ease;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        #additional_fields.show {
            border-color: #0d6efd;
            background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);
        }

        /* تحسين مظهر الحقول حسب النوع */
        .alert-success {
            border-color: #28a745 !important;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
        }

        #recipient_name_field {
            transition: all 0.3s ease;
        }

        #transfer_amount_field {
            transition: all 0.3s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-label.required::after {
            content: " *";
            color: #dc3545;
            font-weight: bold;
        }

        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .card-header {
            border-bottom: 1px solid rgba(0,0,0,.125);
        }
    </style>

    <!-- JavaScript للحسابات التلقائية -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const baseAmountInput = document.getElementById('base_amount');
            const customerRateInput = document.getElementById('customer_rate');
            const operationTypeSelect = document.getElementById('operation_type');
            const exchangeRateInput = document.getElementById('exchange_rate');
            const calculatedAmountInput = document.getElementById('calculated_amount');
            const recipientAmountInput = document.getElementById('recipient_amount');
            const deliveryTypeSelect = document.getElementById('delivery_type');
            const additionalFields = document.getElementById('additional_fields');
            const transferAmountInput = document.getElementById('transfer_amount');
            const recipientNameInput = document.getElementById('recipient_name');
            const transferAmountField = document.getElementById('transfer_amount_field');
            const recipientNameField = document.getElementById('recipient_name_field');
            const infoAlert = document.getElementById('info_alert');
            const infoText = document.getElementById('info_text');
            const infoDescription = document.getElementById('info_description');

            // تشخيص: طباعة العناصر للتأكد من وجودها
            console.log('Elements found:', {
                deliveryTypeSelect: !!deliveryTypeSelect,
                additionalFields: !!additionalFields,
                transferAmountInput: !!transferAmountInput,
                recipientNameInput: !!recipientNameInput
            });

            // حساب المبلغ الناتج والمبلغ للمستلم
            function calculateAmounts() {
                const baseAmount = parseFloat(baseAmountInput.value) || 0;
                const customerRate = parseFloat(customerRateInput.value) || 0;
                const officeRate = parseFloat(document.getElementById('office_rate').value) || 0;
                const operationType = operationTypeSelect.value;
                const exchangeRate = parseFloat(exchangeRateInput.value) || 0;

                // حساب المبلغ الناتج للزبون
                if (baseAmount > 0 && customerRate > 0) {
                    let calculatedAmount = 0;
                    if (operationType === 'multiply') {
                        calculatedAmount = baseAmount * customerRate;
                    } else if (operationType === 'divide') {
                        calculatedAmount = baseAmount / customerRate;
                    }
                    calculatedAmountInput.value = calculatedAmount.toFixed(2);

                    // حساب المبلغ للمستلم
                    if (exchangeRate > 0) {
                        const recipientAmount = Math.floor(calculatedAmount * exchangeRate);
                        recipientAmountInput.value = recipientAmount.toLocaleString();
                    } else {
                        recipientAmountInput.value = '';
                    }
                } else {
                    calculatedAmountInput.value = '';
                    recipientAmountInput.value = '';
                }

                // حساب المبلغ الناتج للمكتب
                if (baseAmount > 0 && officeRate > 0) {
                    let officeAmount = 0;
                    if (operationType === 'multiply') {
                        officeAmount = baseAmount * officeRate;
                    } else if (operationType === 'divide') {
                        officeAmount = baseAmount / officeRate;
                    }
                    document.getElementById('office_amount').value = officeAmount.toFixed(2);
                } else {
                    document.getElementById('office_amount').value = '';
                }
            }

            // إظهار/إخفاء الحقول الإضافية
            function toggleAdditionalFields() {
                if (!deliveryTypeSelect || !additionalFields) {
                    console.error('Required elements not found');
                    return;
                }

                const deliveryType = deliveryTypeSelect.value;
                console.log('Delivery type selected:', deliveryType);

                if (deliveryType === 'cash') {
                    // كاش: إظهار اسم المستلم فقط
                    console.log('Showing cash fields (recipient name only)');
                    additionalFields.style.setProperty('display', 'block', 'important');
                    additionalFields.style.animation = 'fadeIn 0.3s ease';
                    additionalFields.classList.add('show');

                    // إخفاء حقل المبلغ المراد تحويله
                    if (transferAmountField) transferAmountField.style.display = 'none';
                    if (transferAmountInput) {
                        transferAmountInput.required = false;
                        transferAmountInput.value = '';
                    }

                    // إظهار حقل اسم المستلم
                    if (recipientNameField) recipientNameField.style.display = 'block';
                    if (recipientNameInput) recipientNameInput.required = true;

                    // تحديث النص التوضيحي
                    if (infoText) infoText.textContent = 'معلومات المستلم:';
                    if (infoDescription) infoDescription.textContent = 'يرجى إدخال اسم المستلم للمعاملة النقدية';
                    if (infoAlert) infoAlert.className = 'alert alert-success';

                } else if (deliveryType === 'bank' || deliveryType === 'usdt') {
                    // بنكي أو USDT: إظهار كلا الحقلين
                    console.log('Showing bank/USDT fields (both fields)');
                    additionalFields.style.setProperty('display', 'block', 'important');
                    additionalFields.style.animation = 'fadeIn 0.3s ease';
                    additionalFields.classList.add('show');

                    // إظهار حقل المبلغ المراد تحويله
                    if (transferAmountField) transferAmountField.style.display = 'block';
                    if (transferAmountInput) transferAmountInput.required = true;

                    // إظهار حقل اسم المستلم
                    if (recipientNameField) recipientNameField.style.display = 'block';
                    if (recipientNameInput) recipientNameInput.required = true;

                    // تحديث النص التوضيحي
                    const typeText = deliveryType === 'bank' ? 'التحويل البنكي' : 'USDT';
                    if (infoText) infoText.textContent = 'معلومات إضافية مطلوبة:';
                    if (infoDescription) infoDescription.textContent = `يرجى ملء البيانات التالية لـ ${typeText}`;
                    if (infoAlert) infoAlert.className = 'alert alert-info';

                    // ملء المبلغ المراد تحويله تلقائياً بقيمة المبلغ للمستلم
                    if (transferAmountInput && !transferAmountInput.value && recipientAmountInput && recipientAmountInput.value) {
                        const recipientAmount = recipientAmountInput.value.replace(/,/g, ''); // إزالة الفواصل
                        transferAmountInput.value = recipientAmount;
                    }

                } else {
                    // لا يوجد اختيار: إخفاء جميع الحقول
                    console.log('Hiding all additional fields');
                    additionalFields.style.setProperty('display', 'none', 'important');
                    additionalFields.classList.remove('show');

                    // مسح جميع القيم
                    if (transferAmountInput) {
                        transferAmountInput.required = false;
                        transferAmountInput.value = '';
                    }
                    if (recipientNameInput) {
                        recipientNameInput.required = false;
                        recipientNameInput.value = '';
                    }
                }
            }

            // ربط الأحداث
            baseAmountInput.addEventListener('input', calculateAmounts);
            customerRateInput.addEventListener('input', calculateAmounts);
            operationTypeSelect.addEventListener('change', calculateAmounts);
            exchangeRateInput.addEventListener('input', calculateAmounts);
            deliveryTypeSelect.addEventListener('change', function() {
                console.log('Delivery type changed to:', this.value);
                toggleAdditionalFields();
            });

            // تشغيل الدوال عند تحميل الصفحة
            calculateAmounts();

            // تأخير قصير للتأكد من تحميل جميع العناصر
            setTimeout(function() {
                toggleAdditionalFields();
            }, 100);

            // التحقق من صحة النموذج
            const form = document.getElementById('editTransactionForm');
            form.addEventListener('submit', function(event) {
                console.log('Form submit triggered'); // تشخيص: هل الدالة تعمل؟
                // التحقق الإضافي للحقول المطلوبة
                const deliveryType = deliveryTypeSelect.value;
                let hasError = false;

                if (deliveryType === 'cash') {
                    console.log('Delivery type is cash, checking recipientNameInput:', recipientNameInput.value);
                    if (!recipientNameInput.value) {
                        recipientNameInput.classList.add('is-invalid');
                        hasError = true;
                    }
                } else if (deliveryType === 'bank' || deliveryType === 'usdt') {
                    console.log('Delivery type is bank/usdt, checking transferAmountInput:', transferAmountInput.value, 'recipientNameInput:', recipientNameInput.value);
                    if (!transferAmountInput.value) {
                        transferAmountInput.classList.add('is-invalid');
                        hasError = true;
                    }
                    if (!recipientNameInput.value) {
                        recipientNameInput.classList.add('is-invalid');
                        hasError = true;
                    }
                }

                if (hasError) {
                    alert('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
                    event.preventDefault();
                    event.stopPropagation();
                    additionalFields.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    console.log('Form not submitted due to validation error');
                    return false;
                }

                if (!form.checkValidity()) {
                    alert('هناك حقول مطلوبة لم يتم تعبئتها أو بها خطأ!');
                    event.preventDefault();
                    event.stopPropagation();
                    console.log('Form not submitted due to HTML5 validation');
                } else {
                    alert('سيتم إرسال النموذج الآن!');
                    console.log('Form is valid and will be submitted');
                }
                form.classList.add('was-validated');
            });

            // إزالة رسائل الخطأ عند الكتابة
            if (transferAmountInput) {
                transferAmountInput.addEventListener('input', function() {
                    this.classList.remove('is-invalid');
                });
            }

            if (recipientNameInput) {
                recipientNameInput.addEventListener('input', function() {
                    this.classList.remove('is-invalid');
                });
            }

            // بحث تلقائي لاسم المستلم
            const recipientNameSearch = document.getElementById('recipient_name_search');
            const recipientNameHidden = document.getElementById('recipient_name');
            const recipientResults = document.getElementById('recipient_results');
            let recipientAjaxTimeout = null;

            recipientNameSearch.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length < 2) {
                    recipientResults.style.display = 'none';
                    return;
                }
                if (recipientAjaxTimeout) clearTimeout(recipientAjaxTimeout);
                recipientAjaxTimeout = setTimeout(function() {
                    fetch('ajax/search_beneficiaries.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: 'search=' + encodeURIComponent(query)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.customers.length > 0) {
                            let html = '';
                            data.customers.forEach(cust => {
                                html += `<div class="dropdown-item" style="cursor:pointer;" data-name="${cust.full_name.replace(/"/g, '&quot;')}">`
                                    + `<strong>${cust.full_name}</strong>`
                                    + (cust.id_number ? ` <small class="text-muted">(${cust.id_number})</small>` : '')
                                    + (cust.phone ? ` <small class="text-muted">[${cust.phone}]</small>` : '')
                                    + '</div>';
                            });
                            recipientResults.innerHTML = html;
                            recipientResults.style.display = 'block';
                            recipientResults.querySelectorAll('.dropdown-item').forEach(item => {
                                item.addEventListener('click', function() {
                                    const name = this.getAttribute('data-name');
                                    recipientNameSearch.value = name;
                                    recipientNameHidden.value = name;
                                    recipientResults.style.display = 'none';
                                    recipientNameSearch.classList.remove('is-invalid');
                                    recipientNameHidden.classList.remove('is-invalid');
                                });
                            });
                        } else {
                            recipientResults.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                            recipientResults.style.display = 'block';
                        }
                    })
                    .catch(() => {
                        recipientResults.innerHTML = '<div class="dropdown-item text-danger">خطأ في البحث</div>';
                        recipientResults.style.display = 'block';
                    });
                }, 300);
            });
            recipientNameSearch.addEventListener('focus', function() {
                if (this.value.trim().length >= 2 && recipientResults.innerHTML) {
                    recipientResults.style.display = 'block';
                }
            });
            document.addEventListener('click', function(e) {
                if (!recipientNameSearch.contains(e.target) && !recipientResults.contains(e.target)) {
                    recipientResults.style.display = 'none';
                }
            });
            // عند تغيير الاسم يدويًا امسح القيمة المخفية إذا لم تتطابق
            recipientNameSearch.addEventListener('change', function() {
                if (this.value !== recipientNameHidden.value) {
                    recipientNameHidden.value = '';
                }
            });

            // إضافة تشخيص إضافي
            console.log('Initial delivery type:', deliveryTypeSelect.value);
            console.log('Additional fields display:', additionalFields.style.display);
        });

        // دالة إعادة تعيين النموذج
        function resetForm() {
            // إعادة تعيين النموذج إلى القيم الأصلية
            location.reload();
        }

        // بيانات الدول للعملية
        const countriesData = [
            <?php foreach ($countries as $country): ?>
            {
                id: <?php echo $country['id']; ?>,
                name_ar: "<?php echo htmlspecialchars($country['name_ar']); ?>",
                currency_code: "<?php echo htmlspecialchars($country['currency_code']); ?>",
                currency_symbol: "<?php echo htmlspecialchars($country['currency_symbol']); ?>",
                searchText: "<?php echo htmlspecialchars($country['name_ar'] . ' ' . $country['currency_code'] . ' ' . $country['currency_symbol']); ?>"
            },
            <?php endforeach; ?>
        ];
        // البحث التفاعلي في الدول
        function initCountrySearch() {
            const searchInput = document.getElementById('country_search');
            const resultsDiv = document.getElementById('country_results');
            const hiddenInput = document.getElementById('country_id');
            const selectedDiv = document.getElementById('selected_country');
            const selectedText = document.getElementById('selected_country_text');
            // إذا كان هناك قيمة محددة مسبقاً
            const preselectedId = hiddenInput.value;
            if (preselectedId) {
                const preselectedCountry = countriesData.find(c => c.id == preselectedId);
                if (preselectedCountry) {
                    showSelectedCountry(preselectedCountry);
                }
            }
            searchInput.addEventListener('input', function() {
                const query = this.value.trim().toLowerCase();
                if (query.length < 1) {
                    resultsDiv.style.display = 'none';
                    return;
                }
                const filteredCountries = countriesData.filter(country => country.searchText.toLowerCase().includes(query));
                displaySearchResults(filteredCountries);
            });
            searchInput.addEventListener('focus', function() {
                if (this.value.trim().length > 0) {
                    const query = this.value.trim().toLowerCase();
                    const filteredCountries = countriesData.filter(country => country.searchText.toLowerCase().includes(query));
                    displaySearchResults(filteredCountries);
                }
            });
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                    resultsDiv.style.display = 'none';
                }
            });
        }
        function displaySearchResults(countries) {
            const resultsDiv = document.getElementById('country_results');
            if (countries.length === 0) {
                resultsDiv.innerHTML = '<div class="dropdown-item text-muted">لا توجد نتائج</div>';
                resultsDiv.style.display = 'block';
                return;
            }
            let html = '';
            countries.slice(0, 10).forEach(country => {
                html += `
                    <div class="dropdown-item country-option"
                         data-id="${country.id}"
                         data-name="${country.name_ar}"
                         data-currency="${country.currency_code}"
                         data-symbol="${country.currency_symbol}"
                         style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${country.name_ar}</strong>
                                <small class="text-muted d-block">${country.currency_code} - ${country.currency_symbol}</small>
                            </div>
                            <span class="badge bg-primary">${country.currency_code}</span>
                        </div>
                    </div>
                `;
            });
            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
            resultsDiv.querySelectorAll('.country-option').forEach(option => {
                option.addEventListener('click', function() {
                    const countryData = {
                        id: this.dataset.id,
                        name_ar: this.dataset.name,
                        currency_code: this.dataset.currency,
                        currency_symbol: this.dataset.symbol
                    };
                    selectCountry(countryData);
                });
            });
        }
        function selectCountry(country) {
            const searchInput = document.getElementById('country_search');
            const hiddenInput = document.getElementById('country_id');
            const resultsDiv = document.getElementById('country_results');
            hiddenInput.value = country.id;
            searchInput.value = country.name_ar;
            resultsDiv.style.display = 'none';
            showSelectedCountry(country);
            hiddenInput.classList.remove('is-invalid');
            searchInput.classList.remove('is-invalid');
            const parentDiv = searchInput.parentNode;
            const invalidFeedback = parentDiv.querySelector('.invalid-feedback');
            if (invalidFeedback) {
                invalidFeedback.style.display = 'none';
            }
            hiddenInput.dispatchEvent(new Event('change'));
        }
        function showSelectedCountry(country) {
            const selectedDiv = document.getElementById('selected_country');
            const selectedText = document.getElementById('selected_country_text');
            const searchInput = document.getElementById('country_search');
            selectedText.innerHTML = `<strong>${country.name_ar}</strong> - ${country.currency_code} (${country.currency_symbol})`;
            selectedDiv.style.display = 'block';
            searchInput.value = country.name_ar;
            searchInput.disabled = true;
            searchInput.style.backgroundColor = '#e9ecef';
        }
        window.clearCountrySelection = function() {
            const searchInput = document.getElementById('country_search');
            const hiddenInput = document.getElementById('country_id');
            const selectedDiv = document.getElementById('selected_country');
            const resultsDiv = document.getElementById('country_results');
            hiddenInput.value = '';
            searchInput.value = '';
            searchInput.disabled = false;
            searchInput.style.backgroundColor = '';
            selectedDiv.style.display = 'none';
            resultsDiv.style.display = 'none';
            searchInput.classList.remove('is-invalid');
            hiddenInput.classList.remove('is-invalid');
        };
        initCountrySearch();
    </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

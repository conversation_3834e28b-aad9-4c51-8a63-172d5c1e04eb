-- Create Office Operations Table
-- This script creates the office operations system

-- ======= Office Operations Table =======
CREATE TABLE IF NOT EXISTS office_operations (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    office_id INT UNSIGNED NOT NULL,
    operation_name VARCHAR(255) NOT NULL,
    base_amount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    amount DECIMAL(18,2) NOT NULL DEFAULT 0.00,
    operation_type ENUM('multiply', 'divide') NOT NULL DEFAULT 'multiply' COMMENT 'multiply = ضرب, divide = قسمة',
    is_credit BOOLEAN NOT NULL COMMENT 'TRUE = لنا (credit), FALSE = لكم (debit)',
    tracking_number VARCHAR(100) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT fk_op_office FOREIGN KEY (office_id) REFERENCES offices(id) ON DELETE CASCADE ON UPDATE CASCADE,
    INDEX idx_op_office (office_id),
    INDEX idx_op_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ======= Permissions =======
INSERT IGNORE INTO permissions (id, name, description, module) VALUES
    (38, 'offices.operations.view', 'View office operations', 'offices'),
    (39, 'offices.operations.create', 'Create office operation', 'offices'),
    (40, 'offices.operations.edit', 'Edit office operation', 'offices'),
    (41, 'offices.operations.delete', 'Delete office operation', 'offices');

-- ======= Role Permissions =======
INSERT IGNORE INTO role_permissions (role_id, permission_id) VALUES
    (1,38),(1,39),(1,40),(1,41), -- Admin all
    (2,38),(2,39),(2,40), -- Branch Manager
    (3,38); -- Cashier view only

-- ======= Sample Data =======
INSERT IGNORE INTO office_operations (office_id, operation_name, amount, is_credit) VALUES
    (1, 'تحويل دولار', 5000.00, 1),
    (1, 'سحب يورو', 3000.00, 0),
    (1, 'إيداع درهم', 10000.00, 1),
    (2, 'تحويل دولار', 8000.00, 1),
    (2, 'سحب', 4000.00, 0),
    (3, 'إيداع درهم', 7000.00, 1),
    (3, 'تحويل يورو', 6000.00, 0);

COMMIT; 
<?php
/**
 * Delete Bank Account Movement
 * Handle deletion of bank account movements
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/bank_manager.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('cash.movements.delete');

$db = new Database();
$bankManager = new BankManager($db);

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    set_flash('danger', 'طريقة طلب غير صحيحة');
    redirect('cash.php?tab=bank');
    exit;
}

// Get movement ID and bank account ID
$movementId = (int)($_POST['movement_id'] ?? 0);
$bankAccountId = (int)($_POST['bank_account_id'] ?? 0);

if ($movementId <= 0 || $bankAccountId <= 0) {
    set_flash('danger', 'معرفات غير صحيحة');
    redirect('cash.php?tab=bank');
    exit;
}

try {
    // Get movement details first
    $movement = $bankManager->getBankMovementById($movementId);
    
    if (!$movement) {
        set_flash('danger', 'الحركة البنكية غير موجودة');
        redirect('bank_account_history.php?id=' . $bankAccountId);
        exit;
    }
    
    // Check if movement belongs to the specified bank account
    if ($movement['bank_account_id'] != $bankAccountId) {
        set_flash('danger', 'الحركة لا تنتمي لهذا الحساب البنكي');
        redirect('bank_account_history.php?id=' . $bankAccountId);
        exit;
    }
    
    // Check if movement is linked to exchange or transfer
    if (!empty($movement['exchange_id']) || !empty($movement['transfer_id'])) {
        set_flash('danger', 'لا يمكن حذف حركة مرتبطة بعملية صرافة أو تحويل');
        redirect('bank_account_history.php?id=' . $bankAccountId);
        exit;
    }
    
    // Delete the movement
    $success = $bankManager->deleteBankMovement($movementId);
    
    if ($success) {
        set_flash('success', 'تم حذف الحركة البنكية بنجاح');
    } else {
        set_flash('danger', 'فشل في حذف الحركة البنكية');
    }
    
} catch (Exception $e) {
    set_flash('danger', 'خطأ في حذف الحركة البنكية: ' . $e->getMessage());
}

// Redirect back to bank account history
redirect('bank_account_history.php?id=' . $bankAccountId); 
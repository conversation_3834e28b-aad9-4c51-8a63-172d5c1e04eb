<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/system_activity_manager.php';

$auth = new Auth();

// جلب معلومات المستخدم قبل تسجيل الخروج
$currentUser = $auth->getCurrentUser();
$username = $currentUser['username'] ?? 'unknown';

// تسجيل عملية الخروج
$activityManager = new SystemActivityManager();
$activityManager->logLogout($username);

// تسجيل الخروج
$auth->logoutUser();

redirect('login.php');
<?php
/**
 * Final comprehensive test for currencies system
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';

echo "<h2>اختبار شامل نهائي لنظام العملات</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص جدول العملات</h3>\n";
    
    // Check currencies table
    $result = $conn->query("SELECT COUNT(*) as total, 
                                   SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                                   SUM(CASE WHEN country LIKE '%العربية%' OR country LIKE '%الإمارات%' OR country LIKE '%السعودية%' OR country LIKE '%الكويت%' OR country LIKE '%قطر%' OR country LIKE '%البحرين%' OR country LIKE '%عمان%' OR country LIKE '%الأردن%' OR country LIKE '%لبنان%' OR country LIKE '%سوريا%' OR country LIKE '%العراق%' OR country LIKE '%مصر%' OR country LIKE '%ليبيا%' OR country LIKE '%تونس%' OR country LIKE '%الجزائر%' OR country LIKE '%المغرب%' OR country LIKE '%موريتانيا%' OR country LIKE '%السودان%' OR country LIKE '%الصومال%' OR country LIKE '%جيبوتي%' OR country LIKE '%القمر%' OR country LIKE '%اليمن%' THEN 1 ELSE 0 END) as arab_currencies
                           FROM currencies");
    
    if ($result) {
        $stats = $result->fetch_assoc();
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<h4>📊 إحصائيات العملات</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>إجمالي العملات:</strong> {$stats['total']}</li>\n";
        echo "<li><strong>العملات النشطة:</strong> {$stats['active']}</li>\n";
        echo "<li><strong>العملات العربية:</strong> {$stats['arab_currencies']}</li>\n";
        echo "<li><strong>العملات الأجنبية:</strong> " . ($stats['total'] - $stats['arab_currencies']) . "</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    }
    
    echo "<h3>2. اختبار إنشاء صندوق بعملات مختلفة</h3>\n";
    
    // Test creating cash boxes with different currencies
    $testCurrencies = ['USD', 'EUR', 'SAR', 'AED', 'GBP', 'JPY'];
    $createdBoxes = [];
    
    foreach ($testCurrencies as $currency) {
        $data = [
            'name' => "صندوق اختبار $currency",
            'currency_code' => $currency,
            'initial_balance' => 1000.00,
            'branch_id' => 1,
            'responsible_user_id' => 1,
            'is_active' => 1
        ];
        
        $result = $cashManager->addCashBox($data);
        if ($result) {
            $createdBoxes[] = ['id' => $result, 'currency' => $currency];
            echo "<p style='color: green;'>✓ تم إنشاء صندوق بعملة $currency (ID: $result)</p>\n";
        } else {
            echo "<p style='color: red;'>✗ فشل في إنشاء صندوق بعملة $currency</p>\n";
        }
    }
    
    echo "<h3>3. اختبار استرجاع الصناديق مع معلومات العملات</h3>\n";
    
    $allBoxes = $cashManager->getAllCashBoxes();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
    echo "<tr style='background: #f8f9fa;'>\n";
    echo "<th>ID</th><th>اسم الصندوق</th><th>رمز العملة</th><th>رمز العملة المختصر</th><th>الرصيد</th><th>الحالة</th>\n";
    echo "</tr>\n";
    
    foreach (array_slice($allBoxes, -10) as $box) {
        $statusText = $box['is_active'] ? 'نشط' : 'غير نشط';
        $statusColor = $box['is_active'] ? 'green' : 'red';
        
        echo "<tr>\n";
        echo "<td>{$box['id']}</td>\n";
        echo "<td>" . htmlspecialchars($box['name']) . "</td>\n";
        echo "<td><strong>{$box['currency_code']}</strong></td>\n";
        echo "<td>{$box['currency_symbol']}</td>\n";
        echo "<td>" . number_format($box['current_balance'], 2) . " {$box['currency_symbol']}</td>\n";
        echo "<td style='color: $statusColor;'>$statusText</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h3>4. اختبار إضافة حركات مالية بعملات مختلفة</h3>\n";
    
    if (!empty($createdBoxes)) {
        foreach (array_slice($createdBoxes, 0, 3) as $boxInfo) {
            $boxId = $boxInfo['id'];
            $currency = $boxInfo['currency'];
            
            // Add deposit
            $depositResult = $cashManager->addCashMovement(
                $boxId,
                'deposit',
                500.00,
                "إيداع تجريبي بعملة $currency",
                "TEST_DEP_{$currency}_" . time(),
                1
            );
            
            if ($depositResult['success']) {
                echo "<p style='color: green;'>✓ تم إضافة إيداع للصندوق $currency (ID: {$depositResult['movement_id']})</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة إيداع للصندوق $currency</p>\n";
            }
            
            // Add withdrawal
            $withdrawalResult = $cashManager->addCashMovement(
                $boxId,
                'withdrawal',
                200.00,
                "سحب تجريبي بعملة $currency",
                "TEST_WTH_{$currency}_" . time(),
                1
            );
            
            if ($withdrawalResult['success']) {
                echo "<p style='color: green;'>✓ تم إضافة سحب للصندوق $currency (ID: {$withdrawalResult['movement_id']})</p>\n";
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة سحب للصندوق $currency</p>\n";
            }
        }
    }
    
    echo "<h3>5. اختبار استرجاع الحركات مع معلومات العملات</h3>\n";
    
    if (!empty($createdBoxes)) {
        $testBoxId = $createdBoxes[0]['id'];
        $movements = $cashManager->getCashBoxMovements($testBoxId, [], 5, 0);
        
        if (!empty($movements)) {
            echo "<p><strong>حركات الصندوق {$createdBoxes[0]['currency']}:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>ID</th><th>النوع</th><th>المبلغ</th><th>الوصف</th><th>رمز العملة</th><th>الرصيد بعد</th>\n";
            echo "</tr>\n";
            
            foreach ($movements as $movement) {
                $typeColor = ($movement['type'] === 'deposit') ? 'green' : 'orange';
                $amountSign = ($movement['type'] === 'deposit') ? '+' : '-';
                
                echo "<tr>\n";
                echo "<td>{$movement['id']}</td>\n";
                echo "<td style='color: $typeColor;'>{$movement['type']}</td>\n";
                echo "<td style='color: $typeColor;'>$amountSign" . number_format($movement['amount'], 2) . "</td>\n";
                echo "<td>" . htmlspecialchars($movement['description']) . "</td>\n";
                echo "<td><strong>{$movement['currency_code']}</strong></td>\n";
                echo "<td>" . number_format($movement['balance_after'], 2) . " {$movement['currency_symbol']}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p style='color: orange;'>⚠ لا توجد حركات للعرض</p>\n";
        }
    }
    
    echo "<h3>6. اختبار العملات المتوفرة في النظام</h3>\n";
    
    $currenciesResult = $conn->query("SELECT code, name, symbol, country, is_active FROM currencies WHERE is_active = 1 ORDER BY name LIMIT 20");
    
    if ($currenciesResult) {
        echo "<p><strong>عينة من العملات المتوفرة:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>الرمز</th><th>اسم العملة</th><th>الرمز المختصر</th><th>البلد</th>\n";
        echo "</tr>\n";
        
        while ($row = $currenciesResult->fetch_assoc()) {
            echo "<tr>\n";
            echo "<td><strong>{$row['code']}</strong></td>\n";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>\n";
            echo "<td>{$row['symbol']}</td>\n";
            echo "<td>" . htmlspecialchars($row['country']) . "</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>7. تنظيف البيانات التجريبية</h3>\n";
    
    // Clean up test data
    if (!empty($createdBoxes)) {
        foreach ($createdBoxes as $boxInfo) {
            // Delete movements first
            $conn->query("DELETE FROM cash_movements WHERE cash_box_id = {$boxInfo['id']}");
            
            // Delete cash box
            $conn->query("DELETE FROM cash_boxes WHERE id = {$boxInfo['id']}");
            
            echo "<p style='color: green;'>✓ تم حذف الصندوق التجريبي {$boxInfo['currency']}</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>🎉 انتهى الاختبار الشامل لنظام العملات!</h3>\n";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ النتيجة النهائية: نظام العملات يعمل بكفاءة!</h4>\n";
    echo "<p style='margin: 0; color: #155724;'>جميع الوظائف تعمل بشكل صحيح:</p>\n";
    echo "<ul style='color: #155724;'>\n";
    echo "<li>✅ إدخال وإدارة العملات</li>\n";
    echo "<li>✅ إنشاء صناديق بعملات مختلفة</li>\n";
    echo "<li>✅ إضافة حركات مالية بالعملات الصحيحة</li>\n";
    echo "<li>✅ عرض معلومات العملات في الجداول</li>\n";
    echo "<li>✅ حساب الأرصدة بالعملات المناسبة</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0; border-radius: 5px;'>\n";
    echo "<h4>🔗 روابط مفيدة</h4>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank' style='color: #007bff;'>📊 إدارة الصناديق والحسابات</a></li>\n";
    echo "<li><a href='view_all_currencies.php' target='_blank' style='color: #007bff;'>💰 عرض جميع العملات</a></li>\n";
    echo "<li><a href='manage_currencies.php' target='_blank' style='color: #007bff;'>⚙️ إدارة العملات</a></li>\n";
    echo "<li><a href='../dashboard/exchanges.php' target='_blank' style='color: #007bff;'>💱 عمليات الصرافة</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
h2, h3, h4 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
th, td { padding: 8px; text-align: right; border: 1px solid #dee2e6; }
th { background: #e9ecef; font-weight: bold; }
tr:nth-child(even) { background: #f8f9fa; }
tr:hover { background: #e3f2fd; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

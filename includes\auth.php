<?php
/**
 * Authentication & session management helper.
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/functions.php';

class Auth
{
    /** @var mysqli */
    private $conn;

    public function __construct()
    {
        // Ensure session is started
        if (session_status() === PHP_SESSION_NONE) {
            // Extend session lifetime (must be set before session_start)
            ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);
            session_start();
        }

        $this->conn = Database::getConnection();
    }

    /**
     * Check if current session indicates a logged-in user.
     *
     * @return bool
     */
    public function checkSession(): bool
    {
        return isset($_SESSION['user_id']) && isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }

    /**
     * Require login – redirects to login page if not authenticated.
     *
     * @return void
     */
    public function requireLogin(): void
    {
        if (!$this->checkSession()) {
            $this->logoutUser(false); // destroy session (if any) without logging
            redirect(BASE_URL . 'auth/login.php');
        }
    }

    /**
     * Attempt to log in a user.
     *
     * @param string $username
     * @param string $password
     * @return bool True on success, false otherwise (error message handled by caller)
     */
    public function loginUser(string $username, string $password): bool
    {
        // Sanitize username only – password handled by verify.
        $usernameSanitized = sanitize_input($username);

        $stmt = $this->conn->prepare('SELECT id, username, password, status, failed_attempts, role_id, full_name, branch_id, profile_image FROM users WHERE username = ? LIMIT 1');
        $stmt->bind_param('s', $usernameSanitized);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows === 1) {
            $stmt->bind_result($id, $uname, $hash, $status, $failedAttempts, $roleId, $fullName, $branchId, $profileImage);
            $stmt->fetch();

            // Check account status first
            if ($status !== 'active') {
                log_activity($id, 'Login Failed – Account not active');
                $stmt->close();
                return false;
            }

            // Verify password
            if (password_verify($password, $hash)) {
                // Successful login
                $_SESSION['user_id']   = $id;
                $_SESSION['username']  = $uname;
                $_SESSION['role_id']   = $roleId;
                $_SESSION['full_name'] = $fullName;
                $_SESSION['branch_id'] = $branchId;
                $_SESSION['profile_image'] = $profileImage;
                $_SESSION['logged_in'] = true;

                // Load permissions into session for quick access
                $_SESSION['user_permissions'] = $this->loadPermissionsForRole((int) $roleId);

                // Reset failed attempts and update last_login
                $upd = $this->conn->prepare('UPDATE users SET failed_attempts = 0, last_login = NOW() WHERE id = ?');
                $upd->bind_param('i', $id);
                $upd->execute();
                $upd->close();

                log_activity($id, 'Login Success');

                $stmt->close();

                // Redirect to dashboard
                redirect(BASE_URL . 'dashboard/index.php');
                return true; // Not reached due to redirect but good practice
            }

            // Password incorrect – increment failed attempts
            $failedAttempts++;
            $lockAccount = $failedAttempts >= MAX_LOGIN_ATTEMPTS;

            $statusUpdate = $lockAccount ? 'locked' : $status;

            $upd = $this->conn->prepare('UPDATE users SET failed_attempts = ?, status = ? WHERE id = ?');
            $upd->bind_param('isi', $failedAttempts, $statusUpdate, $id);
            $upd->execute();
            $upd->close();

            log_activity($id, 'Login Failed – Wrong Password');
        } else {
            log_activity(null, 'Login Failed – User Not Found', ['username' => $usernameSanitized]);
        }

        $stmt->close();
        return false;
    }

    /**
     * Log out user & destroy session.
     *
     * @param bool $log Whether to record logout in audit logs.
     * @return void
     */
    public function logoutUser(bool $log = true): void
    {
        if ($log && isset($_SESSION['user_id'])) {
            log_activity($_SESSION['user_id'], 'Logout');
        }

        // Destroy session completely
        $_SESSION = [];
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000, $params['path'], $params['domain'], $params['secure'], $params['httponly']);
        }
        session_destroy();
    }

    /**
     * Get currently logged-in user data (from session).
     *
     * @return array|null
     */
    public function getCurrentUser(): ?array
    {
        if (!$this->checkSession()) {
            return null;
        }
        
        // Get role name and other details from database
        $stmt = $this->conn->prepare('SELECT u.id, u.username, u.full_name, u.profile_image, u.role_id, u.branch_id, r.name as role_name FROM users u LEFT JOIN roles r ON u.role_id = r.id WHERE u.id = ?');
        $stmt->bind_param('i', $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();

        if ($user) {
            // إضافة البيانات من الجلسة إذا لم تكن موجودة
            $user['role_id'] = $user['role_id'] ?? $_SESSION['role_id'] ?? null;
            $user['branch_id'] = $user['branch_id'] ?? $_SESSION['branch_id'] ?? null;
            return $user;
        }

        // في حالة عدم وجود المستخدم في قاعدة البيانات، استخدم بيانات الجلسة
        return [
            'id'        => $_SESSION['user_id'],
            'username'  => $_SESSION['username'],
            'role_id'   => $_SESSION['role_id'] ?? null,
            'full_name' => $_SESSION['full_name'] ?? null,
            'branch_id' => $_SESSION['branch_id'] ?? null,
            'profile_image' => $_SESSION['profile_image'] ?? null,
            'role_name' => 'غير محدد'
        ];
    }

    private function loadPermissionsForRole(int $roleId): array
    {
        // System admin gets wildcard permissions
        if ($roleId === 1) {
            return ['*'];
        }

        $perms = [];
        $stmt   = $this->conn->prepare('SELECT p.name FROM role_permissions rp JOIN permissions p ON p.id = rp.permission_id WHERE rp.role_id = ?');
        $stmt->bind_param('i', $roleId);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $perms[] = $row['name'];
        }
        $stmt->close();
        return $perms;
    }

    /**
     * Ensure user permissions are stored in session.
     */
    private function ensurePermissionsLoaded(): void
    {
        if (!$this->checkSession()) {
            return;
        }
        if (!isset($_SESSION['user_permissions'])) {
            $_SESSION['user_permissions'] = $this->loadPermissionsForRole((int) $_SESSION['role_id']);
        }
    }

    /**
     * Basic permission check – admin role (ID=1) bypasses others.
     *
     * @param string $permission
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        // Simple rule: system admin role id 1 has all permissions
        if (!$this->checkSession()) {
            return false;
        }

        // Load permission list if not set
        $this->ensurePermissionsLoaded();

        // Admin bypass
        if ((int) $_SESSION['role_id'] === 1) {
            return true;
        }

        return in_array($permission, $_SESSION['user_permissions'] ?? [], true);
    }

    /**
     * Require a specific permission – redirects or shows error if not granted.
     *
     * @param string $permission
     */
    public function requirePermission(string $permission): void
    {
        $this->requireLogin();
        if (!$this->hasPermission($permission)) {
            // Simple access denied response – could redirect to error page
            echo '<div style="margin:2rem; font-family:Arial, sans-serif; color:#dc3545;"> تم رفض الوصول – لا تملك الصلاحية المطلوبة.</div>';
            exit;
        }
    }
} 
/* تنسيقات صفحة تقارير PayPal */

/* تحسينات عامة للصفحة */
.main-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding: 20px;
}

/* تحسين العنوان الرئيسي */
.main-content h1 {
    color: #2c3e50;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-content h1 i {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0070ba, #003087, #0070ba);
    background-size: 200% 100%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 2px solid #e9ecef;
    padding: 20px;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
}

.card-title i {
    color: #0070ba;
}

/* تحسين بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 112, 186, 0.05) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: scale(0);
}

.stats-card:hover::before {
    transform: scale(1);
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 112, 186, 0.2);
}

/* أيقونات الإحصائيات */
.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.stats-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.stats-card:hover .stats-icon::before {
    left: 100%;
}

.stats-icon.primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
}

.stats-icon.success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
}

.stats-icon.warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
}

.stats-icon.info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    box-shadow: 0 8px 20px rgba(23, 162, 184, 0.3);
}

/* تحسين الأرقام */
.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 10px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stats-label {
    color: #6c757d;
    font-weight: 500;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-sublabel {
    font-size: 0.8rem;
    margin-top: 5px;
}

/* تحسين الفلاتر */
.filters-card {
    background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
    border: 2px solid #e9ecef;
    border-radius: 20px;
}

.filters-card .form-control,
.filters-card .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.filters-card .form-control:focus,
.filters-card .form-select:focus {
    border-color: #0070ba;
    box-shadow: 0 0 0 0.2rem rgba(0, 112, 186, 0.25);
    transform: translateY(-2px);
}

/* تحسين الأزرار */
.btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 10px 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #0070ba 0%, #003087 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 112, 186, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 112, 186, 0.4);
}

.btn-outline-primary {
    border: 2px solid #0070ba;
    color: #0070ba;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #0070ba 0%, #003087 100%);
    transform: translateY(-2px);
}

/* تحسين الجداول */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    color: #2c3e50;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.table tbody td {
    padding: 15px;
    border: none;
    border-bottom: 1px solid #f1f3f4;
}

/* تحسين الشارات */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 600;
    font-size: 0.8rem;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

/* تحسين الرسوم البيانية */
.chart-container {
    position: relative;
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

/* تحسين قائمة العملاء */
.customer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0070ba 0%, #003087 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-left: 15px;
    box-shadow: 0 4px 15px rgba(0, 112, 186, 0.3);
}

.customer-info {
    flex: 1;
}

.customer-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.customer-email {
    color: #6c757d;
    font-size: 0.85rem;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease forwards;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

/* تحسينات للطباعة */
@media print {
    .btn, .filters-card {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
    
    .main-content {
        background: white !important;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-number {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .table-responsive {
        border-radius: 15px;
    }
}

/* تحسين حالة التحميل */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0070ba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الرسائل */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* تحسين حالة عدم وجود بيانات */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-data i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-data h5 {
    margin-bottom: 10px;
    color: #495057;
}

.no-data p {
    margin: 0;
    font-size: 0.9rem;
}

/*
 * Trust Plus Financial System - Transfers Enhanced CSS
 * تصميم محسن لصفحة الحوالات مع مظهر جميل ومنظم
 */

/* ===== PAGE LAYOUT ===== */
.main-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.page-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.75rem;
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-header {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0 !important;
    padding: 1.25rem 1.5rem;
}

/* ===== TABLE STYLING ===== */
.transfers-table {
    font-size: 0.9rem;
    border-collapse: separate;
    border-spacing: 0;
}

.transfers-table thead th {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
    color: #ffffff;
    font-weight: 700;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    padding: 1.2rem 0.8rem;
    vertical-align: middle;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.transfers-table thead th:first-child {
    border-radius: 12px 0 0 0;
}

.transfers-table thead th:last-child {
    border-radius: 0 12px 0 0;
}

.transfers-table tbody tr {
    border: none;
    transition: all 0.3s ease;
    background: #ffffff;
}

.transfers-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.transfers-table tbody tr:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

.transfers-table tbody td {
    border: none;
    padding: 1.2rem 0.8rem;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

/* ===== TRANSFER ROW ELEMENTS ===== */
.transaction-badge .badge {
    font-size: 0.85rem;
    padding: 0.6rem 1rem;
    border-radius: 25px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.country-info {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
}

.country-info i {
    color: #e74c3c;
    font-size: 1.1rem;
}

.tracking-info .badge {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    padding: 0.5rem 0.8rem;
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
    border-radius: 15px;
    font-weight: 600;
}

.beneficiary-info {
    line-height: 1.5;
}

.beneficiary-info .fw-bold {
    font-size: 0.95rem;
    margin-bottom: 0.3rem;
    color: #2c3e50;
}

.beneficiary-info small {
    color: #7f8c8d;
    font-weight: 500;
}

.amount-info {
    text-align: center;
}

.amount-info .fw-bold {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    font-weight: 700;
}

.amount-info .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.7rem;
    border-radius: 12px;
    font-weight: 600;
}

.profit-info .badge {
    font-size: 0.85rem;
    padding: 0.6rem 1rem;
    border-radius: 20px;
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
}

.status-badge .badge {
    font-size: 0.85rem;
    padding: 0.7rem 1.2rem;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.date-info {
    text-align: center;
    line-height: 1.4;
}

.date-info .fw-bold {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
    color: #2c3e50;
}

.date-info small {
    color: #7f8c8d;
    font-weight: 500;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons .btn-group {
    gap: 0.3rem;
}

.action-buttons .btn-group .btn {
    border-radius: 8px;
    padding: 0.5rem 0.7rem;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.action-buttons .btn-group .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.action-buttons .btn-group .btn:hover::before {
    left: 100%;
}

.action-buttons .btn-group .btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* أزرار العرض */
.action-buttons .btn-outline-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-color: #3498db;
    color: white;
}

.action-buttons .btn-outline-info:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
    border-color: #2980b9;
    color: white;
}

/* أزرار التعديل */
.action-buttons .btn-outline-primary {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    border-color: #9b59b6;
    color: white;
}

.action-buttons .btn-outline-primary:hover {
    background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%);
    border-color: #8e44ad;
    color: white;
}

/* أزرار التسليم */
.action-buttons .btn-outline-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    border-color: #27ae60;
    color: white;
}

.action-buttons .btn-outline-success:hover {
    background: linear-gradient(135deg, #229954 0%, #1e8449 100%);
    border-color: #229954;
    color: white;
}

/* ===== STATUS COLORS ===== */
/* حالة معلقة */
.bg-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%) !important;
    color: white !important;
}

/* حالة مقبولة */
.bg-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%) !important;
    color: white !important;
}

/* حالة مرفوضة */
.bg-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    color: white !important;
}

/* حالة مرسلة */
.bg-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
    color: white !important;
}

/* حالة مستلمة */
.bg-primary {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%) !important;
    color: white !important;
}

/* حالة ملغاة */
.bg-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%) !important;
    color: white !important;
}

/* ألوان العملات */
.bg-success.bg-opacity-10 {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1) 0%, rgba(34, 153, 84, 0.1) 100%) !important;
    color: #27ae60 !important;
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.bg-primary.bg-opacity-10 {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(142, 68, 173, 0.1) 100%) !important;
    color: #9b59b6 !important;
    border: 1px solid rgba(155, 89, 182, 0.3);
}

/* ===== EMPTY STATE ===== */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    opacity: 0.5;
    color: #bdc3c7;
}

.empty-state h5 {
    margin-top: 1rem;
    font-weight: 600;
    color: #7f8c8d;
}

.empty-state p {
    color: #95a5a6;
}

.empty-state .btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
}

.empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

/* ===== SPECIAL EFFECTS ===== */
.transfers-table tbody tr {
    position: relative;
}

.transfers-table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3498db 0%, #9b59b6 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.transfers-table tbody tr:hover::before {
    opacity: 1;
}

/* تأثيرات خاصة للشارات */
.badge {
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.badge:hover::before {
    left: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.5rem;
    }

    .card-header {
        padding: 1rem;
    }

    .transfers-table {
        font-size: 0.8rem;
    }

    .transfers-table thead th {
        padding: 0.8rem 0.4rem;
        font-size: 0.7rem;
    }

    .transfers-table tbody td {
        padding: 0.8rem 0.4rem;
    }

    .action-buttons .btn-group .btn {
        padding: 0.4rem 0.5rem;
        font-size: 0.75rem;
        margin: 0.1rem;
    }

    .transaction-badge .badge,
    .status-badge .badge,
    .profit-info .badge {
        font-size: 0.7rem;
        padding: 0.4rem 0.6rem;
    }

    .amount-info .fw-bold {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .transfers-table thead th {
        padding: 0.6rem 0.3rem;
        font-size: 0.65rem;
    }

    .transfers-table tbody td {
        padding: 0.6rem 0.3rem;
    }

    .action-buttons .btn-group {
        flex-direction: column;
        gap: 0.2rem;
    }

    .action-buttons .btn-group .btn {
        width: 100%;
        padding: 0.3rem 0.4rem;
        font-size: 0.7rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

.transfer-row {
    animation: slideInRight 0.4s ease-out;
}

.transfer-row:nth-child(even) {
    animation-delay: 0.1s;
}

.transfer-row:nth-child(odd) {
    animation-delay: 0.05s;
}

/* تأثير النبض للأزرار المهمة */
.action-buttons .btn-outline-info:hover {
    animation: pulse 2s infinite;
}

/* تأثير التألق للشارات */
.badge:hover {
    background-image: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* ===== LOADING STATES ===== */
.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثير التحميل للجدول */
.transfers-table.loading {
    position: relative;
    overflow: hidden;
}

.transfers-table.loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 2s infinite;
    z-index: 1;
}

/* ===== SCROLL EFFECTS ===== */
.table-responsive {
    position: relative;
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3498db 0%, #9b59b6 100%);
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2980b9 0%, #8e44ad 100%);
}

/* ===== STATS GRID LAYOUT ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Responsive grid adjustments */
@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1400px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ===== ENHANCED CUSTOMER SEARCH DROPDOWN ===== */
.customer-search-dropdown {
  position: relative;
  width: 100%;
}

.customer-search-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--bg-color);
}

.customer-search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.search-results-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  display: none;
}

.search-results-list.show {
  display: block;
}

.search-result-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.2s ease;
  direction: rtl;
  text-align: right;
}

.search-result-item:hover {
  background-color: var(--bg-light);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item.selected {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-left: 3px solid var(--primary);
}

.customer-name {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.customer-details {
  font-size: 0.875rem;
  color: var(--text-muted);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.customer-id {
  font-family: monospace;
  background: var(--bg-light);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.customer-status {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* ===== KYC STATUS BADGES ===== */
.badge-kyc-verified {
  background: var(--success);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.badge-kyc-pending {
  background: var(--warning);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.badge-kyc-rejected {
  background: var(--danger);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.badge-kyc-not-submitted {
  background: var(--secondary);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* ===== RISK LEVEL BADGES ===== */
.badge-risk-low {
  background: var(--success);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.badge-risk-medium {
  background: var(--warning);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.badge-risk-high {
  background: var(--danger);
  color: white;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

/* ===== SEARCH LOADING INDICATORS ===== */
.search-loading {
  padding: 16px;
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
}

.search-loading .loading-spinner {
  margin-left: 8px;
}

.search-no-results {
  padding: 16px;
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
}

.search-error {
  padding: 16px;
  text-align: center;
  color: var(--danger);
  background-color: rgba(var(--danger-rgb), 0.1);
  border-radius: 4px;
  margin: 8px;
}

/* ===== TRANSFER FORM SECTIONS ===== */
.transfer-form-section {
  background: var(--bg-color);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.transfer-form-section h5 {
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-light);
  display: flex;
  align-items: center;
}

.transfer-form-section h5 i {
  margin-left: 8px;
  color: var(--primary);
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row .form-group {
  flex: 1;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* ===== SIDEBAR SECTION (if used as visual section) ===== */
.sidebar-section {
  background: var(--bg-light);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.sidebar-section h6 {
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sidebar-section .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-light);
}

.sidebar-section .info-item:last-child {
  border-bottom: none;
}

.sidebar-section .info-label {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.sidebar-section .info-value {
  font-weight: 600;
  color: var(--text-color);
}

/* ===== TRANSFER SUMMARY CARD ===== */
.transfer-summary {
  background: var(--primary-gradient);
  color: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.3);
}

.transfer-summary h5 {
  margin-bottom: 1rem;
  font-weight: 600;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.summary-item:last-child {
  border-bottom: none;
  font-weight: 600;
  font-size: 1.1rem;
  margin-top: 0.5rem;
  padding-top: 1rem;
  border-top: 2px solid rgba(255, 255, 255, 0.3);
}

/* ===== CURRENCY CONVERTER WIDGET ===== */
.currency-converter {
  background: var(--bg-color);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.currency-converter h6 {
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

.converter-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.converter-input {
  flex: 2;
}

.converter-currency {
  flex: 1;
}

.converter-arrow {
  font-size: 1.5rem;
  color: var(--primary);
  text-align: center;
  flex: 0 0 auto;
}

.exchange-rate-display {
  background: var(--bg-light);
  padding: 0.75rem;
  border-radius: 8px;
  text-align: center;
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 1rem;
}

.exchange-rate-value {
  font-weight: 600;
  color: var(--primary);
}

/* ===== RESPONSIVE ADJUSTMENTS FOR TRANSFERS PAGE ===== */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .transfer-form-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .converter-row {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .converter-arrow {
    transform: rotate(90deg);
  }
  
  .search-results-list {
    max-height: 200px;
  }
}

/* ===== ANIMATION ENHANCEMENTS ===== */
.search-result-item {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.transfer-form-section {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== FOCUS STATES AND ACCESSIBILITY ===== */
.search-result-item:focus {
  outline: 2px solid var(--primary);
  outline-offset: -2px;
  background-color: rgba(var(--primary-rgb), 0.1);
}

.customer-search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .badge-kyc-verified,
  .badge-kyc-pending,
  .badge-kyc-rejected,
  .badge-risk-low,
  .badge-risk-medium,
  .badge-risk-high {
    border: 2px solid currentColor;
  }
}

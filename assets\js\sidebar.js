/**
 * Sidebar JavaScript
 * Handles the collapsible sections and other interactive elements in the sidebar
 */

class Sidebar {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.toggleBtn = document.getElementById('sidebar-toggle-btn');
        this.collapsibleHeaders = document.querySelectorAll('.sidebar-section-header.collapsible');
        
        this.init();
    }

    /**
     * Initialize the sidebar functionality
     */
    init() {
        // Initialize collapsible sections
        this.initCollapsibleSections();
        
        // Initialize sidebar toggle for mobile
        if (this.toggleBtn) {
            this.initSidebarToggle();
        }
        
        // Add window resize listener
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    /**
     * Initialize collapsible sections
     */
    initCollapsibleSections() {
        this.collapsibleHeaders.forEach(header => {
            const targetId = header.getAttribute('data-target');
            const targetSection = document.getElementById(targetId);
            
            if (!targetSection) return;
            
            // Check if section should be collapsed by default
            const isActivePage = targetSection.querySelector('.sidebar-sub-link.active');
            const savedState = localStorage.getItem(`sidebar-section-${targetId}`);
            
            // If there's an active page in this section, expand it by default
            // Otherwise, use saved state or collapse by default
            const shouldCollapse = savedState ? savedState === 'collapsed' : !isActivePage;
            
            if (shouldCollapse) {
                header.classList.add('collapsed');
                targetSection.classList.add('collapsed');
            }
            
            // Add click event listener
            header.addEventListener('click', (e) => this.toggleSection(e, header, targetId, targetSection));
        });
    }

    /**
     * Toggle a collapsible section
     * @param {Event} e - The click event
     * @param {HTMLElement} header - The header element that was clicked
     * @param {string} targetId - The ID of the target section
     * @param {HTMLElement} targetSection - The target section element
     */
    toggleSection(e, header, targetId, targetSection) {
        e.preventDefault();
        e.stopPropagation();
        
        const isCollapsed = targetSection.classList.contains('collapsed');
        
        if (isCollapsed) {
            // Expand section
            header.classList.remove('collapsed');
            targetSection.classList.remove('collapsed');
            localStorage.setItem(`sidebar-section-${targetId}`, 'expanded');
        } else {
            // Collapse section
            header.classList.add('collapsed');
            targetSection.classList.add('collapsed');
            localStorage.setItem(`sidebar-section-${targetId}`, 'collapsed');
        }
    }

    /**
     * Initialize sidebar toggle for mobile
     */
    initSidebarToggle() {
        this.toggleBtn.addEventListener('click', () => {
            this.sidebar.classList.toggle('active');
            document.body.classList.toggle('sidebar-open');
            
            // Toggle aria-expanded attribute for accessibility
            const isExpanded = this.toggleBtn.getAttribute('aria-expanded') === 'true' || false;
            this.toggleBtn.setAttribute('aria-expanded', !isExpanded);
            
            // Save sidebar state for page reload
            localStorage.setItem('sidebar-state', this.sidebar.classList.contains('active') ? 'expanded' : 'collapsed');
        });
        
        // Close sidebar when clicking outside
        document.addEventListener('click', (e) => {
            if (this.sidebar.classList.contains('active') && 
                !this.sidebar.contains(e.target) && 
                e.target !== this.toggleBtn && 
                !this.toggleBtn.contains(e.target)) {
                this.sidebar.classList.remove('active');
                document.body.classList.remove('sidebar-open');
                this.toggleBtn.setAttribute('aria-expanded', 'false');
                localStorage.setItem('sidebar-state', 'collapsed');
            }
        });
    }

    /**
     * Handle window resize events
     */
    handleResize() {
        // Close sidebar on mobile when resizing to desktop
        if (window.innerWidth >= 992) {
            this.sidebar.classList.remove('active');
            document.body.classList.remove('sidebar-open');
            if (this.toggleBtn) {
                this.toggleBtn.setAttribute('aria-expanded', 'false');
            }
        }
    }
}

// Initialize the sidebar when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if the sidebar exists on the page
    if (document.getElementById('sidebar')) {
        // eslint-disable-next-line no-unused-vars
        const sidebar = new Sidebar();
    }
});

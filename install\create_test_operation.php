<?php
/**
 * Create Test Operation
 * This script creates a test operation of type "لكم" (is_credit = 0)
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/office_manager.php';

echo "<h2>إنشاء عملية اختبار من نوع \"لكم\"</h2>";

try {
    $db = Database::getConnection();
    $officeMgr = new OfficeManager(new Database());
    
    // Get the first office
    $offices = $officeMgr->getAllOffices();
    if (empty($offices)) {
        throw new Exception("لا توجد مكاتب في النظام");
    }
    $office = $offices[0];
    
    // Get the first exchange rate
    $exchangeRates = $officeMgr->getOfficeExchangeRates($office['id']);
    if (empty($exchangeRates)) {
        throw new Exception("لا توجد أسعار قص للمكتب");
    }
    $exchangeRate = $exchangeRates[0];
    
    // Create test operation data
    $baseAmount = 1000;
    $fee = $baseAmount * $exchangeRate['exchange_rate_percentage'];
    $finalAmount = $baseAmount - $fee;
    
    $operationData = [
        'office_id' => $office['id'],
        'operation_name' => $exchangeRate['operation_name'],
        'base_amount' => $baseAmount,
        'amount' => $finalAmount,
        'is_credit' => 0 // "لكم"
    ];
    
    // Add the operation
    $newOperationId = $officeMgr->addOperation($operationData);
    if (!$newOperationId) {
        throw new Exception("فشل في إنشاء العملية");
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ تم إنشاء عملية اختبار بنجاح</h3>";
    echo "<p style='color: #155724; margin: 0;'>تم إنشاء عملية اختبار من نوع \"لكم\" (is_credit = 0) برقم $newOperationId</p>";
    echo "<p style='color: #155724; margin: 0;'>المكتب: " . htmlspecialchars($office['office_name']) . "</p>";
    echo "<p style='color: #155724; margin: 0;'>العملية: " . htmlspecialchars($exchangeRate['operation_name']) . "</p>";
    echo "<p style='color: #155724; margin: 0;'>المبلغ الأساسي: " . number_format($baseAmount, 2) . "</p>";
    echo "<p style='color: #155724; margin: 0;'>رسوم القص: " . number_format($fee, 2) . " (" . $exchangeRate['exchange_rate_percentage'] . "%)</p>";
    echo "<p style='color: #155724; margin: 0;'>المبلغ النهائي: " . number_format($finalAmount, 2) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/office_details.php?id=" . $office['id'] . "' style='color: #007bff; text-decoration: none;'>→ عرض تفاصيل المكتب</a></p>";
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ خطأ في إنشاء عملية اختبار</h3>";
    echo "<p style='color: #721c24; margin: 0;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<p><a href='../dashboard/' style='color: #007bff; text-decoration: none;'>← العودة للوحة التحكم</a></p>";
}
?> 
<?php
require_once __DIR__ . '/database.php';

class UserManager
{
    /** @var mysqli */
    private $db;

    public function __construct(Database $database)
    {
        // Obtain mysqli connection from Database helper
        $this->db = $database::getConnection();
    }

    /**
     * Fetch all users with their role and branch names.
     *
     * @return array<int, array<string, mixed>>
     */
    public function getAllUsers(): array
    {
        $sql = "SELECT u.id, u.username, u.full_name, u.email, u.phone, u.profile_image,
                       r.name AS role_name, b.name AS branch_name, 
                       u.status, u.last_login, u.created_at
                FROM users u
                LEFT JOIN roles r   ON u.role_id   = r.id
                LEFT JOIN branches b ON u.branch_id = b.id
                ORDER BY u.id ASC";

        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return [];
        }
        $stmt->execute();
        $result = $stmt->get_result();
        $users  = $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
        $stmt->close();
        return $users;
    }

    /**
     * Fetch a single user by ID with role and branch names.
     */
    public function getUserById(int $userId): ?array
    {
        $sql = "SELECT u.*, r.name AS role_name, b.name AS branch_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.id
                LEFT JOIN branches b ON u.branch_id = b.id
                WHERE u.id = ? LIMIT 1";
        $stmt = $this->db->prepare($sql);
        if (!$stmt) {
            return null;
        }
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user   = $result->fetch_assoc() ?: null;
        $stmt->close();
        return $user;
    }

    /**
     * Check if a username already exists (optionally excluding a user ID).
     */
    public function userExists(string $username, int $excludeId = null): bool
    {
        $sql = 'SELECT COUNT(*) FROM users WHERE username = ?';
        if ($excludeId !== null) {
            $sql .= ' AND id <> ?';
        }
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        if ($excludeId !== null) {
            $stmt->bind_param('si', $username, $excludeId);
        } else {
            $stmt->bind_param('s', $username);
        }
        $stmt->execute();
        $stmt->bind_result($cnt);
        $stmt->fetch();
        $stmt->close();
        return $cnt > 0;
    }

    /**
     * Add a new user – returns inserted ID or false.
     */
    public function addUser(array $data)
    {
        $sql = 'INSERT INTO users (username, password, full_name, email, phone, profile_image, role_id, branch_id, status, created_at) VALUES (?,?,?,?,?,?,?,?,?, NOW())';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;

        $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
        $profileImage = $data['profile_image'] ?? null;
        
        $stmt->bind_param(
            'ssssssiis',
            $data['username'],
            $passwordHash,
            $data['full_name'],
            $data['email'],
            $data['phone'],
            $profileImage,
            $data['role_id'],
            $data['branch_id'],
            $data['status']
        );
        if ($stmt->execute()) {
            $newId = $stmt->insert_id;
            $stmt->close();
            return $newId;
        }
        $stmt->close();
        return false;
    }

    /**
     * Update existing user record.
     */
    public function updateUser(int $userId, array $data): bool
    {
        if (empty($data)) {
            return false;
        }
        $fields = [];
        $params = [];
        $types  = '';

        foreach ($data as $key => $value) {
            if ($key === 'password') {
                // Hash new password
                if ($value === '') {
                    continue; // skip empty passwords
                }
                $value = password_hash($value, PASSWORD_DEFAULT);
            }
            $fields[] = "$key = ?";
            $params[] = $value;
            $types   .= is_int($value) ? 'i' : 's';
        }
        if (empty($fields)) return false;

        $sql = 'UPDATE users SET ' . implode(', ', $fields) . ' WHERE id = ?';
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        $types .= 'i';
        $params[] = $userId;
        $stmt->bind_param($types, ...$params);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Delete user profile image.
     */
    public function deleteProfileImage(int $userId): bool
    {
        $stmt = $this->db->prepare('UPDATE users SET profile_image = NULL WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $userId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Get user profile image path.
     */
    public function getProfileImagePath(int $userId): ?string
    {
        $stmt = $this->db->prepare('SELECT profile_image FROM users WHERE id = ?');
        if (!$stmt) return null;
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();
        return $row ? $row['profile_image'] : null;
    }

    /**
     * Delete user by ID.
     */
    public function deleteUser(int $userId): bool
    {
        $stmt = $this->db->prepare('DELETE FROM users WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('i', $userId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Update user status (active/inactive/blocked).
     */
    public function updateUserStatus(int $userId, string $status): bool
    {
        $stmt = $this->db->prepare('UPDATE users SET status = ? WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('si', $status, $userId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }

    /**
     * Reset user password (expects already hashed password).
     */
    public function resetUserPassword(int $userId, string $hashedPassword): bool
    {
        $stmt = $this->db->prepare('UPDATE users SET password = ? WHERE id = ?');
        if (!$stmt) return false;
        $stmt->bind_param('si', $hashedPassword, $userId);
        $ok = $stmt->execute();
        $stmt->close();
        return $ok;
    }
} 
<?php
/**
 * Test edit_movement.php functionality
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';

echo "<h2>اختبار وظائف تعديل الحركات المالية</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    
    echo "<h3>1. فحص الدوال المطلوبة</h3>\n";
    
    // Check if required methods exist
    $requiredMethods = [
        'getCashMovementById',
        'getCashBoxById', 
        'updateCashMovement',
        'getMovementByReference'
    ];
    
    foreach ($requiredMethods as $method) {
        if (method_exists($cashManager, $method)) {
            echo "<p style='color: green;'>✓ دالة $method موجودة</p>\n";
        } else {
            echo "<p style='color: red;'>✗ دالة $method مفقودة</p>\n";
        }
    }
    
    echo "<h3>2. فحص البيانات المتاحة</h3>\n";
    
    // Check for cash movements
    $conn = Database::getConnection();
    $result = $conn->query("SELECT COUNT(*) as count FROM cash_movements");
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<p><strong>عدد الحركات المالية:</strong> $count</p>\n";
        
        if ($count > 0) {
            // Get a sample movement
            $result = $conn->query("SELECT id, cash_box_id, movement_type, amount, description FROM cash_movements ORDER BY id DESC LIMIT 1");
            if ($result) {
                $movement = $result->fetch_assoc();
                echo "<p style='color: green;'>✓ حركة مالية متاحة للاختبار:</p>\n";
                echo "<ul>\n";
                echo "<li>رقم الحركة: {$movement['id']}</li>\n";
                echo "<li>نوع الحركة: {$movement['movement_type']}</li>\n";
                echo "<li>المبلغ: {$movement['amount']}</li>\n";
                echo "<li>الوصف: " . ($movement['description'] ?: 'غير محدد') . "</li>\n";
                echo "</ul>\n";
                
                echo "<h3>3. اختبار دالة getCashMovementById</h3>\n";
                
                $movementData = $cashManager->getCashMovementById($movement['id']);
                if ($movementData) {
                    echo "<p style='color: green;'>✓ تم جلب بيانات الحركة بنجاح</p>\n";
                    echo "<p>البيانات المسترجعة:</p>\n";
                    echo "<ul>\n";
                    echo "<li>رقم الحركة: {$movementData['id']}</li>\n";
                    echo "<li>اسم الصندوق: " . ($movementData['cash_box_name'] ?? 'غير محدد') . "</li>\n";
                    echo "<li>اسم المستخدم: " . ($movementData['user_name'] ?? 'غير محدد') . "</li>\n";
                    echo "<li>رمز العملة: " . ($movementData['currency_code'] ?? 'غير محدد') . "</li>\n";
                    echo "</ul>\n";
                } else {
                    echo "<p style='color: red;'>✗ فشل في جلب بيانات الحركة</p>\n";
                }
                
                echo "<h3>4. اختبار دالة getCashBoxById</h3>\n";
                
                $cashBoxData = $cashManager->getCashBoxById($movement['cash_box_id']);
                if ($cashBoxData) {
                    echo "<p style='color: green;'>✓ تم جلب بيانات الصندوق بنجاح</p>\n";
                    echo "<p>البيانات المسترجعة:</p>\n";
                    echo "<ul>\n";
                    echo "<li>اسم الصندوق: {$cashBoxData['name']}</li>\n";
                    echo "<li>الرصيد الحالي: {$cashBoxData['current_balance']}</li>\n";
                    echo "<li>العملة: " . ($cashBoxData['currency_code'] ?? 'غير محدد') . "</li>\n";
                    echo "<li>الفرع: " . ($cashBoxData['branch_name'] ?? 'غير محدد') . "</li>\n";
                    echo "</ul>\n";
                } else {
                    echo "<p style='color: red;'>✗ فشل في جلب بيانات الصندوق</p>\n";
                }
                
                echo "<h3>5. اختبار دالة getMovementByReference</h3>\n";
                
                // Test with a non-existent reference
                $testRef = 'TEST_REF_' . time();
                $refMovement = $cashManager->getMovementByReference($testRef);
                if ($refMovement === null) {
                    echo "<p style='color: green;'>✓ دالة getMovementByReference تعمل بشكل صحيح (لا توجد حركة بالمرجع المختبر)</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠ دالة getMovementByReference أرجعت نتيجة غير متوقعة</p>\n";
                }
                
                echo "<h3>6. اختبار رابط تعديل الحركة</h3>\n";
                
                $editUrl = "../dashboard/edit_movement.php?id=" . $movement['id'];
                echo "<p><a href='$editUrl' target='_blank' style='color: #007bff; font-weight: bold;'>اختبر تعديل الحركة رقم {$movement['id']}</a></p>\n";
                
            }
        } else {
            echo "<p style='color: orange;'>⚠ لا توجد حركات مالية للاختبار</p>\n";
            echo "<p>يمكنك إضافة حركة مالية من <a href='../dashboard/cash.php' target='_blank'>صفحة الصناديق</a></p>\n";
        }
    }
    
    echo "<h3>7. فحص الصلاحيات المطلوبة</h3>\n";
    
    // Check if auth functions exist
    if (function_exists('verify_csrf_token')) {
        echo "<p style='color: green;'>✓ دالة verify_csrf_token موجودة</p>\n";
    } else {
        echo "<p style='color: red;'>✗ دالة verify_csrf_token مفقودة</p>\n";
    }
    
    if (function_exists('generate_csrf_token')) {
        echo "<p style='color: green;'>✓ دالة generate_csrf_token موجودة</p>\n";
    } else {
        echo "<p style='color: red;'>✗ دالة generate_csrf_token مفقودة</p>\n";
    }
    
    if (function_exists('sanitize_input')) {
        echo "<p style='color: green;'>✓ دالة sanitize_input موجودة</p>\n";
    } else {
        echo "<p style='color: red;'>✗ دالة sanitize_input مفقودة</p>\n";
    }
    
    if (function_exists('log_activity')) {
        echo "<p style='color: green;'>✓ دالة log_activity موجودة</p>\n";
    } else {
        echo "<p style='color: red;'>✗ دالة log_activity مفقودة</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى اختبار وظائف تعديل الحركات المالية</h3>\n";
    echo "<p><strong>النتيجة:</strong> النظام جاهز لتعديل الحركات المالية</p>\n";
    
    if (isset($movement)) {
        echo "<p><a href='../dashboard/edit_movement.php?id={$movement['id']}' target='_blank' style='color: #007bff; font-weight: bold;'>اختبر تعديل حركة مالية الآن</a></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ في الاختبار: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>

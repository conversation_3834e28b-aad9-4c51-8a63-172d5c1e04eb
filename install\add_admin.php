<?php
/**
 * Seed script: Adds a default Administrator role, main branch, and admin user.
 *
 * Usage (from project root):
 *   php install/add_admin.php
 */

require_once __DIR__ . '/../includes/database.php';

$conn = Database::getConnection();

// Ensure Branch
$branchName = 'المركز الرئيسي';
$branchCode = 'MAIN';
$branchStatus = 'active';

$stmt = $conn->prepare('SELECT id FROM branches WHERE code = ? LIMIT 1');
$stmt->bind_param('s', $branchCode);
$stmt->execute();
$stmt->bind_result($branchId);
if (!$stmt->fetch()) {
    // Insert branch
    $stmt->close();
    $stmtIns = $conn->prepare('INSERT INTO branches (name, code, status) VALUES (?, ?, ?)');
    $stmtIns->bind_param('sss', $branchName, $branchCode, $branchStatus);
    if ($stmtIns->execute()) {
        $branchId = $stmtIns->insert_id;
        echo "Branch created with ID {$branchId}.\n";
    } else {
        die('Failed to create branch: ' . $stmtIns->error);
    }
    $stmtIns->close();
} else {
    echo "Branch already exists with ID {$branchId}.\n";
}
$stmt->close();

// Ensure Role
$roleName = 'Administrator';
$roleDesc = 'System Administrator';
$roleStatus = 'active';

$stmt = $conn->prepare('SELECT id FROM roles WHERE name = ? LIMIT 1');
$stmt->bind_param('s', $roleName);
$stmt->execute();
$stmt->bind_result($roleId);
if (!$stmt->fetch()) {
    $stmt->close();
    $stmtIns = $conn->prepare('INSERT INTO roles (name, description, status) VALUES (?, ?, ?)');
    $stmtIns->bind_param('sss', $roleName, $roleDesc, $roleStatus);
    if ($stmtIns->execute()) {
        $roleId = $stmtIns->insert_id;
        echo "Role created with ID {$roleId}.\n";
    } else {
        die('Failed to create role: ' . $stmtIns->error);
    }
    $stmtIns->close();
} else {
    echo "Role already exists with ID {$roleId}.\n";
    $stmt->close();
}

// Ensure Admin User
$username = 'admin';
$passwordPlain = 'admin123';
$passwordHash = password_hash($passwordPlain, PASSWORD_BCRYPT);
$fullName = 'مدير النظام';
$email = '<EMAIL>';
$phone = '0000000000';
$status = 'active';

$stmt = $conn->prepare('SELECT id FROM users WHERE username = ? LIMIT 1');
$stmt->bind_param('s', $username);
$stmt->execute();
$stmt->bind_result($userId);
if (!$stmt->fetch()) {
    $stmt->close();
    $stmtIns = $conn->prepare('INSERT INTO users (username, password, full_name, email, phone, role_id, branch_id, status) VALUES (?,?,?,?,?,?,?,?)');
    $stmtIns->bind_param('sssssiss', $username, $passwordHash, $fullName, $email, $phone, $roleId, $branchId, $status);

    if ($stmtIns->execute()) {
        $userId = $stmtIns->insert_id;
        echo "Admin user created with ID {$userId}.\n";
        echo "\n\033[32mUsername:\033[0m {$username}\n\033[32mPassword:\033[0m {$passwordPlain}\n";
    } else {
        die('Failed to create admin user: ' . $stmtIns->error);
    }
    $stmtIns->close();
} else {
    echo "Admin user already exists with ID {$userId}.\n";
    $stmt->close();
}

$conn->close(); 
<?php
/**
 * Enhanced Transfer Manager with Comprehensive Activity Logging
 * مدير الحوالات المحسن مع تسجيل شامل للأنشطة
 */

require_once __DIR__ . '/transfer_manager.php';

class EnhancedTransferManager extends TransferManager
{
    /** @var mysqli */
    protected $db;

    /**
     * Constructor
     */
    public function __construct(Database $database)
    {
        parent::__construct($database);
        $this->db = Database::getConnection();
    }

    /**
     * Log transfer activity with comprehensive details
     *
     * @param int $transferId Transfer ID
     * @param string $activityType Type of activity
     * @param string $description Activity description
     * @param mixed $oldValue Old value (will be JSON encoded)
     * @param mixed $newValue New value (will be JSO<PERSON> encoded)
     * @param int|null $userId User ID performing the action
     * @param array $additionalData Additional context data
     * @return bool Success status
     */
    public function logActivity(
        int $transferId, 
        string $activityType, 
        string $description, 
        $oldValue = null, 
        $newValue = null, 
        ?int $userId = null,
        array $additionalData = []
    ): bool {
        try {
            // Get user info if not provided
            if ($userId === null && isset($_SESSION['user_id'])) {
                $userId = $_SESSION['user_id'];
            }
            
            // Get client information
            $ipAddress = $this->getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            $location = $additionalData['location'] ?? null;
            
            // Prepare JSON values
            $oldValueJson = $oldValue ? json_encode($oldValue, JSON_UNESCAPED_UNICODE) : null;
            $newValueJson = $newValue ? json_encode($newValue, JSON_UNESCAPED_UNICODE) : null;
            
            $sql = "INSERT INTO transfer_activity_log (
                        transfer_id, activity_type, description, old_value, new_value,
                        user_id, ip_address, user_agent, location, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->db->prepare($sql);
            if (!$stmt) return false;
            
            $stmt->bind_param('issssisss', 
                $transferId, $activityType, $description, 
                $oldValueJson, $newValueJson, $userId, 
                $ipAddress, $userAgent, $location
            );
            
            $result = $stmt->execute();
            $stmt->close();
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Failed to log transfer activity: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Enhanced status update with comprehensive logging
     *
     * @param int $transferId Transfer ID
     * @param string $newStatus New status
     * @param string $notes Optional notes
     * @param int|null $userId User performing the action
     * @param array $additionalData Additional context
     * @return array Result with success status and details
     */
    public function updateStatusWithLogging(
        int $transferId, 
        string $newStatus, 
        string $notes = '', 
        ?int $userId = null,
        array $additionalData = []
    ): array {
        try {
            $this->db->begin_transaction();
            
            // Get current transfer data
            $currentTransfer = $this->getTransferById($transferId);
            if (!$currentTransfer) {
                $this->db->rollback();
                return ['success' => false, 'error' => 'Transfer not found'];
            }
            
            $oldStatus = $currentTransfer['status'];
            
            // Update the status using parent method
            $result = $this->updateTransferStatusSimple($transferId, [
                'status' => $newStatus,
                'notes' => $notes
            ]);
            
            if (!$result) {
                $this->db->rollback();
                return ['success' => false, 'error' => 'Failed to update status'];
            }
            
            // Log the status change - تسجيل مبسط
            $this->logActivity(
                $transferId,
                'status_changed',
                "تم تغيير الحالة إلى '$newStatus'",
                null, // لا نحفظ القيمة القديمة
                ['status' => $newStatus], // فقط الحالة الجديدة
                $userId,
                $additionalData
            );
            
            // Log in traditional status history table as well
            $this->logStatusHistory($transferId, $oldStatus, $newStatus, $userId, $notes, $additionalData['location'] ?? null);
            
            $this->db->commit();
            
            return [
                'success' => true, 
                'old_status' => $oldStatus, 
                'new_status' => $newStatus,
                'message' => 'تم تحديث الحالة وتسجيل النشاط بنجاح'
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Update delivery status with logging
     *
     * @param int $transferId Transfer ID
     * @param string $deliveryStatus New delivery status
     * @param array $deliveryData Delivery details
     * @param int|null $userId User performing the action
     * @return array Result with success status
     */
    public function updateDeliveryStatus(
        int $transferId, 
        string $deliveryStatus, 
        array $deliveryData = [],
        ?int $userId = null
    ): array {
        try {
            $this->db->begin_transaction();
            
            // Get current delivery status
            $currentResult = $this->db->query("SELECT delivery_status, delivery_agent, delivery_location FROM transfers WHERE id = $transferId");
            $currentDelivery = $currentResult ? $currentResult->fetch_assoc() : null;
            
            // Update delivery status in transfers table
            $sql = "UPDATE transfers SET delivery_status = ?";
            $params = [$deliveryStatus];
            $types = 's';
            
            if (isset($deliveryData['delivery_agent'])) {
                $sql .= ", delivery_agent = ?";
                $params[] = $deliveryData['delivery_agent'];
                $types .= 's';
            }
            
            if (isset($deliveryData['delivery_location'])) {
                $sql .= ", delivery_location = ?";
                $params[] = $deliveryData['delivery_location'];
                $types .= 's';
            }
            
            if (isset($deliveryData['delivery_date'])) {
                $sql .= ", delivery_date = ?";
                $params[] = $deliveryData['delivery_date'];
                $types .= 's';
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $transferId;
            $types .= 'i';
            
            $stmt = $this->db->prepare($sql);
            $stmt->bind_param($types, ...$params);
            
            if (!$stmt->execute()) {
                $this->db->rollback();
                return ['success' => false, 'error' => 'Failed to update delivery status'];
            }
            $stmt->close();
            
            // Insert into delivery history
            $historyStmt = $this->db->prepare("
                INSERT INTO transfer_delivery_history (
                    transfer_id, delivery_status, delivery_location, delivery_agent, 
                    delivery_notes, delivery_proof, recipient_name, delivery_date, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            // Prepare variables for bind_param
            $deliveryLocation = $deliveryData['delivery_location'] ?? null;
            $deliveryAgent = $deliveryData['delivery_agent'] ?? null;
            $deliveryNotes = $deliveryData['delivery_notes'] ?? null;
            $deliveryProof = $deliveryData['delivery_proof'] ?? null;
            $recipientName = $deliveryData['recipient_name'] ?? null;
            $deliveryDate = $deliveryData['delivery_date'] ?? null;

            $historyStmt->bind_param('isssssssi',
                $transferId,
                $deliveryStatus,
                $deliveryLocation,
                $deliveryAgent,
                $deliveryNotes,
                $deliveryProof,
                $recipientName,
                $deliveryDate,
                $userId
            );
            
            $historyStmt->execute();
            $historyStmt->close();
            
            // Log the activity - تسجيل مبسط
            $deliveryLabels = [
                'pending' => 'في الانتظار',
                'picked_up' => 'تم الاستلام',
                'in_transit' => 'في الطريق',
                'out_for_delivery' => 'خرج للتسليم',
                'delivered' => 'تم التسليم',
                'failed' => 'فشل التسليم',
                'returned' => 'تم الإرجاع'
            ];

            $this->logActivity(
                $transferId,
                'delivery_updated',
                "تم تحديث التسليم: " . ($deliveryLabels[$deliveryStatus] ?? $deliveryStatus),
                null,
                ['delivery_status' => $deliveryStatus],
                $userId
            );
            
            $this->db->commit();
            
            return [
                'success' => true,
                'delivery_status' => $deliveryStatus,
                'message' => 'تم تحديث حالة التسليم بنجاح'
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Enhanced tracking number assignment with logging
     *
     * @param int $transferId Transfer ID
     * @param int|null $userId User performing the action
     * @return array Result with success status and tracking number
     */
    public function assignTrackingNumberWithLogging(int $transferId, ?int $userId = null): array
    {
        $result = parent::assignTrackingNumber($transferId);
        
        if ($result['success'] && isset($result['tracking_number'])) {
            $this->logActivity(
                $transferId,
                'tracking_assigned',
                "تم تعيين رقم التتبع",
                null,
                ['tracking_number' => $result['tracking_number']],
                $userId
            );
        }
        
        return $result;
    }
    
    /**
     * Log transfer view activity
     *
     * @param int $transferId Transfer ID
     * @param int|null $userId User viewing the transfer
     * @param string $viewType Type of view (details, list, print, etc.)
     * @return bool Success status
     */
    public function logViewActivity(int $transferId, ?int $userId = null, string $viewType = 'details'): bool
    {
        return $this->logActivity(
            $transferId,
            'viewed',
            "تم عرض تفاصيل الحوالة ($viewType)",
            null,
            ['view_type' => $viewType, 'timestamp' => date('Y-m-d H:i:s')],
            $userId
        );
    }
    
    /**
     * Get comprehensive transfer activity log
     *
     * @param int $transferId Transfer ID
     * @param int $limit Number of records to return
     * @return array Activity log entries
     */
    public function getTransferActivityLog(int $transferId, int $limit = 50): array
    {
        $sql = "SELECT tal.*, u.full_name as user_name, u.username
                FROM transfer_activity_log tal
                LEFT JOIN users u ON u.id = tal.user_id
                WHERE tal.transfer_id = ?
                ORDER BY tal.created_at DESC
                LIMIT ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bind_param('ii', $transferId, $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $activities = [];
        while ($row = $result->fetch_assoc()) {
            // Decode JSON values
            if ($row['old_value']) {
                $row['old_value_decoded'] = json_decode($row['old_value'], true);
            }
            if ($row['new_value']) {
                $row['new_value_decoded'] = json_decode($row['new_value'], true);
            }
            $activities[] = $row;
        }
        
        $stmt->close();
        return $activities;
    }
    
    /**
     * Get delivery history for a transfer
     *
     * @param int $transferId Transfer ID
     * @return array Delivery history entries
     */
    public function getDeliveryHistory(int $transferId): array
    {
        $sql = "SELECT tdh.*, u.full_name as created_by_name
                FROM transfer_delivery_history tdh
                LEFT JOIN users u ON u.id = tdh.created_by
                WHERE tdh.transfer_id = ?
                ORDER BY tdh.created_at ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bind_param('i', $transferId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $history = [];
        while ($row = $result->fetch_assoc()) {
            $history[] = $row;
        }
        
        $stmt->close();
        return $history;
    }
    
    /**
     * Get client IP address
     *
     * @return string Client IP address
     */
    private function getClientIP(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Log status history in traditional table
     *
     * @param int $transferId Transfer ID
     * @param string $oldStatus Old status
     * @param string $newStatus New status
     * @param int|null $userId User ID
     * @param string $notes Notes
     * @param string|null $location Location
     * @return bool Success status
     */
    private function logStatusHistory(
        int $transferId, 
        string $oldStatus, 
        string $newStatus, 
        ?int $userId, 
        string $notes = '', 
        ?string $location = null
    ): bool {
        $sql = "INSERT INTO transfer_status_history (
                    transfer_id, old_status, new_status, status_date, 
                    changed_by_user_id, notes, location, action_type, 
                    old_value, new_value, ip_address
                ) VALUES (?, ?, ?, NOW(), ?, ?, ?, 'status_change', ?, ?, ?)";
        
        $stmt = $this->db->prepare($sql);
        if (!$stmt) return false;
        
        $oldValueJson = json_encode(['status' => $oldStatus], JSON_UNESCAPED_UNICODE);
        $newValueJson = json_encode(['status' => $newStatus], JSON_UNESCAPED_UNICODE);
        $ipAddress = $this->getClientIP();
        
        $stmt->bind_param('ississsss', 
            $transferId, $oldStatus, $newStatus, $userId, 
            $notes, $location, $oldValueJson, $newValueJson, $ipAddress
        );
        
        $result = $stmt->execute();
        $stmt->close();
        
        return $result;
    }
}

-- =====================================================
-- الإجراءات المخزنة المفقودة لنظام المعاملات اليومية
-- Missing Stored Procedures for Daily Transactions System
-- =====================================================

DELIMITER //

-- إجراء حذف المعاملات
DROP PROCEDURE IF EXISTS DeleteDailyTransaction//

CREATE PROCEDURE DeleteDailyTransaction(
    IN p_transaction_id INT UNSIGNED,
    IN p_deleted_by INT UNSIGNED,
    OUT p_result_message VARCHAR(500)
)
BEGIN
    DECLARE v_transaction_exists INT DEFAULT 0;
    DECLARE v_user_exists INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            p_result_message = MESSAGE_TEXT;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المعاملة
    SELECT COUNT(*) INTO v_transaction_exists 
    FROM daily_transactions 
    WHERE id = p_transaction_id;
    
    IF v_transaction_exists = 0 THEN
        SET p_result_message = 'المعاملة المحددة غير موجودة';
        ROLLBACK;
        LEAVE DeleteDailyTransaction;
    END IF;
    
    -- التحقق من وجود المستخدم
    IF p_deleted_by IS NOT NULL THEN
        SELECT COUNT(*) INTO v_user_exists 
        FROM users 
        WHERE id = p_deleted_by AND status = 'active';
        
        IF v_user_exists = 0 THEN
            SET p_result_message = 'المستخدم المحدد غير موجود أو غير نشط';
            ROLLBACK;
            LEAVE DeleteDailyTransaction;
        END IF;
    END IF;
    
    -- تعيين المستخدم الحالي للاستخدام في الـ Trigger
    SET @current_user_id = p_deleted_by;
    
    -- حذف المعاملة (سيتم تنفيذ الـ Trigger تلقائياً)
    DELETE FROM daily_transactions WHERE id = p_transaction_id;
    
    SET p_result_message = 'تم حذف المعاملة بنجاح';
    
    COMMIT;
END //

-- إجراء تحديث المعاملات
DROP PROCEDURE IF EXISTS UpdateDailyTransaction//

CREATE PROCEDURE UpdateDailyTransaction(
    IN p_transaction_id INT UNSIGNED,
    IN p_country_id INT UNSIGNED,
    IN p_base_amount DECIMAL(18,2),
    IN p_customer_rate DECIMAL(18,6),
    IN p_operation_type ENUM('multiply', 'divide'),
    IN p_exchange_rate DECIMAL(18,6),
    IN p_delivery_type ENUM('cash', 'bank', 'usdt'),
    IN p_transfer_amount DECIMAL(18,2),
    IN p_recipient_name VARCHAR(255),
    IN p_notes TEXT,
    IN p_updated_by INT UNSIGNED,
    OUT p_result_message VARCHAR(500)
)
BEGIN
    DECLARE v_error_count INT DEFAULT 0;
    DECLARE v_transaction_exists INT DEFAULT 0;
    DECLARE v_country_exists INT DEFAULT 0;
    DECLARE v_user_exists INT DEFAULT 0;
    DECLARE v_old_values JSON;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            p_result_message = MESSAGE_TEXT;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المعاملة
    SELECT COUNT(*) INTO v_transaction_exists 
    FROM daily_transactions 
    WHERE id = p_transaction_id;
    
    IF v_transaction_exists = 0 THEN
        SET p_result_message = 'المعاملة المحددة غير موجودة';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    -- التحقق من وجود الدولة
    SELECT COUNT(*) INTO v_country_exists 
    FROM countries 
    WHERE id = p_country_id AND is_active = 1;
    
    IF v_country_exists = 0 THEN
        SET p_result_message = 'الدولة المحددة غير موجودة أو غير نشطة';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    -- التحقق من وجود المستخدم
    IF p_updated_by IS NOT NULL THEN
        SELECT COUNT(*) INTO v_user_exists 
        FROM users 
        WHERE id = p_updated_by AND status = 'active';
        
        IF v_user_exists = 0 THEN
            SET p_result_message = 'المستخدم المحدد غير موجود أو غير نشط';
            SET v_error_count = v_error_count + 1;
        END IF;
    END IF;
    
    -- التحقق من صحة المبالغ
    IF p_base_amount <= 0 THEN
        SET p_result_message = 'مبلغ الحوالة الأساسي يجب أن يكون أكبر من صفر';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    IF p_customer_rate <= 0 THEN
        SET p_result_message = 'سعر القص للزبون يجب أن يكون أكبر من صفر';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    IF p_exchange_rate <= 0 THEN
        SET p_result_message = 'سعر الصرف يجب أن يكون أكبر من صفر';
        SET v_error_count = v_error_count + 1;
    END IF;
    
    -- التحقق من البيانات المطلوبة للتحويل البنكي أو USDT
    IF p_delivery_type IN ('bank', 'usdt') THEN
        IF p_transfer_amount IS NULL OR p_transfer_amount <= 0 THEN
            SET p_result_message = 'المبلغ المراد تحويله مطلوب للتحويل البنكي أو USDT';
            SET v_error_count = v_error_count + 1;
        END IF;
        
        IF p_recipient_name IS NULL OR TRIM(p_recipient_name) = '' THEN
            SET p_result_message = 'اسم المستلم مطلوب للتحويل البنكي أو USDT';
            SET v_error_count = v_error_count + 1;
        END IF;
    END IF;
    
    -- إذا كانت هناك أخطاء، إلغاء العملية
    IF v_error_count > 0 THEN
        ROLLBACK;
        -- p_result_message تم تعيينها بالفعل
    ELSE
        -- حفظ القيم القديمة للتاريخ
        SELECT JSON_OBJECT(
            'country_id', country_id,
            'base_amount', base_amount,
            'customer_rate', customer_rate,
            'operation_type', operation_type,
            'exchange_rate', exchange_rate,
            'delivery_type', delivery_type,
            'transfer_amount', transfer_amount,
            'recipient_name', recipient_name,
            'notes', notes
        ) INTO v_old_values
        FROM daily_transactions
        WHERE id = p_transaction_id;
        
        -- تحديث المعاملة
        UPDATE daily_transactions SET
            country_id = p_country_id,
            base_amount = p_base_amount,
            customer_rate = p_customer_rate,
            operation_type = p_operation_type,
            exchange_rate = p_exchange_rate,
            delivery_type = p_delivery_type,
            transfer_amount = p_transfer_amount,
            recipient_name = p_recipient_name,
            notes = p_notes,
            updated_by = p_updated_by,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = p_transaction_id;
        
        -- إضافة سجل في تاريخ المعاملات
        INSERT INTO daily_transaction_history (
            transaction_id,
            action_type,
            old_values,
            new_values,
            changed_by,
            ip_address
        ) VALUES (
            p_transaction_id,
            'updated',
            v_old_values,
            JSON_OBJECT(
                'country_id', p_country_id,
                'base_amount', p_base_amount,
                'customer_rate', p_customer_rate,
                'operation_type', p_operation_type,
                'exchange_rate', p_exchange_rate,
                'delivery_type', p_delivery_type,
                'transfer_amount', p_transfer_amount,
                'recipient_name', p_recipient_name,
                'notes', p_notes
            ),
            p_updated_by,
            NULL
        );
        
        SET p_result_message = 'تم تحديث المعاملة بنجاح';
        
        COMMIT;
    END IF;
END //

-- إجراء عرض المعاملات المحذوفة
DROP PROCEDURE IF EXISTS GetDeletedDailyTransactions//

CREATE PROCEDURE GetDeletedDailyTransactions(
    IN p_date_from DATE,
    IN p_date_to DATE,
    IN p_limit_count INT,
    IN p_offset_count INT
)
BEGIN
    DECLARE v_sql TEXT DEFAULT '';
    DECLARE v_where_clause TEXT DEFAULT '';
    DECLARE v_conditions TEXT DEFAULT '';
    
    -- بناء استعلام البحث ديناميكياً
    SET v_sql = 'SELECT 
        ddt.id,
        ddt.original_id,
        ddt.transaction_number,
        ddt.deleted_at,
        c.name_ar as country_name,
        c.currency_code,
        ddt.base_amount,
        ddt.customer_rate,
        ddt.operation_type,
        ddt.calculated_amount,
        ddt.exchange_rate,
        ddt.recipient_amount,
        ddt.delivery_type,
        ddt.transfer_amount,
        ddt.recipient_name,
        ddt.notes,
        b.name as branch_name,
        u_created.full_name as created_by_name,
        u_updated.full_name as updated_by_name,
        u_deleted.full_name as deleted_by_name,
        ddt.created_at,
        ddt.updated_at,
        ddt.deleted_at
    FROM deleted_daily_transactions ddt
    LEFT JOIN countries c ON ddt.country_id = c.id
    LEFT JOIN branches b ON ddt.branch_id = b.id
    LEFT JOIN users u_created ON ddt.created_by = u_created.id
    LEFT JOIN users u_updated ON ddt.updated_by = u_updated.id
    LEFT JOIN users u_deleted ON ddt.deleted_by = u_deleted.id';
    
    -- إضافة شروط البحث
    IF p_date_from IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND DATE(ddt.deleted_at) >= ''', p_date_from, '''');
    END IF;
    
    IF p_date_to IS NOT NULL THEN
        SET v_conditions = CONCAT(v_conditions, ' AND DATE(ddt.deleted_at) <= ''', p_date_to, '''');
    END IF;
    
    -- إضافة WHERE إذا كانت هناك شروط
    IF LENGTH(v_conditions) > 0 THEN
        SET v_where_clause = CONCAT(' WHERE 1=1', v_conditions);
    END IF;
    
    -- إضافة الترتيب والحد
    SET v_sql = CONCAT(v_sql, v_where_clause, ' ORDER BY ddt.deleted_at DESC');
    
    IF p_limit_count IS NOT NULL AND p_limit_count > 0 THEN
        SET v_sql = CONCAT(v_sql, ' LIMIT ', p_limit_count);
        
        IF p_offset_count IS NOT NULL AND p_offset_count > 0 THEN
            SET v_sql = CONCAT(v_sql, ' OFFSET ', p_offset_count);
        END IF;
    END IF;
    
    -- تنفيذ الاستعلام
    SET @sql_statement = v_sql;
    PREPARE stmt FROM @sql_statement;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END //

DELIMITER ;

<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define the base directory
$baseDir = dirname(__DIR__);

// Include the database configuration
$configFile = $baseDir . '/config/database.php';
if (!file_exists($configFile)) {
    die('Error: Database configuration file not found at ' . $configFile);
}
require_once $configFile;

// Include the database class
$dbClassFile = $baseDir . '/includes/database.php';
if (!file_exists($dbClassFile)) {
    die('Error: Database class file not found at ' . $dbClassFile);
}
require_once $dbClassFile;

// Include other required files
$requiredFiles = [
    'auth.php',
    'functions.php',
    'exchange_manager.php',
    'simple_exchange_manager.php',
    'customer_manager.php',
    'currency_manager.php',
    'cash_manager.php',
    'bank_manager.php',
    'branch_manager.php'
];

foreach ($requiredFiles as $file) {
    $filePath = $baseDir . '/includes/' . $file;
    if (!file_exists($filePath)) {
        die('Error: Required file not found: ' . $filePath);
    }
    require_once $filePath;
}

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('exchange.view');

$currentUser = $auth->getCurrentUser();
$userId = $currentUser['id'];

// Get user's branch ID
$branchId = $currentUser['branch_id'] ?? null;
if (!$branchId) {
    try {
        $database = new Database();
        $branchMgr = new BranchManager($database);
        $branchId = $branchMgr->getDefaultBranchId();
        if (!$branchId) {
            set_flash('danger', 'لا توجد فروع متاحة في النظام. يرجى إنشاء فرع أولاً.');
            redirect('dashboard.php');
            exit;
        }
    } catch (Exception $e) {
        die('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
    }
}

try {
    // Create database instance
    $database = new Database();
    
    // Initialize managers - use simple exchange manager to avoid locks
    $exManager = new SimpleExchangeManager($database);
    $customerMgr = new CustomerManager($database);
    $curManager = new CurrencyManager($database);
    $cashMgr = new CashManager($database);
    $bankMgr = new BankManager($database);
    
    // Set default timezone
    date_default_timezone_set('Asia/Riyadh');
    
} catch (Exception $e) {
    die('خطأ في تهيئة النظام: ' . $e->getMessage() . 
        '<br>File: ' . $e->getFile() . 
        '<br>Line: ' . $e->getLine());
}

$errors = [];
$success = false;

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        die('رمز الأمان غير صالح. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
    }

    // Get form data
    $amountFrom = floatval($_POST['amount_from'] ?? 0);
    $amountTo = floatval($_POST['amount_to'] ?? 0);
    $userAmountField = $_POST['user_amount_field'] ?? 'amount_from';
    $fundingType = $_POST['funding_type'] ?? '';
    $fundingId = intval($_POST['funding_id'] ?? 0);
    $disburseType = $_POST['disburse_type'] ?? '';
    $disburseId = intval($_POST['disburse_id'] ?? 0);
    $customerId = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : 0;
    $notes = trim($_POST['notes'] ?? '');



    // Validation
    if (empty($errors)) {
        if ($customerId <= 0) {
            $errors[] = 'يرجى اختيار العميل';
        }
        if ($amountFrom <= 0 || $amountTo <= 0) {
            $errors[] = 'يرجى إدخال المبلغين (المستلم والمدفوع) بقيم صحيحة أكبر من الصفر';
        }
        if ($fundingId <= 0) {
            $errors[] = 'يرجى اختيار مصدر التمويل';
        }
        if ($disburseId <= 0) {
            $errors[] = 'يرجى اختيار مصدر الصرف';
        }
    }



    // Process exchange if no errors
    if (empty($errors)) {
        // Prepare data array for exchange processing
        $exchangeData = [
            'customer_id' => $customerId,
            'amount_from' => $amountFrom,
            'amount_to' => $amountTo,
            'user_amount_field' => $userAmountField,
            'funding_source' => ['type' => $fundingType, 'id' => $fundingId],
            'disburse_source' => ['type' => $disburseType, 'id' => $disburseId],
            'created_by' => $userId,
            'branch_id' => $branchId,
            'notes' => $notes
        ];

        // Process the exchange using direct database operations
        $result = processExchangeDirectly($exchangeData, $database);

        if ($result['success']) {
            $success = true;
            $exchangeId = $result['exchange_id'];
            log_activity($userId, 'exchange.create', ['exchange_id' => $exchangeId]);
            set_flash('success', 'تم إنشاء عملية الصرف بنجاح');

            // Redirect to view the created exchange
            redirect('view_exchange.php?id=' . $exchangeId);
            exit;
        } else {
            $errors[] = 'فشل في إنشاء عملية الصرف: ' . $result['error'];
        }
    }
}

// Get data for form
$customers = $customerMgr->getAllCustomers(['status' => 'active']);
$currencies = $curManager->getAllCurrencies(true);
$cashBoxes = $cashMgr->getAllCashBoxes($branchId);
$bankAccounts = $bankMgr->getAllBankAccounts($branchId);

$pageTitle = 'عملية صرف جديدة';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3><i class="fas fa-exchange-alt"></i> عملية صرف جديدة</h3>
                <div>
                    <a href="exchange_rates.php" class="btn btn-outline-secondary">
                        <i class="fas fa-chart-line"></i> أسعار الصرف
                    </a>
                    <a href="cash.php" class="btn btn-outline-primary">
                        <i class="fas fa-cash-register"></i> الصناديق
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php foreach (get_flash() as $type => $msg): ?>
        <div class="alert alert-<?php echo $type; ?> alert-dismissible fade show" role="alert">
            <?php echo $msg; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endforeach; ?>

    <!-- Error Messages -->
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <h6><i class="fas fa-exclamation-triangle"></i> يرجى تصحيح الأخطاء التالية:</h6>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if ($auth->hasPermission('exchange.create')): ?>
    <div class="row">
        <div class="col-lg-8">
            <!-- Exchange Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle"></i> بيانات عملية الصرف
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="exchangeForm">
                        <input type="hidden" name="csrf_token" value="<?php echo get_csrf_token(); ?>">
                        <input type="hidden" name="user_amount_field" id="userAmountField" value="amount_from">

                        <!-- Customer Selection -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label required">العميل</label>
                                <select name="customer_id" class="form-select" required>
                                    <option value="">-- اختر العميل --</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>"
                                                <?php echo (isset($_POST['customer_id']) && $_POST['customer_id'] == $customer['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($customer['full_name']); ?>
                                            <?php if (!empty($customer['id_number'])): ?>
                                                - <?php echo htmlspecialchars($customer['id_number']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <!-- العملات يتم تحديدها تلقائياً من الصناديق والحسابات المختارة -->



                        <!-- Calculator Section -->
                        <div class="card mb-3" style="background-color: #f8f9fa;">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-calculator"></i> حاسبة العملية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">المبلغ المستلم</label>
                                        <input type="number" id="calcReceived" class="form-control"
                                               step="0.01" min="0" placeholder="0.00"
                                               title="أدخل المبلغ المستلم من العميل">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">العملية</label>
                                        <select id="calcOperation" class="form-select" title="اختر نوع العملية الحسابية">
                                            <option value="multiply">ضرب (×)</option>
                                            <option value="divide">قسمة (÷)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">المعامل</label>
                                        <input type="number" id="calcFactor" class="form-control"
                                               step="0.0001" min="0" placeholder="1.0000"
                                               title="أدخل المعامل المستخدم في العملية (سعر الصرف مثلاً)">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">الناتج <small class="text-muted">(قابل للتعديل)</small></label>
                                        <input type="number" id="calcResult" class="form-control"
                                               step="0.01" min="0" placeholder="0.00"
                                               title="الناتج النهائي - يمكنك تعديله يدوياً">
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid gap-1">
                                            <button type="button" id="copyFromMain" class="btn btn-outline-info btn-sm" title="نسخ من الحقول الرئيسية">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            <button type="button" id="applyCalculation" class="btn btn-primary btn-sm" title="تطبيق على الحقول الرئيسية">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            <button type="button" id="clearCalculation" class="btn btn-outline-secondary btn-sm" title="مسح الحاسبة">
                                                <i class="fas fa-eraser"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>كيفية الاستخدام:</strong> أدخل المبلغ المستلم، اختر العملية (ضرب/قسمة)، أدخل المعامل (سعر الصرف)، ثم اضغط "تطبيق" لنقل النتيجة للحقول الرئيسية
                                        </small>
                                    </div>
                                </div>
                                <div class="row mt-2" id="calculationFormula" style="display: none;">
                                    <div class="col-12">
                                        <div class="alert alert-light py-2 mb-0">
                                            <small class="text-primary">
                                                <i class="fas fa-calculator"></i>
                                                <span id="formulaText"></span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Amount Fields -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">المبلغ المستلم (من العميل)</label>
                                <div class="input-group">
                                    <input type="number" name="amount_from" id="amountFrom"
                                           class="form-control" step="0.01" min="0.01"
                                           value="<?php echo htmlspecialchars($_POST['amount_from'] ?? ''); ?>"
                                           placeholder="0.00" required>
                                    <span class="input-group-text" id="fromCurrencySymbol">---</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">المبلغ المدفوع (للعميل)</label>
                                <div class="input-group">
                                    <input type="number" name="amount_to" id="amountTo"
                                           class="form-control" step="0.01" min="0.01"
                                           value="<?php echo htmlspecialchars($_POST['amount_to'] ?? ''); ?>"
                                           placeholder="0.00" required>
                                    <span class="input-group-text" id="toCurrencySymbol">---</span>
                                </div>
                            </div>
                        </div>

                        <!-- Funding Source -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label required">مصدر التمويل (استلام المبلغ)</label>
                                <div class="row">
                                    <div class="col-4">
                                        <select name="funding_type" id="fundingType" class="form-select" required>
                                            <option value="cash" <?php echo (isset($_POST['funding_type']) && $_POST['funding_type'] === 'cash') ? 'selected' : ''; ?>>صندوق</option>
                                            <option value="bank" <?php echo (isset($_POST['funding_type']) && $_POST['funding_type'] === 'bank') ? 'selected' : ''; ?>>بنك</option>
                                        </select>
                                    </div>
                                    <div class="col-8">
                                        <select name="funding_id" id="fundingId" class="form-select" required>
                                            <option value="">-- اختر المصدر --</option>
                                            <!-- Cash boxes -->
                                            <?php foreach ($cashBoxes as $cashBox): ?>
                                                <option value="<?php echo $cashBox['id']; ?>"
                                                        data-type="cash"
                                                        data-currency="<?php echo $cashBox['currency_id']; ?>"
                                                        <?php echo (isset($_POST['funding_id']) && $_POST['funding_id'] == $cashBox['id'] && ($_POST['funding_type'] ?? 'cash') === 'cash') ? 'selected' : ''; ?>>
                                                    صندوق: <?php echo htmlspecialchars($cashBox['name']); ?>
                                                    (<?php echo htmlspecialchars($cashBox['currency_code'] ?? 'N/A'); ?>) -
                                                    رصيد: <?php echo number_format($cashBox['current_balance'], 2); ?>
                                                </option>
                                            <?php endforeach; ?>
                                            <!-- Bank accounts -->
                                            <?php foreach ($bankAccounts as $account): ?>
                                                <option value="<?php echo $account['id']; ?>"
                                                        data-type="bank"
                                                        data-currency="<?php echo $account['currency_id']; ?>"
                                                        <?php echo (isset($_POST['funding_id']) && $_POST['funding_id'] == $account['id'] && ($_POST['funding_type'] ?? 'cash') === 'bank') ? 'selected' : ''; ?>>
                                                    حساب: <?php echo htmlspecialchars($account['account_name']); ?>
                                                    (<?php echo htmlspecialchars($account['currency_code'] ?? 'N/A'); ?>) -
                                                    رصيد: <?php echo number_format($account['current_balance'], 2); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required">مصدر الصرف (دفع المبلغ)</label>
                                <div class="row">
                                    <div class="col-4">
                                        <select name="disburse_type" id="disburseType" class="form-select" required>
                                            <option value="cash" <?php echo (isset($_POST['disburse_type']) && $_POST['disburse_type'] === 'cash') ? 'selected' : ''; ?>>صندوق</option>
                                            <option value="bank" <?php echo (isset($_POST['disburse_type']) && $_POST['disburse_type'] === 'bank') ? 'selected' : ''; ?>>بنك</option>
                                        </select>
                                    </div>
                                    <div class="col-8">
                                        <select name="disburse_id" id="disburseId" class="form-select" required>
                                            <option value="">-- اختر المصدر --</option>
                                            <!-- Cash boxes -->
                                            <?php foreach ($cashBoxes as $cashBox): ?>
                                                <option value="<?php echo $cashBox['id']; ?>"
                                                        data-type="cash"
                                                        data-currency="<?php echo $cashBox['currency_id']; ?>"
                                                        <?php echo (isset($_POST['disburse_id']) && $_POST['disburse_id'] == $cashBox['id'] && ($_POST['disburse_type'] ?? 'cash') === 'cash') ? 'selected' : ''; ?>>
                                                    صندوق: <?php echo htmlspecialchars($cashBox['name']); ?>
                                                    (<?php echo htmlspecialchars($cashBox['currency_code'] ?? 'N/A'); ?>) -
                                                    رصيد: <?php echo number_format($cashBox['current_balance'], 2); ?>
                                                </option>
                                            <?php endforeach; ?>
                                            <!-- Bank accounts -->
                                            <?php foreach ($bankAccounts as $account): ?>
                                                <option value="<?php echo $account['id']; ?>"
                                                        data-type="bank"
                                                        data-currency="<?php echo $account['currency_id']; ?>"
                                                        <?php echo (isset($_POST['disburse_id']) && $_POST['disburse_id'] == $account['id'] && ($_POST['disburse_type'] ?? 'cash') === 'bank') ? 'selected' : ''; ?>>
                                                    حساب: <?php echo htmlspecialchars($account['account_name']); ?>
                                                    (<?php echo htmlspecialchars($account['currency_code'] ?? 'N/A'); ?>) -
                                                    رصيد: <?php echo number_format($account['current_balance'], 2); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label">ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="3"
                                          placeholder="ملاحظات إضافية حول عملية الصرف..."><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> إنشاء عملية الصرف
                                </button>
                                <a href="cash.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar Info -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> معلومات مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> نصائح:</h6>
                        <ul class="mb-0 small">
                            <li>أدخل المبلغ في كلا الحقلين (المستلم والمدفوع)</li>
                            <li>تأكد من اختيار الصناديق أو الحسابات الصحيحة للعملات</li>
                            <li>استخدم الحاسبة لتسهيل العمليات الحسابية</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تنبيه:</h6>
                        <p class="mb-0 small">تأكد من وجود رصيد كافي في مصادر التمويل والصرف قبل إتمام العملية.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> ليس لديك صلاحية إنشاء عمليات صرف.
        </div>
    <?php endif; ?>
</div>

<script src="../assets/js/exchange.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {

    // Handle funding type change
    const fundingType = document.getElementById('fundingType');
    const fundingId = document.getElementById('fundingId');
    const disburseType = document.getElementById('disburseType');
    const disburseId = document.getElementById('disburseId');


    function updateSourceOptions(typeSelect, idSelect) {
        const selectedType = typeSelect.value;
        const options = idSelect.querySelectorAll('option[data-type]');

        // Hide all options first
        options.forEach(option => {
            option.style.display = 'none';
        });

        // Show options matching selected type
        options.forEach(option => {
            if (option.dataset.type === selectedType) {
                option.style.display = ''; // Use empty string to show option
            }
        });

        // Reset selection if current selection doesn't match type
        const currentOption = idSelect.querySelector('option:checked');
        if (currentOption && currentOption.dataset.type && currentOption.dataset.type !== selectedType) {
            idSelect.value = '';
        }
    }

    // Initialize on page load
    updateSourceOptions(fundingType, fundingId);
    updateSourceOptions(disburseType, disburseId);

    // Handle changes
    fundingType.addEventListener('change', () => updateSourceOptions(fundingType, fundingId));
    disburseType.addEventListener('change', () => updateSourceOptions(disburseType, disburseId));

    // Update currency symbols based on selected sources
    const fromSymbol = document.getElementById('fromCurrencySymbol');
    const toSymbol = document.getElementById('toCurrencySymbol');

    function updateCurrencySymbols() {
        // Update from currency symbol
        const fundingOption = fundingId.options[fundingId.selectedIndex];
        if (fundingOption && fundingOption.value) {
            const fundingText = fundingOption.textContent;
            const currencyMatch = fundingText.match(/\(([A-Z]{3})\)/);
            if (currencyMatch) {
                fromSymbol.textContent = currencyMatch[1];
            }
        } else {
            fromSymbol.textContent = '---';
        }

        // Update to currency symbol
        const disburseOption = disburseId.options[disburseId.selectedIndex];
        if (disburseOption && disburseOption.value) {
            const disburseText = disburseOption.textContent;
            const currencyMatch = disburseText.match(/\(([A-Z]{3})\)/);
            if (currencyMatch) {
                toSymbol.textContent = currencyMatch[1];
            }
        } else {
            toSymbol.textContent = '---';
        }
    }

    // Listen for changes in source selection
    fundingId.addEventListener('change', updateCurrencySymbols);
    disburseId.addEventListener('change', updateCurrencySymbols);

    // Initialize symbols
    updateCurrencySymbols();

    // Track which field user is editing
    const amountFrom = document.getElementById('amountFrom');
    const amountTo = document.getElementById('amountTo');
    const userAmountField = document.getElementById('userAmountField');

    amountFrom.addEventListener('focus', () => userAmountField.value = 'amount_from');
    amountTo.addEventListener('focus', () => userAmountField.value = 'amount_to');

    // Calculator functionality
    const calcReceived = document.getElementById('calcReceived');
    const calcOperation = document.getElementById('calcOperation');
    const calcFactor = document.getElementById('calcFactor');
    const calcResult = document.getElementById('calcResult');
    const applyCalculation = document.getElementById('applyCalculation');
    const clearCalculation = document.getElementById('clearCalculation');
    const copyFromMain = document.getElementById('copyFromMain');

    // Auto-calculate when inputs change
    function performCalculation() {
        const received = parseFloat(calcReceived.value) || 0;
        const factor = parseFloat(calcFactor.value) || 0;
        const operation = calcOperation.value;
        const formulaDiv = document.getElementById('calculationFormula');
        const formulaText = document.getElementById('formulaText');

        if (received > 0 && factor > 0) {
            let result = 0;
            let operationSymbol = '';

            if (operation === 'multiply') {
                result = received * factor;
                operationSymbol = '×';
            } else if (operation === 'divide') {
                result = received / factor;
                operationSymbol = '÷';
            }

            calcResult.value = result.toFixed(2);

            // Show formula
            formulaText.textContent = `${received} ${operationSymbol} ${factor} = ${result.toFixed(2)}`;
            formulaDiv.style.display = 'block';
        } else {
            calcResult.value = '';
            formulaDiv.style.display = 'none';
        }
    }

    // Event listeners for auto-calculation
    calcReceived.addEventListener('input', performCalculation);
    calcOperation.addEventListener('change', performCalculation);
    calcFactor.addEventListener('input', performCalculation);

    // Allow manual editing of result
    calcResult.addEventListener('input', function() {
        // User can manually edit the result
        // No automatic recalculation when result is manually changed
    });

    // Apply calculation to main form
    applyCalculation.addEventListener('click', function() {
        const received = parseFloat(calcReceived.value) || 0;
        const result = parseFloat(calcResult.value) || 0;

        if (received > 0 && result > 0) {
            document.getElementById('amountFrom').value = received.toFixed(2);
            document.getElementById('amountTo').value = result.toFixed(2);
            document.getElementById('userAmountField').value = 'amount_from';
        } else if (received > 0) {
            document.getElementById('amountFrom').value = received.toFixed(2);
            document.getElementById('userAmountField').value = 'amount_from';
        } else if (result > 0) {
            document.getElementById('amountTo').value = result.toFixed(2);
            document.getElementById('userAmountField').value = 'amount_to';
        }

        // Show success message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show mt-2';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle"></i> تم تطبيق الحساب بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert after calculator card
        const calculatorCard = document.querySelector('.card[style*="background-color: #f8f9fa"]');
        calculatorCard.parentNode.insertBefore(alertDiv, calculatorCard.nextSibling);

        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    });

    // Clear calculation
    clearCalculation.addEventListener('click', function() {
        calcReceived.value = '';
        calcFactor.value = '';
        calcResult.value = '';
        calcOperation.value = 'multiply';
        document.getElementById('calculationFormula').style.display = 'none';
    });

    // Copy from main fields
    copyFromMain.addEventListener('click', function() {
        const fromAmount = parseFloat(amountFrom.value) || 0;
        const toAmount = parseFloat(amountTo.value) || 0;

        if (fromAmount > 0) {
            calcReceived.value = fromAmount.toFixed(2);
        }

        if (toAmount > 0) {
            calcResult.value = toAmount.toFixed(2);
        }
    });
});
</script>
<?php

/**
 * Process exchange directly without using complex managers
 */
function processExchangeDirectly(array $data, Database $database): array {
    $db = $database::getConnection();

    try {
        // Start transaction
        $db->autocommit(false);

        // Validate required data
        if (!isset($data['customer_id'], $data['amount_from'], $data['amount_to'],
                   $data['funding_source'], $data['disburse_source'],
                   $data['created_by'], $data['branch_id'])) {
            throw new Exception('بيانات مطلوبة مفقودة');
        }

        $amountFrom = floatval($data['amount_from']);
        $amountTo = floatval($data['amount_to']);

        if ($amountFrom <= 0 || $amountTo <= 0) {
            throw new Exception('المبالغ يجب أن تكون أكبر من الصفر');
        }

        // Calculate exchange rate
        $exchangeRate = $amountTo / $amountFrom;
        $commission = round($amountFrom * 0.002, 4);
        $profit = round($amountFrom * 0.001, 4);

        // Prepare exchange data
        $fundingSource = $data['funding_source'];
        $disburseSource = $data['disburse_source'];

        // Use a simple, reliable approach - fixed SQL structure
        if ($fundingSource['type'] === 'cash' && $disburseSource['type'] === 'cash') {
            // Both are cash boxes
            $sql = "INSERT INTO exchanges (
                        customer_id, from_amount, to_amount, exchange_rate,
                        commission, profit, status, notes, branch_id, created_by,
                        from_cash_box_id, to_cash_box_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            // Prepare variables for binding
            $status = 'completed';
            $notes = $data['notes'] ?? '';
            $customerId = $data['customer_id'];
            $branchId = $data['branch_id'];
            $createdBy = $data['created_by'];
            $fromCashBoxId = intval($fundingSource['id']);
            $toCashBoxId = intval($disburseSource['id']);

            $stmt = $db->prepare($sql);
            if (!$stmt) {
                throw new Exception('فشل في تحضير استعلام الإدراج');
            }

            $stmt->bind_param('iddddssiiiii',
                $customerId, $amountFrom, $amountTo, $exchangeRate,
                $commission, $profit, $status, $notes,
                $branchId, $createdBy, $fromCashBoxId, $toCashBoxId
            );

        } elseif ($fundingSource['type'] === 'bank' && $disburseSource['type'] === 'bank') {
            // Both are bank accounts
            $sql = "INSERT INTO exchanges (
                        customer_id, from_amount, to_amount, exchange_rate,
                        commission, profit, status, notes, branch_id, created_by,
                        from_bank_account_id, to_bank_account_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            // Prepare variables for binding
            $status = 'completed';
            $notes = $data['notes'] ?? '';
            $customerId = $data['customer_id'];
            $branchId = $data['branch_id'];
            $createdBy = $data['created_by'];
            $fromBankAccountId = intval($fundingSource['id']);
            $toBankAccountId = intval($disburseSource['id']);

            $stmt = $db->prepare($sql);
            if (!$stmt) {
                throw new Exception('فشل في تحضير استعلام الإدراج');
            }

            $stmt->bind_param('iddddssiiiii',
                $customerId, $amountFrom, $amountTo, $exchangeRate,
                $commission, $profit, $status, $notes,
                $branchId, $createdBy, $fromBankAccountId, $toBankAccountId
            );

        } elseif ($fundingSource['type'] === 'cash' && $disburseSource['type'] === 'bank') {
            // Cash to Bank
            $sql = "INSERT INTO exchanges (
                        customer_id, from_amount, to_amount, exchange_rate,
                        commission, profit, status, notes, branch_id, created_by,
                        from_cash_box_id, to_bank_account_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            // Prepare variables for binding
            $status = 'completed';
            $notes = $data['notes'] ?? '';
            $customerId = $data['customer_id'];
            $branchId = $data['branch_id'];
            $createdBy = $data['created_by'];
            $fromCashBoxId = intval($fundingSource['id']);
            $toBankAccountId = intval($disburseSource['id']);

            $stmt = $db->prepare($sql);
            if (!$stmt) {
                throw new Exception('فشل في تحضير استعلام الإدراج');
            }

            $stmt->bind_param('iddddssiiiii',
                $customerId, $amountFrom, $amountTo, $exchangeRate,
                $commission, $profit, $status, $notes,
                $branchId, $createdBy, $fromCashBoxId, $toBankAccountId
            );

        } else { // $fundingSource['type'] === 'bank' && $disburseSource['type'] === 'cash'
            // Bank to Cash
            $sql = "INSERT INTO exchanges (
                        customer_id, from_amount, to_amount, exchange_rate,
                        commission, profit, status, notes, branch_id, created_by,
                        from_bank_account_id, to_cash_box_id, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            // Prepare variables for binding
            $status = 'completed';
            $notes = $data['notes'] ?? '';
            $customerId = $data['customer_id'];
            $branchId = $data['branch_id'];
            $createdBy = $data['created_by'];
            $fromBankAccountId = intval($fundingSource['id']);
            $toCashBoxId = intval($disburseSource['id']);

            $stmt = $db->prepare($sql);
            if (!$stmt) {
                throw new Exception('فشل في تحضير استعلام الإدراج');
            }

            $stmt->bind_param('iddddssiiiii',
                $customerId, $amountFrom, $amountTo, $exchangeRate,
                $commission, $profit, $status, $notes,
                $branchId, $createdBy, $fromBankAccountId, $toCashBoxId
            );
        }
        if (!$stmt->execute()) {
            throw new Exception('فشل في إدراج عملية الصرف: ' . $stmt->error);
        }

        $exchangeId = $stmt->insert_id;
        $stmt->close();

        // Update balances
        updateSourceBalance($db, $fundingSource, $amountFrom, 'deposit');
        updateSourceBalance($db, $disburseSource, $amountTo, 'withdrawal');

        // Insert movements
        insertMovement($db, $fundingSource, $amountFrom, 'deposit', $exchangeId, $data['created_by']);
        insertMovement($db, $disburseSource, $amountTo, 'withdrawal', $exchangeId, $data['created_by']);

        // Commit transaction
        $db->commit();
        $db->autocommit(true);

        return ['success' => true, 'exchange_id' => $exchangeId];

    } catch (Exception $e) {
        $db->rollback();
        $db->autocommit(true);
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function updateSourceBalance($db, $source, $amount, $type) {
    $operator = ($type === 'deposit') ? '+' : '-';

    if ($source['type'] === 'cash') {
        $sql = "UPDATE cash_boxes SET current_balance = current_balance $operator ?, updated_at = NOW() WHERE id = ?";
    } else {
        $sql = "UPDATE bank_accounts SET current_balance = current_balance $operator ?, updated_at = NOW() WHERE id = ?";
    }

    $stmt = $db->prepare($sql);
    if (!$stmt) {
        throw new Exception('فشل في تحضير استعلام تحديث الرصيد');
    }

    $stmt->bind_param('di', $amount, $source['id']);
    if (!$stmt->execute()) {
        throw new Exception('فشل في تحديث الرصيد: ' . $stmt->error);
    }
    $stmt->close();
}

function insertMovement($db, $source, $amount, $type, $exchangeId, $userId) {
    if ($source['type'] === 'cash') {
        $sql = "INSERT INTO cash_movements (cash_box_id, movement_type, amount, description, exchange_id, user_id, created_at)
                VALUES (?, ?, ?, 'Exchange operation', ?, ?, NOW())";
    } else {
        $sql = "INSERT INTO bank_movements (bank_account_id, movement_type, amount, description, exchange_id, user_id, created_at)
                VALUES (?, ?, ?, 'Exchange operation', ?, ?, NOW())";
    }

    $stmt = $db->prepare($sql);
    if (!$stmt) {
        throw new Exception('فشل في تحضير استعلام إدراج الحركة');
    }

    $stmt->bind_param('isdii', $source['id'], $type, $amount, $exchangeId, $userId);
    if (!$stmt->execute()) {
        throw new Exception('فشل في إدراج الحركة: ' . $stmt->error);
    }
    $stmt->close();
}

require_once __DIR__ . '/../includes/footer.php'; ?>

<?php
/**
 * جلب تفاصيل سجل العملية
 */

require_once __DIR__ . '/../../includes/auth.php';
require_once __DIR__ . '/../../includes/database.php';

$auth = new Auth();
$auth->requireLogin();

// التحقق من صلاحية الأدمن فقط
$currentUser = $auth->getCurrentUser();
if (!$currentUser || !isset($currentUser['role_id']) || (int)$currentUser['role_id'] !== 1) {
    http_response_code(403);
    echo '<div class="alert alert-danger">غير مسموح لك بالوصول إلى هذه البيانات</div>';
    exit;
}

$logId = (int)($_GET['id'] ?? 0);
if (!$logId) {
    echo '<div class="alert alert-danger">معرف السجل غير صحيح</div>';
    exit;
}

$db = Database::getConnection();

// جلب تفاصيل السجل
$stmt = $db->prepare("SELECT * FROM system_activity_logs WHERE id = ?");
$stmt->bind_param('i', $logId);
$stmt->execute();
$result = $stmt->get_result();
$log = $result->fetch_assoc();

if (!$log) {
    echo '<div class="alert alert-danger">السجل غير موجود</div>';
    exit;
}

// تحويل JSON إلى مصفوفات
$oldValues = $log['old_values'] ? json_decode($log['old_values'], true) : null;
$newValues = $log['new_values'] ? json_decode($log['new_values'], true) : null;
$changedFields = $log['changed_fields'] ? json_decode($log['changed_fields'], true) : null;
$additionalData = $log['additional_data'] ? json_decode($log['additional_data'], true) : null;

?>

<div class="row">
    <!-- المعلومات الأساسية -->
    <div class="col-md-6">
        <h6 class="fw-bold text-primary">المعلومات الأساسية</h6>
        <table class="table table-sm table-bordered">
            <tr>
                <td class="fw-bold">معرف السجل:</td>
                <td><?php echo htmlspecialchars($log['id']); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">التاريخ والوقت:</td>
                <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">المستخدم:</td>
                <td>
                    <?php echo htmlspecialchars($log['username'] ?? 'N/A'); ?>
                    <?php if ($log['user_full_name']): ?>
                        <br><small class="text-muted"><?php echo htmlspecialchars($log['user_full_name']); ?></small>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td class="fw-bold">الدور:</td>
                <td><?php echo htmlspecialchars($log['user_role'] ?? 'N/A'); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">نوع العملية:</td>
                <td><span class="badge bg-primary"><?php echo htmlspecialchars($log['action_type']); ?></span></td>
            </tr>
            <tr>
                <td class="fw-bold">الوحدة:</td>
                <td><?php echo htmlspecialchars($log['module']); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">العملية:</td>
                <td><?php echo htmlspecialchars($log['operation']); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">الحالة:</td>
                <td>
                    <?php
                    $statusColors = [
                        'SUCCESS' => 'success',
                        'FAILED' => 'danger',
                        'PARTIAL' => 'warning',
                        'PENDING' => 'info'
                    ];
                    $statusColor = $statusColors[$log['status']] ?? 'secondary';
                    ?>
                    <span class="badge bg-<?php echo $statusColor; ?>"><?php echo htmlspecialchars($log['status']); ?></span>
                </td>
            </tr>
        </table>
    </div>

    <!-- معلومات الشبكة والجلسة -->
    <div class="col-md-6">
        <h6 class="fw-bold text-primary">معلومات الشبكة والجلسة</h6>
        <table class="table table-sm table-bordered">
            <tr>
                <td class="fw-bold">عنوان IP:</td>
                <td><?php echo htmlspecialchars($log['ip_address'] ?? 'N/A'); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">معرف الجلسة:</td>
                <td><small><?php echo htmlspecialchars($log['session_id'] ?? 'N/A'); ?></small></td>
            </tr>
            <tr>
                <td class="fw-bold">طريقة الطلب:</td>
                <td><?php echo htmlspecialchars($log['request_method'] ?? 'N/A'); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">رابط الطلب:</td>
                <td><small><?php echo htmlspecialchars($log['request_url'] ?? 'N/A'); ?></small></td>
            </tr>
            <tr>
                <td class="fw-bold">وقت التنفيذ:</td>
                <td><?php echo $log['execution_time'] ? $log['execution_time'] . ' ثانية' : 'N/A'; ?></td>
            </tr>
            <?php if ($log['error_message']): ?>
            <tr>
                <td class="fw-bold">رسالة الخطأ:</td>
                <td class="text-danger"><?php echo htmlspecialchars($log['error_message']); ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
</div>

<!-- الوصف -->
<div class="row mt-3">
    <div class="col-12">
        <h6 class="fw-bold text-primary">وصف العملية</h6>
        <div class="alert alert-light">
            <?php echo htmlspecialchars($log['description']); ?>
        </div>
    </div>
</div>

<!-- معلومات السجل المتأثر -->
<?php if ($log['target_table'] || $log['target_id'] || $log['target_identifier']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="fw-bold text-primary">معلومات السجل المتأثر</h6>
        <table class="table table-sm table-bordered">
            <?php if ($log['target_table']): ?>
            <tr>
                <td class="fw-bold">الجدول:</td>
                <td><?php echo htmlspecialchars($log['target_table']); ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($log['target_id']): ?>
            <tr>
                <td class="fw-bold">معرف السجل:</td>
                <td><?php echo htmlspecialchars($log['target_id']); ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($log['target_identifier']): ?>
            <tr>
                <td class="fw-bold">المعرف الإضافي:</td>
                <td><?php echo htmlspecialchars($log['target_identifier']); ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
</div>
<?php endif; ?>

<!-- البيانات المتغيرة -->
<?php if ($changedFields): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="fw-bold text-primary">الحقول المتغيرة</h6>
        <div class="table-responsive">
            <table class="table table-sm table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>الحقل</th>
                        <th>القيمة القديمة</th>
                        <th>القيمة الجديدة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($changedFields as $field => $values): ?>
                    <tr>
                        <td class="fw-bold"><?php echo htmlspecialchars($field); ?></td>
                        <td class="text-muted">
                            <?php 
                            $oldVal = $values['old'] ?? '';
                            echo is_array($oldVal) ? json_encode($oldVal, JSON_UNESCAPED_UNICODE) : htmlspecialchars($oldVal);
                            ?>
                        </td>
                        <td class="text-success">
                            <?php 
                            $newVal = $values['new'] ?? '';
                            echo is_array($newVal) ? json_encode($newVal, JSON_UNESCAPED_UNICODE) : htmlspecialchars($newVal);
                            ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- البيانات القديمة -->
<?php if ($oldValues): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="fw-bold text-primary">البيانات القديمة</h6>
        <div class="alert alert-light">
            <pre class="mb-0" style="max-height: 200px; overflow-y: auto;"><?php echo htmlspecialchars(json_encode($oldValues, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- البيانات الجديدة -->
<?php if ($newValues): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="fw-bold text-primary">البيانات الجديدة</h6>
        <div class="alert alert-light">
            <pre class="mb-0" style="max-height: 200px; overflow-y: auto;"><?php echo htmlspecialchars(json_encode($newValues, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- البيانات الإضافية -->
<?php if ($additionalData): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="fw-bold text-primary">بيانات إضافية</h6>
        <div class="alert alert-info">
            <pre class="mb-0" style="max-height: 200px; overflow-y: auto;"><?php echo htmlspecialchars(json_encode($additionalData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- معلومات المتصفح -->
<?php if ($log['user_agent']): ?>
<div class="row mt-3">
    <div class="col-12">
        <h6 class="fw-bold text-primary">معلومات المتصفح</h6>
        <div class="alert alert-light">
            <small><?php echo htmlspecialchars($log['user_agent']); ?></small>
        </div>
    </div>
</div>
<?php endif; ?>

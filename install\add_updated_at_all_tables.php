<?php
/**
 * Add updated_at column to all relevant tables
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

echo "<h2>إضافة عمود updated_at إلى جميع الجداول المطلوبة</h2>\n";

try {
    $conn = Database::getConnection();
    
    // Tables that should have updated_at column
    $tables = [
        'cash_movements',
        'bank_movements', 
        'cash_boxes',
        'bank_accounts',
        'customers',
        'currencies',
        'exchange_rates',
        'exchanges',
        'transfers',
        'users'
    ];
    
    foreach ($tables as $table) {
        echo "<h3>جدول: $table</h3>\n";
        
        // Check if table exists
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows === 0) {
            echo "<p style='color: orange;'>⚠ جدول $table غير موجود</p>\n";
            continue;
        }
        
        // Check if updated_at column exists
        $result = $conn->query("SHOW COLUMNS FROM $table LIKE 'updated_at'");
        
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✓ عمود updated_at موجود في $table</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ عمود updated_at غير موجود في $table، سيتم إضافته</p>\n";
            
            // Add updated_at column
            $addColumnSql = "ALTER TABLE $table 
                            ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL";
            
            // Try to add after created_at if it exists
            $createdAtResult = $conn->query("SHOW COLUMNS FROM $table LIKE 'created_at'");
            if ($createdAtResult && $createdAtResult->num_rows > 0) {
                $addColumnSql .= " AFTER created_at";
            }
            
            if ($conn->query($addColumnSql)) {
                echo "<p style='color: green;'>✓ تم إضافة عمود updated_at إلى $table</p>\n";
                
                // Update existing records
                $updateSql = "UPDATE $table SET updated_at = ";
                
                // Use created_at if available, otherwise use NOW()
                $createdAtResult = $conn->query("SHOW COLUMNS FROM $table LIKE 'created_at'");
                if ($createdAtResult && $createdAtResult->num_rows > 0) {
                    $updateSql .= "created_at";
                } else {
                    $updateSql .= "NOW()";
                }
                $updateSql .= " WHERE updated_at IS NULL";
                
                if ($conn->query($updateSql)) {
                    $affectedRows = $conn->affected_rows;
                    echo "<p style='color: blue;'>ℹ️ تم تحديث $affectedRows سجل في $table</p>\n";
                }
                
            } else {
                echo "<p style='color: red;'>✗ فشل في إضافة عمود updated_at إلى $table: " . $conn->error . "</p>\n";
            }
        }
        
        echo "<hr style='margin: 10px 0; border: 1px solid #eee;'>\n";
    }
    
    echo "<h3>ملخص النتائج</h3>\n";
    
    $summary = [];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            $result = $conn->query("SHOW COLUMNS FROM $table LIKE 'updated_at'");
            if ($result && $result->num_rows > 0) {
                $summary[$table] = 'موجود';
            } else {
                $summary[$table] = 'مفقود';
            }
        } else {
            $summary[$table] = 'الجدول غير موجود';
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background: #f8f9fa;'>\n";
    echo "<th>اسم الجدول</th><th>حالة عمود updated_at</th>\n";
    echo "</tr>\n";
    
    foreach ($summary as $table => $status) {
        $color = ($status === 'موجود') ? 'green' : (($status === 'مفقود') ? 'red' : 'orange');
        echo "<tr>\n";
        echo "<td>$table</td>\n";
        echo "<td style='color: $color;'>$status</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ تم فحص وإضافة عمود updated_at لجميع الجداول!</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='test_update_movement.php' target='_blank'>اختبر دالة updateCashMovement</a></li>\n";
    echo "<li><a href='../dashboard/edit_movement.php?id=28' target='_blank'>اختبر صفحة تعديل الحركة</a></li>\n";
    echo "<li><a href='check_cash_movements_table.php' target='_blank'>تحقق من هيكل الجدول</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>

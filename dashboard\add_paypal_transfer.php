<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/paypal_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
// استخدام صلاحية PayPal المخصصة
$auth->requirePermission('paypal.create');

$paypalMgr = new PayPalManager(new Database());
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrf = $_POST['csrf_token'] ?? '';
    if (!verify_csrf_token($csrf)) {
        $errors[] = 'CSRF غير صالح';
    } else {
        // Sanitize and validate inputs
        $recipient_name    = sanitize_input($_POST['recipient_name'] ?? '');
        $recipient_phone   = sanitize_input($_POST['recipient_phone'] ?? '');
        $transaction_code  = sanitize_input($_POST['transaction_code'] ?? '');
        $sender_name       = sanitize_input($_POST['sender_name'] ?? '');
        $amount            = (float)($_POST['amount'] ?? 0);
        $status            = ($_POST['status'] ?? 'لم يستلم') === 'مستلم' ? 'مستلم' : 'لم يستلم';

        if ($recipient_name === '')    $errors[] = 'اسم المستلم مطلوب';
        if ($recipient_phone === '')   $errors[] = 'رقم جوال المستلم مطلوب';
        if ($transaction_code === '')  $errors[] = 'رمز المعاملة مطلوب';
        if ($sender_name === '')       $errors[] = 'اسم المرسل مطلوب';
        if ($amount <= 0)              $errors[] = 'المبلغ يجب أن يكون أكبر من صفر';

        // Check for duplicate transaction code
        if ($paypalMgr->transactionCodeExists($transaction_code)) {
            $errors[] = 'رمز المعاملة موجود مسبقاً';
        }

        if (empty($errors)) {
            $data = [
                'recipient_name'   => $recipient_name,
                'recipient_phone'  => $recipient_phone,
                'transaction_code' => $transaction_code,
                'sender_name'      => $sender_name,
                'amount'           => $amount,
                'status'           => $status,
            ];

            $newId = $paypalMgr->addTransfer($data);
            if ($newId) {
                // تسجيل عملية إضافة حوالة PayPal
                ActivityHelper::logCreate(
                    'paypal_transfers',
                    "حوالة PayPal - $recipient_name",
                    $data,
                    $newId
                );
                set_flash('success', 'تم تسجيل حوالة PayPal بنجاح');
                redirect('paypal_transfers.php');
            } else {
                // تسجيل فشل إضافة الحوالة
                ActivityHelper::logError(
                    'paypal_transfers',
                    'add_paypal_transfer',
                    'فشل في إضافة حوالة PayPal جديدة: ' . $recipient_name,
                    ['attempted_data' => $data]
                );
                $errors[] = 'حدث خطأ أثناء حفظ البيانات';
            }
        }
    }
}

$csrf = get_csrf_token();
$pageTitle = 'تسجيل حوالة PayPal جديدة';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/sidebar.php';
?>

<div class="container p-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0"><i class="fab fa-paypal"></i> تسجيل حوالة PayPal</h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $e): ?>
                                    <li><?php echo htmlspecialchars($e); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf; ?>">

                        <div class="mb-3">
                            <label for="recipient_name" class="form-label">اسم المستلم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="recipient_name" name="recipient_name" required value="<?php echo htmlspecialchars($_POST['recipient_name'] ?? ''); ?>">
                            <div class="invalid-feedback">اسم المستلم مطلوب</div>
                        </div>

                        <div class="mb-3">
                            <label for="recipient_phone" class="form-label">رقم جوال المستلم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="recipient_phone" name="recipient_phone" required value="<?php echo htmlspecialchars($_POST['recipient_phone'] ?? ''); ?>">
                            <div class="invalid-feedback">رقم الجوال مطلوب</div>
                        </div>

                        <div class="mb-3">
                            <label for="transaction_code" class="form-label">رمز المعاملة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="transaction_code" name="transaction_code" required value="<?php echo htmlspecialchars($_POST['transaction_code'] ?? ''); ?>">
                            <div class="invalid-feedback">رمز المعاملة مطلوب ولا يجب تكراره</div>
                        </div>

                        <div class="mb-3">
                            <label for="sender_name" class="form-label">اسم المرسل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="sender_name" name="sender_name" required value="<?php echo htmlspecialchars($_POST['sender_name'] ?? ''); ?>">
                            <div class="invalid-feedback">اسم المرسل مطلوب</div>
                        </div>

                        <div class="mb-3">
                            <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="amount" name="amount" required value="<?php echo htmlspecialchars($_POST['amount'] ?? ''); ?>">
                            <div class="invalid-feedback">المبلغ مطلوب ويجب أن يكون أكبر من صفر</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label d-block">الحالة</label>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" id="status_received" name="status" value="مستلم" <?php echo (($_POST['status'] ?? '') === 'مستلم') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="status_received">مستلم</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" id="status_not_received" name="status" value="لم يستلم" <?php echo (($_POST['status'] ?? 'لم يستلم') === 'لم يستلم') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="status_not_received">لم يستلم</label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="paypal_transfers.php" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> رجوع</a>
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function () {
    'use strict';
    window.addEventListener('load', function () {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function (form) {
            form.addEventListener('submit', function (event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>

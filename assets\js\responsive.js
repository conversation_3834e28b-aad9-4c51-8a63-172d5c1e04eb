/*
 * نظام الاستجابة JavaScript
 * Responsive JavaScript System
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Responsive JavaScript loaded');

    // متغيرات النظام
    const breakpoints = {
        xs: 0,
        sm: 576,
        md: 768,
        lg: 992,
        xl: 1200,
        xxl: 1400
    };

    let currentBreakpoint = getCurrentBreakpoint();
    let sidebarVisible = false;

    // الحصول على نقطة التوقف الحالية
    function getCurrentBreakpoint() {
        const width = window.innerWidth;
        if (width >= breakpoints.xxl) return 'xxl';
        if (width >= breakpoints.xl) return 'xl';
        if (width >= breakpoints.lg) return 'lg';
        if (width >= breakpoints.md) return 'md';
        if (width >= breakpoints.sm) return 'sm';
        return 'xs';
    }

    // إدارة الشريط الجانبي
    function initSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.querySelector('.sidebar-toggle-btn');
        const mainContent = document.querySelector('.main-content');

        if (!sidebar) return;

        // إنشاء زر التبديل إذا لم يكن موجوداً
        if (!toggleBtn) {
            createSidebarToggleButton();
        }

        // إعداد الأحداث
        setupSidebarEvents();

        // تحديث حالة الشريط الجانبي حسب حجم الشاشة
        updateSidebarState();

        // إخفاء الشريط الجانبي تلقائياً على الأجهزة المحمولة عند التحميل
        if (isMobileDevice()) {
            hideSidebar();
        }
    }

    // إنشاء زر تبديل الشريط الجانبي
    function createSidebarToggleButton() {
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'btn btn-primary sidebar-toggle-btn';
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        toggleBtn.setAttribute('aria-label', 'تبديل القائمة الجانبية');
        
        document.body.appendChild(toggleBtn);
        
        toggleBtn.addEventListener('click', toggleSidebar);
    }

    // إعداد أحداث الشريط الجانبي
    function setupSidebarEvents() {
        const toggleBtn = document.querySelector('.sidebar-toggle-btn');
        if (toggleBtn) {
            // إزالة المستمعين السابقين لتجنب التكرار
            toggleBtn.removeEventListener('click', toggleSidebar);
            toggleBtn.addEventListener('click', toggleSidebar);
        }

        // إغلاق الشريط الجانبي عند النقر خارجه على الأجهزة المحمولة
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggleBtn = document.querySelector('.sidebar-toggle-btn');

            if (isMobileDevice()) {
                if (sidebarVisible && sidebar && !sidebar.contains(e.target) &&
                    toggleBtn && !toggleBtn.contains(e.target)) {
                    hideSidebar();
                }
            }
        });

        // إغلاق الشريط الجانبي عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebarVisible && isMobileDevice()) {
                hideSidebar();
            }
        });
    }

    // فحص ما إذا كان الجهاز محمول
    function isMobileDevice() {
        return currentBreakpoint === 'xs' || currentBreakpoint === 'sm' || currentBreakpoint === 'md';
    }

    // تبديل حالة الشريط الجانبي
    function toggleSidebar() {
        console.log('Toggle sidebar clicked, current state:', sidebarVisible);
        if (sidebarVisible) {
            hideSidebar();
        } else {
            showSidebar();
        }
    }

    // إظهار الشريط الجانبي
    function showSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.querySelector('.sidebar-toggle-btn');

        if (sidebar) {
            sidebar.classList.add('show');
            sidebarVisible = true;

            // تحديث أيقونة الزر
            if (toggleBtn) {
                const icon = toggleBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-times';
                }
            }

            // إضافة overlay للأجهزة المحمولة
            if (isMobileDevice()) {
                createOverlay();
            }

            console.log('Sidebar shown');
        }
    }

    // إخفاء الشريط الجانبي
    function hideSidebar() {
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.querySelector('.sidebar-toggle-btn');

        if (sidebar) {
            sidebar.classList.remove('show');
            sidebarVisible = false;

            // تحديث أيقونة الزر
            if (toggleBtn) {
                const icon = toggleBtn.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            }

            removeOverlay();
            console.log('Sidebar hidden');
        }
    }

    // إنشاء طبقة التغطية
    function createOverlay() {
        let overlay = document.querySelector('.sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 999;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            document.body.appendChild(overlay);
            
            // تأثير الظهور
            setTimeout(() => {
                overlay.style.opacity = '1';
            }, 10);
            
            overlay.addEventListener('click', hideSidebar);
        }
    }

    // إزالة طبقة التغطية
    function removeOverlay() {
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.remove();
            }, 300);
        }
    }

    // تحديث حالة الشريط الجانبي
    function updateSidebarState() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        const toggleBtn = document.querySelector('.sidebar-toggle-btn');

        if (!sidebar) return;

        if (currentBreakpoint === 'lg' || currentBreakpoint === 'xl' || currentBreakpoint === 'xxl') {
            // أجهزة الكمبيوتر - إظهار الشريط الجانبي دائماً وإخفاء الزر
            sidebar.classList.remove('show');
            sidebarVisible = false;
            removeOverlay();

            if (toggleBtn) {
                toggleBtn.style.display = 'none';
            }
        } else {
            // الأجهزة المحمولة - إخفاء الشريط الجانبي وإظهار الزر
            if (sidebarVisible) {
                hideSidebar();
            }

            if (toggleBtn) {
                toggleBtn.style.display = 'flex';
            }
        }
    }

    // تحويل الجداول للعرض المتجاوب
    function makeTablesResponsive() {
        const tables = document.querySelectorAll('table:not(.table-responsive table)');
        
        tables.forEach(table => {
            if (!table.closest('.table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    }

    // إنشاء عرض البطاقات للجداول على الأجهزة المحمولة
    function createMobileTableCards() {
        const tables = document.querySelectorAll('.table-responsive table');
        
        tables.forEach(table => {
            if (currentBreakpoint === 'xs') {
                createMobileCardsForTable(table);
            } else {
                removeMobileCardsForTable(table);
            }
        });
    }

    // إنشاء بطاقات للجدول
    function createMobileCardsForTable(table) {
        const existingCards = table.parentNode.querySelector('.table-mobile-cards');
        if (existingCards) return;

        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');
        
        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'table-mobile-cards d-mobile-block';
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const card = document.createElement('div');
            card.className = 'mobile-card';
            
            // عنوان البطاقة (أول خلية عادة)
            if (cells.length > 0) {
                const header = document.createElement('div');
                header.className = 'mobile-card-header';
                header.textContent = cells[0].textContent.trim();
                card.appendChild(header);
            }
            
            // باقي البيانات
            cells.forEach((cell, index) => {
                if (index === 0) return; // تخطي العنوان
                
                const cardRow = document.createElement('div');
                cardRow.className = 'mobile-card-row';
                
                const label = document.createElement('div');
                label.className = 'mobile-card-label';
                label.textContent = headers[index] || `العمود ${index + 1}`;
                
                const value = document.createElement('div');
                value.className = 'mobile-card-value';
                value.innerHTML = cell.innerHTML;
                
                cardRow.appendChild(label);
                cardRow.appendChild(value);
                card.appendChild(cardRow);
            });
            
            cardsContainer.appendChild(card);
        });
        
        table.parentNode.appendChild(cardsContainer);
        table.classList.add('table-mobile-stack', 'd-mobile-none');
    }

    // إزالة بطاقات الجدول
    function removeMobileCardsForTable(table) {
        const cardsContainer = table.parentNode.querySelector('.table-mobile-cards');
        if (cardsContainer) {
            cardsContainer.remove();
        }
        table.classList.remove('table-mobile-stack', 'd-mobile-none');
    }

    // تحسين النماذج للأجهزة المحمولة
    function optimizeFormsForMobile() {
        if (currentBreakpoint === 'xs' || currentBreakpoint === 'sm') {
            // تحويل مجموعات الأزرار إلى عمودي
            const btnGroups = document.querySelectorAll('.btn-group:not(.btn-group-vertical)');
            btnGroups.forEach(group => {
                group.classList.add('btn-group-vertical');
                group.style.width = '100%';
            });
            
            // تحسين النوافذ المنبثقة
            const modals = document.querySelectorAll('.modal-dialog');
            modals.forEach(modal => {
                modal.style.margin = '0.5rem';
                modal.style.maxWidth = 'calc(100% - 1rem)';
            });
        } else {
            // إعادة تعيين للشاشات الكبيرة
            const btnGroups = document.querySelectorAll('.btn-group-vertical');
            btnGroups.forEach(group => {
                group.classList.remove('btn-group-vertical');
                group.style.width = '';
            });
        }
    }

    // معالج تغيير حجم النافذة
    function handleResize() {
        const newBreakpoint = getCurrentBreakpoint();

        if (newBreakpoint !== currentBreakpoint) {
            const oldBreakpoint = currentBreakpoint;
            currentBreakpoint = newBreakpoint;
            console.log('Breakpoint changed from', oldBreakpoint, 'to:', currentBreakpoint);

            // تحديث حالة الشريط الجانبي
            updateSidebarState();

            // إذا انتقلنا من جهاز محمول إلى سطح المكتب، إخفاء الشريط الجانبي
            if ((oldBreakpoint === 'xs' || oldBreakpoint === 'sm' || oldBreakpoint === 'md') &&
                (currentBreakpoint === 'lg' || currentBreakpoint === 'xl' || currentBreakpoint === 'xxl')) {
                hideSidebar();
            }

            createMobileTableCards();
            optimizeFormsForMobile();

            // إطلاق حدث مخصص
            window.dispatchEvent(new CustomEvent('breakpointChange', {
                detail: {
                    breakpoint: currentBreakpoint,
                    oldBreakpoint: oldBreakpoint
                }
            }));
        }
    }

    // تهيئة النظام
    function init() {
        initSidebar();
        makeTablesResponsive();
        createMobileTableCards();
        optimizeFormsForMobile();
        
        // إعداد معالج تغيير حجم النافذة
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleResize, 150);
        });
        
        console.log('Responsive system initialized');
    }

    // تشغيل النظام
    init();

    // إتاحة الوظائف للاستخدام الخارجي
    window.ResponsiveSystem = {
        getCurrentBreakpoint,
        toggleSidebar,
        showSidebar,
        hideSidebar,
        makeTablesResponsive,
        createMobileTableCards
    };
});

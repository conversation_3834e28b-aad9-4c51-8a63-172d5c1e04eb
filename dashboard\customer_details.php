<?php
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/customer_manager.php';
require_once __DIR__ . '/../includes/activity_helper.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('customers.view');

$db              = new Database();
$customerManager = new CustomerManager($db);
$current_user    = $auth->getCurrentUser();

$customerId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
if ($customerId <= 0) {
    echo '<div class="alert alert-danger m-4">معرّف العميل غير صالح</div>';
    exit;
}

$customer = $customerManager->getCustomerById($customerId);
if (!$customer) {
    echo '<div class="alert alert-danger m-4">العميل غير موجود</div>';
    exit;
}

// تسجيل عملية عرض تفاصيل العميل
ActivityHelper::logView(
    'customers',
    'عرض تفاصيل العميل: ' . $customer['full_name'],
    ['customer_id' => $customerId]
);

// Handle document upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_document'])) {
    if ($auth->hasPermission('customers.edit')) {
        $documentType = $_POST['document_type'] ?? '';
        $uploadedFile = $_FILES['document_file'] ?? null;
        
        if ($uploadedFile && $uploadedFile['error'] === UPLOAD_ERR_OK) {
            $uploadDir = __DIR__ . '/../uploads/documents/' . $customerId;
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            $fileName = time() . '_' . basename($uploadedFile['name']);
            $filePath = $uploadDir . '/' . $fileName;
            
            if (move_uploaded_file($uploadedFile['tmp_name'], $filePath)) {
                $relativeFilePath = 'uploads/documents/' . $customerId . '/' . $fileName;
                
                $stmt = $db::getConnection()->prepare('INSERT INTO documents (customer_id, type, file_path, uploaded_at) VALUES (?, ?, ?, NOW())');
                $stmt->bind_param('iss', $customerId, $documentType, $relativeFilePath);
                
                if ($stmt->execute()) {
                    $successMessage = 'تم رفع المستند بنجاح';
                } else {
                    $errorMessage = 'خطأ في حفظ المستند في قاعدة البيانات';
                }
                $stmt->close();
            } else {
                $errorMessage = 'خطأ في رفع الملف';
            }
        } else {
            $errorMessage = 'يرجى اختيار ملف صالح';
        }
    } else {
        $errorMessage = 'ليس لديك صلاحية لرفع المستندات';
    }
}

// Handle document deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_document'])) {
    if ($auth->hasPermission('customers.edit')) {
        $docId = (int)$_POST['delete_document'];
        
        // Get document info first
        $stmt = $db::getConnection()->prepare('SELECT * FROM documents WHERE id = ? AND customer_id = ?');
        $stmt->bind_param('ii', $docId, $customerId);
        $stmt->execute();
        $result = $stmt->get_result();
        $document = $result->fetch_assoc();
        $stmt->close();
        
        if ($document) {
            // Delete file from filesystem
            $filePath = __DIR__ . '/../' . $document['file_path'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            
            // Delete from database
            $stmt = $db::getConnection()->prepare('DELETE FROM documents WHERE id = ?');
            $stmt->bind_param('i', $docId);
            if ($stmt->execute()) {
                $successMessage = 'تم حذف المستند بنجاح';
            } else {
                $errorMessage = 'خطأ في حذف المستند';
            }
            $stmt->close();
        } else {
            $errorMessage = 'المستند غير موجود';
        }
    } else {
        $errorMessage = 'ليس لديك صلاحية لحذف المستندات';
    }
}

// Handle document status actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['verify_document'])) {
    if ($auth->hasPermission('customers.edit')) {
        $docId = (int)$_POST['verify_document'];
        if ($customerManager->updateDocumentStatus($docId, 'verified', $current_user['id'])) {
            $successMessage = 'تم التحقق من المستند بنجاح';
        } else {
            $errorMessage = 'تعذر تحديث حالة المستند';
        }
    }
}
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reject_document'])) {
    if ($auth->hasPermission('customers.edit')) {
        $docId = (int)$_POST['reject_document'];
        if ($customerManager->updateDocumentStatus($docId, 'rejected', $current_user['id'])) {
            $successMessage = 'تم رفض المستند';
        } else {
            $errorMessage = 'تعذر تحديث حالة المستند';
        }
    }
}

// Get customer data
$customerDocuments = $customerManager->getCustomerDocuments($customerId);
$customerOperations = $customerManager->getCustomerOperations($customerId, 1000); // Get all operations
$customerStats = $customerManager->getCustomerStats($customerId);

$pageTitle = 'بيانات العميل: ' . $customer['full_name'];
include __DIR__ . '/../includes/header.php';
include __DIR__ . '/../includes/sidebar.php';
?>

<main class="content p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">بيانات العميل</h2>
        <div>
            <?php if ($auth->hasPermission('customers.edit')): ?>
                <a href="edit_customer.php?id=<?php echo $customerId; ?>" class="btn btn-primary">تعديل</a>
            <?php endif; ?>
            <a href="customers.php" class="btn btn-secondary">عودة للقائمة</a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($successMessage)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $successMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($errorMessage)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $errorMessage; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Customer Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary"><?php echo $customerStats['total_exchanges']; ?></h5>
                    <p class="card-text text-muted">عمليات الصرافة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success"><?php echo $customerStats['total_transfers']; ?></h5>
                    <p class="card-text text-muted">الحوالات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info"><?php echo $customerStats['total_documents']; ?></h5>
                    <p class="card-text text-muted">المستندات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">$<?php echo number_format($customerStats['total_volume'], 2); ?></h5>
                    <p class="card-text text-muted">إجمالي الحجم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs Navigation -->
    <ul class="nav nav-tabs" id="customerTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="info-tab" data-bs-toggle="tab" data-bs-target="#info" type="button" role="tab">
                <i class="fas fa-user me-2"></i>المعلومات الأساسية
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab">
                <i class="fas fa-file-alt me-2"></i>المستندات
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="operations-tab" data-bs-toggle="tab" data-bs-target="#operations" type="button" role="tab">
                <i class="fas fa-history me-2"></i>سجل العمليات
            </button>
        </li>
    </ul>

    <!-- Tabs Content -->
    <div class="tab-content mt-4" id="customerTabsContent">
        <!-- Customer Info Tab -->
        <div class="tab-pane fade show active" id="info" role="tabpanel">
            <div class="card">
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-md-3">الاسم الكامل</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['full_name']); ?></dd>
                        <dt class="col-md-3">رقم الهوية</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['id_number']); ?></dd>
                        <dt class="col-md-3">نوع الهوية</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['id_type']); ?></dd>
                        <dt class="col-md-3">الجنسية</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['nationality']); ?></dd>
                        <dt class="col-md-3">تاريخ الميلاد</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['birth_date']); ?></dd>
                        <dt class="col-md-3">الهاتف</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['phone']); ?></dd>
                        <dt class="col-md-3">البريد الإلكتروني</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['email']); ?></dd>
                        <dt class="col-md-3">العنوان</dt><dd class="col-md-9"><?php echo nl2br(htmlspecialchars($customer['address'])); ?></dd>
                        <dt class="col-md-3">المهنة</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['occupation']); ?></dd>
                        <dt class="col-md-3">مستوى المخاطر</dt>
                        <dd class="col-md-9">
                            <span class="badge bg-<?php echo $customer['risk_level'] === 'high' ? 'danger' : ($customer['risk_level'] === 'medium' ? 'warning' : 'success'); ?>">
                                <?php echo htmlspecialchars($customer['risk_level']); ?>
                            </span>
                        </dd>
                        <dt class="col-md-3">حالة KYC</dt>
                        <dd class="col-md-9">
                            <span class="badge bg-<?php echo $customer['kyc_status'] === 'verified' ? 'success' : ($customer['kyc_status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                <?php echo htmlspecialchars($customer['kyc_status']); ?>
                            </span>
                        </dd>
                        <dt class="col-md-3">الحالة</dt>
                        <dd class="col-md-9">
                            <span class="badge bg-<?php echo $customer['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                <?php echo htmlspecialchars($customer['status']); ?>
                            </span>
                        </dd>
                        <dt class="col-md-3">تاريخ الإنشاء</dt><dd class="col-md-9"><?php echo htmlspecialchars($customer['created_at']); ?></dd>
                        <dt class="col-md-3">آخر نشاط</dt><dd class="col-md-9"><?php echo $customerStats['last_activity'] ? htmlspecialchars($customerStats['last_activity']) : 'لا يوجد'; ?></dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Documents Tab -->
        <div class="tab-pane fade" id="documents" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المستندات</h5>
                    <?php if ($auth->hasPermission('customers.edit')): ?>
                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#uploadDocumentModal">
                            <i class="fas fa-upload me-2"></i>رفع مستند
                        </button>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($customerDocuments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مستندات مرفوعة</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>نوع المستند</th>
                                        <th>تاريخ الرفع</th>
                                        <th>الحالة</th>
                                        <th>المحقق</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($customerDocuments as $doc): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                <?php echo htmlspecialchars($doc['type']); ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($doc['uploaded_at'])); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $doc['status'] === 'verified' ? 'success' : ($doc['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                    <?php echo htmlspecialchars($doc['status'] ?? 'pending'); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($doc['verifier_name'] ?? 'غير محدد'); ?></td>
                                            <td>
                                                <?php if ($doc['status'] === 'pending'): ?>
                                                    <button class="btn btn-sm btn-success me-1" onclick="verifyDocument(<?php echo $doc['id']; ?>)">
                                                        <i class="fas fa-check"></i> تأكيد
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" onclick="rejectDocument(<?php echo $doc['id']; ?>)">
                                                        <i class="fas fa-times"></i> رفض
                                                    </button>
                                                <?php endif; ?>
                                                <a href="../<?php echo htmlspecialchars($doc['file_path']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if ($auth->hasPermission('customers.edit')): ?>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteDocument(<?php echo $doc['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Operations Tab -->
        <div class="tab-pane fade" id="operations" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">سجل العمليات</h5>
                    <?php if (!empty($customerOperations)): ?>
                        <button class="btn btn-sm btn-outline-danger" onclick="exportOperationsToPdf()">
                            <i class="fas fa-file-pdf me-1"></i>
                            تصدير PDF
                        </button>
                    <?php endif; ?>
                </div>
                <div class="card-body">
                    <?php if (empty($customerOperations)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد عمليات مسجلة</p>
                        </div>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($customerOperations as $operation): ?>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-<?php echo $operation['operation_type'] === 'exchange' ? 'primary' : 'success'; ?>">
                                        <i class="fas fa-<?php echo $operation['operation_type'] === 'exchange' ? 'exchange-alt' : 'paper-plane'; ?>"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php if ($operation['operation_type'] === 'exchange'): ?>
                                                        عملية صرافة - <?php echo htmlspecialchars($operation['from_currency_name'] ?? 'غير محدد'); ?> إلى <?php echo htmlspecialchars($operation['to_currency_name'] ?? 'غير محدد'); ?>
                                                    <?php else: ?>
                                                        حوالة مالية - <?php echo htmlspecialchars($operation['transfer_type'] ?? 'غير محدد'); ?>
                                                        <small class="text-muted">(رقم: <?php echo htmlspecialchars($operation['transaction_number'] ?? ''); ?>)</small>
                                                    <?php endif; ?>
                                                </h6>
                                                <p class="text-muted mb-1">
                                                    <?php if ($operation['operation_type'] === 'exchange'): ?>
                                                        المبلغ: <?php echo number_format($operation['amount_from'], 2); ?> <?php echo htmlspecialchars($operation['from_currency_symbol'] ?? ''); ?> 
                                                        → <?php echo number_format($operation['amount_to'], 2); ?> <?php echo htmlspecialchars($operation['to_currency_symbol'] ?? ''); ?>
                                                    <?php else: ?>
                                                        المبلغ: <?php echo number_format($operation['sending_amount'], 2); ?> <?php echo htmlspecialchars($operation['sending_currency_symbol'] ?? ''); ?> 
                                                        → <?php echo number_format($operation['receiving_amount'], 2); ?> <?php echo htmlspecialchars($operation['receiving_currency_symbol'] ?? ''); ?>
                                                        <br>
                                                        <small>من: <?php echo htmlspecialchars($operation['sender_name'] ?? ''); ?> إلى: <?php echo htmlspecialchars($operation['beneficiary_name'] ?? ''); ?></small>
                                                    <?php endif; ?>
                                                </p>
                                                <small class="text-muted">
                                                    بواسطة: <?php echo htmlspecialchars($operation['created_by_name'] ?? 'غير محدد'); ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">
                                                    <?php echo date('Y-m-d H:i', strtotime($operation['created_at'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Document Modal -->
    <div class="modal fade" id="uploadDocumentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">رفع مستند جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="document_type" class="form-label">نوع المستند</label>
                            <select name="document_type" id="document_type" class="form-select" required>
                                <option value="">اختر نوع المستند</option>
                                <option value="identity">هوية شخصية</option>
                                <option value="passport">جواز سفر</option>
                                <option value="address_proof">إثبات عنوان</option>
                                <option value="income_proof">إثبات دخل</option>
                                <option value="bank_statement">كشف حساب بنكي</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="document_file" class="form-label">الملف</label>
                            <input type="file" name="document_file" id="document_file" class="form-control" accept=".pdf,.jpg,.jpeg,.png" required>
                            <div class="form-text">الملفات المسموحة: PDF, JPG, JPEG, PNG (أقصى حجم: 10MB)</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="upload_document" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>رفع المستند
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

<style>
/* Timeline styles */
.timeline {
    position: relative;
    padding-left: 3rem;
}

.timeline-item {
    position: relative;
    padding-bottom: 2rem;
    border-left: 2px solid #e9ecef;
}

.timeline-item:last-child {
    border-left: none;
}

.timeline-marker {
    position: absolute;
    left: -1rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.timeline-content {
    margin-left: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

/* Stats cards hover effect */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

/* Tab content styling */
.tab-content {
    min-height: 400px;
}

/* Document table styling */
.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badge improvements */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Modal improvements */
.modal-dialog {
    max-width: 500px;
}

/* File upload styling */
.form-control[type="file"] {
    padding: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .timeline {
        padding-left: 2rem;
    }
    
    .timeline-marker {
        left: -0.75rem;
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .timeline-content {
        margin-left: 0.5rem;
    }
}
</style>

<script>
// Document management functions
function deleteDocument(docId) {
    if (confirm('هل أنت متأكد من حذف هذا المستند؟')) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';
        
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'delete_document';
        input.value = docId;
        
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

// File upload validation
document.getElementById('document_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileSize = file.size / (1024 * 1024); // Size in MB
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        
        if (fileSize > 10) {
            alert('حجم الملف يجب أن يكون أقل من 10 ميجابايت');
            e.target.value = '';
            return;
        }
        
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. يرجى اختيار ملف PDF أو صورة');
            e.target.value = '';
            return;
        }
    }
});

// Auto-refresh page after document upload
<?php if (isset($successMessage)): ?>
    setTimeout(function() {
        window.location.reload();
    }, 2000);
<?php endif; ?>

// Tab switching with URL hash
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in URL
    const hash = window.location.hash;
    if (hash) {
        const tab = document.querySelector(`button[data-bs-target="${hash}"]`);
        if (tab) {
            tab.click();
        }
    }
    
    // Update URL hash when tab changes
    document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(function(tab) {
        tab.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('data-bs-target');
            history.replaceState(null, null, target);
        });
    });
});

// Statistics animation
document.addEventListener('DOMContentLoaded', function() {
    const statCards = document.querySelectorAll('.card-title');
    statCards.forEach(function(card) {
        const finalValue = parseInt(card.textContent.replace(/[^\d]/g, ''));
        if (finalValue > 0) {
            animateCounter(card, finalValue);
        }
    });
});

function animateCounter(element, finalValue) {
    const duration = 1000; // 1 second
    const startValue = 0;
    const increment = finalValue / (duration / 16); // 60fps
    let current = startValue;
    
    const timer = setInterval(function() {
        current += increment;
        if (current >= finalValue) {
            current = finalValue;
            clearInterval(timer);
        }
        
        // Format with original prefix/suffix
        const originalText = element.textContent;
        const prefix = originalText.match(/^[^\d]*/)[0];
        const suffix = originalText.match(/[^\d]*$/)[0];
        element.textContent = prefix + Math.floor(current).toLocaleString() + suffix;
    }, 16);
}

function verifyDocument(id) {
    const form = document.createElement('form');
    form.method = 'POST';
    const inp = document.createElement('input');
    inp.type = 'hidden';
    inp.name = 'verify_document';
    inp.value = id;
    form.appendChild(inp);
    document.body.appendChild(form);
    form.submit();
}
function rejectDocument(id) {
    const form = document.createElement('form');
    form.method = 'POST';
    const inp = document.createElement('input');
    inp.type = 'hidden';
    inp.name = 'reject_document';
    inp.value = id;
    form.appendChild(inp);
    document.body.appendChild(form);
    form.submit();
}
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script>
function exportOperationsToPdf() {
    const customerId = <?php echo $customerId; ?>;
    const customerName = '<?php echo addslashes($customer['full_name']); ?>';
    
    fetch('ajax/get_customer_operations.php?customer_id=' + customerId)
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert(data.error || 'Error fetching operations');
                return;
            }
            
            // Create HTML content for PDF
            let htmlContent = `
                <div dir="rtl" style="font-family: 'Arial', sans-serif; padding: 20px; direction: rtl;">
                    <h1 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">سجل عمليات العميل</h1>
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h3>${customerName}</h3>
                        <p>رقم العميل: ${customerId}</p>
                        <p>تاريخ التصدير: ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>
                    
                    <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                        <thead>
                            <tr style="background-color: #3498db; color: white;">
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">ID</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">النوع</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">التاريخ</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">المبلغ</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">العملة</th>
                                <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">التفاصيل</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            data.operations.forEach(op => {
                const isExchange = op.operation_type === 'exchange';
                const operationType = isExchange ? 'صرافة' : 'تحويل';
                const amount = isExchange ? op.amount_from : op.amount;
                const currency = isExchange ? 
                    (op.from_currency_symbol || op.from_currency_name) : 
                    (op.sending_currency_symbol || op.sending_currency_name);
                const details = isExchange ? 
                    `${op.from_currency_symbol || ''} → ${op.to_currency_symbol || ''}` : 
                    `${op.sending_currency_symbol || ''} → ${op.receiving_currency_symbol || ''}`;
                htmlContent += `
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${op.id}</td>
                        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${operationType}</td>
                        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${new Date(op.created_at).toLocaleDateString('ar-SA')}</td>
                        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${amount}</td>
                        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${currency}</td>
                        <td style="border: 1px solid #ddd; padding: 6px; text-align: center;">${details}</td>
                    </tr>
                `;
            });
            
            htmlContent += `
                        </tbody>
                    </table>
                    
                    <div style="margin-top: 20px; text-align: right;">
                        <p><strong>إجمالي العمليات: ${data.operations.length}</strong></p>
                    </div>
                </div>
            `;
            
            // Create element and generate PDF
            const element = document.createElement('div');
            element.innerHTML = htmlContent;
            document.body.appendChild(element);
            
            const opt = {
                margin: 1,
                filename: `customer_${customerId}_operations_${new Date().toISOString().slice(0,10)}.pdf`,
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
            };
            
            html2pdf().set(opt).from(element).save().then(() => {
                document.body.removeChild(element);
            });
        })
        .catch(() => alert('خطأ في الاتصال بالخادم'));
}
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?> 
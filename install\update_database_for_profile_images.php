<?php
/**
 * Update database to add profile_image column to users table
 * Run this file once to add the profile_image column
 */

require_once __DIR__ . '/../includes/database.php';

try {
    $db = new Database();
    $connection = $db::getConnection();
    
    // Check if column already exists
    $result = $connection->query("SHOW COLUMNS FROM users LIKE 'profile_image'");
    if ($result->num_rows > 0) {
        echo "عمود profile_image موجود بالفعل في جدول users.\n";
        exit;
    }
    
    // Add profile_image column
    $sql = "ALTER TABLE users ADD COLUMN profile_image VARCHAR(255) NULL AFTER phone";
    if ($connection->query($sql)) {
        echo "تم إضافة عمود profile_image بنجاح إلى جدول users.\n";
    } else {
        echo "خطأ في إضافة العمود: " . $connection->error . "\n";
    }
    
    // Create uploads directory
    $uploadDir = __DIR__ . '/../uploads/profiles/';
    if (!is_dir($uploadDir)) {
        if (mkdir($uploadDir, 0755, true)) {
            echo "تم إنشاء مجلد uploads/profiles/ بنجاح.\n";
        } else {
            echo "خطأ في إنشاء مجلد uploads/profiles/.\n";
        }
    } else {
        echo "مجلد uploads/profiles/ موجود بالفعل.\n";
    }
    
    echo "تم تحديث قاعدة البيانات بنجاح!\n";
    
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . "\n";
}
?> 
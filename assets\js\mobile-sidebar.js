/**
 * Mobile Sidebar Enhancement
 * تحسينات الشريط الجانبي للأجهزة المحمولة
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Mobile Sidebar Enhancement loaded');

    let sidebarState = {
        isVisible: false,
        isMobile: false
    };

    // فحص ما إذا كان الجهاز محمول
    function isMobileDevice() {
        return window.innerWidth < 992;
    }

    // تحديث حالة الجهاز
    function updateDeviceState() {
        sidebarState.isMobile = isMobileDevice();
        console.log('Device state updated - isMobile:', sidebarState.isMobile);
    }

    // إنشاء أو تحديث زر التبديل
    function createOrUpdateToggleButton() {
        let toggleBtn = document.querySelector('.sidebar-toggle-btn');
        
        if (!toggleBtn) {
            toggleBtn = document.createElement('button');
            toggleBtn.className = 'btn btn-primary sidebar-toggle-btn';
            toggleBtn.setAttribute('aria-label', 'تبديل القائمة الجانبية');
            toggleBtn.setAttribute('type', 'button');
            document.body.appendChild(toggleBtn);
        }

        // تحديث محتوى الزر
        toggleBtn.innerHTML = sidebarState.isVisible ? 
            '<i class="fas fa-times"></i>' : 
            '<i class="fas fa-bars"></i>';

        // إظهار أو إخفاء الزر حسب حجم الشاشة
        if (sidebarState.isMobile) {
            toggleBtn.style.display = 'flex';
        } else {
            toggleBtn.style.display = 'none';
        }

        return toggleBtn;
    }

    // إنشاء طبقة التغطية
    function createOverlay() {
        let overlay = document.querySelector('.sidebar-overlay');
        
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);
            
            // إضافة مستمع النقر للإغلاق
            overlay.addEventListener('click', hideSidebar);
        }
        
        return overlay;
    }

    // إزالة طبقة التغطية
    function removeOverlay() {
        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.classList.remove('show');
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    }

    // إظهار الشريط الجانبي
    function showSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar || !sidebarState.isMobile) return;

        console.log('Showing sidebar');
        
        sidebar.classList.add('show');
        sidebarState.isVisible = true;
        
        // إنشاء طبقة التغطية
        const overlay = createOverlay();
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);
        
        // تحديث زر التبديل
        createOrUpdateToggleButton();
        
        // منع التمرير في الخلفية
        document.body.style.overflow = 'hidden';
    }

    // إخفاء الشريط الجانبي
    function hideSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) return;

        console.log('Hiding sidebar');
        
        sidebar.classList.remove('show');
        sidebarState.isVisible = false;
        
        // إزالة طبقة التغطية
        removeOverlay();
        
        // تحديث زر التبديل
        createOrUpdateToggleButton();
        
        // إعادة تمكين التمرير
        document.body.style.overflow = '';
    }

    // تبديل حالة الشريط الجانبي
    function toggleSidebar() {
        if (!sidebarState.isMobile) return;
        
        if (sidebarState.isVisible) {
            hideSidebar();
        } else {
            showSidebar();
        }
    }

    // إعداد الأحداث
    function setupEvents() {
        // زر التبديل
        document.addEventListener('click', function(e) {
            if (e.target.closest('.sidebar-toggle-btn')) {
                e.preventDefault();
                e.stopPropagation();
                toggleSidebar();
            }
        });

        // إغلاق عند النقر خارج الشريط الجانبي
        document.addEventListener('click', function(e) {
            if (!sidebarState.isMobile || !sidebarState.isVisible) return;
            
            const sidebar = document.getElementById('sidebar');
            const toggleBtn = document.querySelector('.sidebar-toggle-btn');
            
            if (sidebar && !sidebar.contains(e.target) && 
                toggleBtn && !toggleBtn.contains(e.target)) {
                hideSidebar();
            }
        });

        // إغلاق عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && sidebarState.isMobile && sidebarState.isVisible) {
                hideSidebar();
            }
        });

        // مراقبة تغيير حجم النافذة
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                const wasMobile = sidebarState.isMobile;
                updateDeviceState();
                
                // إذا انتقلنا من محمول إلى سطح المكتب
                if (wasMobile && !sidebarState.isMobile) {
                    hideSidebar();
                }
                
                createOrUpdateToggleButton();
            }, 250);
        });
    }

    // تهيئة النظام
    function init() {
        updateDeviceState();
        
        // إخفاء الشريط الجانبي على الأجهزة المحمولة عند التحميل
        if (sidebarState.isMobile) {
            hideSidebar();
        }
        
        createOrUpdateToggleButton();
        setupEvents();
        
        console.log('Mobile sidebar system initialized');
    }

    // تشغيل النظام
    init();

    // إتاحة الوظائف للاستخدام الخارجي
    window.MobileSidebar = {
        show: showSidebar,
        hide: hideSidebar,
        toggle: toggleSidebar,
        isVisible: () => sidebarState.isVisible,
        isMobile: () => sidebarState.isMobile
    };
});

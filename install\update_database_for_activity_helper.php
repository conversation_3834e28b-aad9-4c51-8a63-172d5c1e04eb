<?php
/**
 * تحديث قاعدة البيانات لدعم نظام ActivityHelper
 * Database Update Script for ActivityHelper Integration
 * 
 * هذا الملف يقوم بتحديث قاعدة البيانات لإضافة جدول system_activity_logs
 * والصلاحيات المطلوبة لنظام تسجيل العمليات الشامل
 */

// منع الوصول المباشر
if (!defined('INSTALL_MODE')) {
    define('INSTALL_MODE', true);
}

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';

// إعداد الترميز
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تحديث قاعدة البيانات - نظام ActivityHelper</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        h1, h2, h3 { color: #333; }
        .progress { width: 100%; background: #e9ecef; border-radius: 4px; margin: 10px 0; }
        .progress-bar { height: 20px; background: #007bff; border-radius: 4px; text-align: center; color: white; line-height: 20px; }
    </style>
</head>
<body>
<div class='container'>";

echo "<h1>🔄 تحديث قاعدة البيانات لنظام ActivityHelper</h1>";
echo "<p>هذا التحديث سيقوم بإضافة جدول <code>system_activity_logs</code> والصلاحيات المطلوبة.</p>";

$steps = [
    'اختبار الاتصال بقاعدة البيانات',
    'فحص الجداول الموجودة',
    'إنشاء جدول system_activity_logs',
    'إضافة الفهارس المطلوبة',
    'إضافة الصلاحيات الجديدة',
    'ربط الصلاحيات بدور الأدمن',
    'إدراج بيانات تجريبية',
    'التحقق من التحديث'
];

$currentStep = 0;
$totalSteps = count($steps);

function showProgress($current, $total, $message) {
    $percentage = ($current / $total) * 100;
    echo "<div class='progress'>";
    echo "<div class='progress-bar' style='width: {$percentage}%'>{$current}/{$total} - {$message}</div>";
    echo "</div>";
}

function showStep($stepNumber, $title, $status = 'info') {
    $icons = ['info' => 'ℹ️', 'success' => '✅', 'error' => '❌', 'warning' => '⚠️'];
    $icon = $icons[$status] ?? 'ℹ️';
    echo "<div class='step'>";
    echo "<h3>{$icon} الخطوة {$stepNumber}: {$title}</h3>";
}

function endStep($message, $status = 'success') {
    echo "<div class='{$status}'>{$message}</div>";
    echo "</div>";
}

try {
    // الخطوة 1: اختبار الاتصال
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    $db = new Database();
    $conn = Database::getConnection();
    
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    endStep('تم الاتصال بقاعدة البيانات بنجاح');

    // الخطوة 2: فحص الجداول الموجودة
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    $result = $conn->query("SHOW TABLES LIKE 'system_activity_logs'");
    $tableExists = $result && $result->num_rows > 0;
    
    if ($tableExists) {
        endStep('جدول system_activity_logs موجود بالفعل', 'warning');
    } else {
        endStep('جدول system_activity_logs غير موجود - سيتم إنشاؤه');
    }

    // الخطوة 3: إنشاء جدول system_activity_logs
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    if (!$tableExists) {
        $createTableSQL = file_get_contents(__DIR__ . '/create_system_activity_logs.sql');
        
        if ($createTableSQL === false) {
            throw new Exception('فشل في قراءة ملف إنشاء الجدول');
        }
        
        // تقسيم الاستعلامات
        $queries = array_filter(array_map('trim', explode(';', $createTableSQL)));
        
        foreach ($queries as $query) {
            if (!empty($query) && !preg_match('/^--/', $query)) {
                if (!$conn->query($query)) {
                    throw new Exception('فشل في تنفيذ الاستعلام: ' . $conn->error);
                }
            }
        }
        
        endStep('تم إنشاء جدول system_activity_logs بنجاح');
    } else {
        endStep('تم تخطي إنشاء الجدول - موجود بالفعل', 'info');
    }

    // الخطوة 4: إضافة الفهارس
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    // التحقق من وجود الفهارس وإضافتها إذا لم تكن موجودة
    $indexes = [
        'idx_comprehensive_search' => 'CREATE INDEX idx_comprehensive_search ON system_activity_logs (module, action_type, created_at, user_id)',
        'idx_description_search' => 'CREATE FULLTEXT INDEX idx_description_search ON system_activity_logs (description)'
    ];
    
    foreach ($indexes as $indexName => $indexSQL) {
        $checkIndex = $conn->query("SHOW INDEX FROM system_activity_logs WHERE Key_name = '$indexName'");
        if (!$checkIndex || $checkIndex->num_rows == 0) {
            if ($conn->query($indexSQL)) {
                echo "<div class='success'>تم إنشاء الفهرس: $indexName</div>";
            } else {
                echo "<div class='warning'>تحذير: فشل في إنشاء الفهرس $indexName: " . $conn->error . "</div>";
            }
        } else {
            echo "<div class='info'>الفهرس $indexName موجود بالفعل</div>";
        }
    }
    
    endStep('تم التحقق من الفهارس');

    // الخطوة 5: إضافة الصلاحيات
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    $permissions = [
        ['system_activity_logs.view', 'عرض سجل العمليات الشامل للنظام', 'system'],
        ['system_activity_logs.export', 'تصدير سجل العمليات', 'system'],
        ['system_activity_logs.details', 'عرض تفاصيل سجل العمليات', 'system'],
        ['system_activity_logs.admin_only', 'الوصول الكامل لسجل العمليات (أدمن فقط)', 'system']
    ];
    
    $addedPermissions = 0;
    foreach ($permissions as $perm) {
        $stmt = $conn->prepare("INSERT IGNORE INTO permissions (name, description, module, created_at) VALUES (?, ?, ?, NOW())");
        $stmt->bind_param('sss', $perm[0], $perm[1], $perm[2]);
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $addedPermissions++;
        }
        $stmt->close();
    }
    
    endStep("تم إضافة $addedPermissions صلاحية جديدة");

    // الخطوة 6: ربط الصلاحيات بدور الأدمن
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    $linkSQL = "INSERT IGNORE INTO role_permissions (role_id, permission_id) 
                SELECT 1, id FROM permissions 
                WHERE name LIKE 'system_activity_logs.%'";
    
    if ($conn->query($linkSQL)) {
        $linkedPermissions = $conn->affected_rows;
        endStep("تم ربط $linkedPermissions صلاحية بدور الأدمن");
    } else {
        endStep('فشل في ربط الصلاحيات: ' . $conn->error, 'error');
    }

    // الخطوة 7: إدراج بيانات تجريبية
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    $testDataSQL = "INSERT INTO system_activity_logs (
        user_id, username, user_full_name, user_role,
        action_type, module, operation, description,
        target_table, additional_data, ip_address, status
    ) VALUES (
        1, 'admin', 'مدير النظام', 'مدير',
        'SYSTEM', 'system', 'database_update', 'تحديث قاعدة البيانات لدعم نظام ActivityHelper',
        'system_activity_logs', JSON_OBJECT('update_version', '1.0', 'update_date', NOW()),
        ?, 'SUCCESS'
    )";
    
    $stmt = $conn->prepare($testDataSQL);
    $userIP = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    $stmt->bind_param('s', $userIP);
    
    if ($stmt->execute()) {
        endStep('تم إدراج سجل تجريبي بنجاح');
    } else {
        endStep('تحذير: فشل في إدراج السجل التجريبي', 'warning');
    }
    $stmt->close();

    // الخطوة 8: التحقق النهائي
    showProgress(++$currentStep, $totalSteps, $steps[$currentStep-1]);
    showStep($currentStep, $steps[$currentStep-1]);
    
    // فحص الجدول
    $tableCheck = $conn->query("SELECT COUNT(*) as count FROM system_activity_logs");
    $recordCount = $tableCheck->fetch_assoc()['count'];
    
    // فحص الصلاحيات
    $permCheck = $conn->query("SELECT COUNT(*) as count FROM permissions WHERE module = 'system'");
    $permCount = $permCheck->fetch_assoc()['count'];
    
    echo "<div class='success'>";
    echo "<h4>✅ تم التحديث بنجاح!</h4>";
    echo "<ul>";
    echo "<li>جدول system_activity_logs: $recordCount سجل</li>";
    echo "<li>صلاحيات النظام: $permCount صلاحية</li>";
    echo "<li>تاريخ التحديث: " . date('Y-m-d H:i:s') . "</li>";
    echo "</ul>";
    echo "</div>";
    
    endStep('تم التحقق من التحديث بنجاح');

} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ خطأ في التحديث</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<div class='warning'>";
    echo "<h4>⚠️ خطوات الإصلاح المقترحة:</h4>";
    echo "<ol>";
    echo "<li>تأكد من صحة إعدادات قاعدة البيانات في config/config.php</li>";
    echo "<li>تأكد من وجود صلاحيات الكتابة في قاعدة البيانات</li>";
    echo "<li>تأكد من وجود الجداول الأساسية (users, permissions, roles)</li>";
    echo "<li>جرب تشغيل الاستعلامات يدوياً من phpMyAdmin</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<div class='info'>";
echo "<h4>📋 الخطوات التالية:</h4>";
echo "<ol>";
echo "<li>تأكد من عمل صفحة سجل العمليات: <code>dashboard/system_activity_logs.php</code></li>";
echo "<li>اختبر تسجيل العمليات في صفحات مختلفة من النظام</li>";
echo "<li>راجع ملف التوثيق: <code>ACTIVITY_HELPER_INTEGRATION_README.md</code></li>";
echo "<li>قم بعمل نسخة احتياطية من قاعدة البيانات</li>";
echo "</ol>";
echo "</div>";

echo "</div></body></html>";
?>

/* Enhanced Dashboard Styles for TrustPlus */

/* ===== ENHANCED DASHBOARD LAYOUT ===== */
.dashboard-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.dashboard-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin: 0 auto;
    max-width: 1400px;
}

/* ===== ENHANCED PAGE HEADER ===== */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    color: white;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.dashboard-header-content {
    position: relative;
    z-index: 2;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.dashboard-date {
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    font-weight: 500;
}

/* ===== ENHANCED STATISTICS CARDS ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stats-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-card.primary::before { background: linear-gradient(90deg, #667eea, #764ba2); }
.stats-card.success::before { background: linear-gradient(90deg, #56ab2f, #a8e6cf); }
.stats-card.warning::before { background: linear-gradient(90deg, #f093fb, #f5576c); }
.stats-card.info::before { background: linear-gradient(90deg, #4facfe, #00f2fe); }

.stats-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.stats-icon {
    width: 70px;
    height: 70px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.stats-icon.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
.stats-icon.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stats-icon.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

.stats-details {
    flex: 1;
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
    line-height: 1;
}

.stats-label {
    font-size: 1rem;
    color: #718096;
    margin-bottom: 10px;
    font-weight: 500;
}

.stats-change {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 5px 12px;
    border-radius: 15px;
    width: fit-content;
}

.stats-change.positive {
    background: rgba(72, 187, 120, 0.1);
    color: #48bb78;
}

.stats-change.negative {
    background: rgba(245, 101, 101, 0.1);
    color: #f56565;
}

/* ===== ENHANCED CHARTS SECTION ===== */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.chart-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.chart-filters {
    display: flex;
    gap: 10px;
}

.chart-filter-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;
    color: #718096;
}

.chart-filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.chart-filter-btn:hover:not(.active) {
    background: #edf2f7;
    transform: translateY(-1px);
}

/* ===== ENHANCED EXCHANGE RATES ===== */
.exchange-rates-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== OFFICES STATISTICS ===== */
.offices-stats {
    padding: 20px 0;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f8fafc;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #edf2f7;
    transform: translateX(-5px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-icon.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
.stat-icon.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.stat-icon.danger { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); }

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 500;
}

/* ===== PAYPAL STATISTICS ===== */
.paypal-stats {
    padding: 20px 0;
}

.paypal-stats .stat-item {
    margin-bottom: 12px;
}

.paypal-stats .stat-value {
    font-size: 1.3rem;
}

/* ===== SYSTEM STATUS ===== */
.system-status {
    padding: 20px 0;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    margin-bottom: 10px;
    background: #f8fafc;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.status-item:hover {
    background: #edf2f7;
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.status-info {
    flex: 1;
}

.status-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2px;
}

.status-value {
    font-size: 0.8rem;
    color: #48bb78;
    font-weight: 500;
}

.rate-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 15px;
    background: #f8fafc;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.rate-item:hover {
    background: #edf2f7;
    transform: translateX(-5px);
}

.rate-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.rate-icon {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.rate-details h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2px;
}

.rate-details p {
    font-size: 0.85rem;
    color: #a0aec0;
    margin: 0;
}

.rate-value {
    text-align: left;
}

.rate-value .amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 2px;
}

.rate-value .change {
    font-size: 0.8rem;
    color: #48bb78;
    font-weight: 600;
}

/* ===== ENHANCED ACTIVITY SECTION ===== */
.activity-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.activity-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f8fafc;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #edf2f7;
    transform: translateX(-5px);
}

.activity-icon {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.activity-details {
    flex: 1;
}

.activity-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 3px;
    line-height: 1.4;
}

.activity-time {
    font-size: 0.8rem;
    color: #a0aec0;
}

.activity-amount {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
}

/* ===== ENHANCED QUICK ACTIONS ===== */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.quick-action-card {
    background: white;
    border-radius: 20px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.quick-action-card:hover::before {
    opacity: 1;
}

.quick-action-icon {
    width: 70px;
    height: 70px;
    border-radius: 20px;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    position: relative;
    z-index: 2;
}

.quick-action-card.exchange .quick-action-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.quick-action-card.transfer .quick-action-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.quick-action-card.customer .quick-action-icon { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
.quick-action-card.reports .quick-action-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.quick-action-card.office .quick-action-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.quick-action-card.paypal .quick-action-icon { background: linear-gradient(135deg, #003087 0%, #009cde 100%); }

.quick-action-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
    position: relative;
    z-index: 2;
}

.quick-action-desc {
    font-size: 0.85rem;
    color: #718096;
    position: relative;
    z-index: 2;
}

/* ===== ENHANCED NAVIGATION BUTTONS ===== */
.navigation-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.nav-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.nav-btn.secondary {
    background: white;
    color: #2d3748;
    border: 2px solid #e2e8f0;
}

.nav-btn.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.nav-btn.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.nav-btn.primary:hover { color: white; }
.nav-btn.secondary:hover { color: #2d3748; }
.nav-btn.success:hover { color: white; }
.nav-btn.warning:hover { color: white; }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .activity-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .dashboard-content {
        padding: 20px;
    }
    
    .dashboard-header {
        padding: 20px;
    }
    
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-card {
        padding: 20px;
    }
    
    .stats-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .stats-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .stats-value {
        font-size: 2rem;
    }
    
    .navigation-buttons {
        justify-content: center;
    }
    
    .nav-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .quick-action-card {
        padding: 20px;
    }
    
    .quick-action-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .stat-value {
        font-size: 1.8rem;
    }
    
    .status-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .status-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .dashboard-header h1 {
        font-size: 1.5rem;
    }
    
    .dashboard-header p {
        font-size: 1rem;
    }
    
    .stats-value {
        font-size: 1.8rem;
    }
    
    .chart-card,
    .exchange-rates-card,
    .activity-card {
        padding: 20px;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .status-title {
        font-size: 0.8rem;
    }
    
    .status-value {
        font-size: 0.7rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card,
.chart-card,
.exchange-rates-card,
.activity-card,
.quick-action-card {
    animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

/* ===== LOADING STATES ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* ===== SCROLLBAR STYLING ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
} 
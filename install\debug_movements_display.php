<?php
/**
 * Debug movements display issues
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/cash_manager.php';

echo "<h2>تشخيص مشاكل عرض الحركات المالية</h2>\n";

try {
    $db = new Database();
    $cashManager = new CashManager($db);
    $conn = Database::getConnection();
    
    echo "<h3>1. فحص البيانات الخام من قاعدة البيانات</h3>\n";
    
    // Get raw data from cash_movements
    $result = $conn->query("SELECT * FROM cash_movements ORDER BY id DESC LIMIT 3");
    if ($result) {
        echo "<p><strong>البيانات الخام من جدول cash_movements:</strong></p>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr style='background: #f8f9fa;'>\n";
        echo "<th>ID</th><th>cash_box_id</th><th>movement_type</th><th>amount</th><th>description</th><th>reference_number</th><th>user_id</th><th>created_at</th>\n";
        echo "</tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>\n";
            echo "<td>{$row['id']}</td>\n";
            echo "<td>{$row['cash_box_id']}</td>\n";
            echo "<td>{$row['movement_type']}</td>\n";
            echo "<td>{$row['amount']}</td>\n";
            echo "<td>" . htmlspecialchars($row['description']) . "</td>\n";
            echo "<td>" . htmlspecialchars($row['reference_number']) . "</td>\n";
            echo "<td>{$row['user_id']}</td>\n";
            echo "<td>{$row['created_at']}</td>\n";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    echo "<h3>2. اختبار دالة getCashBoxMovements</h3>\n";
    
    // Get a cash box ID
    $result = $conn->query("SELECT id FROM cash_boxes ORDER BY id DESC LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $cashBoxId = $result->fetch_assoc()['id'];
        echo "<p><strong>اختبار مع الصندوق رقم:</strong> $cashBoxId</p>\n";
        
        $movements = $cashManager->getCashBoxMovements($cashBoxId, [], 5, 0);
        
        echo "<p><strong>عدد الحركات المسترجعة:</strong> " . count($movements) . "</p>\n";
        
        if (!empty($movements)) {
            echo "<p><strong>بيانات الحركة الأولى:</strong></p>\n";
            $firstMovement = $movements[0];
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>المفتاح</th><th>القيمة</th>\n";
            echo "</tr>\n";
            
            foreach ($firstMovement as $key => $value) {
                echo "<tr>\n";
                echo "<td><strong>$key</strong></td>\n";
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
            
            echo "<h3>3. فحص الحقول المطلوبة</h3>\n";
            
            $requiredFields = ['id', 'movement_type', 'type', 'amount', 'description', 'reference_number', 'user_name', 'balance_after', 'created_at'];
            
            foreach ($requiredFields as $field) {
                if (array_key_exists($field, $firstMovement)) {
                    $value = $firstMovement[$field];
                    if ($value !== null && $value !== '') {
                        echo "<p style='color: green;'>✓ $field: " . htmlspecialchars($value) . "</p>\n";
                    } else {
                        echo "<p style='color: orange;'>⚠ $field: فارغ أو NULL</p>\n";
                    }
                } else {
                    echo "<p style='color: red;'>✗ $field: مفقود</p>\n";
                }
            }
            
            echo "<h3>4. اختبار حساب الرصيد</h3>\n";
            
            $balanceAfter = $firstMovement['balance_after'] ?? null;
            if ($balanceAfter !== null) {
                echo "<p style='color: green;'>✓ الرصيد بعد الحركة: " . number_format($balanceAfter, 2) . "</p>\n";
            } else {
                echo "<p style='color: red;'>✗ الرصيد بعد الحركة غير محسوب</p>\n";
                
                // Calculate manually
                $movementId = $firstMovement['id'];
                $manualBalanceQuery = "
                    SELECT SUM(CASE WHEN movement_type = 'deposit' THEN amount ELSE -amount END) as balance
                    FROM cash_movements 
                    WHERE cash_box_id = $cashBoxId 
                    AND created_at <= '{$firstMovement['created_at']}'
                    AND id <= $movementId
                ";
                
                $balanceResult = $conn->query($manualBalanceQuery);
                if ($balanceResult) {
                    $manualBalance = $balanceResult->fetch_assoc()['balance'];
                    echo "<p style='color: blue;'>ℹ️ الرصيد المحسوب يدوياً: " . number_format($manualBalance, 2) . "</p>\n";
                }
            }
            
        } else {
            echo "<p style='color: red;'>✗ لم يتم استرجاع أي حركات</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ لا توجد صناديق للاختبار</p>\n";
    }
    
    echo "<h3>5. فحص الاستعلام المستخدم</h3>\n";
    
    // Show the actual SQL query
    $sql = 'SELECT cm.*, u.full_name AS user_name, cb.name as cash_box_name,
                   curr.code as currency_code, curr.symbol as currency_symbol,
                   cm.movement_type as type, cm.exchange_id, cm.transfer_id,
                   (SELECT
                       COALESCE(
                           (SELECT SUM(CASE WHEN cm2.movement_type = "deposit" THEN cm2.amount ELSE -cm2.amount END)
                            FROM cash_movements cm2
                            WHERE cm2.cash_box_id = cm.cash_box_id
                            AND cm2.created_at <= cm.created_at
                            AND cm2.id <= cm.id),
                           0
                       )
                   ) as balance_after
            FROM cash_movements cm
            LEFT JOIN users u ON u.id = cm.user_id
            LEFT JOIN cash_boxes cb ON cb.id = cm.cash_box_id
            LEFT JOIN currencies curr ON curr.id = cb.currency_id
            WHERE cm.cash_box_id = ? ORDER BY cm.created_at DESC, cm.id DESC LIMIT 3';
    
    echo "<p><strong>الاستعلام المستخدم:</strong></p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-size: 12px;'>";
    echo htmlspecialchars($sql);
    echo "</pre>\n";
    
    echo "<h3>6. اختبار الاستعلام مباشرة</h3>\n";
    
    if (isset($cashBoxId)) {
        $stmt = $conn->prepare($sql);
        if ($stmt) {
            $stmt->bind_param('i', $cashBoxId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            echo "<p><strong>نتائج الاستعلام المباشر:</strong></p>\n";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th>ID</th><th>movement_type</th><th>type</th><th>amount</th><th>user_name</th><th>balance_after</th>\n";
            echo "</tr>\n";
            
            while ($row = $result->fetch_assoc()) {
                echo "<tr>\n";
                echo "<td>{$row['id']}</td>\n";
                echo "<td>{$row['movement_type']}</td>\n";
                echo "<td>{$row['type']}</td>\n";
                echo "<td>{$row['amount']}</td>\n";
                echo "<td>" . htmlspecialchars($row['user_name'] ?? 'NULL') . "</td>\n";
                echo "<td>{$row['balance_after']}</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
            
            $stmt->close();
        } else {
            echo "<p style='color: red;'>✗ فشل في تحضير الاستعلام: " . $conn->error . "</p>\n";
        }
    }
    
    echo "<hr>\n";
    echo "<h3 style='color: green;'>✅ انتهى تشخيص مشاكل عرض الحركات</h3>\n";
    echo "<p><strong>الخطوات التالية:</strong></p>\n";
    echo "<ul>\n";
    echo "<li><a href='../dashboard/cash_box_history.php?id=$cashBoxId' target='_blank'>اختبر صفحة تاريخ الصندوق</a></li>\n";
    echo "<li><a href='../dashboard/cash.php' target='_blank'>العودة لصفحة الصناديق</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>خطأ: " . $e->getMessage() . "</p>\n";
    echo "<p>تفاصيل الخطأ:</p>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>" . $e->getTraceAsString() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h2, h3 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
hr { margin: 20px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background: #f8f9fa; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
pre { font-size: 12px; }
</style>
